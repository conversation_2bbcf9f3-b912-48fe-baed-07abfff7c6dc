import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import 'package:yassincil/providers/food_provider.dart';
import 'package:yassincil/models/food_item.dart';

class AddEditFoodScreen extends StatefulWidget {
  final FoodItem? foodItem;

  const AddEditFoodScreen({super.key, this.foodItem});

  @override
  State<AddEditFoodScreen> createState() => _AddEditFoodScreenState();
}

class _AddEditFoodScreenState extends State<AddEditFoodScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _detailsController = TextEditingController();
  final _imageUrlController = TextEditingController();
  final _ingredientsController = TextEditingController();
  final _warningsController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _brandController = TextEditingController();

  bool _isGlutenFree = true;
  File? _selectedImage;
  bool _isLoading = false;
  bool _useImageUrl = false;
  String _selectedCategory = 'حبوب ومخبوزات';

  final List<String> _categories = [
    'حبوب ومخبوزات',
    'ألبان ومنتجاتها',
    'خضروات',
    'فواكه',
    'لحوم وأسماك',
    'حلويات',
    'مشروبات',
    'توابل وبهارات',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.foodItem != null) {
      _nameController.text = widget.foodItem!.name;
      _detailsController.text = widget.foodItem!.details;
      _isGlutenFree = widget.foodItem!.isGlutenFree;
      _selectedCategory = widget.foodItem!.category;
      _barcodeController.text = widget.foodItem!.barcode ?? '';
      _brandController.text = widget.foodItem!.brand ?? '';
      _ingredientsController.text = widget.foodItem!.ingredients ?? '';
      _warningsController.text = widget.foodItem!.warnings ?? '';
      if (widget.foodItem!.imageUrl != null) {
        _imageUrlController.text = widget.foodItem!.imageUrl!;
        _useImageUrl = true;
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _detailsController.dispose();
    _imageUrlController.dispose();
    _barcodeController.dispose();
    _brandController.dispose();
    _ingredientsController.dispose();
    _warningsController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 800,
      maxHeight: 600,
      imageQuality: 80,
    );

    if (pickedFile != null) {
      setState(() {
        _selectedImage = File(pickedFile.path);
        _useImageUrl = false;
        _imageUrlController.clear();
      });
    }
  }

  Future<void> _saveFood() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final foodProvider = Provider.of<FoodProvider>(context, listen: false);

      String? finalImageUrl;
      if (_useImageUrl && _imageUrlController.text.trim().isNotEmpty) {
        finalImageUrl = _imageUrlController.text.trim();
      }

      if (widget.foodItem == null) {
        await foodProvider.addFoodItem(
          name: _nameController.text.trim(),
          details: _detailsController.text.trim(),
          category: _selectedCategory,
          isGlutenFree: _isGlutenFree,
          imageFile: _selectedImage,
          imageUrl: finalImageUrl,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم إضافة الطعام بنجاح',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        }
      } else {
        await foodProvider.updateFoodItem(
          foodId: widget.foodItem!.id!,
          name: _nameController.text.trim(),
          details: _detailsController.text.trim(),
          category: _selectedCategory,
          isGlutenFree: _isGlutenFree,
          imageFile: _selectedImage,
          imageUrl: finalImageUrl,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم تحديث الطعام بنجاح',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEditing = widget.foodItem != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isEditing ? 'تعديل الطعام' : 'إضافة طعام جديد',
        ),
        centerTitle: true,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.save_rounded),
              onPressed: _saveFood,
              tooltip: 'حفظ',
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildImageSection(),
            const SizedBox(height: 20),
            _buildNameField(),
            const SizedBox(height: 16),
            _buildBrandField(),
            const SizedBox(height: 16),
            _buildBarcodeField(),
            const SizedBox(height: 16),
            _buildCategoryDropdown(),
            const SizedBox(height: 16),
            _buildIngredientsField(),
            const SizedBox(height: 16),
            _buildGlutenFreeSwitch(),
            const SizedBox(height: 16),
            _buildDetailsField(),
            const SizedBox(height: 16),
            _buildWarningsField(),
            const SizedBox(height: 32),
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryDropdown() {
    final theme = Theme.of(context);
    return DropdownButtonFormField<String>(
      value: _selectedCategory,
      style: theme.textTheme.bodyLarge,
      decoration: InputDecoration(
        labelText: 'فئة الطعام',
        prefixIcon: Icon(
          Icons.category_rounded,
          color: theme.primaryColor,
        ),
      ),
      items: _categories.map((category) {
        return DropdownMenuItem(
          value: category,
          child: Text(category),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedCategory = value!;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى اختيار فئة الطعام';
        }
        return null;
      },
    );
  }

  Widget _buildImageSection() {
    final theme = Theme.of(context);
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        children: [
          Container(
            height: 200,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
              gradient: LinearGradient(
                colors: [theme.primaryColor.withOpacity(0.2), theme.primaryColor.withOpacity(0.05)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Stack(
              children: [
                if (_selectedImage != null)
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                    child: Image.file(
                      _selectedImage!,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  )
                else if (_useImageUrl && _imageUrlController.text.isNotEmpty)
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                    child: Image.network(
                      _imageUrlController.text,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildImagePlaceholder(),
                    ),
                  )
                else if (widget.foodItem?.imageUrl != null && !_useImageUrl)
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                    child: Image.network(
                      widget.foodItem!.imageUrl!,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildImagePlaceholder(),
                    ),
                  )
                else
                  _buildImagePlaceholder(),

                Positioned(
                  bottom: 16,
                  right: 16,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      FloatingActionButton.small(
                        onPressed: _pickImage,
                        heroTag: "food_camera",
                        child: const Icon(Icons.camera_alt),
                      ),
                      const SizedBox(width: 8),
                      FloatingActionButton.small(
                        onPressed: () {
                          setState(() {
                            _useImageUrl = !_useImageUrl;
                            if (_useImageUrl) {
                              _selectedImage = null;
                            } else {
                              _imageUrlController.clear();
                            }
                          });
                        },
                        heroTag: "food_link",
                        child: const Icon(Icons.link),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          if (_useImageUrl)
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextFormField(
                controller: _imageUrlController,
                decoration: const InputDecoration(
                  labelText: 'رابط الصورة',
                  prefixIcon: Icon(Icons.link),
                ),
                onChanged: (value) => setState(() {}),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildImagePlaceholder() {
    final theme = Theme.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.restaurant_menu, size: 60, color: theme.primaryColor.withOpacity(0.5)),
          const SizedBox(height: 8),
          Text(
            'اضغط لإضافة صورة',
            style: theme.textTheme.titleLarge?.copyWith(color: theme.primaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    int maxLines = 1,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        alignLabelWithHint: maxLines > 1,
      ),
      validator: validator,
    );
  }

  Widget _buildNameField() {
    return _buildCustomTextField(
      controller: _nameController,
      label: 'اسم الطعام',
      icon: Icons.fastfood,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال اسم الطعام';
        }
        return null;
      },
    );
  }

  Widget _buildBrandField() {
    return _buildCustomTextField(
      controller: _brandController,
      label: 'العلامة التجارية (اختياري)',
      icon: Icons.business,
    );
  }

  Widget _buildBarcodeField() {
    return _buildCustomTextField(
      controller: _barcodeController,
      label: 'الباركود (اختياري)',
      icon: Icons.qr_code_scanner,
      keyboardType: TextInputType.number,
    );
  }

  Widget _buildIngredientsField() {
    return _buildCustomTextField(
      controller: _ingredientsController,
      label: 'المكونات (اختياري)',
      icon: Icons.science,
      maxLines: 3,
    );
  }

  Widget _buildDetailsField() {
    return _buildCustomTextField(
      controller: _detailsController,
      label: 'تفاصيل إضافية',
      icon: Icons.note_add,
      maxLines: 4,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال التفاصيل';
        }
        return null;
      },
    );
  }

  Widget _buildWarningsField() {
    return _buildCustomTextField(
      controller: _warningsController,
      label: 'تحذيرات (اختياري)',
      icon: Icons.warning,
      maxLines: 2,
    );
  }

  Widget _buildGlutenFreeSwitch() {
    final theme = Theme.of(context);
    return SwitchListTile(
      title: Text('خالي من الجلوتين', style: theme.textTheme.titleLarge),
      subtitle: Text(_isGlutenFree ? 'آمن للاستخدام' : 'يحتوي على جلوتين'),
      value: _isGlutenFree,
      onChanged: (value) {
        setState(() {
          _isGlutenFree = value;
        });
      },
      activeColor: theme.colorScheme.primary,
      inactiveThumbColor: theme.colorScheme.error,
    );
  }

  Widget _buildSaveButton() {
    return ElevatedButton.icon(
      onPressed: _isLoading ? null : _saveFood,
      icon: _isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
              ),
            )
          : const Icon(Icons.save),
      label: Text(
        _isLoading
            ? 'جاري الحفظ...'
            : (widget.foodItem == null ? 'إضافة الطعام' : 'حفظ التغييرات'),
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:yassincil/models/comment.dart';
import 'package:yassincil/providers/doctor_provider.dart';
import 'package:yassincil/providers/auth_provider.dart' as app_auth;
import 'package:yassincil/utils/app_colors.dart';

class AdvancedDoctorCommentWidget extends StatefulWidget {
  final Comment comment;
  final String doctorId;

  const AdvancedDoctorCommentWidget({
    super.key,
    required this.comment,
    required this.doctorId,
  });

  @override
  State<AdvancedDoctorCommentWidget> createState() =>
      _AdvancedDoctorCommentWidgetState();
}

class _AdvancedDoctorCommentWidgetState
    extends State<AdvancedDoctorCommentWidget> {
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  child: Text(widget.comment.username.substring(0, 1)),
                ),
                const SizedBox(width: 8),
                Text(
                  widget.comment.username,
                  style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(widget.comment.content, style: GoogleFonts.cairo()),
          ],
        ),
      ),
    );
  }
}

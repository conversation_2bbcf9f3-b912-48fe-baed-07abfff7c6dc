import 'package:cloud_firestore/cloud_firestore.dart';

class SliderItem {
  String? id;
  final String imageUrl;
  final String title;
  final String? description;
  final String? linkUrl;
  final String targetScreen;
  final Timestamp createdAt;
  final int order;
  final bool isActive;
  final Timestamp? startDate;
  final Timestamp? endDate;
  final List<String> likes; // قائمة معرفات المستخدمين الذين أعجبوا بالسلايدر
  final int likesCount;
  final int commentsCount;

  SliderItem({
    this.id,
    required this.imageUrl,
    required this.title,
    this.description,
    this.linkUrl,
    required this.targetScreen,
    required this.createdAt,
    this.order = 0,
    this.isActive = true,
    this.startDate,
    this.endDate,
    this.likes = const [],
    this.likesCount = 0,
    this.commentsCount = 0,
  });

  factory SliderItem.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return SliderItem(
      id: doc.id,
      imageUrl: data['imageUrl'] ?? '',
      title: data['title'] ?? 'لا يوجد عنوان',
      description: data['description'],
      linkUrl: data['linkUrl'],
      targetScreen: data['targetScreen'] ?? 'home',
      createdAt: data['createdAt'] ?? Timestamp.now(),
      order: data['order'] ?? 0,
      isActive: data['isActive'] ?? true,
      startDate: data['startDate'],
      endDate: data['endDate'],
      likes: List<String>.from(data['likes'] ?? []),
      likesCount: data['likesCount'] ?? 0,
      commentsCount: data['commentsCount'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'imageUrl': imageUrl,
      'title': title,
      'description': description,
      'linkUrl': linkUrl,
      'targetScreen': targetScreen,
      'createdAt': createdAt,
      'order': order,
      'isActive': isActive,
      'startDate': startDate,
      'endDate': endDate,
      'likes': likes,
      'likesCount': likesCount,
      'commentsCount': commentsCount,
    };
  }
}

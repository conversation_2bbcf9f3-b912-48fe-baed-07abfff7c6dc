import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/providers/search_provider.dart';
import 'package:yassincil/services/advanced_search_service.dart';
import 'package:yassincil/screens/foods/food_detail_screen.dart';
import 'package:yassincil/screens/medications/medication_detail_screen.dart';
import 'package:yassincil/screens/recipes/recipe_detail_screen.dart';
import 'package:yassincil/screens/restaurants/restaurant_detail_screen_enhanced.dart';
import 'package:yassincil/screens/articles/article_detail_screen.dart';
import 'package:yassincil/screens/barcode/product_detail_screen.dart';
import 'package:yassincil/screens/forum/post_detail_screen.dart';

class SearchResultsScreen extends StatefulWidget {
  final String query;

  const SearchResultsScreen({super.key, required this.query});

  @override
  State<SearchResultsScreen> createState() => _SearchResultsScreenState();
}

class _SearchResultsScreenState extends State<SearchResultsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchController.text = widget.query;
    _tabController = TabController(
      length: SearchCategory.values.length,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _performNewSearch() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      Provider.of<SearchProvider>(context, listen: false).searchAll(query);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'نتائج البحث',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث مرة أخرى...',
                hintStyle: GoogleFonts.cairo(color: Colors.grey[500]),
                prefixIcon: Icon(Icons.search, color: theme.primaryColor),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.search),
                  onPressed: _performNewSearch,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              onSubmitted: (_) => _performNewSearch(),
            ),
          ),
        ),
      ),
      body: Consumer<SearchProvider>(
        builder: (context, searchProvider, child) {
          if (searchProvider.isSearching) {
            return const Center(child: CircularProgressIndicator());
          }

          if (searchProvider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ في البحث',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    searchProvider.errorMessage!,
                    style: GoogleFonts.cairo(color: Colors.grey[600]),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      searchProvider.clearError();
                      _performNewSearch();
                    },
                    child: Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (!searchProvider.searchResults.hasResults) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد نتائج',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'جرب استخدام كلمات مفتاحية مختلفة',
                    style: GoogleFonts.cairo(color: Colors.grey[600]),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // إحصائيات النتائج
              _buildResultsStats(searchProvider, theme),

              // تبويبات الفئات
              _buildCategoryTabs(searchProvider, theme),

              // محتوى النتائج
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: SearchCategory.values.map((category) {
                    return _buildCategoryResults(category, searchProvider);
                  }).toList(),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildResultsStats(SearchProvider searchProvider, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.deepPurple.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(Icons.search, color: theme.primaryColor),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'نتائج البحث عن: "${widget.query}"',
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  'تم العثور على ${searchProvider.searchResults.totalCount} نتيجة',
                  style: GoogleFonts.cairo(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryTabs(SearchProvider searchProvider, ThemeData theme) {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: theme.primaryColor,
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: theme.primaryColor,
        tabs: SearchCategory.values.map((category) {
          final count = searchProvider.getCategoryResultCount(category);
          return Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(searchProvider.getCategoryIcon(category), size: 16),
                const SizedBox(width: 6),
                Text(
                  '${searchProvider.getCategoryName(category)} ($count)',
                  style: GoogleFonts.cairo(fontSize: 12),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildCategoryResults(
    SearchCategory category,
    SearchProvider searchProvider,
  ) {
    switch (category) {
      case SearchCategory.foods:
        return _buildFoodsList(searchProvider.searchResults.foods);
      case SearchCategory.medications:
        return _buildMedicationsList(searchProvider.searchResults.medications);
      case SearchCategory.recipes:
        return _buildRecipesList(searchProvider.searchResults.recipes);
      case SearchCategory.restaurants:
        return _buildRestaurantsList(searchProvider.searchResults.restaurants);
      case SearchCategory.articles:
        return _buildArticlesList(searchProvider.searchResults.articles);
      case SearchCategory.products:
        return _buildProductsList(searchProvider.searchResults.products);
      case SearchCategory.forumPosts:
        return _buildForumPostsList(searchProvider.searchResults.forumPosts);
    }
  }

  Widget _buildFoodsList(List foods) {
    if (foods.isEmpty) {
      return const Center(child: Text('لا توجد أطعمة'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: foods.length,
      itemBuilder: (context, index) {
        final food = foods[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: food.isGlutenFree ? Colors.green : Colors.orange,
              child: Icon(Icons.fastfood, color: Colors.white),
            ),
            title: Text(food.name, style: GoogleFonts.cairo()),
            subtitle: Text(food.description, style: GoogleFonts.cairo()),
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => FoodDetailScreen(foodItem: food),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildMedicationsList(List medications) {
    if (medications.isEmpty) {
      return const Center(child: Text('لا توجد أدوية'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: medications.length,
      itemBuilder: (context, index) {
        final medication = medications[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: medication.isGlutenFree
                  ? Colors.green
                  : Colors.red,
              child: Icon(Icons.medical_services, color: Colors.white),
            ),
            title: Text(medication.name, style: GoogleFonts.cairo()),
            subtitle: Text(medication.description, style: GoogleFonts.cairo()),
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) =>
                      MedicationDetailScreen(medication: medication),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildRecipesList(List recipes) {
    if (recipes.isEmpty) {
      return const Center(child: Text('لا توجد وصفات'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: recipes.length,
      itemBuilder: (context, index) {
        final recipe = recipes[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: recipe.isGlutenFree
                  ? Colors.green
                  : Colors.orange,
              child: Icon(Icons.menu_book, color: Colors.white),
            ),
            title: Text(recipe.title, style: GoogleFonts.cairo()),
            subtitle: Text(recipe.description, style: GoogleFonts.cairo()),
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => RecipeDetailScreen(recipe: recipe),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildRestaurantsList(List restaurants) {
    if (restaurants.isEmpty) {
      return const Center(child: Text('لا توجد مطاعم'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: restaurants.length,
      itemBuilder: (context, index) {
        final restaurant = restaurants[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: restaurant.hasGlutenFreeOptions
                  ? Colors.green
                  : Colors.grey,
              child: Icon(Icons.restaurant, color: Colors.white),
            ),
            title: Text(restaurant.name, style: GoogleFonts.cairo()),
            subtitle: Text(restaurant.address, style: GoogleFonts.cairo()),
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) =>
                      RestaurantDetailScreenEnhanced(restaurant: restaurant),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildArticlesList(List articles) {
    if (articles.isEmpty) {
      return const Center(child: Text('لا توجد مقالات'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: articles.length,
      itemBuilder: (context, index) {
        final article = articles[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.blue,
              child: Icon(Icons.article, color: Colors.white),
            ),
            title: Text(article.title, style: GoogleFonts.cairo()),
            subtitle: Text(
              article.content.length > 100
                  ? '${article.content.substring(0, 100)}...'
                  : article.content,
              style: GoogleFonts.cairo(),
            ),
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => ArticleDetailScreen(article: article),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildProductsList(List products) {
    if (products.isEmpty) {
      return const Center(child: Text('لا توجد منتجات'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: product.isGlutenFree == true
                  ? Colors.green
                  : Colors.orange,
              child: Icon(Icons.inventory, color: Colors.white),
            ),
            title: Text(product.name, style: GoogleFonts.cairo()),
            subtitle: Text(
              '${product.brand} - ${product.barcode}',
              style: GoogleFonts.cairo(),
            ),
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => ProductDetailScreen(product: product),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildForumPostsList(List posts) {
    if (posts.isEmpty) {
      return const Center(child: Text('لا توجد منشورات'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        final post = posts[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.purple,
              child: Icon(Icons.forum, color: Colors.white),
            ),
            title: Text(post.title, style: GoogleFonts.cairo()),
            subtitle: Text(
              post.content.length > 100
                  ? '${post.content.substring(0, 100)}...'
                  : post.content,
              style: GoogleFonts.cairo(),
            ),
            trailing: Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => PostDetailScreen(post: post),
                ),
              );
            },
          ),
        );
      },
    );
  }
}

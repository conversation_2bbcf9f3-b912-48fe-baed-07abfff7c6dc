import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class FilterSortScreen extends StatefulWidget {
  final String selectedCategory;
  final bool showOnlyGlutenFree;
  final String sortBy;

  const FilterSortScreen({
    super.key,
    required this.selectedCategory,
    required this.showOnlyGlutenFree,
    required this.sortBy,
  });

  @override
  State<FilterSortScreen> createState() => _FilterSortScreenState();
}

class _FilterSortScreenState extends State<FilterSortScreen> {
  late String _selectedCategory;
  late bool _showOnlyGlutenFree;
  late String _sortBy;

  final List<Map<String, dynamic>> _categories = [
    {'name': 'الكل', 'icon': Icons.fastfood_rounded},
    {'name': 'فطور', 'icon': Icons.free_breakfast_rounded},
    {'name': 'غداء', 'icon': Icons.lunch_dining_rounded},
    {'name': 'عشاء', 'icon': Icons.dinner_dining_rounded},
    {'name': 'حلويات', 'icon': Icons.cake_rounded},
    {'name': 'مشروبات', 'icon': Icons.local_bar_rounded},
    {'name': 'سلطات', 'icon': Icons.restaurant_menu_rounded},
    {'name': 'مقبلات', 'icon': Icons.fastfood_outlined},
    {'name': 'مخبوزات', 'icon': Icons.bakery_dining_rounded},
    {'name': 'أخرى', 'icon': Icons.more_horiz_rounded},
  ];

  final Map<String, String> _sortOptions = {
    'date': 'الأحدث أولاً',
    'name': 'الاسم (أ-ي)',
    'popularity': 'الأكثر شعبية',
  };

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.selectedCategory;
    _showOnlyGlutenFree = widget.showOnlyGlutenFree;
    _sortBy = widget.sortBy;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(theme),
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSectionTitle('الفئة', Icons.category_rounded, theme),
                  const SizedBox(height: 16),
                  _buildCategoryGrid(),
                  const SizedBox(height: 32),
                  _buildSectionTitle('الترتيب حسب', Icons.sort_by_alpha_rounded, theme),
                  const SizedBox(height: 16),
                  _buildSortOptions(theme),
                  const SizedBox(height: 32),
                  _buildGlutenFreeSwitch(theme),
                ],
              ),
            ),
          ),
          _buildApplyButton(theme),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'الفلترة والترتيب',
            style: GoogleFonts.cairo(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close_rounded),
            onPressed: () => Navigator.of(context).pop(),
          )
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon, ThemeData theme) {
    return Row(
      children: [
        Icon(icon, color: theme.colorScheme.primary, size: 24),
        const SizedBox(width: 12),
        Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryGrid() {
    return Wrap(
      spacing: 12.0,
      runSpacing: 12.0,
      children: _categories.map((category) {
        final isSelected = _selectedCategory == category['name'];
        return InkWell(
          onTap: () {
            setState(() {
              _selectedCategory = category['name']!;
            });
          },
          borderRadius: BorderRadius.circular(16),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isSelected ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.outline.withOpacity(0.3),
                width: 1.5,
              ),
              boxShadow: isSelected ? [
                BoxShadow(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                )
              ] : [],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  category['icon'],
                  color: isSelected ? Theme.of(context).colorScheme.onPrimary : Theme.of(context).colorScheme.onSurface,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  category['name']!,
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.w600,
                    color: isSelected ? Theme.of(context).colorScheme.onPrimary : Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSortOptions(ThemeData theme) {
    return Column(
      children: _sortOptions.entries.map((entry) {
        final isSelected = _sortBy == entry.key;
        return InkWell(
          onTap: () => setState(() => _sortBy = entry.key),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
            decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: theme.dividerColor, width: 0.5)),
            ),
            child: Row(
              children: [
                Text(
                  entry.value,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                if (isSelected)
                  Icon(
                    Icons.check_circle_rounded,
                    color: theme.colorScheme.primary,
                  )
                else
                  Icon(
                    Icons.radio_button_unchecked_rounded,
                    color: theme.colorScheme.outline,
                  ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildGlutenFreeSwitch(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.3), width: 1.5),
      ),
      child: SwitchListTile(
        title: Text(
          'إظهار الوصفات الخالية من الجلوتين فقط',
          style: GoogleFonts.cairo(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        value: _showOnlyGlutenFree,
        onChanged: (value) {
          setState(() {
            _showOnlyGlutenFree = value;
          });
        },
        activeColor: theme.colorScheme.primary,
        contentPadding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildApplyButton(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          Navigator.of(context).pop({
            'category': _selectedCategory,
            'glutenFree': _showOnlyGlutenFree,
            'sortBy': _sortBy,
          });
        },
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          textStyle: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        child: const Text('تطبيق الفلاتر'),
      ),
    );
  }
}

// lib/config/interaction_config.dart
import 'package:flutter/material.dart';

class InteractionConfig {
  // Animation durations
  static const Duration likeAnimationDuration = Duration(milliseconds: 300);
  static const Duration commentExpandDuration = Duration(milliseconds: 250);
  static const Duration shareAnimationDuration = Duration(milliseconds: 200);

  // Colors
  static const int facebookBlue = 0xFF1877F2;
  static const int likeColor = 0xFF1877F2;
  static const int commentColor = 0xFF65676B;
  static const int shareColor = 0xFF65676B;
  static const int ratingColor = 0xFFFFA726;

  // Sizes
  static const double actionButtonHeight = 48.0;
  static const double iconSize = 20.0;
  static const double avatarRadius = 18.0;
  static const double borderRadius = 16.0;

  // Limits
  static const int maxCommentsToShow = 5;
  static const int maxCommentLength = 500;
  static const int maxRating = 5;

  // Haptic feedback settings
  static const bool enableHapticFeedback = true;

  // Cache settings
  static const Duration cacheExpiration = Duration(minutes: 5);
  static const int maxCachedInteractions = 100;
}

// Interaction types enum
enum InteractionType { like, comment, share, rating, save, report }

// Animation curves
class InteractionCurves {
  static const elasticOut = Curves.elasticOut;
  static const easeInOut = Curves.easeInOut;
  static const bounceIn = Curves.bounceIn;
  static const fastOutSlowIn = Curves.fastOutSlowIn;
}

// Text styles configuration
class InteractionTextStyles {
  static const double headerFontSize = 18.0;
  static const double bodyFontSize = 14.0;
  static const double captionFontSize = 12.0;
  static const double buttonFontSize = 14.0;
}

// Layout configuration
class InteractionLayout {
  static const double horizontalPadding = 16.0;
  static const double verticalPadding = 12.0;
  static const double itemSpacing = 8.0;
  static const double sectionSpacing = 16.0;
}

// lib/providers/food_provider.dart
import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:yassincil/models/food_item.dart';
import 'package:yassincil/models/comment.dart';
import 'package:yassincil/services/firestore_service.dart';
import 'package:yassincil/services/storage_service.dart';
import 'package:yassincil/utils/app_constants.dart';

class FoodProvider with ChangeNotifier {
  final FirestoreService _firestoreService;
  final StorageService _storageService;
  List<FoodItem> _foodItems = [];
  bool _isLoading = false;
  String? _errorMessage;
  StreamSubscription? _foodItemsSubscription;

  List<FoodItem> get foodItems => _foodItems;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  FoodProvider(this._firestoreService, this._storageService) {
    fetchFoodItems();
  }

  Future<void> fetchFoodItems() async {
    // تجنب إعادة جلب البيانات إذا كانت قيد التحميل بالفعل
    if (_isLoading) return;
    final completer = Completer<void>();

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    // إلغاء الاشتراك السابق بأمان
    _foodItemsSubscription?.cancel();
    _foodItemsSubscription = null;

    try {
      _foodItemsSubscription = _firestoreService
          .getCollectionStream(
            AppConstants.foodItemsCollection,
            orderBy: 'name',
            limit: 50, // Fetch latest 50 items for good performance
          )
          .listen(
            (snapshot) {
              if (snapshot.docs.isNotEmpty) {
                _foodItems = snapshot.docs
                    .map((doc) => FoodItem.fromFirestore(doc))
                    .toList();
              }
              _isLoading = false;
              _errorMessage = null;
              notifyListeners();
              if (!completer.isCompleted) completer.complete();
            },
            onError: (error) {
              _errorMessage = "حدث خطأ أثناء جلب الأطعمة: $error";
              _isLoading = false;
              debugPrint("Error fetching food items: $error");
              notifyListeners();
              if (!completer.isCompleted) completer.completeError(error);
            },
          );
    } catch (e) {
      _errorMessage = "حدث خطأ في الاتصال: $e";
      _isLoading = false;
      debugPrint("Error setting up stream: $e");
      notifyListeners();
      if (!completer.isCompleted) completer.completeError(e);
    }
    return completer.future;
  }

  Future<void> deleteFoodItem(String foodItemId) async {
    try {
      // Deleting subcollections should ideally be handled by a Cloud Function.
      final commentsSnapshot = await _firestoreService.getCollection(
        '${AppConstants.foodItemsCollection}/$foodItemId/${AppConstants.commentsSubcollection}',
      );
      for (var doc in commentsSnapshot.docs) {
        await doc.reference.delete();
      }
      final likesSnapshot = await _firestoreService.getCollection(
        '${AppConstants.foodItemsCollection}/$foodItemId/likes',
      );
      for (var doc in likesSnapshot.docs) {
        await doc.reference.delete();
      }

      await _firestoreService.deleteDocument(
        AppConstants.foodItemsCollection,
        foodItemId,
      );
      // No need to manually update list, stream will do it.
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء حذف العنصر الغذائي: ${e.toString()}";
      rethrow;
    }
  }

  // ... (like and comment functions remain the same, but they will trigger the stream)

  @override
  void dispose() {
    _foodItemsSubscription?.cancel();
    _foodItemsSubscription = null;
    super.dispose();
  }

  // دوال جديدة للإضافة والتحديث مع معاملات منفصلة
  Future<void> addFoodItem({
    required String name,
    required String details,
    required String category,
    required bool isGlutenFree,
    File? imageFile,
    String? imageUrl,
  }) async {
    try {
      String? finalImageUrl = imageUrl;

      // رفع الصورة إذا كانت موجودة
      if (imageFile != null) {
        finalImageUrl = await _storageService.uploadFile(
          imageFile,
          'food_images/${DateTime.now().millisecondsSinceEpoch}.jpg',
        );
      }

      final foodItem = FoodItem(
        name: name,
        details: details,
        category: category,
        isGlutenFree: isGlutenFree,
        imageUrls: finalImageUrl != null && finalImageUrl.isNotEmpty
            ? [finalImageUrl]
            : [],
        likesCount: 0,
        commentsCount: 0,
        createdAt: DateTime.now(),
      );

      await _firestoreService.addDocument(
        AppConstants.foodItemsCollection,
        foodItem.toMap(),
      );
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء إضافة العنصر الغذائي: ${e.toString()}";
      rethrow;
    }
  }

  Future<void> updateFoodItem({
    required String foodId,
    required String name,
    required String details,
    required String category,
    required bool isGlutenFree,
    File? imageFile,
    String? imageUrl,
  }) async {
    try {
      String? finalImageUrl = imageUrl;

      // رفع الصورة الجديدة إذا كانت موجودة
      if (imageFile != null) {
        finalImageUrl = await _storageService.uploadFile(
          imageFile,
          'food_images/${DateTime.now().millisecondsSinceEpoch}.jpg',
        );
      }

      final updateData = {
        'name': name,
        'details': details,
        'category': category,
        'isGlutenFree': isGlutenFree,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // إضافة رابط الصورة فقط إذا كانت موجودة
      if (finalImageUrl != null) {
        updateData['imageUrl'] = finalImageUrl;
      }

      await _firestoreService.updateDocument(
        AppConstants.foodItemsCollection,
        foodId,
        updateData,
      );
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء تحديث العنصر الغذائي: ${e.toString()}";
      rethrow;
    }
  }

  Future<FoodItem?> searchFoodByBarcode(String barcode) async {
    try {
      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.foodItemsCollection)
          .where('barcode', isEqualTo: barcode)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return FoodItem.fromFirestore(querySnapshot.docs.first);
      }
      return null;
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء البحث بالباركود: $e";
      debugPrint("Error searching by barcode: $e");
      notifyListeners();
      return null;
    }
  }

  // --- Like and Comment functions (no major changes needed for them to work with streams) ---
  Future<bool> hasLikedFoodItem(String foodItemId, String userId) async {
    try {
      final doc = await _firestoreService.getDocument(
        '${AppConstants.foodItemsCollection}/$foodItemId/likes',
        userId,
      );
      return doc.exists;
    } catch (e) {
      debugPrint("Error checking like status: $e");
      return false;
    }
  }

  Future<void> toggleLikeOnFoodItem(String foodItemId, String userId) async {
    try {
      // التحقق من تسجيل الدخول أولاً
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception("يجب تسجيل الدخول أولاً");
      }

      final likeRef = _firestoreService.db
          .collection(AppConstants.foodItemsCollection)
          .doc(foodItemId)
          .collection('likes')
          .doc(user.uid);
      final foodItemRef = _firestoreService.db
          .collection(AppConstants.foodItemsCollection)
          .doc(foodItemId);

      await _firestoreService.db.runTransaction((transaction) async {
        final foodItemSnapshot = await transaction.get(foodItemRef);
        if (!foodItemSnapshot.exists) {
          throw Exception("Food item does not exist!");
        }

        int currentLikes =
            (foodItemSnapshot.data()?['likesCount'] as int?) ?? 0;
        final likeDoc = await transaction.get(likeRef);

        if (likeDoc.exists) {
          transaction.delete(likeRef);
          transaction.update(foodItemRef, {'likesCount': currentLikes - 1});
        } else {
          transaction.set(likeRef, {
            'userId': user.uid,
            'timestamp': FieldValue.serverTimestamp(),
          });
          transaction.update(foodItemRef, {'likesCount': currentLikes + 1});
        }
      });

      // تحديث قائمة الأطعمة لعرض العدد الجديد للإعجابات
      fetchFoodItems();
    } catch (e) {
      debugPrint("Error toggling like on food item: $e");

      // معالجة خاصة لأخطاء الصلاحيات
      if (e.toString().contains('permission-denied')) {
        throw Exception(
          "ليس لديك صلاحية للإعجاب. يرجى التأكد من تسجيل الدخول.",
        );
      } else if (e.toString().contains('unauthenticated')) {
        throw Exception("يجب تسجيل الدخول أولاً للإعجاب.");
      }

      rethrow;
    }
  }

  Stream<List<Comment>> getCommentsForFoodItem(String foodItemId) {
    try {
      return _firestoreService
          .getCollectionStream(
            '${AppConstants.foodItemsCollection}/$foodItemId/${AppConstants.commentsSubcollection}',
            orderBy: 'createdAt',
          )
          .map(
            (snapshot) =>
                snapshot.docs.map((doc) => Comment.fromFirestore(doc)).toList(),
          );
    } catch (e) {
      debugPrint("Error getting comments stream: $e");
      return Stream.value([]);
    }
  }

  // دالة مساعدة لجلب التعليقات مرة واحدة
  Future<List<Comment>> fetchCommentsForFoodItem(String foodItemId) async {
    try {
      final snapshot = await _firestoreService.db
          .collection(AppConstants.foodItemsCollection)
          .doc(foodItemId)
          .collection(AppConstants.commentsSubcollection)
          .orderBy('createdAt', descending: false)
          .get();

      return snapshot.docs.map((doc) => Comment.fromFirestore(doc)).toList();
    } catch (e) {
      debugPrint("Error fetching comments: $e");
      return [];
    }
  }

  Future<void> addCommentToFoodItem(String foodItemId, Comment comment) async {
    try {
      // التحقق من تسجيل الدخول أولاً
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception("يجب تسجيل الدخول أولاً");
      }

      debugPrint("Adding comment for user: ${user.uid}");
      debugPrint("User display name: ${user.displayName}");
      debugPrint("User email: ${user.email}");

      // إنشاء التعليق مع معرف المستخدم الصحيح
      final commentData = {
        'content': comment.content,
        'userId': user.uid,
        'username': user.displayName ?? user.email ?? 'مستخدم',
        'createdAt': FieldValue.serverTimestamp(),
      };

      debugPrint("Comment data: $commentData");

      // محاولة إضافة التعليق مباشرة بدون transaction أولاً
      final foodItemRef = _firestoreService.db
          .collection(AppConstants.foodItemsCollection)
          .doc(foodItemId);

      // إضافة التعليق مباشرة
      await foodItemRef
          .collection(AppConstants.commentsSubcollection)
          .add(commentData);

      // تحديث عدد التعليقات
      await foodItemRef.update({'commentsCount': FieldValue.increment(1)});

      debugPrint("Comment added successfully");

      // تحديث قائمة الأطعمة لعرض العدد الجديد للتعليقات
      fetchFoodItems();
    } catch (e) {
      debugPrint("Error adding comment to food item: $e");
      debugPrint("Error type: ${e.runtimeType}");

      // معالجة خاصة لأخطاء الصلاحيات
      if (e.toString().contains('permission-denied')) {
        throw Exception(
          "ليس لديك صلاحية لإضافة التعليقات. تأكد من تحديث قواعد Firebase.",
        );
      } else if (e.toString().contains('unauthenticated')) {
        throw Exception("يجب تسجيل الدخول أولاً لإضافة التعليقات.");
      } else if (e.toString().contains('PERMISSION_DENIED')) {
        throw Exception(
          "مشكلة في صلاحيات Firebase. يرجى التحقق من قواعد الأمان.",
        );
      }

      rethrow;
    }
  }

  Future<void> deleteCommentFromFoodItem(
    String foodItemId,
    String commentId,
  ) async {
    try {
      final commentRef = _firestoreService.db
          .collection(AppConstants.foodItemsCollection)
          .doc(foodItemId)
          .collection(AppConstants.commentsSubcollection)
          .doc(commentId);
      final foodItemRef = _firestoreService.db
          .collection(AppConstants.foodItemsCollection)
          .doc(foodItemId);

      await _firestoreService.db.runTransaction((transaction) async {
        final foodItemSnapshot = await transaction.get(foodItemRef);
        if (!foodItemSnapshot.exists) {
          throw Exception("Food item does not exist!");
        }
        int currentComments =
            (foodItemSnapshot.data()?['commentsCount'] as int?) ?? 0;

        transaction.delete(commentRef);
        transaction.update(foodItemRef, {'commentsCount': currentComments - 1});
      });

      // تحديث قائمة الأطعمة لعرض العدد الجديد للتعليقات
      fetchFoodItems();
    } catch (e) {
      debugPrint("Error deleting comment from food item: $e");
      rethrow;
    }
  }

  // ===== دوال التعليقات المتقدمة =====

  /// إضافة رد على تعليق
  Future<void> addReplyToComment({
    required String foodItemId,
    required String parentCommentId,
    required Comment reply,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception("يجب تسجيل الدخول أولاً");

      final replyData = reply
          .copyWith(
            userId: user.uid,
            username: user.displayName ?? user.email ?? 'مستخدم',
            parentCommentId: parentCommentId,
            createdAt: DateTime.now(),
          )
          .toMap();

      final foodItemRef = _firestoreService.db
          .collection(AppConstants.foodItemsCollection)
          .doc(foodItemId);

      // إضافة الرد
      await foodItemRef
          .collection(AppConstants.commentsSubcollection)
          .add(replyData);

      // تحديث عدد الردود في التعليق الأصلي
      final parentCommentRef = foodItemRef
          .collection(AppConstants.commentsSubcollection)
          .doc(parentCommentId);

      await parentCommentRef.update({'repliesCount': FieldValue.increment(1)});

      fetchFoodItems();
    } catch (e) {
      debugPrint("Error adding reply: $e");
      rethrow;
    }
  }

  /// تعديل تعليق
  Future<void> updateComment({
    required String foodItemId,
    required String commentId,
    required String newContent,
    List<String>? newImageUrls,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception("يجب تسجيل الدخول أولاً");

      final commentRef = _firestoreService.db
          .collection(AppConstants.foodItemsCollection)
          .doc(foodItemId)
          .collection(AppConstants.commentsSubcollection)
          .doc(commentId);

      final updateData = {
        'content': newContent,
        'updatedAt': FieldValue.serverTimestamp(),
        'isEdited': true,
      };

      if (newImageUrls != null) {
        updateData['imageUrls'] = newImageUrls;
      }

      await commentRef.update(updateData);
      fetchFoodItems();
    } catch (e) {
      debugPrint("Error updating comment: $e");
      rethrow;
    }
  }

  /// حذف تعليق
  Future<void> deleteComment({
    required String foodItemId,
    required String commentId,
    bool isAdmin = false,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception("يجب تسجيل الدخول أولاً");

      final commentRef = _firestoreService.db
          .collection(AppConstants.foodItemsCollection)
          .doc(foodItemId)
          .collection(AppConstants.commentsSubcollection)
          .doc(commentId);

      // حذف التعليق (أو تمييزه كمحذوف)
      if (isAdmin) {
        // المشرف يحذف نهائياً
        await commentRef.delete();
      } else {
        // المستخدم يمييز كمحذوف
        await commentRef.update({
          'isDeleted': true,
          'content': 'تم حذف هذا التعليق',
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      // تحديث عدد التعليقات
      final foodItemRef = _firestoreService.db
          .collection(AppConstants.foodItemsCollection)
          .doc(foodItemId);

      await foodItemRef.update({'commentsCount': FieldValue.increment(-1)});

      fetchFoodItems();
    } catch (e) {
      debugPrint("Error deleting comment: $e");
      rethrow;
    }
  }

  /// الإعجاب بتعليق
  Future<void> toggleCommentLike({
    required String foodItemId,
    required String commentId,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception("يجب تسجيل الدخول أولاً");

      final commentRef = _firestoreService.db
          .collection(AppConstants.foodItemsCollection)
          .doc(foodItemId)
          .collection(AppConstants.commentsSubcollection)
          .doc(commentId);

      final likeRef = commentRef.collection('likes').doc(user.uid);

      await _firestoreService.db.runTransaction((transaction) async {
        final likeDoc = await transaction.get(likeRef);
        final commentDoc = await transaction.get(commentRef);

        if (!commentDoc.exists) return;

        final currentLikes = (commentDoc.data()?['likesCount'] as int?) ?? 0;

        if (likeDoc.exists) {
          // إزالة الإعجاب
          transaction.delete(likeRef);
          transaction.update(commentRef, {'likesCount': currentLikes - 1});
        } else {
          // إضافة الإعجاب
          transaction.set(likeRef, {
            'userId': user.uid,
            'timestamp': FieldValue.serverTimestamp(),
          });
          transaction.update(commentRef, {'likesCount': currentLikes + 1});
        }
      });

      fetchFoodItems();
    } catch (e) {
      debugPrint("Error toggling comment like: $e");
      rethrow;
    }
  }

  /// فحص إذا كان المستخدم أعجب بالتعليق
  Future<bool> hasLikedComment({
    required String foodItemId,
    required String commentId,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return false;

      final likeDoc = await _firestoreService.db
          .collection(AppConstants.foodItemsCollection)
          .doc(foodItemId)
          .collection(AppConstants.commentsSubcollection)
          .doc(commentId)
          .collection('likes')
          .doc(user.uid)
          .get();

      return likeDoc.exists;
    } catch (e) {
      debugPrint("Error checking comment like: $e");
      return false;
    }
  }

  /// جلب الردود على تعليق
  Stream<List<Comment>> getRepliesForComment({
    required String foodItemId,
    required String parentCommentId,
  }) {
    try {
      return _firestoreService.db
          .collection(AppConstants.foodItemsCollection)
          .doc(foodItemId)
          .collection(AppConstants.commentsSubcollection)
          .where('parentCommentId', isEqualTo: parentCommentId)
          .snapshots()
          .map((snapshot) {
            final comments = snapshot.docs
                .map((doc) => Comment.fromFirestore(doc))
                .toList();
            // ترتيب الردود محلياً بدلاً من في Firestore
            comments.sort((a, b) => a.createdAt.compareTo(b.createdAt));
            return comments;
          });
    } catch (e) {
      debugPrint("Error getting replies: $e");
      return Stream.value([]);
    }
  }

  Future<void> updateFoodApproval(String foodId, bool isApproved) async {
    try {
      await _firestoreService.updateDocument(
        AppConstants.foodItemsCollection,
        foodId,
        {'isApproved': isApproved},
      );
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء تحديث حالة الموافقة: ${e.toString()}";
      rethrow;
    }
  }

  /// رفع صورة للتعليق
  Future<String?> uploadCommentImage(BuildContext context) async {
    try {
      final storageService = StorageService();
      return await storageService.showImageUploadOptions(
        context: context,
        collection: 'comment_images',
        fileName: 'comment_${DateTime.now().millisecondsSinceEpoch}',
      );
    } catch (e) {
      debugPrint("Error uploading comment image: $e");
      return null;
    }
  }
}

import 'package:flutter/material.dart';

class AppColors {
  // الألوان الأساسية - تصميم عصري
  static const Color primary = Color(0xFF2ECC71); // أخضر عصري
  static const Color primaryLight = Color(0xFF58D68D); // أخضر فاتح
  static const Color primaryDark = Color(0xFF28B463); // أخضر غامق

  // الألوان الثانوية
  static const Color secondary = Color(0xFFF39C12); // برتقالي عصري
  static const Color secondaryLight = Color(0xFFF5B041); // برتقالي فاتح
  static const Color secondaryDark = Color(0xFFD68910); // برتقالي غامق

  // ألوان الخلفية - تصميم عصري
  static const Color background = Color(0xFFF5F5F5); // رمادي فاتح عصري
  static const Color surface = Color(0xFFFFFFFF); // أبيض نقي
  static const Color cardBackground = Color(0xFFFFFFFF); // أبيض نقي
  static const Color surfaceVariant = Color(0xFFEEEEEE); // رمادي متوسط

  // ألوان النص - عصرية
  static const Color textPrimary = Color(0xFF333333); // رمادي داكن عصري
  static const Color textSecondary = Color(0xFF757575); // رمادي متوسط عصري
  static const Color textHint = Color(0xFFBDBDBD); // رمادي فاتح عصري
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textOnSecondary = Color(0xFFFFFFFF);

  // ألوان الحالة - عصرية
  static const Color success = Color(0xFF2ECC71); // أخضر عصري
  static const Color warning = Color(0xFFF39C12); // برتقالي عصري
  static const Color error = Color(0xFFE74C3C); // أحمر عصري
  static const Color info = Color(0xFF3498DB); // أزرق عصري

  // ألوان خاصة بالتطبيق - عصرية
  static const Color safe = Color(0xFF2ECC71); // أخضر عصري للأطعمة الآمنة
  static const Color unsafe = Color(0xFFE74C3C); // أحمر عصري للأطعمة غير الآمنة
  static const Color caution = Color(0xFFF39C12); // برتقالي عصري للحذر

  // ألوان إضافية عصرية
  static const Color accent = Color(0xFFE84393); // وردي عصري
  static const Color neutral = Color(0xFF9E9E9E); // رمادي محايد
  static const Color border = Color(0xFFE0E0E0); // لون الحدود
  static const Color divider = Color(0xFFEEEEEE); // لون الفواصل

  // تدرجات لونية
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static Color blackWithOpacity(double opacity) {
    return Colors.black.withOpacity(opacity);
  }
}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/models/product.dart';
import 'package:yassincil/providers/product_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/utils/dialog_utils.dart';

class AddProductScreen extends StatefulWidget {
  final String? scannedBarcode;

  const AddProductScreen({super.key, this.scannedBarcode});

  @override
  State<AddProductScreen> createState() => _AddProductScreenState();
}

class _AddProductScreenState extends State<AddProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final _barcodeController = TextEditingController();
  final _nameController = TextEditingController();
  final _brandController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _categoryController = TextEditingController();
  final _ingredientsController = TextEditingController();
  final _allergensController = TextEditingController();
  final _certificationsController = TextEditingController();

  bool? _isGlutenFree;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.scannedBarcode != null) {
      _barcodeController.text = widget.scannedBarcode!;
    }
  }

  @override
  void dispose() {
    _barcodeController.dispose();
    _nameController.dispose();
    _brandController.dispose();
    _descriptionController.dispose();
    _categoryController.dispose();
    _ingredientsController.dispose();
    _allergensController.dispose();
    _certificationsController.dispose();
    super.dispose();
  }

  Future<void> _scanBarcode() async {
    final productProvider = Provider.of<ProductProvider>(
      context,
      listen: false,
    );
    final product = await productProvider.scanProduct(context);

    if (product != null) {
      _barcodeController.text = product.barcode;
    }
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final productProvider = Provider.of<ProductProvider>(
        context,
        listen: false,
      );

      final product = Product(
        barcode: _barcodeController.text.trim(),
        name: _nameController.text.trim(),
        brand: _brandController.text.trim(),
        description: _descriptionController.text.trim(),
        category: _categoryController.text.trim(),
        ingredients: _ingredientsController.text
            .split(',')
            .map((e) => e.trim())
            .where((e) => e.isNotEmpty)
            .toList(),
        allergens: _allergensController.text
            .split(',')
            .map((e) => e.trim())
            .where((e) => e.isNotEmpty)
            .toList(),
        certifications: _certificationsController.text
            .split(',')
            .map((e) => e.trim())
            .where((e) => e.isNotEmpty)
            .toList(),
        isGlutenFree: _isGlutenFree,
        addedBy: authProvider.currentUser?.uid,
        addedAt: DateTime.now(),
        isVerified: authProvider.userProfile?.role == 'admin',
      );

      await productProvider.addProduct(product);

      if (mounted) {
        DialogUtils.showSuccessDialog(
          context,
          title: 'تم الحفظ بنجاح',
          message: 'تم إضافة المنتج بنجاح.',
          onOk: () {
            Navigator.of(context).pop();
          },
        );
      }
    } catch (e) {
      if (mounted) {
        DialogUtils.showErrorDialog(
          context,
          title: 'خطأ',
          message: 'فشل في إضافة المنتج: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'إضافة منتج جديد',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // الباركود
              _buildTextField(
                controller: _barcodeController,
                label: 'الباركود',
                hint: 'أدخل رقم الباركود أو امسحه',
                icon: Icons.qr_code,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال الباركود';
                  }
                  return null;
                },
                suffixIcon: IconButton(
                  icon: const Icon(Icons.qr_code_scanner),
                  onPressed: _scanBarcode,
                  tooltip: 'مسح الباركود',
                ),
              ),

              const SizedBox(height: 16),

              // اسم المنتج
              _buildTextField(
                controller: _nameController,
                label: 'اسم المنتج',
                hint: 'أدخل اسم المنتج',
                icon: Icons.shopping_bag,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال اسم المنتج';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // العلامة التجارية
              _buildTextField(
                controller: _brandController,
                label: 'العلامة التجارية',
                hint: 'أدخل العلامة التجارية',
                icon: Icons.business,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال العلامة التجارية';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // الفئة
              _buildTextField(
                controller: _categoryController,
                label: 'الفئة',
                hint: 'مثل: مخبوزات، ألبان، حلويات',
                icon: Icons.category,
              ),

              const SizedBox(height: 16),

              // الوصف
              _buildTextField(
                controller: _descriptionController,
                label: 'الوصف',
                hint: 'وصف مختصر للمنتج',
                icon: Icons.description,
                maxLines: 3,
              ),

              const SizedBox(height: 16),

              // المكونات
              _buildTextField(
                controller: _ingredientsController,
                label: 'المكونات',
                hint: 'اكتب المكونات مفصولة بفاصلة',
                icon: Icons.list_alt,
                maxLines: 3,
              ),

              const SizedBox(height: 16),

              // مسببات الحساسية
              _buildTextField(
                controller: _allergensController,
                label: 'مسببات الحساسية',
                hint: 'اكتب مسببات الحساسية مفصولة بفاصلة',
                icon: Icons.warning_amber,
                maxLines: 2,
              ),

              const SizedBox(height: 16),

              // الشهادات
              _buildTextField(
                controller: _certificationsController,
                label: 'الشهادات',
                hint: 'مثل: خالي من الغلوتين، عضوي، حلال',
                icon: Icons.verified,
                maxLines: 2,
              ),

              const SizedBox(height: 20),

              // حالة الغلوتين
              _buildGlutenFreeSection(theme),

              const SizedBox(height: 32),

              // زر الحفظ
              Container(
                height: 56,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    colors: [
                      theme.primaryColor,
                      theme.primaryColor.withValues(alpha: 0.8),
                    ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: theme.primaryColor.withValues(alpha: 0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 6),
                    ),
                  ],
                ),
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveProduct,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    shadowColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 24,
                          width: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Text(
                          'حفظ المنتج',
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
    int maxLines = 1,
    Widget? suffixIcon,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.deepPurple.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        maxLines: maxLines,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          labelStyle: GoogleFonts.cairo(),
          hintStyle: GoogleFonts.cairo(color: Colors.grey[500]),
          prefixIcon: Icon(icon, color: Theme.of(context).primaryColor),
          suffixIcon: suffixIcon,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
        ),
        validator: validator,
      ),
    );
  }

  Widget _buildGlutenFreeSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.deepPurple.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'حالة الغلوتين',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 12),
          Column(
            children: [
              RadioListTile<bool?>(
                title: Text('خالي من الغلوتين', style: GoogleFonts.cairo()),
                value: true,
                groupValue: _isGlutenFree,
                onChanged: (value) {
                  setState(() {
                    _isGlutenFree = value;
                  });
                },
                activeColor: Colors.green,
              ),
              RadioListTile<bool?>(
                title: Text('يحتوي على غلوتين', style: GoogleFonts.cairo()),
                value: false,
                groupValue: _isGlutenFree,
                onChanged: (value) {
                  setState(() {
                    _isGlutenFree = value;
                  });
                },
                activeColor: Colors.red,
              ),
              RadioListTile<bool?>(
                title: Text('غير محدد', style: GoogleFonts.cairo()),
                value: null,
                groupValue: _isGlutenFree,
                onChanged: (value) {
                  setState(() {
                    _isGlutenFree = value;
                  });
                },
                activeColor: Colors.orange,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/models/restaurant.dart';
import 'package:yassincil/providers/restaurant_provider.dart';
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/utils/error_handler.dart';

class AddEditRestaurantScreen extends StatefulWidget {
  final Restaurant? restaurant; // Null if adding, not null if editing

  const AddEditRestaurantScreen({super.key, this.restaurant});

  @override
  State<AddEditRestaurantScreen> createState() =>
      _AddEditRestaurantScreenState();
}

class _AddEditRestaurantScreenState extends State<AddEditRestaurantScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _addressController;
  late TextEditingController _phoneController;
  late TextEditingController _descriptionController;
  late TextEditingController _glutenFreeOptionsController;

  late TextEditingController _imageUrlController;

  // متغيرات الموقع
  GeoPoint? _selectedLocation;
  String _locationText = 'لم يتم تحديد الموقع';
  bool _isLoadingLocation = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(
      text: widget.restaurant?.name ?? '',
    );
    _addressController = TextEditingController(
      text: widget.restaurant?.address ?? '',
    );
    _phoneController = TextEditingController(
      text: widget.restaurant?.phone ?? '',
    );
    _descriptionController = TextEditingController(
      text: widget.restaurant?.description ?? '',
    );
    _glutenFreeOptionsController = TextEditingController(
      text: widget.restaurant?.glutenFreeOptions.join(', ') ?? '',
    );
    _imageUrlController = TextEditingController(
      text: widget.restaurant?.imageUrl ?? '',
    );

    // تهيئة الموقع إذا كان موجوداً
    if (widget.restaurant?.location != null) {
      _selectedLocation = widget.restaurant!.location;
      _locationText = 'تم تحديد الموقع';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _descriptionController.dispose();
    _glutenFreeOptionsController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  // دوال الموقع
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      // التحقق من الصلاحيات
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('تم رفض صلاحية الوصول للموقع');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('صلاحية الوصول للموقع مرفوضة نهائياً');
      }

      // الحصول على الموقع الحالي
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      setState(() {
        _selectedLocation = GeoPoint(position.latitude, position.longitude);
        _locationText = 'تم تحديد الموقع الحالي';
      });

      if (mounted) {
        ErrorHandler.showSuccessSnackBar(context, 'تم تحديد الموقع بنجاح');
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(context, 'فشل في تحديد الموقع: $e');
      }
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  void _clearLocation() {
    setState(() {
      _selectedLocation = null;
      _locationText = 'لم يتم تحديد الموقع';
    });
  }

  Future<void> _saveRestaurant() async {
    if (_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('جارٍ الحفظ...')));
      try {
        String imageUrl = _imageUrlController.text.trim();

        final List<String> glutenFreeOptions = _glutenFreeOptionsController.text
            .split(',')
            .map((e) => e.trim())
            .where((e) => e.isNotEmpty)
            .toList();

        final newRestaurant = Restaurant(
          id: widget.restaurant?.id,
          name: _nameController.text,
          address: _addressController.text,
          phone: _phoneController.text,
          description: _descriptionController.text,
          imageUrl: imageUrl,
          rating: widget.restaurant?.rating ?? 0.0,
          reviewCount: widget.restaurant?.reviewCount ?? 0,
          glutenFreeOptions: glutenFreeOptions,
          location: _selectedLocation,
        );

        if (widget.restaurant == null) {
          await Provider.of<RestaurantProvider>(
            context,
            listen: false,
          ).addRestaurant(newRestaurant);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تمت إضافة المطعم بنجاح.')),
            );
          }
        } else {
          await Provider.of<RestaurantProvider>(
            context,
            listen: false,
          ).updateRestaurant(newRestaurant);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم تحديث المطعم بنجاح.')),
            );
          }
        }
        if (mounted) {
          Navigator.of(context).pop();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'فشل الحفظ: ${e.toString().replaceFirst('Exception: ', '')}',
              ),
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.restaurant == null ? 'إضافة مطعم جديد' : 'تعديل مطعم',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 22,
            fontFamily: 'Cairo',
          ),
        ),
        backgroundColor: Colors.deepPurple,
        elevation: 2,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(18)),
        ),
      ),
      backgroundColor: Colors.grey[100],
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(18.0),
          child: Form(
            key: _formKey,
            child: Container(
              padding: const EdgeInsets.all(18),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(18),
                boxShadow: [
                  BoxShadow(
                    color: Colors.deepPurple.withValues(alpha: 0.08),
                    blurRadius: 16,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Center(
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.deepPurple.withValues(alpha: 0.12),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: _imageUrlController.text.isNotEmpty
                            ? Image.network(
                                _imageUrlController.text,
                                height: 150,
                                width: 150,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) =>
                                    Container(
                                      height: 150,
                                      width: 150,
                                      color: Colors.grey[200],
                                      child: const Icon(
                                        Icons.broken_image,
                                        size: 50,
                                        color: Colors.grey,
                                      ),
                                    ),
                              )
                            : Container(
                                height: 150,
                                width: 150,
                                color: Colors.grey[200],
                                child: Icon(
                                  Icons.image,
                                  size: 50,
                                  color: Colors.grey[400],
                                ),
                              ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 18),
                  TextFormField(
                    controller: _imageUrlController,
                    decoration: InputDecoration(
                      labelText: 'رابط صورة المطعم',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(
                        Icons.link,
                        color: Colors.deepPurple,
                      ),
                    ),
                    style: const TextStyle(fontFamily: 'Cairo', fontSize: 16),
                    onChanged: (_) => setState(() {}),
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: _nameController,
                    decoration: InputDecoration(
                      labelText: 'اسم المطعم',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(
                        Icons.store,
                        color: Colors.deepPurple,
                      ),
                    ),
                    style: const TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'الرجاء إدخال اسم المطعم';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 15),
                  TextFormField(
                    controller: _addressController,
                    decoration: InputDecoration(
                      labelText: 'العنوان',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(
                        Icons.location_on,
                        color: Colors.deepPurple,
                      ),
                    ),
                    style: const TextStyle(fontFamily: 'Cairo', fontSize: 16),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'الرجاء إدخال العنوان';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 15),
                  TextFormField(
                    controller: _phoneController,
                    decoration: InputDecoration(
                      labelText: 'رقم الهاتف (اختياري)',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(
                        Icons.phone,
                        color: Colors.deepPurple,
                      ),
                    ),
                    style: const TextStyle(fontFamily: 'Cairo', fontSize: 16),
                    keyboardType: TextInputType.phone,
                  ),
                  const SizedBox(height: 15),
                  TextFormField(
                    controller: _descriptionController,
                    decoration: InputDecoration(
                      labelText: 'وصف المطعم (اختياري)',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(
                        Icons.info,
                        color: Colors.deepPurple,
                      ),
                    ),
                    style: const TextStyle(fontFamily: 'Cairo', fontSize: 16),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 15),
                  TextFormField(
                    controller: _glutenFreeOptionsController,
                    decoration: InputDecoration(
                      labelText:
                          'خيارات خالية من الغلوتين (افصل بينها بفاصلة ,)',
                      hintText: 'مثال: خبز خالي، معكرونة خالية، بيتزا خالية',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(
                        Icons.no_food,
                        color: Colors.deepPurple,
                      ),
                    ),
                    style: const TextStyle(fontFamily: 'Cairo', fontSize: 16),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 20),

                  // قسم الموقع
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(
                              Icons.location_on,
                              color: Colors.deepPurple,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'موقع المطعم (اختياري)',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.deepPurple,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          _locationText,
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: _selectedLocation != null
                                ? AppColors.success
                                : Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: _isLoadingLocation
                                    ? null
                                    : _getCurrentLocation,
                                icon: _isLoadingLocation
                                    ? const SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                        ),
                                      )
                                    : const Icon(Icons.my_location),
                                label: Text(
                                  'تحديد الموقع الحالي',
                                  style: GoogleFonts.cairo(),
                                ),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.deepPurple,
                                  side: const BorderSide(
                                    color: Colors.deepPurple,
                                  ),
                                ),
                              ),
                            ),
                            if (_selectedLocation != null) ...[
                              const SizedBox(width: 8),
                              IconButton(
                                onPressed: _clearLocation,
                                icon: const Icon(Icons.clear),
                                color: Colors.red,
                                tooltip: 'إزالة الموقع',
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 30),
                  Center(
                    child: ElevatedButton.icon(
                      onPressed: _saveRestaurant,
                      icon: const Icon(Icons.save),
                      label: Text(
                        widget.restaurant == null
                            ? 'إضافة المطعم'
                            : 'حفظ التعديلات',
                        style: const TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.deepPurple,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 40,
                          vertical: 15,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(14),
                        ),
                        elevation: 3,
                        shadowColor: Colors.deepPurple.withValues(alpha: 0.18),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

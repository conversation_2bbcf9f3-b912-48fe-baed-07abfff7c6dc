import 'package:flutter/material.dart';
import 'package:yassincil/widgets/medications_app_bar.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/services.dart';

import 'package:yassincil/providers/medication_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/models/medication.dart';
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/screens/medications/medication_detail_screen.dart';

class MedicationReviewScreen extends StatefulWidget {
  const MedicationReviewScreen({super.key});

  @override
  State<MedicationReviewScreen> createState() => _MedicationReviewScreenState();
}

class _MedicationReviewScreenState extends State<MedicationReviewScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  int _pendingCount = 0;
  int _reviewedToday = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    _animationController.forward();
    _updateCounts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _updateCounts() {
    final medicationProvider = Provider.of<MedicationProvider>(
      context,
      listen: false,
    );
    final medications = medicationProvider.medications;

    setState(() {
      _pendingCount = medications
          .where((m) => m.approvalStatus == MedicationApprovalStatus.pending)
          .length;

      _reviewedToday = medications
          .where(
            (m) =>
                m.reviewedAt != null &&
                DateTime.now().difference(m.reviewedAt!).inDays == 0,
          )
          .length;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isAdmin = Provider.of<AuthProvider>(context).isAdmin;
    if (!isAdmin) {
      return Scaffold(
        appBar: MedicationsAppBar(title: 'مراجعة الأدوية'),
        body: Center(
          child: Text(
            'ليست لديك صلاحية للوصول إلى هذه الصفحة',
            style: GoogleFonts.cairo(fontSize: 16),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF8FFFE),
      appBar: MedicationsAppBar(
        title: 'مراجعة الأدوية',
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list, color: Colors.white),
            tooltip: 'تصفية',
          ),
          IconButton(
            onPressed: () => _refreshData(),
            icon: const Icon(Icons.refresh, color: Colors.white),
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildStatisticsHeader(),
            _buildTabBar(),
            Expanded(child: _buildTabContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.95),
            Colors.white.withValues(alpha: 0.85),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF00BFA5).withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF00BFA5).withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              title: 'في الانتظار',
              value: '$_pendingCount',
              icon: Icons.pending_actions,
              color: Colors.orange,
            ),
          ),
          Container(
            width: 1,
            height: 60,
            color: Colors.grey.shade300,
            margin: const EdgeInsets.symmetric(horizontal: 16),
          ),
          Expanded(
            child: _buildStatCard(
              title: 'تمت مراجعتها اليوم',
              value: '$_reviewedToday',
              icon: Icons.done_all,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          title,
          style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey.shade600),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(25),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFF00BFA5), Color(0xFF00796B)],
          ),
          borderRadius: BorderRadius.circular(25),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey.shade600,
        labelStyle: GoogleFonts.cairo(
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
        unselectedLabelStyle: GoogleFonts.cairo(fontSize: 12),
        tabs: [
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.pending, size: 16),
                const SizedBox(width: 4),
                Flexible(
                  child: Text(
                    'معلق ($_pendingCount)',
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
          const Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.check_circle, size: 16),
                SizedBox(width: 4),
                Flexible(
                  child: Text(
                    'معتمد',
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
          const Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.cancel, size: 16),
                SizedBox(width: 4),
                Flexible(
                  child: Text(
                    'مرفوض',
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
          const Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.edit, size: 16),
                SizedBox(width: 4),
                Flexible(
                  child: Text(
                    'يحتاج تعديل',
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildMedicationsList(MedicationApprovalStatus.pending),
        _buildMedicationsList(MedicationApprovalStatus.approved),
        _buildMedicationsList(MedicationApprovalStatus.rejected),
        _buildMedicationsList(MedicationApprovalStatus.needsRevision),
      ],
    );
  }

  Widget _buildMedicationsList(MedicationApprovalStatus status) {
    return Consumer<MedicationProvider>(
      builder: (context, medicationProvider, child) {
        final medications = medicationProvider.medications
            .where((m) => m.approvalStatus == status)
            .toList();

        if (medications.isEmpty) {
          return _buildEmptyState(status);
        }

        return RefreshIndicator(
          onRefresh: _refreshData,
          color: const Color(0xFF00BFA5),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: medications.length,
            itemBuilder: (context, index) {
              return _buildMedicationCard(medications[index]);
            },
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(MedicationApprovalStatus status) {
    String message;
    IconData icon;
    Color color;

    switch (status) {
      case MedicationApprovalStatus.pending:
        message = 'لا توجد أدوية في انتظار المراجعة';
        icon = Icons.inbox;
        color = Colors.grey;
        break;
      case MedicationApprovalStatus.approved:
        message = 'لا توجد أدوية معتمدة';
        icon = Icons.check_circle_outline;
        color = Colors.green;
        break;
      case MedicationApprovalStatus.rejected:
        message = 'لا توجد أدوية مرفوضة';
        icon = Icons.cancel_outlined;
        color = Colors.red;
        break;
      case MedicationApprovalStatus.needsRevision:
        message = 'لا توجد أدوية تحتاج تعديل';
        icon = Icons.edit_outlined;
        color = Colors.orange;
        break;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, size: 64, color: color),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: GoogleFonts.cairo(fontSize: 16, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildMedicationCard(Medication medication) {
    Color statusColor;
    String statusText;

    switch (medication.approvalStatus) {
      case MedicationApprovalStatus.pending:
        statusColor = Colors.orange;
        statusText = 'معلق';
        break;
      case MedicationApprovalStatus.approved:
        statusColor = Colors.green;
        statusText = 'معتمد';
        break;
      case MedicationApprovalStatus.rejected:
        statusColor = Colors.red;
        statusText = 'مرفوض';
        break;
      case MedicationApprovalStatus.needsRevision:
        statusColor = Colors.orange;
        statusText = 'يحتاج تعديل';
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.95),
            Colors.white.withValues(alpha: 0.85),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: statusColor.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _viewMedicationDetails(medication),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // صورة الدواء
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        border: Border.all(color: Colors.grey.shade300),
                        color: Colors.grey.shade100,
                      ),
                      child: medication.imageUrls.isNotEmpty
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(15),
                              child: Image.network(
                                medication.imageUrls.first,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) =>
                                    Icon(
                                      Icons.medication,
                                      color: Colors.grey.shade500,
                                    ),
                              ),
                            )
                          : Icon(Icons.medication, color: Colors.grey.shade500),
                    ),
                    const SizedBox(width: 16),
                    // معلومات الدواء
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            medication.name,
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            medication.company,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: statusColor.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  statusText,
                                  style: GoogleFonts.cairo(
                                    fontSize: 12,
                                    color: statusColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Icon(
                                medication.isAllowed
                                    ? Icons.check_circle
                                    : Icons.cancel,
                                color: medication.isAllowed
                                    ? Colors.green
                                    : Colors.red,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                medication.isAllowed ? 'آمن' : 'غير آمن',
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: medication.isAllowed
                                      ? Colors.green
                                      : Colors.red,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // معلومات المساهم
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.person, color: Colors.grey.shade600, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        'بواسطة: ${medication.username ?? "مستخدم"}',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        _formatDate(medication.createdAt),
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                if (medication.approvalStatus ==
                    MedicationApprovalStatus.pending) ...[
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _reviewMedication(
                            medication,
                            MedicationApprovalStatus.approved,
                          ),
                          icon: const Icon(Icons.check, size: 16),
                          label: Text(
                            'موافقة',
                            style: GoogleFonts.cairo(fontSize: 12),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 8),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _reviewMedication(
                            medication,
                            MedicationApprovalStatus.needsRevision,
                          ),
                          icon: const Icon(Icons.edit, size: 16),
                          label: Text(
                            'تعديل',
                            style: GoogleFonts.cairo(fontSize: 12),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 8),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _reviewMedication(
                            medication,
                            MedicationApprovalStatus.rejected,
                          ),
                          icon: const Icon(Icons.close, size: 16),
                          label: Text(
                            'رفض',
                            style: GoogleFonts.cairo(fontSize: 12),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
                if (medication.reviewerComment != null) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: statusColor.withValues(alpha: 0.2),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.comment, color: statusColor, size: 14),
                            const SizedBox(width: 6),
                            Text(
                              'تعليق المراجع:',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: statusColor,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 6),
                        Text(
                          medication.reviewerComment!,
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _viewMedicationDetails(Medication medication) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MedicationDetailScreen(
          medication: medication,
          isForVerification: true,
        ),
      ),
    );
  }

  void _reviewMedication(
    Medication medication,
    MedicationApprovalStatus newStatus,
  ) {
    _showReviewDialog(medication, newStatus);
  }

  void _showReviewDialog(
    Medication medication,
    MedicationApprovalStatus newStatus,
  ) {
    final commentController = TextEditingController();
    String title;
    Color color;
    IconData icon;

    switch (newStatus) {
      case MedicationApprovalStatus.approved:
        title = 'موافقة على الدواء';
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case MedicationApprovalStatus.rejected:
        title = 'رفض الدواء';
        color = Colors.red;
        icon = Icons.cancel;
        break;
      case MedicationApprovalStatus.needsRevision:
        title = 'طلب تعديل';
        color = Colors.orange;
        icon = Icons.edit;
        break;
      default:
        title = 'مراجعة الدواء';
        color = const Color(0xFF00BFA5);
        icon = Icons.rate_review;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الدواء: ${medication.name}',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'الشركة: ${medication.company}',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: commentController,
              maxLines: 4,
              decoration: InputDecoration(
                labelText: newStatus == MedicationApprovalStatus.approved
                    ? 'تعليق (اختياري)'
                    : 'سبب ${newStatus == MedicationApprovalStatus.rejected ? "الرفض" : "طلب التعديل"}',
                hintText: newStatus == MedicationApprovalStatus.rejected
                    ? 'اشرح سبب رفض هذا الدواء...'
                    : newStatus == MedicationApprovalStatus.needsRevision
                    ? 'اشرح التعديلات المطلوبة...'
                    : 'أي ملاحظات إضافية...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: color, width: 2),
                ),
              ),
              style: GoogleFonts.cairo(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(color: Colors.grey.shade600),
            ),
          ),
          ElevatedButton(
            onPressed: () => _confirmReview(
              medication,
              newStatus,
              commentController.text.trim(),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text('تأكيد', style: GoogleFonts.cairo(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _confirmReview(
    Medication medication,
    MedicationApprovalStatus newStatus,
    String comment,
  ) async {
    Navigator.pop(context); // إغلاق الـ dialog

    try {
      final medicationProvider = Provider.of<MedicationProvider>(
        context,
        listen: false,
      );

      // استخدام الدوال المخصصة للمراجعة
      switch (newStatus) {
        case MedicationApprovalStatus.approved:
          await medicationProvider.approveMedication(medication.id!);
          break;
        case MedicationApprovalStatus.rejected:
          await medicationProvider.rejectMedication(medication.id!, comment);
          break;
        case MedicationApprovalStatus.needsRevision:
          await medicationProvider.requestMedicationRevision(
            medication.id!,
            comment,
          );
          break;
        default:
          throw Exception('حالة موافقة غير مدعومة');
      }

      HapticFeedback.lightImpact();

      String message;
      switch (newStatus) {
        case MedicationApprovalStatus.approved:
          message = 'تم اعتماد الدواء بنجاح';
          break;
        case MedicationApprovalStatus.rejected:
          message = 'تم رفض الدواء';
          break;
        case MedicationApprovalStatus.needsRevision:
          message = 'تم طلب تعديل الدواء';
          break;
        default:
          message = 'تم تحديث حالة الدواء';
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }

      _updateCounts();

      // إرسال إشعار للمستخدم (يمكن تطويره لاحقاً)
      _notifyUser(medication, newStatus);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحديث حالة الدواء: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }

  void _notifyUser(Medication medication, MedicationApprovalStatus status) {
    // TODO: إضافة نظام إشعارات للمستخدمين
    debugPrint(
      'إرسال إشعار للمستخدم ${medication.username} بحالة الدواء: ${status.arabicName}',
    );
  }

  void _showFilterDialog() {
    // TODO: إضافة dialog للتصفية حسب التاريخ، المستخدم، إلخ
    debugPrint('عرض خيارات التصفية');
  }

  Future<void> _refreshData() async {
    final medicationProvider = Provider.of<MedicationProvider>(
      context,
      listen: false,
    );
    await medicationProvider.loadMedications();
    _updateCounts();
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes} دقيقة';
      }
      return '${difference.inHours} ساعة';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

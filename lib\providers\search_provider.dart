import 'package:flutter/material.dart';
import 'package:yassincil/services/advanced_search_service.dart';

class SearchProvider extends ChangeNotifier {
  SearchResults _searchResults = SearchResults();
  bool _isSearching = false;
  String _currentQuery = '';
  List<SearchCategory> _selectedCategories = SearchCategory.values;
  SearchFilters _filters = SearchFilters();
  List<String> _searchHistory = [];
  List<String> _suggestions = [];
  String? _errorMessage;

  // Getters
  SearchResults get searchResults => _searchResults;
  bool get isSearching => _isSearching;
  String get currentQuery => _currentQuery;
  List<SearchCategory> get selectedCategories => _selectedCategories;
  SearchFilters get filters => _filters;
  List<String> get searchHistory => _searchHistory;
  List<String> get suggestions => _suggestions;
  String? get errorMessage => _errorMessage;

  /// البحث الشامل
  Future<void> searchAll(String query) async {
    if (query.trim().isEmpty) {
      _clearResults();
      return;
    }

    _isSearching = true;
    _currentQuery = query;
    _errorMessage = null;
    notifyListeners();

    try {
      _searchResults = await AdvancedSearchService.searchAll(
        query,
        categories: _selectedCategories,
        filters: _filters,
      );

      // إضافة الاستعلام لتاريخ البحث
      _addToSearchHistory(query);
    } catch (e) {
      _errorMessage = e.toString();
      debugPrint('خطأ في البحث: $e');
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }

  /// البحث الصوتي
  Future<void> voiceSearch(String audioQuery) async {
    _isSearching = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _searchResults = await AdvancedSearchService.voiceSearch(audioQuery);
      _currentQuery = audioQuery;
      _addToSearchHistory(audioQuery);
    } catch (e) {
      _errorMessage = e.toString();
      debugPrint('خطأ في البحث الصوتي: $e');
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }

  /// تحديث الفئات المحددة للبحث
  void updateSelectedCategories(List<SearchCategory> categories) {
    _selectedCategories = categories;
    notifyListeners();
    
    // إعادة البحث إذا كان هناك استعلام حالي
    if (_currentQuery.isNotEmpty) {
      searchAll(_currentQuery);
    }
  }

  /// تفعيل/إلغاء تفعيل فئة بحث
  void toggleCategory(SearchCategory category) {
    if (_selectedCategories.contains(category)) {
      _selectedCategories.remove(category);
    } else {
      _selectedCategories.add(category);
    }
    notifyListeners();
    
    // إعادة البحث إذا كان هناك استعلام حالي
    if (_currentQuery.isNotEmpty) {
      searchAll(_currentQuery);
    }
  }

  /// تحديث فلاتر البحث
  void updateFilters(SearchFilters filters) {
    _filters = filters;
    notifyListeners();
    
    // إعادة البحث إذا كان هناك استعلام حالي
    if (_currentQuery.isNotEmpty) {
      searchAll(_currentQuery);
    }
  }

  /// تفعيل/إلغاء تفعيل فلتر "خالي من الغلوتين فقط"
  void toggleGlutenFreeFilter() {
    _filters = SearchFilters(
      glutenFreeOnly: !_filters.glutenFreeOnly,
      category: _filters.category,
      dateFrom: _filters.dateFrom,
      dateTo: _filters.dateTo,
      minRating: _filters.minRating,
      verifiedOnly: _filters.verifiedOnly,
    );
    notifyListeners();
    
    // إعادة البحث إذا كان هناك استعلام حالي
    if (_currentQuery.isNotEmpty) {
      searchAll(_currentQuery);
    }
  }

  /// تحديث فلتر الفئة
  void updateCategoryFilter(String? category) {
    _filters = SearchFilters(
      glutenFreeOnly: _filters.glutenFreeOnly,
      category: category,
      dateFrom: _filters.dateFrom,
      dateTo: _filters.dateTo,
      minRating: _filters.minRating,
      verifiedOnly: _filters.verifiedOnly,
    );
    notifyListeners();
    
    // إعادة البحث إذا كان هناك استعلام حالي
    if (_currentQuery.isNotEmpty) {
      searchAll(_currentQuery);
    }
  }

  /// جلب اقتراحات البحث
  Future<void> getSuggestions(String query) async {
    try {
      _suggestions = await AdvancedSearchService.getSearchSuggestions(query);
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في جلب الاقتراحات: $e');
    }
  }

  /// إضافة استعلام لتاريخ البحث
  void _addToSearchHistory(String query) {
    final trimmedQuery = query.trim();
    if (trimmedQuery.isEmpty) return;

    // إزالة الاستعلام إذا كان موجوداً مسبقاً
    _searchHistory.removeWhere((item) => item.toLowerCase() == trimmedQuery.toLowerCase());
    
    // إضافة الاستعلام في المقدمة
    _searchHistory.insert(0, trimmedQuery);
    
    // الاحتفاظ بآخر 20 استعلام فقط
    if (_searchHistory.length > 20) {
      _searchHistory = _searchHistory.take(20).toList();
    }
  }

  /// مسح تاريخ البحث
  void clearSearchHistory() {
    _searchHistory.clear();
    notifyListeners();
  }

  /// حذف استعلام من تاريخ البحث
  void removeFromSearchHistory(String query) {
    _searchHistory.remove(query);
    notifyListeners();
  }

  /// مسح النتائج
  void _clearResults() {
    _searchResults.clear();
    _currentQuery = '';
    notifyListeners();
  }

  /// مسح جميع البيانات
  void clearAll() {
    _clearResults();
    _suggestions.clear();
    _errorMessage = null;
    notifyListeners();
  }

  /// إعادة تعيين الفلاتر
  void resetFilters() {
    _filters = SearchFilters();
    _selectedCategories = SearchCategory.values;
    notifyListeners();
    
    // إعادة البحث إذا كان هناك استعلام حالي
    if (_currentQuery.isNotEmpty) {
      searchAll(_currentQuery);
    }
  }

  /// الحصول على اسم الفئة بالعربية
  String getCategoryName(SearchCategory category) {
    switch (category) {
      case SearchCategory.foods:
        return 'الأطعمة';
      case SearchCategory.medications:
        return 'الأدوية';
      case SearchCategory.recipes:
        return 'الوصفات';
      case SearchCategory.restaurants:
        return 'المطاعم';
      case SearchCategory.articles:
        return 'المقالات';
      case SearchCategory.products:
        return 'المنتجات';
      case SearchCategory.forumPosts:
        return 'المنتدى';
    }
  }

  /// الحصول على أيقونة الفئة
  IconData getCategoryIcon(SearchCategory category) {
    switch (category) {
      case SearchCategory.foods:
        return Icons.fastfood;
      case SearchCategory.medications:
        return Icons.medical_services;
      case SearchCategory.recipes:
        return Icons.menu_book;
      case SearchCategory.restaurants:
        return Icons.restaurant;
      case SearchCategory.articles:
        return Icons.article;
      case SearchCategory.products:
        return Icons.inventory;
      case SearchCategory.forumPosts:
        return Icons.forum;
    }
  }

  /// الحصول على عدد النتائج لفئة معينة
  int getCategoryResultCount(SearchCategory category) {
    switch (category) {
      case SearchCategory.foods:
        return _searchResults.foods.length;
      case SearchCategory.medications:
        return _searchResults.medications.length;
      case SearchCategory.recipes:
        return _searchResults.recipes.length;
      case SearchCategory.restaurants:
        return _searchResults.restaurants.length;
      case SearchCategory.articles:
        return _searchResults.articles.length;
      case SearchCategory.products:
        return _searchResults.products.length;
      case SearchCategory.forumPosts:
        return _searchResults.forumPosts.length;
    }
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}

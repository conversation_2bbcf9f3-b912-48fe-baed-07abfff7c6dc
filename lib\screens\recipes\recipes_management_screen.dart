
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:timeago/timeago.dart' as timeago;

import 'package:yassincil/models/recipe.dart';
import 'package:yassincil/providers/recipe_provider.dart';
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/screens/recipes/recipe_detail_screen.dart'; // To view details

class RecipesManagementScreen extends StatefulWidget {
  const RecipesManagementScreen({super.key});

  @override
  State<RecipesManagementScreen> createState() => _RecipesManagementScreenState();
}

class _RecipesManagementScreenState extends State<RecipesManagementScreen> {
  @override
  void initState() {
    super.initState();
    // Fetch unapproved recipes when the screen initializes
    Provider.of<RecipeProvider>(context, listen: false)
        .fetchInitialRecipes(approvedStatus: false);
    timeago.setLocaleMessages('ar', timeago.ArMessages());
  }

  Future<void> _refreshRecipes() async {
    await Provider.of<RecipeProvider>(context, listen: false)
        .fetchInitialRecipes(approvedStatus: false);
  }

  Future<void> _approveRecipe(String recipeId) async {
    try {
      await Provider.of<RecipeProvider>(context, listen: false)
          .updateRecipeApprovalStatus(recipeId, true);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تمت الموافقة على الوصفة بنجاح', style: GoogleFonts.cairo()),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الموافقة على الوصفة: $e', style: GoogleFonts.cairo()),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      _refreshRecipes(); // Refresh the list after action
    }
  }

  Future<void> _rejectRecipe(String recipeId) async {
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text('تأكيد الرفض', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد أنك تريد رفض هذه الوصفة؟ سيتم حذفها نهائياً.',
          style: GoogleFonts.cairo(),
        ),
        actions: <Widget>[
          TextButton(
            child: Text('إلغاء', style: GoogleFonts.cairo()),
            onPressed: () {
              Navigator.of(ctx).pop(false);
            },
          ),
          TextButton(
            child: Text('رفض', style: GoogleFonts.cairo(color: AppColors.error)),
            onPressed: () {
              Navigator.of(ctx).pop(true);
            },
          ),
        ],
      ),
    );

    if (shouldDelete == true) {
      try {
        await Provider.of<RecipeProvider>(context, listen: false)
            .deleteRecipe(recipeId); // Reuse deleteRecipe
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم رفض الوصفة وحذفها بنجاح', style: GoogleFonts.cairo()),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في رفض الوصفة: $e', style: GoogleFonts.cairo()),
              backgroundColor: AppColors.error,
            ),
          );
        }
      } finally {
        _refreshRecipes(); // Refresh the list after action
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final recipeProvider = Provider.of<RecipeProvider>(context);
    final unapprovedRecipes = recipeProvider.recipes; // This will contain unapproved recipes due to fetchInitialRecipes(approvedStatus: false)

    return Scaffold(
      appBar: AppBar(
        title: Text('إدارة الوصفات', style: GoogleFonts.cairo(fontWeight: FontWeight.bold)),
        backgroundColor: AppColors.primary,
        centerTitle: true,
      ),
      body: RefreshIndicator(
        onRefresh: _refreshRecipes,
        child: recipeProvider.isLoading
            ? const Center(child: CircularProgressIndicator())
            : unapprovedRecipes.isEmpty
                ? Center(
                    child: Text(
                      'لا توجد وصفات بانتظار الموافقة',
                      style: GoogleFonts.cairo(fontSize: 18, color: AppColors.textSecondary),
                      textAlign: TextAlign.center,
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: unapprovedRecipes.length,
                    itemBuilder: (context, index) {
                      final recipe = unapprovedRecipes[index];
                      return _RecipeManagementCard(
                        recipe: recipe,
                        onApprove: _approveRecipe,
                        onReject: _rejectRecipe,
                      );
                    },
                  ),
      ),
    );
  }
}

class _RecipeManagementCard extends StatelessWidget {
  final Recipe recipe;
  final Function(String) onApprove;
  final Function(String) onReject;

  const _RecipeManagementCard({
    required this.recipe,
    required this.onApprove,
    required this.onReject,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => RecipeDetailScreen(recipe: recipe),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                recipe.title,
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'بواسطة: ${recipe.username}',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'تاريخ الإضافة: ${timeago.format(recipe.createdAt, locale: 'ar')}',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton.icon(
                    onPressed: () => onApprove(recipe.id!),
                    icon: const Icon(Icons.check_circle_outline, size: 20),
                    label: Text('موافقة', style: GoogleFonts.cairo()),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.success,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: () => onReject(recipe.id!),
                    icon: const Icon(Icons.cancel_outlined, size: 20),
                    label: Text('رفض', style: GoogleFonts.cairo()),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.error,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:io';

import 'package:yassincil/providers/medication_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/models/medication.dart';
import 'package:yassincil/utils/app_colors.dart';

class EnhancedAddEditMedicationScreen extends StatefulWidget {
  final Medication? medication;

  const EnhancedAddEditMedicationScreen({super.key, this.medication});

  @override
  State<EnhancedAddEditMedicationScreen> createState() =>
      _EnhancedAddEditMedicationScreenState();
}

class _EnhancedAddEditMedicationScreenState
    extends State<EnhancedAddEditMedicationScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late PageController _pageController;

  // Controllers للحقول الأساسية
  final _nameController = TextEditingController();
  final _companyController = TextEditingController();
  final _ingredientsController = TextEditingController();
  final _notesController = TextEditingController();
  final _barcodeController = TextEditingController();

  // Controllers للمعلومات الغذائية
  final _caloriesController = TextEditingController();
  final _proteinController = TextEditingController();
  final _carbohydratesController = TextEditingController();
  final _fatController = TextEditingController();
  final _allergensController = TextEditingController();

  // Controllers للمعلومات الطبية
  final _activeIngredientController = TextEditingController();
  final _dosageController = TextEditingController();
  final _sideEffectsController = TextEditingController();
  final _contraindicationsController = TextEditingController();
  final _interactionsController = TextEditingController();
  final _alternativesController = TextEditingController();

  // Controllers للمعلومات الإضافية
  final _prescriptionRequiredController = TextEditingController();
  final _ageGroupController = TextEditingController();
  final _pregnancyCategoryController = TextEditingController();
  final _storageConditionsController = TextEditingController();
  final _expiryDateController = TextEditingController();
  final _sourceController = TextEditingController();
  final _tagsController = TextEditingController();

  // State variables
  bool _isAllowed = true;
  bool _isLoading = false;
  String _selectedCategory = 'مسكنات الألم';
  String _selectedPrescriptionRequired = 'لا';
  String _selectedAgeGroup = 'البالغين (18-64 سنة)';
  String _selectedCeliacSafetyCategory = 'غير محدد';
  int _currentStep = 0;

  // Images
  final List<File> _selectedImages = [];
  final List<TextEditingController> _imageUrlControllers = [];
  List<String> _imageUrls = [];

  // Options
  final List<String> _categories = [
    'مسكنات الألم',
    'مضادات الحيوية',
    'أدوية الضغط',
    'أدوية السكري',
    'أدوية الجهاز الهضمي',
    'أدوية الحساسية',
    'فيتامينات ومكملات',
    'أدوية القلب',
    'أدوية الجهاز التنفسي',
    'أدوية الجلد',
    'أدوية العيون',
    'أدوية الأذن',
    'أدوية النساء والتوليد',
    'أدوية الأطفال',
    'أخرى',
  ];

  final List<String> _prescriptionOptions = ['نعم', 'لا', 'حسب الجرعة'];
  final List<String> _ageGroups = [
    'الأطفال (0-12 سنة)',
    'المراهقين (13-17 سنة)',
    'البالغين (18-64 سنة)',
    'كبار السن (65+ سنة)',
    'جميع الأعمار',
  ];
  final List<String> _celiacSafetyCategories = [
    'آمن لمرضى السيلياك',
    'غير آمن لمرضى السيلياك',
    'يستخدم بحذر لمرضى السيلياك',
    'غير محدد',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _pageController = PageController();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _initializeData();
    _animationController.forward();
  }

  void _initializeData() {
    if (widget.medication != null) {
      final medication = widget.medication!;
      _nameController.text = medication.name;
      _companyController.text = medication.company;
      _ingredientsController.text = medication.ingredients;
      _notesController.text = medication.notes;
      _isAllowed = medication.isAllowed;
      _selectedCategory = medication.category;
      _barcodeController.text = medication.barcode ?? '';
      _caloriesController.text = medication.calories?.toString() ?? '';
      _proteinController.text = medication.protein?.toString() ?? '';
      _carbohydratesController.text =
          medication.carbohydrates?.toString() ?? '';
      _fatController.text = medication.fat?.toString() ?? '';
      _allergensController.text = medication.allergens?.join(', ') ?? '';
      _activeIngredientController.text = medication.activeIngredient ?? '';
      _dosageController.text = medication.dosage ?? '';
      _sideEffectsController.text = medication.sideEffects ?? '';
      _contraindicationsController.text = medication.contraindications ?? '';
      _interactionsController.text = medication.interactions ?? '';
      _alternativesController.text = medication.alternatives.join(', ');
      _selectedPrescriptionRequired = medication.prescriptionRequired ?? 'لا';
      _selectedAgeGroup = medication.ageGroup ?? 'البالغين (18-64 سنة)';
      _selectedCeliacSafetyCategory =
          medication.pregnancyCategory ?? 'غير محدد';
      _storageConditionsController.text = medication.storageConditions ?? '';
      _expiryDateController.text = medication.expiryDate ?? '';
      _sourceController.text = medication.source ?? '';
      _tagsController.text = medication.tags.join(', ');
      _imageUrls = medication.imageUrls;

      for (var url in _imageUrls) {
        _imageUrlControllers.add(TextEditingController(text: url));
      }
    } else {
      _imageUrlControllers.add(TextEditingController());
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _pageController.dispose();

    // Dispose all controllers
    _nameController.dispose();
    _companyController.dispose();
    _ingredientsController.dispose();
    _notesController.dispose();
    _barcodeController.dispose();
    _caloriesController.dispose();
    _proteinController.dispose();
    _carbohydratesController.dispose();
    _fatController.dispose();
    _allergensController.dispose();
    _activeIngredientController.dispose();
    _dosageController.dispose();
    _sideEffectsController.dispose();
    _contraindicationsController.dispose();
    _interactionsController.dispose();
    _alternativesController.dispose();
    _prescriptionRequiredController.dispose();
    _ageGroupController.dispose();
    _pregnancyCategoryController.dispose();
    _storageConditionsController.dispose();
    _expiryDateController.dispose();
    _sourceController.dispose();
    _tagsController.dispose();

    for (var controller in _imageUrlControllers) {
      controller.dispose();
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.medication != null;
    final isAdmin = Provider.of<AuthProvider>(context).isAdmin;

    if (!isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            isEditing ? 'تعديل الدواء' : 'إضافة دواء',
            style: GoogleFonts.cairo(),
          ),
        ),
        body: Center(
          child: Text(
            'ليست لديك صلاحية للوصول إلى هذه الصفحة',
            style: GoogleFonts.cairo(fontSize: 16),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: CustomScrollView(
          slivers: [
            _buildSliverAppBar(isEditing),
            SliverToBoxAdapter(
              child: Column(
                children: [_buildProgressIndicator(), _buildStepContent()],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildSliverAppBar(bool isEditing) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: AppColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          isEditing ? 'تعديل الدواء' : 'إضافة دواء جديد',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontSize: 18,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primary,
                AppColors.primary.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                right: -50,
                top: -50,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.1),
                  ),
                ),
              ),
              Positioned(
                left: -30,
                bottom: -30,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.05),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        if (isEditing)
          IconButton(
            icon: const Icon(Icons.delete_outline, color: Colors.white),
            onPressed: _showDeleteConfirmation,
            tooltip: 'حذف الدواء',
          ),
        IconButton(
          icon: const Icon(Icons.save_outlined, color: Colors.white),
          onPressed: _saveMedication,
          tooltip: 'حفظ',
        ),
      ],
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.timeline_rounded, color: AppColors.primary, size: 24),
              const SizedBox(width: 12),
              Text(
                'خطوات إضافة الدواء',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              _buildStepIndicator(0, 'المعلومات الأساسية', Icons.info_outline),
              _buildStepConnector(0),
              _buildStepIndicator(
                1,
                'المعلومات الطبية',
                Icons.medical_services_outlined,
              ),
              _buildStepConnector(1),
              _buildStepIndicator(
                2,
                'الصور والوسائط',
                Icons.photo_library_outlined,
              ),
              _buildStepConnector(2),
              _buildStepIndicator(
                3,
                'المراجعة والحفظ',
                Icons.check_circle_outline,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int step, String title, IconData icon) {
    final isActive = _currentStep == step;
    final isCompleted = _currentStep > step;

    return Expanded(
      child: GestureDetector(
        onTap: () => _goToStep(step),
        child: Column(
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: isCompleted
                    ? Colors.green
                    : isActive
                    ? AppColors.primary
                    : Colors.grey.shade300,
                shape: BoxShape.circle,
                boxShadow: isActive
                    ? [
                        BoxShadow(
                          color: AppColors.primary.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: Icon(
                isCompleted ? Icons.check_rounded : icon,
                color: isCompleted || isActive
                    ? Colors.white
                    : Colors.grey.shade600,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                color: isActive ? AppColors.primary : Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepConnector(int step) {
    final isCompleted = _currentStep > step;

    return Container(
      width: 30,
      height: 2,
      margin: const EdgeInsets.only(bottom: 30),
      decoration: BoxDecoration(
        color: isCompleted ? Colors.green : Colors.grey.shade300,
        borderRadius: BorderRadius.circular(1),
      ),
    );
  }

  Widget _buildStepContent() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: _getStepContent(_currentStep),
      ),
    );
  }

  Widget _getStepContent(int step) {
    switch (step) {
      case 0:
        return _buildBasicInfoStep();
      case 1:
        return _buildMedicalInfoStep();
      case 2:
        return _buildImagesStep();
      case 3:
        return _buildReviewStep();
      default:
        return _buildBasicInfoStep();
    }
  }

  Widget _buildBasicInfoStep() {
    return Form(
      key: _formKey,
      child: Column(
        key: const ValueKey('basic_info'),
        children: [
          _buildSectionCard('المعلومات الأساسية', Icons.info_outline, [
            _buildTextField(
              controller: _nameController,
              label: 'اسم الدواء',
              hint: 'أدخل اسم الدواء',
              icon: Icons.medication,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم الدواء';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: _companyController,
              label: 'الشركة المصنعة',
              hint: 'أدخل اسم الشركة المصنعة',
              icon: Icons.business,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم الشركة المصنعة';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            _buildDropdownField(
              value: _selectedCategory,
              label: 'فئة الدواء',
              icon: Icons.category,
              items: _categories,
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: _barcodeController,
              label: 'الباركود (اختياري)',
              hint: 'أدخل رقم الباركود',
              icon: Icons.qr_code,
              keyboardType: TextInputType.number,
            ),
          ]),

          const SizedBox(height: 16),

          _buildSectionCard('المكونات والوصف', Icons.science, [
            _buildTextField(
              controller: _ingredientsController,
              label: 'المكونات الفعالة',
              hint: 'أدخل المكونات الفعالة',
              icon: Icons.science,
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال المكونات الفعالة';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: _notesController,
              label: 'ملاحظات إضافية',
              hint: 'أدخل أي ملاحظات إضافية',
              icon: Icons.note_add,
              maxLines: 4,
            ),
          ]),

          const SizedBox(height: 16),

          _buildSectionCard('حالة الدواء', Icons.verified_user, [
            _buildSwitchTile(
              title: 'حالة السماح',
              subtitle: _isAllowed
                  ? 'آمن لمرضى السيلياك'
                  : 'غير آمن لمرضى السيلياك',
              value: _isAllowed,
              onChanged: (value) {
                setState(() {
                  _isAllowed = value;
                });
                HapticFeedback.lightImpact();
              },
              activeColor: Colors.green,
              inactiveColor: Colors.red,
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildMedicalInfoStep() {
    return Column(
      key: const ValueKey('medical_info'),
      children: [
        _buildSectionCard('المعلومات الطبية', Icons.medical_services, [
          _buildTextField(
            controller: _activeIngredientController,
            label: 'المادة الفعالة الرئيسية',
            hint: 'أدخل المادة الفعالة الرئيسية',
            icon: Icons.science,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _dosageController,
            label: 'الجرعة المقترحة',
            hint: 'أدخل الجرعة المقترحة',
            icon: Icons.medication_liquid,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDropdownField(
                  value: _selectedPrescriptionRequired,
                  label: 'يحتاج وصفة طبية',
                  icon: Icons.receipt_long,
                  items: _prescriptionOptions,
                  onChanged: (value) {
                    setState(() {
                      _selectedPrescriptionRequired = value!;
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDropdownField(
                  value: _selectedAgeGroup,
                  label: 'الفئة العمرية',
                  icon: Icons.people,
                  items: _ageGroups,
                  onChanged: (value) {
                    setState(() {
                      _selectedAgeGroup = value!;
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildDropdownField(
            value: _selectedCeliacSafetyCategory,
            label: 'آمن لمرضى السيلياك',
            icon: Icons.health_and_safety,
            items: _celiacSafetyCategories,
            onChanged: (value) {
              setState(() {
                _selectedCeliacSafetyCategory = value!;
              });
            },
          ),
        ]),

        const SizedBox(height: 16),

        _buildSectionCard('الآثار الجانبية والتحذيرات', Icons.warning_amber, [
          _buildTextField(
            controller: _sideEffectsController,
            label: 'الآثار الجانبية',
            hint: 'أدخل الآثار الجانبية المحتملة',
            icon: Icons.warning,
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _contraindicationsController,
            label: 'موانع الاستعمال',
            hint: 'أدخل موانع الاستعمال',
            icon: Icons.block,
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _interactionsController,
            label: 'التفاعلات الدوائية',
            hint: 'أدخل التفاعلات مع الأدوية الأخرى',
            icon: Icons.merge_type,
            maxLines: 3,
          ),
        ]),

        const SizedBox(height: 16),

        _buildSectionCard('معلومات إضافية', Icons.info, [
          _buildTextField(
            controller: _alternativesController,
            label: 'البدائل الآمنة',
            hint: 'أدخل البدائل الآمنة (مفصولة بفواصل)',
            icon: Icons.swap_horiz,
            maxLines: 2,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _storageConditionsController,
            label: 'ظروف التخزين',
            hint: 'أدخل ظروف التخزين المطلوبة',
            icon: Icons.storage,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _allergensController,
            label: 'مسببات الحساسية',
            hint: 'أدخل مسببات الحساسية (مفصولة بفواصل)',
            icon: Icons.coronavirus,
          ),
        ]),
      ],
    );
  }

  Widget _buildImagesStep() {
    return Column(
      key: const ValueKey('images'),
      children: [
        _buildSectionCard('صور الدواء', Icons.photo_library, [
          Text(
            'أضف صور واضحة للدواء من زوايا مختلفة',
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 20),

          // عرض الصور المحددة
          if (_selectedImages.isNotEmpty || _imageUrls.isNotEmpty)
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length + _imageUrls.length,
                itemBuilder: (context, index) {
                  if (index < _selectedImages.length) {
                    return _buildImagePreview(
                      imageFile: _selectedImages[index],
                      onRemove: () {
                        setState(() {
                          _selectedImages.removeAt(index);
                        });
                      },
                    );
                  } else {
                    final urlIndex = index - _selectedImages.length;
                    return _buildImagePreview(
                      imageUrl: _imageUrls[urlIndex],
                      onRemove: () {
                        setState(() {
                          _imageUrls.removeAt(urlIndex);
                          _imageUrlControllers.removeAt(urlIndex);
                        });
                      },
                    );
                  }
                },
              ),
            ),

          const SizedBox(height: 20),

          // أزرار إضافة الصور
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  label: 'اختيار من المعرض',
                  icon: Icons.photo_library,
                  onPressed: _pickImages,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildActionButton(
                  label: 'التقاط صورة',
                  icon: Icons.camera_alt,
                  onPressed: _takePhoto,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ]),

        const SizedBox(height: 16),

        _buildSectionCard('روابط الصور', Icons.link, [
          Text(
            'أو أضف روابط الصور من الإنترنت',
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 16),

          for (int index = 0; index < _imageUrlControllers.length; index++)
            Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Expanded(
                    child: _buildTextField(
                      controller: _imageUrlControllers[index],
                      label: 'رابط الصورة ${index + 1}',
                      hint: 'https://example.com/image.jpg',
                      icon: Icons.link,
                      keyboardType: TextInputType.url,
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _imageUrlControllers.removeAt(index);
                      });
                    },
                    icon: const Icon(Icons.remove_circle, color: Colors.red),
                  ),
                ],
              ),
            ),

          const SizedBox(height: 12),

          TextButton.icon(
            onPressed: () {
              setState(() {
                _imageUrlControllers.add(TextEditingController());
              });
            },
            icon: const Icon(Icons.add),
            label: Text('إضافة رابط جديد', style: GoogleFonts.cairo()),
          ),
        ]),
      ],
    );
  }

  Widget _buildReviewStep() {
    return Column(
      key: const ValueKey('review'),
      children: [
        _buildSectionCard('مراجعة البيانات', Icons.preview, [
          Text(
            'تأكد من صحة جميع البيانات قبل الحفظ',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 20),

          _buildReviewItem('اسم الدواء', _nameController.text),
          _buildReviewItem('الشركة المصنعة', _companyController.text),
          _buildReviewItem('الفئة', _selectedCategory),
          _buildReviewItem('المكونات الفعالة', _ingredientsController.text),
          _buildReviewItem(
            'حالة السماح',
            _isAllowed ? 'آمن لمرضى السيلياك' : 'غير آمن لمرضى السيلياك',
          ),
          _buildReviewItem('يحتاج وصفة', _selectedPrescriptionRequired),
          _buildReviewItem('الفئة العمرية', _selectedAgeGroup),
          _buildReviewItem('آمن لمرضى السيلياك', _selectedCeliacSafetyCategory),

          if (_activeIngredientController.text.isNotEmpty)
            _buildReviewItem(
              'المادة الفعالة',
              _activeIngredientController.text,
            ),

          if (_dosageController.text.isNotEmpty)
            _buildReviewItem('الجرعة', _dosageController.text),

          if (_sideEffectsController.text.isNotEmpty)
            _buildReviewItem('الآثار الجانبية', _sideEffectsController.text),

          const SizedBox(height: 20),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.info, color: Colors.blue.shade600),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'سيتم مراجعة الدواء من قبل المشرفين قبل الموافقة عليه',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ]),
      ],
    );
  }

  Widget _buildSectionCard(String title, IconData icon, List<Widget> children) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: AppColors.primary, size: 20),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType keyboardType = TextInputType.text,
    int maxLines = 1,
  }) {
    return TextFormField(
      controller: controller,
      validator: validator,
      keyboardType: keyboardType,
      maxLines: maxLines,
      style: GoogleFonts.cairo(fontSize: 16),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: AppColors.primary),
        labelStyle: GoogleFonts.cairo(color: Colors.grey.shade700),
        hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
    );
  }

  Widget _buildDropdownField({
    required String value,
    required String label,
    required IconData icon,
    required List<String> items,
    required void Function(String?) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      onChanged: onChanged,
      style: GoogleFonts.cairo(fontSize: 16, color: Colors.grey.shade800),
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: AppColors.primary),
        labelStyle: GoogleFonts.cairo(color: Colors.grey.shade700),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
      items: items.map((item) {
        return DropdownMenuItem<String>(
          value: item,
          child: Text(item, style: GoogleFonts.cairo()),
        );
      }).toList(),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required void Function(bool) onChanged,
    required Color activeColor,
    required Color inactiveColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: value
            ? activeColor.withValues(alpha: 0.1)
            : inactiveColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: value
              ? activeColor.withValues(alpha: 0.3)
              : inactiveColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            value ? Icons.check_circle : Icons.cancel,
            color: value ? activeColor : inactiveColor,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade800,
                  ),
                ),
                Text(
                  subtitle,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: activeColor,
            inactiveThumbColor: inactiveColor,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(label, style: GoogleFonts.cairo(fontSize: 14)),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: 2,
      ),
    );
  }

  Widget _buildImagePreview({
    File? imageFile,
    String? imageUrl,
    required VoidCallback onRemove,
  }) {
    return Container(
      width: 100,
      height: 100,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: imageFile != null
                ? Image.file(
                    imageFile,
                    width: 100,
                    height: 100,
                    fit: BoxFit.cover,
                  )
                : imageUrl != null
                ? CachedNetworkImage(
                    imageUrl: imageUrl,
                    width: 100,
                    height: 100,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey.shade200,
                      child: const Center(
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey.shade200,
                      child: Icon(Icons.error, color: Colors.grey.shade400),
                    ),
                  )
                : Container(
                    color: Colors.grey.shade200,
                    child: Icon(Icons.image, color: Colors.grey.shade400),
                  ),
          ),
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: onRemove,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewItem(String label, String value) {
    if (value.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade800,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            if (_currentStep > 0)
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _previousStep,
                  icon: const Icon(Icons.arrow_back),
                  label: Text('السابق', style: GoogleFonts.cairo()),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),

            if (_currentStep > 0) const SizedBox(width: 16),

            Expanded(
              flex: 2,
              child: ElevatedButton.icon(
                onPressed: _isLoading
                    ? null
                    : (_currentStep < 3 ? _nextStep : _saveMedication),
                icon: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : Icon(_currentStep < 3 ? Icons.arrow_forward : Icons.save),
                label: Text(
                  _isLoading
                      ? 'جاري الحفظ...'
                      : _currentStep < 3
                      ? 'التالي'
                      : 'حفظ الدواء',
                  style: GoogleFonts.cairo(),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper Methods
  void _goToStep(int step) {
    if (step <= _currentStep || _validateCurrentStep()) {
      setState(() {
        _currentStep = step;
      });
      HapticFeedback.lightImpact();
    }
  }

  void _nextStep() {
    if (_validateCurrentStep()) {
      if (_currentStep < 3) {
        setState(() {
          _currentStep++;
        });
        HapticFeedback.lightImpact();
      }
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      HapticFeedback.lightImpact();
    }
  }

  bool _validateCurrentStep() {
    switch (_currentStep) {
      case 0:
        return _formKey.currentState?.validate() ?? false;
      case 1:
      case 2:
      case 3:
        return true;
      default:
        return true;
    }
  }

  Future<void> _pickImages() async {
    try {
      final picker = ImagePicker();
      final pickedFiles = await picker.pickMultiImage(
        maxWidth: 800,
        maxHeight: 600,
        imageQuality: 80,
      );

      if (pickedFiles.isNotEmpty) {
        setState(() {
          _selectedImages.addAll(pickedFiles.map((file) => File(file.path)));
        });
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء اختيار الصور');
    }
  }

  Future<void> _takePhoto() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 800,
        maxHeight: 600,
        imageQuality: 80,
      );

      if (pickedFile != null) {
        setState(() {
          _selectedImages.add(File(pickedFile.path));
        });
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء التقاط الصورة');
    }
  }

  Future<void> _saveMedication() async {
    if (!_validateCurrentStep()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final medicationProvider = Provider.of<MedicationProvider>(
        context,
        listen: false,
      );

      // جمع روابط الصور
      List<String> finalImageUrls = _imageUrlControllers
          .map((controller) => controller.text.trim())
          .where((url) => url.isNotEmpty)
          .toList();

      // إضافة الصور المحلية (في التطبيق الحقيقي، يجب رفعها إلى الخادم أولاً)
      // TODO: رفع الصور المحلية إلى Firebase Storage

      if (widget.medication == null) {
        // إضافة دواء جديد
        await medicationProvider.addMedicationLegacy(
          name: _nameController.text.trim(),
          company: _companyController.text.trim(),
          ingredients: _ingredientsController.text.trim(),
          notes: _notesController.text.trim(),
          isAllowed: _isAllowed,
          category: _selectedCategory,
          barcode: _barcodeController.text.trim(),
          calories: double.tryParse(_caloriesController.text.trim()) ?? 0.0,
          protein: double.tryParse(_proteinController.text.trim()) ?? 0.0,
          carbohydrates:
              double.tryParse(_carbohydratesController.text.trim()) ?? 0.0,
          fat: double.tryParse(_fatController.text.trim()) ?? 0.0,
          allergens: _allergensController.text
              .trim()
              .split(',')
              .map((e) => e.trim())
              .where((e) => e.isNotEmpty)
              .toList(),
          activeIngredient: _activeIngredientController.text.trim(),
          dosage: _dosageController.text.trim(),
          sideEffects: _sideEffectsController.text.trim(),
          contraindications: _contraindicationsController.text.trim(),
          interactions: _interactionsController.text.trim(),
          alternatives: _alternativesController.text
              .trim()
              .split(',')
              .map((e) => e.trim())
              .where((e) => e.isNotEmpty)
              .toList(),
          prescriptionRequired: _selectedPrescriptionRequired,
          ageGroup: _selectedAgeGroup,
          pregnancyCategory: _selectedCeliacSafetyCategory,
          storageConditions: _storageConditionsController.text.trim(),
          expiryDate: _expiryDateController.text.trim(),
          source: _sourceController.text.trim(),
          tags: _tagsController.text
              .trim()
              .split(',')
              .map((e) => e.trim())
              .where((e) => e.isNotEmpty)
              .toList(),
          imageUrls: finalImageUrls,
        );
      } else {
        // تعديل دواء موجود
        final updatedMedication = widget.medication!.copyWith(
          name: _nameController.text.trim(),
          company: _companyController.text.trim(),
          ingredients: _ingredientsController.text.trim(),
          notes: _notesController.text.trim(),
          isAllowed: _isAllowed,
          category: _selectedCategory,
          barcode: _barcodeController.text.trim(),
          calories: double.tryParse(_caloriesController.text.trim()),
          protein: double.tryParse(_proteinController.text.trim()),
          carbohydrates: double.tryParse(_carbohydratesController.text.trim()),
          fat: double.tryParse(_fatController.text.trim()),
          allergens: _allergensController.text
              .trim()
              .split(',')
              .map((e) => e.trim())
              .where((e) => e.isNotEmpty)
              .toList(),
          activeIngredient: _activeIngredientController.text.trim(),
          dosage: _dosageController.text.trim(),
          sideEffects: _sideEffectsController.text.trim(),
          contraindications: _contraindicationsController.text.trim(),
          interactions: _interactionsController.text.trim(),
          alternatives: _alternativesController.text
              .trim()
              .split(',')
              .map((e) => e.trim())
              .where((e) => e.isNotEmpty)
              .toList(),
          prescriptionRequired: _selectedPrescriptionRequired,
          ageGroup: _selectedAgeGroup,
          pregnancyCategory: _selectedCeliacSafetyCategory,
          storageConditions: _storageConditionsController.text.trim(),
          expiryDate: _expiryDateController.text.trim(),
          source: _sourceController.text.trim(),
          tags: _tagsController.text
              .trim()
              .split(',')
              .map((e) => e.trim())
              .where((e) => e.isNotEmpty)
              .toList(),
          imageUrls: finalImageUrls,
        );

        await medicationProvider.updateMedication(updatedMedication);
      }

      if (mounted) {
        HapticFeedback.heavyImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text(
                  widget.medication == null
                      ? 'تم إضافة الدواء بنجاح'
                      : 'تم تحديث الدواء بنجاح',
                  style: GoogleFonts.cairo(),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('حدث خطأ أثناء حفظ الدواء: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد الحذف', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف "${widget.medication?.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteMedication();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _deleteMedication() async {
    if (widget.medication?.id == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final medicationProvider = Provider.of<MedicationProvider>(
        context,
        listen: false,
      );
      await medicationProvider.deleteMedication(widget.medication!.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف الدواء بنجاح', style: GoogleFonts.cairo()),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('حدث خطأ أثناء حذف الدواء');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message, style: GoogleFonts.cairo())),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}

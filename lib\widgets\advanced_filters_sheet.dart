import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../utils/app_colors.dart';

class AdvancedFiltersSheet extends StatefulWidget {
  const AdvancedFiltersSheet({super.key});

  @override
  State<AdvancedFiltersSheet> createState() => _AdvancedFiltersSheetState();
}

class _AdvancedFiltersSheetState extends State<AdvancedFiltersSheet> {
  double _maxDistance = 10.0;
  double _minRating = 0.0;
  bool _hasGlutenFreeMenu = false;
  bool _hasCertification = false;
  bool _hasDelivery = false;
  bool _isOpenNow = false;

  final List<String> _selectedCuisines = [];
  final List<String> _availableCuisines = [
    'عربي',
    'إيطالي',
    'آسيوي',
    'مكسيكي',
    'هندي',
    'تركي',
    'فرنسي',
    'أمريكي',
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDistanceFilter(),
                  const SizedBox(height: 24),
                  _buildRatingFilter(),
                  const SizedBox(height: 24),
                  _buildCuisineFilter(),
                  const SizedBox(height: 24),
                  _buildFeatureFilters(),
                ],
              ),
            ),
          ),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const Spacer(),
          Text(
            'فلاتر متقدمة',
            style: GoogleFonts.cairo(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const Spacer(),
          TextButton(
            onPressed: _resetFilters,
            child: Text(
              'إعادة تعيين',
              style: GoogleFonts.cairo(color: AppColors.primary),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDistanceFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المسافة القصوى',
          style: GoogleFonts.cairo(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: Slider(
                value: _maxDistance,
                min: 1.0,
                max: 50.0,
                divisions: 49,
                activeColor: AppColors.primary,
                onChanged: (value) {
                  setState(() {
                    _maxDistance = value;
                  });
                },
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '${_maxDistance.toInt()} كم',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRatingFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التقييم الأدنى',
          style: GoogleFonts.cairo(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: Slider(
                value: _minRating,
                min: 0.0,
                max: 5.0,
                divisions: 10,
                activeColor: Colors.amber,
                onChanged: (value) {
                  setState(() {
                    _minRating = value;
                  });
                },
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.amber.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.star, size: 16, color: Colors.amber.shade600),
                  const SizedBox(width: 4),
                  Text(
                    _minRating.toStringAsFixed(1),
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.w600,
                      color: Colors.amber.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCuisineFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع المطبخ',
          style: GoogleFonts.cairo(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _availableCuisines.map((cuisine) {
            final isSelected = _selectedCuisines.contains(cuisine);
            return FilterChip(
              label: Text(
                cuisine,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: isSelected ? Colors.white : AppColors.textPrimary,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedCuisines.add(cuisine);
                  } else {
                    _selectedCuisines.remove(cuisine);
                  }
                });
              },
              selectedColor: AppColors.primary,
              backgroundColor: Colors.grey.shade100,
              checkmarkColor: Colors.white,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildFeatureFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الميزات',
          style: GoogleFonts.cairo(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        _buildSwitchTile(
          'قائمة طعام خالية من الجلوتين',
          _hasGlutenFreeMenu,
          (value) => setState(() => _hasGlutenFreeMenu = value),
          Icons.restaurant_menu,
        ),
        _buildSwitchTile(
          'شهادة خالي من الجلوتين',
          _hasCertification,
          (value) => setState(() => _hasCertification = value),
          Icons.verified,
        ),
        _buildSwitchTile(
          'خدمة التوصيل',
          _hasDelivery,
          (value) => setState(() => _hasDelivery = value),
          Icons.delivery_dining,
        ),
        _buildSwitchTile(
          'مفتوح الآن',
          _isOpenNow,
          (value) => setState(() => _isOpenNow = value),
          Icons.access_time,
        ),
      ],
    );
  }

  Widget _buildSwitchTile(
    String title,
    bool value,
    ValueChanged<bool> onChanged,
    IconData icon,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Icon(icon, size: 20, color: AppColors.primary),
          const SizedBox(width: 12),
          Expanded(child: Text(title, style: GoogleFonts.cairo(fontSize: 14))),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey.shade200)),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text('إلغاء', style: GoogleFonts.cairo()),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: _applyFilters,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'تطبيق الفلاتر',
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _resetFilters() {
    setState(() {
      _maxDistance = 10.0;
      _minRating = 0.0;
      _hasGlutenFreeMenu = false;
      _hasCertification = false;
      _hasDelivery = false;
      _isOpenNow = false;
      _selectedCuisines.clear();
    });
  }

  void _applyFilters() {
    // تطبيق الفلاتر (مؤقتاً: رسالة تأكيد)
    Navigator.pop(context, {
      'maxDistance': _maxDistance,
      'minRating': _minRating,
      'hasGlutenFreeMenu': _hasGlutenFreeMenu,
      'hasCertification': _hasCertification,
      'hasDelivery': _hasDelivery,
      'isOpenNow': _isOpenNow,
      'selectedCuisines': _selectedCuisines,
    });
  }
}

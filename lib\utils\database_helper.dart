import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:yassincil/models/medication.dart';

class DatabaseHelper {
  static final DatabaseHelper instance = DatabaseHelper._init();
  static Database? _database;

  DatabaseHelper._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB('medications.db');
    return _database!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);

    return await openDatabase(
      path,
      version: 5,
      onCreate: _createDB,
      onUpgrade: _onUpgrade,
    );
  }

  Future _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      await db.execute(
        "ALTER TABLE medications ADD COLUMN likes TEXT NOT NULL DEFAULT '[]'",
      );
    }
    if (oldVersion < 3) {
      await db.execute('''
        CREATE TABLE favorites (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          userId TEXT NOT NULL,
          medicationId TEXT NOT NULL,
          addedAt TEXT NOT NULL,
          UNIQUE(userId, medicationId)
        )
      ''');
    }
    if (oldVersion < 4) {
      // إضافة حقول نظام المراجعة الجديدة
      await db.execute(
        "ALTER TABLE medications ADD COLUMN approvalStatus TEXT NOT NULL DEFAULT 'approved'",
      );
      await db.execute(
        "ALTER TABLE medications ADD COLUMN reviewerComment TEXT NOT NULL DEFAULT ''",
      );
      await db.execute(
        "ALTER TABLE medications ADD COLUMN reviewerId TEXT NOT NULL DEFAULT ''",
      );
      await db.execute(
        "ALTER TABLE medications ADD COLUMN reviewerName TEXT NOT NULL DEFAULT ''",
      );
      await db.execute(
        "ALTER TABLE medications ADD COLUMN reviewedAt TEXT NOT NULL DEFAULT ''",
      );
    }
    if (oldVersion < 5) {
      await db.execute("ALTER TABLE favorites RENAME TO medication_favorites");
      await db.execute('''
        CREATE TABLE food_favorites (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          userId TEXT NOT NULL,
          foodId TEXT NOT NULL,
          addedAt TEXT NOT NULL,
          UNIQUE(userId, foodId)
        )
      ''');
    }
  }

  Future _createDB(Database db, int version) async {
    const idType = 'TEXT PRIMARY KEY';
    const textType = 'TEXT NOT NULL';
    const boolType = 'INTEGER NOT NULL';
    const intType = 'INTEGER NOT NULL';
    const doubleType = 'REAL NOT NULL';

    await db.execute('''
CREATE TABLE medications (
  id $idType,
  name $textType,
  company $textType,
  ingredients $textType,
  isAllowed $boolType,
  notes $textType,
  imageUrls $textType,
  category $textType,
  calories $doubleType,
  protein $doubleType,
  carbohydrates $doubleType,
  fat $doubleType,
  allergens $textType,
  likes TEXT NOT NULL,
  likesCount $intType,
  commentsCount $intType,
  ratingsCount $intType,
  averageRating $doubleType,
  activeIngredient $textType,
  dosage $textType,
  sideEffects $textType,
  contraindications $textType,
  interactions $textType,
  alternatives $textType,
  prescriptionRequired $textType,
  ageGroup $textType,
  pregnancyCategory $textType,
  storageConditions $textType,
  expiryDate $textType,
  barcode $textType,
  source $textType,
  isApproved $boolType,
  approvalStatus TEXT NOT NULL DEFAULT 'approved',
  reviewerComment TEXT NOT NULL DEFAULT '',
  reviewerId TEXT NOT NULL DEFAULT '',
  reviewerName TEXT NOT NULL DEFAULT '',
  reviewedAt TEXT NOT NULL DEFAULT '',
  isFeatured $boolType,
  userId $textType,
  username $textType,
  createdAt $textType,
  updatedAt $textType,
  tags $textType
)''');

    // إنشاء جدول المفضلة
    await db.execute('''
      CREATE TABLE medication_favorites (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        userId TEXT NOT NULL,
        medicationId TEXT NOT NULL,
        addedAt TEXT NOT NULL,
        UNIQUE(userId, medicationId)
      )
    ''');

    await db.execute('''
      CREATE TABLE food_favorites (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        userId TEXT NOT NULL,
        foodId TEXT NOT NULL,
        addedAt TEXT NOT NULL,
        UNIQUE(userId, foodId)
      )
    ''');
  }

  Future<void> insertMedication(Medication medication) async {
    final db = await instance.database;
    await db.insert(
      'medications',
      medication.toMap(forDb: true),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<Medication>> getMedications() async {
    final db = await instance.database;
    final maps = await db.query('medications');

    if (maps.isNotEmpty) {
      return maps.map((json) => Medication.fromMap(json)).toList();
    } else {
      return [];
    }
  }

  Future<void> clearMedications() async {
    final db = await instance.database;
    await db.delete('medications');
  }

  // ==================== دوال المفضلة ====================

  Future<void> addFavorite(String userId, String itemId, {String type = 'medication'}) async {
    final db = await instance.database;
    final tableName = type == 'medication' ? 'medication_favorites' : 'food_favorites';
    final columnName = type == 'medication' ? 'medicationId' : 'foodId';
    await db.insert(tableName, {
      'userId': userId,
      columnName: itemId,
      'addedAt': DateTime.now().toIso8601String(),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<void> removeFavorite(String userId, String itemId, {String type = 'medication'}) async {
    final db = await instance.database;
    final tableName = type == 'medication' ? 'medication_favorites' : 'food_favorites';
    final columnName = type == 'medication' ? 'medicationId' : 'foodId';
    await db.delete(
      tableName,
      where: 'userId = ? AND $columnName = ?',
      whereArgs: [userId, itemId],
    );
  }

  Future<List<String>> getFavorites(String userId, {String type = 'medication'}) async {
    final db = await instance.database;
    final tableName = type == 'medication' ? 'medication_favorites' : 'food_favorites';
    final columnName = type == 'medication' ? 'medicationId' : 'foodId';
    final maps = await db.query(
      tableName,
      columns: [columnName],
      where: 'userId = ?',
      whereArgs: [userId],
      orderBy: 'addedAt DESC',
    );

    return maps.map((map) => map[columnName] as String).toList();
  }

  Future<bool> isFavorite(String userId, String itemId, {String type = 'medication'}) async {
    final db = await instance.database;
    final tableName = type == 'medication' ? 'medication_favorites' : 'food_favorites';
    final columnName = type == 'medication' ? 'medicationId' : 'foodId';
    final maps = await db.query(
      tableName,
      where: 'userId = ? AND $columnName = ?',
      whereArgs: [userId, itemId],
      limit: 1,
    );

    return maps.isNotEmpty;
  }

  Future<void> clearFavorites(String userId, {String type = 'medication'}) async {
    final db = await instance.database;
    final tableName = type == 'medication' ? 'medication_favorites' : 'food_favorites';
    await db.delete(tableName, where: 'userId = ?', whereArgs: [userId]);
  }

  Future<int> getFavoritesCount(String userId, {String type = 'medication'}) async {
    final db = await instance.database;
    final tableName = type == 'medication' ? 'medication_favorites' : 'food_favorites';
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $tableName WHERE userId = ?',
      [userId],
    );

    return result.first['count'] as int;
  }

  Future<List<Medication>> getFavoriteMedications(String userId) async {
    final db = await instance.database;
    final maps = await db.rawQuery(
      '''
      SELECT m.* FROM medications m
      INNER JOIN medication_favorites f ON m.id = f.medicationId
      WHERE f.userId = ?
      ORDER BY f.addedAt DESC
    ''',
      [userId],
    );

    return maps.map((map) => Medication.fromMap(map)).toList();
  }

  Future close() async {
    final db = await instance.database;
    db.close();
  }
}
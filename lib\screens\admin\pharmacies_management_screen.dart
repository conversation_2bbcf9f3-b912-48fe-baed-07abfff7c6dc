import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/providers/pharmacy_provider.dart';

class PharmaciesManagementScreen extends StatefulWidget {
  const PharmaciesManagementScreen({super.key});

  @override
  State<PharmaciesManagementScreen> createState() => _PharmaciesManagementScreenState();
}

class _PharmaciesManagementScreenState extends State<PharmaciesManagementScreen> {
  String _searchQuery = '';
  String _selectedFilter = 'الكل';
  final List<String> _filters = [
    'الكل',
    'مفتوح الآن',
    'يوصل',
    'يقبل التأمين',
    'منتجات خالية من الجلوتين',
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final pharmacyProvider = Provider.of<PharmacyProvider>(context, listen: false);
      pharmacyProvider.fetchPharmacies();
    });
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final isAdmin = authProvider.isAdmin;

    if (!isAdmin) {
      return Scaffold(
        appBar: AppBar(title: Text('غير مصرح', style: GoogleFonts.cairo())),
        body: Center(
          child: Text(
            'ليس لديك صلاحية للوصول لهذه الصفحة',
            style: GoogleFonts.cairo(),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'إدارة الصيدليات',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue.shade600,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics, color: Colors.white),
            onPressed: () => _showStatistics(),
            tooltip: 'الإحصائيات',
          ),
        ],
      ),
      body: Consumer<PharmacyProvider>(
        builder: (context, pharmacyProvider, child) {
          if (pharmacyProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          final filteredPharmacies = _getFilteredPharmacies(pharmacyProvider.pharmacies);

          return Column(
            children: [
              _buildSearchAndFilters(),
              _buildStatisticsCard(pharmacyProvider.pharmacies),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    await pharmacyProvider.fetchPharmacies();
                  },
                  child: filteredPharmacies.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: filteredPharmacies.length,
                          itemBuilder: (context, index) {
                            return _buildPharmacyCard(filteredPharmacies[index], pharmacyProvider);
                          },
                        ),
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _addNewPharmacy(),
        backgroundColor: Colors.blue.shade600,
        icon: const Icon(Icons.add, color: Colors.white),
        label: Text(
          'إضافة صيدلية',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade600,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // شريط البحث
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'البحث في الصيدليات...',
                hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500),
                prefixIcon: Icon(Icons.search, color: Colors.blue.shade600),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 15,
                ),
              ),
              style: GoogleFonts.cairo(),
            ),
          ),
          const SizedBox(height: 16),
          // فلاتر
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _filters.length,
              itemBuilder: (context, index) {
                final filter = _filters[index];
                final isSelected = _selectedFilter == filter;
                
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(
                      filter,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: isSelected ? Colors.white : Colors.blue.shade700,
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedFilter = filter;
                      });
                    },
                    backgroundColor: Colors.blue.shade50,
                    selectedColor: Colors.blue.shade700,
                    checkmarkColor: Colors.white,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCard(List pharmacies) {
    final totalPharmacies = pharmacies.length;
    final openNow = pharmacies.where((p) => p.isOpen).length;
    final withDelivery = pharmacies.where((p) => p.hasDelivery).length;
    final withGlutenFree = pharmacies.where((p) => p.hasGlutenFreeProducts).length;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade400, Colors.blue.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إجمالي الصيدليات',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                Text(
                  '$totalPharmacies',
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'مفتوح الآن: $openNow',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'خدمات إضافية',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                Text(
                  'توصيل: $withDelivery',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
                Text(
                  'منتجات خالية: $withGlutenFree',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.local_pharmacy,
            size: 48,
            color: Colors.white.withValues(alpha: 0.7),
          ),
        ],
      ),
    );
  }

  Widget _buildPharmacyCard(pharmacy, pharmacyProvider) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.local_pharmacy,
                    color: Colors.blue.shade600,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        pharmacy.name,
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.location_on, size: 14, color: Colors.grey.shade500),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              pharmacy.address,
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: pharmacy.isOpen ? Colors.green.shade50 : Colors.red.shade50,
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: pharmacy.isOpen ? Colors.green.shade200 : Colors.red.shade200,
                              ),
                            ),
                            child: Text(
                              pharmacy.isOpen ? 'مفتوح' : 'مغلق',
                              style: GoogleFonts.cairo(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: pharmacy.isOpen ? Colors.green.shade700 : Colors.red.shade700,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (pharmacy.hasDelivery)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade50,
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: Colors.blue.shade200),
                              ),
                              child: Text(
                                'توصيل',
                                style: GoogleFonts.cairo(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.blue.shade700,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  icon: Icon(Icons.more_vert, color: Colors.grey.shade600),
                  onSelected: (value) {
                    if (value == 'edit') {
                      _editPharmacy(pharmacy);
                    } else if (value == 'delete') {
                      _deletePharmacy(pharmacy, pharmacyProvider);
                    } else if (value == 'view') {
                      _viewPharmacyDetails(pharmacy);
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'view',
                      child: Row(
                        children: [
                          const Icon(Icons.visibility, size: 18),
                          const SizedBox(width: 8),
                          Text('عرض التفاصيل', style: GoogleFonts.cairo()),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          const Icon(Icons.edit, size: 18),
                          const SizedBox(width: 8),
                          Text('تعديل', style: GoogleFonts.cairo()),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          const Icon(Icons.delete, size: 18, color: Colors.red),
                          const SizedBox(width: 8),
                          Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (pharmacy.hasGlutenFreeProducts)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Text(
                  'يتوفر منتجات خالية من الجلوتين',
                  style: GoogleFonts.cairo(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: Colors.green.shade700,
                  ),
                ),
              ),
            const SizedBox(height: 12),
            Row(
              children: [
                if (pharmacy.phoneNumber.isNotEmpty)
                  IconButton(
                    onPressed: () => _callPharmacy(pharmacy.phoneNumber),
                    icon: Icon(Icons.phone, color: Colors.green.shade600, size: 20),
                    tooltip: 'اتصال',
                  ),
                IconButton(
                  onPressed: () => _getDirections(pharmacy),
                  icon: Icon(Icons.directions, color: Colors.blue.shade600, size: 20),
                  tooltip: 'الاتجاهات',
                ),
                const Spacer(),
                Text(
                  'التقييم: ${pharmacy.rating.toStringAsFixed(1)}',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange.shade600,
                  ),
                ),
                Icon(Icons.star, color: Colors.orange.shade600, size: 16),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.local_pharmacy_outlined, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'لا توجد صيدليات',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على صيدليات تطابق البحث',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  List _getFilteredPharmacies(List pharmacies) {
    var filtered = pharmacies.where((pharmacy) {
      if (_searchQuery.isNotEmpty) {
        return pharmacy.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               pharmacy.address.toLowerCase().contains(_searchQuery.toLowerCase());
      }
      return true;
    }).toList();

    switch (_selectedFilter) {
      case 'مفتوح الآن':
        filtered = filtered.where((p) => p.isOpen).toList();
        break;
      case 'يوصل':
        filtered = filtered.where((p) => p.hasDelivery).toList();
        break;
      case 'يقبل التأمين':
        filtered = filtered.where((p) => p.acceptsInsurance).toList();
        break;
      case 'منتجات خالية من الجلوتين':
        filtered = filtered.where((p) => p.hasGlutenFreeProducts).toList();
        break;
    }

    return filtered;
  }

  void _addNewPharmacy() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('ميزة إضافة صيدلية قيد التطوير', style: GoogleFonts.cairo()),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _editPharmacy(pharmacy) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('ميزة تعديل الصيدلية قيد التطوير', style: GoogleFonts.cairo()),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _deletePharmacy(pharmacy, pharmacyProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف الصيدلية', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف "${pharmacy.name}"؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف الصيدلية', style: GoogleFonts.cairo()),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _viewPharmacyDetails(pharmacy) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(pharmacy.name, style: GoogleFonts.cairo()),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('العنوان: ${pharmacy.address}', style: GoogleFonts.cairo()),
              const SizedBox(height: 8),
              Text('الهاتف: ${pharmacy.phoneNumber}', style: GoogleFonts.cairo()),
              const SizedBox(height: 8),
              Text('ساعات العمل: ${pharmacy.openingHours}', style: GoogleFonts.cairo()),
              const SizedBox(height: 8),
              Text('التقييم: ${pharmacy.rating}', style: GoogleFonts.cairo()),
              const SizedBox(height: 8),
              Text('الحالة: ${pharmacy.isOpen ? "مفتوح" : "مغلق"}', style: GoogleFonts.cairo()),
              const SizedBox(height: 8),
              Text('التوصيل: ${pharmacy.hasDelivery ? "متوفر" : "غير متوفر"}', style: GoogleFonts.cairo()),
              const SizedBox(height: 8),
              Text('منتجات خالية من الجلوتين: ${pharmacy.hasGlutenFreeProducts ? "متوفر" : "غير متوفر"}', style: GoogleFonts.cairo()),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _callPharmacy(String phone) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('الاتصال بـ $phone', style: GoogleFonts.cairo()),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _getDirections(pharmacy) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('فتح الاتجاهات إلى ${pharmacy.name}', style: GoogleFonts.cairo()),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _showStatistics() {
    final pharmacyProvider = Provider.of<PharmacyProvider>(context, listen: false);
    final pharmacies = pharmacyProvider.pharmacies;
    
    final totalPharmacies = pharmacies.length;
    final openNow = pharmacies.where((p) => p.isOpen).length;
    final withDelivery = pharmacies.where((p) => p.hasDelivery).length;
    final withGlutenFree = pharmacies.where((p) => p.hasGlutenFreeProducts).length;
    final withInsurance = pharmacies.where((p) => p.acceptsInsurance).length;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إحصائيات الصيدليات', style: GoogleFonts.cairo()),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatRow('إجمالي الصيدليات', '$totalPharmacies'),
            _buildStatRow('مفتوح الآن', '$openNow'),
            _buildStatRow('خدمة التوصيل', '$withDelivery'),
            _buildStatRow('منتجات خالية من الجلوتين', '$withGlutenFree'),
            _buildStatRow('يقبل التأمين', '$withInsurance'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: GoogleFonts.cairo()),
          Text(value, style: GoogleFonts.cairo(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}

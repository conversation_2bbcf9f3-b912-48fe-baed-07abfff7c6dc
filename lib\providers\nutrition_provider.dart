import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:yassincil/models/nutrition_entry.dart';
import 'package:yassincil/services/firestore_service.dart';
import 'package:yassincil/utils/app_constants.dart';

class NutritionProvider extends ChangeNotifier {
  final FirestoreService _firestoreService;

  List<NutritionEntry> _nutritionEntries = [];
  NutritionGoals? _nutritionGoals;
  bool _isLoading = false;
  String? _errorMessage;
  DateTime _selectedDate = DateTime.now();

  // Getters
  List<NutritionEntry> get nutritionEntries => _nutritionEntries;
  NutritionGoals? get nutritionGoals => _nutritionGoals;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  DateTime get selectedDate => _selectedDate;

  NutritionProvider(this._firestoreService);

  // Get entries for selected date
  List<NutritionEntry> get todayEntries {
    final today = DateTime(
      _selectedDate.year,
      _selectedDate.month,
      _selectedDate.day,
    );
    return _nutritionEntries.where((entry) {
      final entryDate = DateTime(
        entry.date.year,
        entry.date.month,
        entry.date.day,
      );
      return entryDate.isAtSameMomentAs(today);
    }).toList();
  }

  // Get daily summary
  DailyNutritionSummary get dailySummary {
    return DailyNutritionSummary.fromEntries(_selectedDate, todayEntries);
  }

  // Get entries by meal type
  List<NutritionEntry> getEntriesByMealType(String mealType) {
    return todayEntries.where((entry) => entry.mealType == mealType).toList();
  }

  // Set selected date
  void setSelectedDate(DateTime date) {
    _selectedDate = date;
    notifyListeners();
  }

  // Fetch nutrition entries for user
  Future<void> fetchNutritionEntries(String userId) async {
    _setLoading(true);
    try {
      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.nutritionEntriesCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('date', descending: true)
          .limit(100) // Last 100 entries
          .get();

      _nutritionEntries = querySnapshot.docs
          .map((doc) => NutritionEntry.fromFirestore(doc))
          .toList();

      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'فشل في تحميل بيانات التغذية: $e';
      debugPrint('Error fetching nutrition entries: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Fetch nutrition goals
  Future<void> fetchNutritionGoals(String userId) async {
    try {
      final doc = await _firestoreService.db
          .collection(AppConstants.nutritionGoalsCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        _nutritionGoals = NutritionGoals.fromFirestore(doc);
      } else {
        // Create default goals
        _nutritionGoals = NutritionGoals(
          userId: userId,
          updatedAt: DateTime.now(),
        );
        await saveNutritionGoals(_nutritionGoals!);
      }
    } catch (e) {
      debugPrint('Error fetching nutrition goals: $e');
      // Use default goals if fetch fails
      _nutritionGoals = NutritionGoals(
        userId: userId,
        updatedAt: DateTime.now(),
      );
    }
    notifyListeners();
  }

  // Add nutrition entry
  Future<void> addNutritionEntry(NutritionEntry entry) async {
    _setLoading(true);
    try {
      final docRef = await _firestoreService.db
          .collection(AppConstants.nutritionEntriesCollection)
          .add(entry.toMap());

      final newEntry = entry.copyWith(id: docRef.id);
      _nutritionEntries.insert(0, newEntry);

      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'فشل في إضافة الوجبة: $e';
      debugPrint('Error adding nutrition entry: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Update nutrition entry
  Future<void> updateNutritionEntry(NutritionEntry entry) async {
    _setLoading(true);
    try {
      await _firestoreService.db
          .collection(AppConstants.nutritionEntriesCollection)
          .doc(entry.id)
          .update(entry.toMap());

      final index = _nutritionEntries.indexWhere((e) => e.id == entry.id);
      if (index != -1) {
        _nutritionEntries[index] = entry;
      }

      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'فشل في تحديث الوجبة: $e';
      debugPrint('Error updating nutrition entry: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Delete nutrition entry
  Future<void> deleteNutritionEntry(String entryId) async {
    _setLoading(true);
    try {
      await _firestoreService.db
          .collection(AppConstants.nutritionEntriesCollection)
          .doc(entryId)
          .delete();

      _nutritionEntries.removeWhere((entry) => entry.id == entryId);

      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'فشل في حذف الوجبة: $e';
      debugPrint('Error deleting nutrition entry: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Save nutrition goals
  Future<void> saveNutritionGoals(NutritionGoals goals) async {
    try {
      await _firestoreService.db
          .collection(AppConstants.nutritionGoalsCollection)
          .doc(goals.userId)
          .set(goals.toMap());

      _nutritionGoals = goals;
      notifyListeners();
    } catch (e) {
      debugPrint('Error saving nutrition goals: $e');
      rethrow;
    }
  }

  // Get weekly summary
  List<DailyNutritionSummary> getWeeklySummary() {
    final List<DailyNutritionSummary> weeklySummary = [];
    final now = DateTime.now();

    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayEntries = _nutritionEntries.where((entry) {
        final entryDate = DateTime(
          entry.date.year,
          entry.date.month,
          entry.date.day,
        );
        final targetDate = DateTime(date.year, date.month, date.day);
        return entryDate.isAtSameMomentAs(targetDate);
      }).toList();

      weeklySummary.add(DailyNutritionSummary.fromEntries(date, dayEntries));
    }

    return weeklySummary;
  }

  // Get monthly summary
  List<DailyNutritionSummary> getMonthlySummary() {
    final List<DailyNutritionSummary> monthlySummary = [];
    final now = DateTime.now();
    // final firstDayOfMonth = DateTime(now.year, now.month, 1);
    final lastDayOfMonth = DateTime(now.year, now.month + 1, 0);

    for (int day = 1; day <= lastDayOfMonth.day; day++) {
      final date = DateTime(now.year, now.month, day);
      final dayEntries = _nutritionEntries.where((entry) {
        final entryDate = DateTime(
          entry.date.year,
          entry.date.month,
          entry.date.day,
        );
        final targetDate = DateTime(date.year, date.month, date.day);
        return entryDate.isAtSameMomentAs(targetDate);
      }).toList();

      monthlySummary.add(DailyNutritionSummary.fromEntries(date, dayEntries));
    }

    return monthlySummary;
  }

  // Calculate progress percentage for goals
  Map<String, double> getGoalsProgress() {
    if (_nutritionGoals == null) return {};

    final summary = dailySummary;
    return {
      'calories':
          (summary.totalCalories / _nutritionGoals!.dailyCaloriesGoal * 100)
              .clamp(0, 100),
      'protein':
          (summary.totalProtein / _nutritionGoals!.dailyProteinGoal * 100)
              .clamp(0, 100),
      'carbs': (summary.totalCarbs / _nutritionGoals!.dailyCarbsGoal * 100)
          .clamp(0, 100),
      'fat': (summary.totalFat / _nutritionGoals!.dailyFatGoal * 100).clamp(
        0,
        100,
      ),
      'fiber': (summary.totalFiber / _nutritionGoals!.dailyFiberGoal * 100)
          .clamp(0, 100),
    };
  }

  // Get nutrition insights
  Map<String, dynamic> getNutritionInsights() {
    final summary = dailySummary;
    final goals = _nutritionGoals;

    if (goals == null) return {};

    return {
      'caloriesStatus': _getCaloriesStatus(
        summary.totalCalories,
        goals.dailyCaloriesGoal,
      ),
      'proteinStatus': _getProteinStatus(
        summary.totalProtein,
        goals.dailyProteinGoal,
      ),
      'glutenFreePercentage': summary.totalItems > 0
          ? (summary.glutenFreeItems / summary.totalItems * 100).round()
          : 100,
      'mealBalance': _getMealBalance(summary.mealCalories),
      'recommendations': _getRecommendations(summary, goals),
    };
  }

  String _getCaloriesStatus(double consumed, double goal) {
    final percentage = consumed / goal;
    if (percentage < 0.8) return 'منخفض';
    if (percentage > 1.2) return 'مرتفع';
    return 'مثالي';
  }

  String _getProteinStatus(double consumed, double goal) {
    final percentage = consumed / goal;
    if (percentage < 0.8) return 'منخفض';
    if (percentage > 1.5) return 'مرتفع';
    return 'جيد';
  }

  Map<String, double> _getMealBalance(Map<String, double> mealCalories) {
    final total = mealCalories.values.fold(
      0.0,
      (total, calories) => total + calories,
    );
    if (total == 0) return {};

    return mealCalories.map(
      (meal, calories) => MapEntry(meal, calories / total * 100),
    );
  }

  List<String> _getRecommendations(
    DailyNutritionSummary summary,
    NutritionGoals goals,
  ) {
    final List<String> recommendations = [];

    if (summary.totalCalories < goals.dailyCaloriesGoal * 0.8) {
      recommendations.add('تحتاج لزيادة السعرات الحرارية');
    }

    if (summary.totalProtein < goals.dailyProteinGoal * 0.8) {
      recommendations.add('أضف المزيد من البروتين');
    }

    if (summary.totalFiber < goals.dailyFiberGoal * 0.8) {
      recommendations.add('تناول المزيد من الألياف');
    }

    if (summary.glutenFreeItems < summary.totalItems) {
      recommendations.add('تأكد من أن جميع الأطعمة خالية من الجلوتين');
    }

    return recommendations;
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}

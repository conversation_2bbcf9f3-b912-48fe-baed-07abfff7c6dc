import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:yassincil/services/auth_service.dart';
import 'package:yassincil/services/notification_service.dart';
import '../models/user_profile.dart';
import '../services/firestore_service.dart'; // For fetching user profile initially
import '../utils/app_constants.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AuthProvider with ChangeNotifier {
  // قائمة معرفات العناصر المفضلة لهذا المستخدم (للواجهة فقط)
  List<String> _favoriteFoodIds = [];
  List<String> get favoriteFoodIds => _favoriteFoodIds;

  // جلب المفضلة من Firestore بعد تسجيل الدخول (من subcollection favorites)
  Future<void> fetchFavorites() async {
    if (_currentUser == null) return;
    try {
      final snapshot = await _firestoreService.db
          .collection(AppConstants.usersCollection)
          .doc(_currentUser!.uid)
          .collection(AppConstants.favoritesSubcollection)
          .orderBy('addedAt', descending: true)
          .limit(500)
          .get();

      _favoriteFoodIds = snapshot.docs.map((d) => d.id).toList();
      notifyListeners();
    } catch (e) {
      debugPrint('Error fetching favorites: $e');
    }
  }

  // إضافة عنصر إلى المفضلة (subcollection + تحديث قائمة الواجهة)
  Future<void> addFavorite(String recipeId) async {
    if (_currentUser == null) return;
    if (_favoriteFoodIds.contains(recipeId)) return;

    _favoriteFoodIds.add(recipeId);
    notifyListeners();

    try {
      await _firestoreService.db
        .collection(AppConstants.usersCollection)
        .doc(_currentUser!.uid)
        .collection(AppConstants.favoritesSubcollection)
        .doc(recipeId)
        .set({'addedAt': FieldValue.serverTimestamp()});
    } catch (e) {
      // رجوع في حال الفشل
      _favoriteFoodIds.remove(recipeId);
      notifyListeners();
      rethrow;
    }
  }

  // إزالة عنصر من المفضلة (subcollection + تحديث قائمة الواجهة)
  Future<void> removeFavorite(String recipeId) async {
    if (_currentUser == null) return;

    final removed = _favoriteFoodIds.remove(recipeId);
    if (removed) notifyListeners();

    try {
      await _firestoreService.db
        .collection(AppConstants.usersCollection)
        .doc(_currentUser!.uid)
        .collection(AppConstants.favoritesSubcollection)
        .doc(recipeId)
        .delete();
    } catch (e) {
      // رجوع في حال الفشل
      if (!removed) return;
      _favoriteFoodIds.add(recipeId);
      notifyListeners();
      rethrow;
    }
  }

  // هل العنصر مفضل؟
  bool isFavorite(String foodId) => _favoriteFoodIds.contains(foodId);

  // بث مباشر لمعرفات المفضلة للمستخدم الحالي
  Stream<List<String>> favoritesStream() {
    if (_currentUser == null) {
      return Stream.value(<String>[]);
    }
    return _firestoreService.db
        .collection(AppConstants.usersCollection)
        .doc(_currentUser!.uid)
        .collection(AppConstants.favoritesSubcollection)
        .orderBy('addedAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs.map((d) => d.id).toList());
  }

  final AuthService _authService;
  final FirestoreService _firestoreService; // Added to get user profile

  User? _currentUser;
  UserProfile? _userProfile;
  bool _isLoadingAuth = true; // To track initial auth state check

  User? get currentUser => _currentUser;
  UserProfile? get userProfile => _userProfile;
  bool get isLoadingAuth => _isLoadingAuth;
  bool get isAdmin => _userProfile?.role == 'admin';

  AuthProvider(this._authService, this._firestoreService) {
    // Listen to changes in Firebase Auth state
    _authService.authStateChanges.listen((firebaseUser) async {
      _currentUser = firebaseUser;
      debugPrint('AuthProvider: currentUser = \${_currentUser?.uid}');
      if (_currentUser != null) {
        // If user is logged in, try to fetch their profile from Firestore
        try {
          final doc = await _firestoreService.getDocument(
            'users',
            _currentUser!.uid,
          );
          debugPrint(
            'AuthProvider: Firestore user doc exists = \${doc.exists}',
          );
          if (doc.exists) {
            _userProfile = UserProfile.fromFirestore(doc);
            debugPrint(
              'AuthProvider: userProfile.role = \${_userProfile?.role}',
            );

            // حفظ رمز FCM للمستخدم المسجل
            await NotificationService.saveUserToken(_currentUser!.uid);
          } else {
            // This case happens if user is authenticated but no profile in Firestore (e.g., deleted manually)
            _userProfile = null;
          }
        } catch (e) {
          debugPrint(
            'Error getting document ${_currentUser!.uid} from users: $e',
          );
          // في حالة عدم وجود صلاحيات أو خطأ في الاتصال، نحاول إنشاء مستند جديد
          try {
            await _createUserProfileIfNotExists();
          } catch (createError) {
            debugPrint('Error creating user profile: $createError');
            _userProfile = null;
          }
        }
      } else {
        // If no user is logged in, clear profile
        if (_userProfile != null) {
          // إزالة رمز FCM عند تسجيل الخروج
          await NotificationService.removeUserToken(_userProfile!.uid);
        }
        _userProfile = null;
      }
      _isLoadingAuth = false; // Authentication check is complete
      debugPrint('AuthProvider: isLoadingAuth = false, notifyListeners');
      await fetchFavorites();
      notifyListeners(); // Notify listeners (like MyApp's Consumer) to rebuild
    });
  }

  Future<void> signIn(String email, String password) async {
    _isLoadingAuth = true;
    notifyListeners();
    try {
      await _authService.signIn(email, password);
    } catch (e) {
      rethrow;
    }
    // لا تعيد _isLoadingAuth = false هنا، المستمع سيقوم بذلك بعد جلب بيانات المستخدم
  }

  Future<void> signUp(
    String email,
    String password,
    String username, {
    String? firstName,
    String? lastName,
  }) async {
    _isLoadingAuth = true;
    notifyListeners();
    try {
      await _authService.signUp(
        email,
        password,
        username,
        firstName: firstName,
        lastName: lastName,
      );
    } catch (e) {
      rethrow;
    }
    // لا تعيد _isLoadingAuth = false هنا، المستمع سيقوم بذلك بعد جلب بيانات المستخدم
  }

  Future<void> signOut() async {
    _isLoadingAuth = true;
    notifyListeners();
    try {
      await _authService.signOut();
    } catch (e) {
      rethrow;
    } finally {
      _isLoadingAuth = false;
      notifyListeners();
    }
  }

  Future<void> resetPassword(String email) async {
    await _authService.resetPassword(email);
  }

  Future<void> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    await _authService.changePassword(currentPassword, newPassword);
  }

  Future<void> updateEmail(String newEmail, String password) async {
    await _authService.updateEmail(newEmail, password);
  }

  // Update user profile from settings/admin (e.g., username change)
  Future<void> updateCurrentUserProfile(Map<String, dynamic> data) async {
    if (_currentUser == null) return;
    try {
      await _firestoreService.updateDocument('users', _currentUser!.uid, data);
      // Re-fetch profile to ensure UI updates
      final doc = await _firestoreService.getDocument(
        'users',
        _currentUser!.uid,
      );
      if (doc.exists) {
        _userProfile = UserProfile.fromFirestore(doc);
        notifyListeners();
      }
    } catch (e) {
      debugPrint("Error updating user profile: $e");
      rethrow;
    }
  }

  /// إنشاء مستند المستخدم إذا لم يكن موجوداً
  Future<void> _createUserProfileIfNotExists() async {
    if (_currentUser == null) return;

    try {
      final newProfile = UserProfile(
        uid: _currentUser!.uid,
        email: _currentUser!.email ?? '',
        username: _currentUser!.displayName ?? 'مستخدم',
        role: 'user', // Default role
        createdAt: DateTime.now(),
      );

      await _firestoreService.addDocumentWithId(
        'users',
        _currentUser!.uid,
        newProfile.toMap(),
      );

      _userProfile = newProfile;
      debugPrint('تم إنشاء مستند المستخدم بنجاح');
    } catch (e) {
      debugPrint('خطأ في إنشاء مستند المستخدم: $e');
      rethrow;
    }
  }
}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:share_plus/share_plus.dart';

import 'package:yassincil/providers/favorites_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/models/medication.dart';
import 'package:yassincil/screens/medications/medication_detail_screen_enhanced.dart';
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/widgets/medications_app_bar.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late TabController _tabController;
  String _searchQuery = '';
  String _selectedCategory = 'الكل';
  bool? _selectedStatus; // null = الكل، true = آمن، false = غير آمن

  final List<String> _categories = [
    'الكل',
    'مسكنات الألم',
    'مضادات الحيوية',
    'أدوية الضغط',
    'أدوية السكري',
    'أدوية الجهاز الهضمي',
    'أدوية الحساسية',
    'فيتامينات ومكملات',
    'أدوية القلب',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _tabController = TabController(length: _categories.length, vsync: this);

    // جلب المفضلة عند تحميل الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        Provider.of<FavoritesProvider>(context, listen: false).fetchFavorites();
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  List<Medication> _getFilteredFavorites(List<Medication> favorites) {
    List<Medication> filtered = favorites;

    // فلترة البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((medication) {
        return medication.name.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            medication.company.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            medication.category.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            medication.notes.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // فلترة الفئة
    if (_selectedCategory != 'الكل') {
      filtered = filtered.where((medication) {
        return medication.category == _selectedCategory;
      }).toList();
    }

    // فلترة الحالة
    if (_selectedStatus != null) {
      filtered = filtered.where((medication) {
        return medication.isAllowed == _selectedStatus;
      }).toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;

    if (user == null) {
      return Scaffold(
        appBar: const MedicationsAppBar(title: 'المفضلة'),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.login, size: 80, color: Colors.grey.shade400),
              const SizedBox(height: 16),
              Text(
                'يجب تسجيل الدخول أولاً',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'سجل دخولك لحفظ الأدوية المفضلة',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Consumer<FavoritesProvider>(
      builder: (context, favoritesProvider, child) {
        final filteredFavorites = _getFilteredFavorites(
          favoritesProvider.favoriteMedications,
        );

        return Scaffold(
          backgroundColor: const Color(0xFFF8FAFC),
          body: CustomScrollView(
            slivers: [
              _buildSliverAppBar(favoritesProvider),
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    _buildSearchAndFilters(),
                    _buildCategoryTabs(),
                    _buildStatistics(favoritesProvider),
                  ],
                ),
              ),
              if (favoritesProvider.isLoading)
                const SliverToBoxAdapter(
                  child: Center(
                    child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: CircularProgressIndicator(),
                    ),
                  ),
                )
              else if (favoritesProvider.errorMessage != null)
                SliverToBoxAdapter(
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(32.0),
                      child: Column(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 60,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'حدث خطأ',
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            favoritesProvider.errorMessage!,
                            style: GoogleFonts.cairo(fontSize: 14),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton.icon(
                            onPressed: () => favoritesProvider.fetchFavorites(),
                            icon: const Icon(Icons.refresh),
                            label: Text(
                              'إعادة المحاولة',
                              style: GoogleFonts.cairo(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              else if (filteredFavorites.isEmpty)
                SliverToBoxAdapter(
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(32.0),
                      child: Column(
                        children: [
                          Icon(
                            _searchQuery.isNotEmpty ||
                                    _selectedCategory != 'الكل' ||
                                    _selectedStatus != null
                                ? Icons.search_off
                                : Icons.favorite_border,
                            size: 80,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _searchQuery.isNotEmpty ||
                                    _selectedCategory != 'الكل' ||
                                    _selectedStatus != null
                                ? 'لا توجد نتائج'
                                : 'لا توجد أدوية مفضلة',
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _searchQuery.isNotEmpty ||
                                    _selectedCategory != 'الكل' ||
                                    _selectedStatus != null
                                ? 'جرب تغيير معايير البحث'
                                : 'ابدأ بإضافة أدوية لقائمة المفضلة',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.grey.shade500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              else
                SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final medication = filteredFavorites[index];
                    return Container(
                      margin: const EdgeInsets.only(
                        bottom: 16,
                        left: 16,
                        right: 16,
                      ),
                      child: _buildFavoriteMedicationCard(
                        medication,
                        favoritesProvider,
                      ),
                    );
                  }, childCount: filteredFavorites.length),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSliverAppBar(FavoritesProvider favoritesProvider) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: AppColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'المفضلة',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontSize: 18,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primary,
                AppColors.primary.withValues(alpha: 0.85),
              ],
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                right: -50,
                top: -50,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.1),
                  ),
                ),
              ),
              Positioned(
                left: -30,
                bottom: -30,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.05),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh_rounded, color: Colors.white),
          onPressed: () {
            Provider.of<FavoritesProvider>(
              context,
              listen: false,
            ).fetchFavorites();
          },
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              style: GoogleFonts.cairo(fontSize: 16),
              decoration: InputDecoration(
                hintText: 'ابحث في المفضلة...',
                hintStyle: GoogleFonts.cairo(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                ),
                prefixIcon: Container(
                  padding: const EdgeInsets.all(12),
                  child: Icon(
                    Icons.search_rounded,
                    color: const Color(0xFF2563EB),
                    size: 24,
                  ),
                ),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: Icon(
                          Icons.clear_rounded,
                          color: Colors.grey.shade600,
                        ),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryTabs() {
    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(vertical: 12),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: const Color(0xFFE91E63),
        indicatorWeight: 3,
        indicatorSize: TabBarIndicatorSize.label,
        labelColor: const Color(0xFFE91E63),
        unselectedLabelColor: Colors.grey.shade600,
        labelStyle: GoogleFonts.cairo(
          fontWeight: FontWeight.bold,
          fontSize: 15,
        ),
        unselectedLabelStyle: GoogleFonts.cairo(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
        labelPadding: const EdgeInsets.symmetric(horizontal: 20),
        onTap: (index) {
          setState(() {
            _selectedCategory = _categories[index];
          });
        },
        tabs: _categories
            .map(
              (category) => Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: Text(category),
              ),
            )
            .toList(),
      ),
    );
  }

  Widget _buildStatistics(FavoritesProvider favoritesProvider) {
    final stats = favoritesProvider.getFavoritesStatistics();

    if (stats['total'] == 0) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildStatCard(
            'المجموع',
            stats['total'].toString(),
            Icons.favorite,
            const Color(0xFFE91E63),
          ),
          const SizedBox(width: 16),
          _buildStatCard(
            'آمن',
            stats['safe'].toString(),
            Icons.check_circle,
            Colors.green,
          ),
          const SizedBox(width: 16),
          _buildStatCard(
            'غير آمن',
            stats['unsafe'].toString(),
            Icons.warning,
            Colors.orange,
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoriteMedicationCard(
    Medication medication,
    FavoritesProvider favoritesProvider,
  ) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) =>
                EnhancedMedicationDetailScreen(medication: medication),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: const Color(0xFFE91E63).withValues(alpha: 0.1),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFE91E63).withValues(alpha: 0.1),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFFE91E63).withValues(alpha: 0.1),
                    const Color(0xFFAD1457).withValues(alpha: 0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  // Medication Image
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      gradient: LinearGradient(
                        colors: [
                          Colors.white.withValues(alpha: 0.9),
                          Colors.white.withValues(alpha: 0.7),
                        ],
                      ),
                      border: Border.all(
                        color: const Color(0xFFE91E63).withValues(alpha: 0.3),
                        width: 2,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(13),
                      child:
                          medication.imageUrl != null &&
                              medication.imageUrl!.isNotEmpty
                          ? Image.network(
                              medication.imageUrl!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return const Icon(
                                  Icons.medical_services_rounded,
                                  color: Color(0xFFE91E63),
                                  size: 30,
                                );
                              },
                            )
                          : const Icon(
                              Icons.medical_services_rounded,
                              color: Color(0xFFE91E63),
                              size: 30,
                            ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Medication Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          medication.name,
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF1F2937),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          medication.company,
                          style: GoogleFonts.cairo(
                            fontSize: 13,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: medication.isAllowed
                                  ? [
                                      Colors.green.withValues(alpha: 0.2),
                                      Colors.green.withValues(alpha: 0.1),
                                    ]
                                  : [
                                      Colors.orange.withValues(alpha: 0.2),
                                      Colors.orange.withValues(alpha: 0.1),
                                    ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                medication.isAllowed
                                    ? Icons.check_circle_rounded
                                    : Icons.warning_rounded,
                                size: 14,
                                color: medication.isAllowed
                                    ? Colors.green
                                    : Colors.orange,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                medication.isAllowed ? 'آمن' : 'غير آمن',
                                style: GoogleFonts.cairo(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w600,
                                  color: medication.isAllowed
                                      ? Colors.green
                                      : Colors.orange,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Favorite Button
                  Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFFE91E63).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(
                        Icons.favorite,
                        color: Color(0xFFE91E63),
                        size: 22,
                      ),
                      onPressed: () async {
                        final wasFavorite = favoritesProvider.isFavorite(
                          medication.id!,
                        );
                        await favoritesProvider.toggleFavorite(medication);
                        if (!mounted) return;
                        ScaffoldMessenger.of(context).hideCurrentSnackBar();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              wasFavorite
                                  ? 'تمت الإزالة من المفضلة'
                                  : 'تمت الإضافة إلى المفضلة',
                              style: GoogleFonts.cairo(color: Colors.white),
                            ),
                            action: SnackBarAction(
                              label: 'تراجع',
                              textColor: Colors.white,
                              onPressed: () {
                                favoritesProvider.toggleFavorite(medication);
                              },
                            ),
                            backgroundColor: wasFavorite
                                ? Colors.red
                                : Colors.green,
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            // Body
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    medication.category,
                    style: GoogleFonts.cairo(
                      fontSize: 13,
                      color: const Color(0xFFE91E63),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (medication.notes.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Text(
                      medication.notes,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/providers/recipe_provider.dart';
import 'package:yassincil/models/recipe.dart';
import 'package:yassincil/screens/recipes/recipe_detail_screen.dart';

class FavoriteRecipesScreen extends StatefulWidget {
  const FavoriteRecipesScreen({super.key});

  @override
  State<FavoriteRecipesScreen> createState() => _FavoriteRecipesScreenState();
}

class _FavoriteRecipesScreenState extends State<FavoriteRecipesScreen> {
  late Future<List<Recipe>> _favoriteRecipesFuture;
  bool _lactoseFreeOnly = false;

  @override
  void initState() {
    super.initState();
    _favoriteRecipesFuture = _fetchFavoriteRecipes();
  }

  Future<List<Recipe>> _fetchFavoriteRecipes() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final recipeProvider = Provider.of<RecipeProvider>(context, listen: false);

    if (authProvider.currentUser == null) {
      return [];
    }

    // We first need the IDs from the auth provider
    await authProvider.fetchFavorites();
    final favoriteIds = authProvider.favoriteFoodIds;

    if (favoriteIds.isEmpty) {
      return [];
    }

    // Then we fetch the recipe data for those IDs
    return recipeProvider.fetchRecipesByIds(favoriteIds);
  }

  Future<void> _refresh() async {
    setState(() {
      _favoriteRecipesFuture = _fetchFavoriteRecipes();
    });
  }

  @override
  Widget build(BuildContext context) {
    final auth = Provider.of<AuthProvider>(context);

    if (auth.currentUser == null) {
      return Scaffold(
        appBar: AppBar(
          title: Text('وصفاتي المفضلة', style: GoogleFonts.cairo()),
        ),
        body: Center(
          child: Text(
            'يجب تسجيل الدخول لعرض المفضلة',
            style: GoogleFonts.cairo(),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        backgroundColor: const Color(0xFF00BFA5),
        elevation: 0,
        title: Row(
          children: [
            const Icon(Icons.favorite_rounded, color: Colors.white),
            const SizedBox(width: 8),
            Text(
              'وصفاتي المفضلة',
              style: GoogleFonts.cairo(color: Colors.white),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFilterBar(),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refresh,
              child: FutureBuilder<List<Recipe>>(
                future: _favoriteRecipesFuture,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (snapshot.hasError) {
                    return Center(
                      child: Text(
                        'حدث خطأ أثناء تحميل المفضلة',
                        style: GoogleFonts.cairo(),
                      ),
                    );
                  }

                  final favoriteRecipes = snapshot.data ?? [];
                  final filteredRecipes = _lactoseFreeOnly
                      ? favoriteRecipes.where((r) => r.isLactoseFree).toList()
                      : favoriteRecipes;

                  if (filteredRecipes.isEmpty) {
                    return ListView(
                      children: [
                        SizedBox(
                          height: MediaQuery.of(context).size.height * 0.5,
                          child: Center(
                            child: Text(
                              _lactoseFreeOnly
                                  ? 'لا توجد وصفات مفضلة خالية من اللاكتوز'
                                  : 'لا توجد وصفات مفضلة بعد',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  }

                  return ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredRecipes.length,
                    itemBuilder: (context, index) {
                      final recipe = filteredRecipes[index];
                      return _FavoriteRecipeCard(
                        recipe: recipe,
                        onUnfavorite: _refresh,
                      );
                    },
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      color: Colors.white,
      child: Row(
        children: [
          FilterChip(
            label: Text('خالي من اللاكتوز فقط', style: GoogleFonts.cairo()),
            selected: _lactoseFreeOnly,
            onSelected: (selected) {
              setState(() {
                _lactoseFreeOnly = selected;
              });
            },
            selectedColor: const Color(0xFF00BFA5).withOpacity(0.3),
            checkmarkColor: const Color(0xFF00BFA5),
          ),
        ],
      ),
    );
  }
}

class _FavoriteRecipeCard extends StatelessWidget {
  final Recipe recipe;
  final VoidCallback onUnfavorite;
  const _FavoriteRecipeCard({required this.recipe, required this.onUnfavorite});

  @override
  Widget build(BuildContext context) {
    final auth = Provider.of<AuthProvider>(context, listen: false);
    final recipeProvider = Provider.of<RecipeProvider>(context, listen: false);

    return GestureDetector(
      onTap: () {
        Navigator.of(context)
            .push(
              MaterialPageRoute(
                builder: (context) => RecipeDetailScreen(recipe: recipe),
              ),
            )
            .then((_) => onUnfavorite()); // Refresh when returning
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: const Color(0xFF00BFA5).withOpacity(0.12)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            // صورة
            Container(
              width: 110,
              height: 90,
              margin: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Colors.grey.shade100,
              ),
              clipBehavior: Clip.antiAlias,
              child: recipe.imageUrls.isNotEmpty
                  ? Image.network(
                      recipe.imageUrls.first,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return const Center(
                          child: Icon(
                            Icons.broken_image_outlined,
                            color: Colors.grey,
                          ),
                        );
                      },
                    )
                  : const Center(
                      child: Icon(
                        Icons.restaurant_menu_rounded,
                        color: Colors.grey,
                      ),
                    ),
            ),
            // معلومات
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(right: 12, top: 12, bottom: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            recipe.title,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF1F2937),
                            ),
                          ),
                        ),
                        if (recipe.ratingsCount > 0) ...[
                          const Icon(
                            Icons.star_rounded,
                            color: Colors.amber,
                            size: 18,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            recipe.averageRating.toStringAsFixed(1),
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '(${recipe.ratingsCount})',
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 6),
                    Text(
                      recipe.category,
                      style: GoogleFonts.cairo(
                        fontSize: 13,
                        color: const Color(0xFF00BFA5),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        _chip(
                          icon: Icons.schedule_rounded,
                          text: '${recipe.prepTime + recipe.cookTime} دقيقة',
                        ),
                        const SizedBox(width: 8),
                        _chip(
                          icon: Icons.person_rounded,
                          text: '${recipe.servings} حصص',
                        ),
                        const Spacer(),
                        IconButton(
                          tooltip: 'إزالة من المفضلة',
                          onPressed: () async {
                            final id = recipe.id;
                            if (id == null) return;
                            await auth.removeFavorite(id);
                            // عداد المفضلة يتم تحديثه عبر Cloud Function
                            onUnfavorite(); // Trigger refresh
                          },
                          icon: const Icon(
                            Icons.favorite,
                            color: Colors.redAccent,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _chip({required IconData icon, required String text}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.grey.shade600),
          const SizedBox(width: 4),
          Text(
            text,
            style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey.shade700),
          ),
        ],
      ),
    );
  }
}

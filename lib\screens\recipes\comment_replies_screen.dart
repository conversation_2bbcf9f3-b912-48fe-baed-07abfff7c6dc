import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:yassincil/models/comment.dart';
import 'package:yassincil/providers/recipe_provider.dart';
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/widgets/advanced_recipe_comment_widget.dart';
import 'package:yassincil/widgets/add_recipe_comment_widget.dart';

class CommentRepliesScreen extends StatelessWidget {
  final String recipeId;
  final Comment parentComment;

  const CommentRepliesScreen({
    super.key,
    required this.recipeId,
    required this.parentComment,
  });

  @override
  Widget build(BuildContext context) {
    final recipeProvider = Provider.of<RecipeProvider>(context, listen: false);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'الردود على ${parentComment.username}',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: AppColors.textOnPrimary,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        actions: [
          // زر إضافة بيانات تجريبية للاختبار
          IconButton(
            icon: const Icon(Icons.add_comment),
            onPressed: () async {
              try {
                await recipeProvider.addSampleRepliesToComment(
                  recipeId: recipeId,
                  parentCommentId: parentComment.id!,
                );
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم إضافة ردود تجريبية للاختبار'),
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في إضافة الردود: $e'),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              }
            },
            tooltip: 'إضافة ردود تجريبية',
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: CustomScrollView(
              slivers: [
                // Parent Comment
                SliverToBoxAdapter(
                  child: Container(
                    color: Colors.white,
                    padding: const EdgeInsets.all(8.0),
                    margin: const EdgeInsets.only(bottom: 8.0),
                    child: AdvancedRecipeCommentWidget(
                      comment: parentComment,
                      recipeId: recipeId,
                      showReplies: false, // Don't show replies recursively here
                    ),
                  ),
                ),
                // Replies List
                StreamBuilder<List<Comment>>(
                  stream: recipeProvider.getRepliesForComment(
                    recipeId: recipeId,
                    parentCommentId: parentComment.id!,
                  ),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return SliverToBoxAdapter(
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            children: [
                              const CircularProgressIndicator(),
                              const SizedBox(height: 16),
                              Text(
                                'جاري تحميل الردود...',
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                    if (snapshot.hasError) {
                      debugPrint('خطأ في تحميل الردود: ${snapshot.error}');
                      return SliverToBoxAdapter(
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            children: [
                              Icon(
                                Icons.error_outline,
                                size: 48,
                                color: AppColors.error,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'حدث خطأ في تحميل الردود',
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.error,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'الخطأ: ${snapshot.error}',
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: AppColors.textSecondary,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'يرجى المحاولة مرة أخرى',
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: () {
                                  // إعادة تحميل الصفحة
                                  Navigator.of(context).pushReplacement(
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          CommentRepliesScreen(
                                            recipeId: recipeId,
                                            parentComment: parentComment,
                                          ),
                                    ),
                                  );
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.primary,
                                ),
                                child: Text(
                                  'إعادة المحاولة',
                                  style: GoogleFonts.cairo(
                                    color: AppColors.textOnPrimary,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                    final replies = snapshot.data ?? [];
                    if (replies.isEmpty) {
                      return SliverToBoxAdapter(
                        child: Container(
                          padding: const EdgeInsets.all(40),
                          child: Column(
                            children: [
                              Icon(
                                Icons.chat_bubble_outline,
                                size: 64,
                                color: AppColors.textSecondary.withValues(
                                  alpha: 0.5,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'لا توجد ردود بعد',
                                style: GoogleFonts.cairo(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'كن أول من يرد على هذا التعليق!',
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  color: AppColors.textSecondary.withValues(
                                    alpha: 0.7,
                                  ),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                    return SliverList(
                      delegate: SliverChildBuilderDelegate((context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(
                            left: 16.0,
                            right: 8.0,
                            bottom: 8.0,
                          ),
                          child: AdvancedRecipeCommentWidget(
                            comment: replies[index],
                            recipeId: recipeId,
                            showReplies: false,
                          ),
                        );
                      }, childCount: replies.length),
                    );
                  },
                ),
              ],
            ),
          ),
          // Add Reply Widget
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: AppColors.surface,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: AddRecipeCommentWidget(
              recipeId: recipeId,
              parentCommentId: parentComment.id,
              replyToUsername: parentComment.username,
              onCommentAdded: () {
                // The stream builder will automatically update the list
              },
            ),
          ),
        ],
      ),
    );
  }
}

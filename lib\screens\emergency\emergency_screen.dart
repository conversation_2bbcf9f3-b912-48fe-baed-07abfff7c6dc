import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:provider/provider.dart';

import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/screens/emergency/nearby_hospitals_screen.dart';

class EmergencyScreen extends StatefulWidget {
  const EmergencyScreen({super.key});

  @override
  State<EmergencyScreen> createState() => _EmergencyScreenState();
}

class _EmergencyScreenState extends State<EmergencyScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  final List<Map<String, dynamic>> _emergencyContacts = [
    {
      'name': 'الطوارئ العامة',
      'number': '997',
      'icon': Icons.local_hospital,
      'color': Colors.red,
      'description': 'خدمة الطوارئ العامة في المملكة العربية السعودية',
      'isAvailable24h': true,
    },
    {
      'name': 'الإسعاف',
      'number': '997',
      'icon': Icons.local_hospital,
      'color': Colors.red.shade700,
      'description': 'خدمة الإسعاف السريع',
      'isAvailable24h': true,
    },
    {
      'name': 'مركز السموم',
      'number': '966112880000',
      'icon': Icons.warning,
      'color': Colors.orange,
      'description': 'مركز معلومات السموم والأدوية',
      'isAvailable24h': true,
    },
    {
      'name': 'الخط الساخن للصحة',
      'number': '937',
      'icon': Icons.phone_in_talk,
      'color': Colors.blue,
      'description': 'الخط الساخن لوزارة الصحة',
      'isAvailable24h': true,
    },
    {
      'name': 'خط مساعدة السيلياك',
      'number': '************',
      'icon': Icons.support_agent,
      'color': Colors.green,
      'description': 'خط مساعدة متخصص لمرضى السيلياك',
      'isAvailable24h': false,
    },
  ];

  final List<Map<String, dynamic>> _celiacFirstAidGuides = [
    {
      'id': 'gluten_exposure',
      'title': 'التعرض للجلوتين',
      'description': 'خطوات التعامل مع التعرض العرضي للجلوتين',
      'symptoms': ['ألم في البطن', 'إسهال', 'غثيان', 'انتفاخ', 'صداع'],
      'steps': [
        'توقف فوراً عن تناول أي طعام قد يحتوي على الجلوتين',
        'اشرب كمية كافية من الماء لمساعدة الجسم على التخلص من السموم',
        'راقب الأعراض وسجلها، واتصل بالطبيب إذا ساءت',
      ],
      'severity': 'moderate',
      'icon': Icons.warning,
      'color': Colors.orange,
    },
    {
      'id': 'severe_reaction',
      'title': 'رد فعل شديد',
      'description': 'التعامل مع رد الفعل الشديد للجلوتين',
      'symptoms': [
        'قيء شديد',
        'إسهال مستمر',
        'ألم شديد',
        'دوخة',
        'صعوبة في التنفس',
      ],
      'steps': [
        'اتصل بـ 997 فوراً - هذه حالة طوارئ طبية',
        'حافظ على الهدوء وساعد المريض على الجلوس أو الاستلقاء',
        'راقب التنفس والنبض حتى وصول المساعدة',
      ],
      'severity': 'critical',
      'icon': Icons.emergency,
      'color': Colors.red,
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'الطوارئ',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.red.shade600,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.local_hospital, color: Colors.white),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const NearbyHospitalsScreen(),
                ),
              );
            },
            tooltip: 'المستشفيات القريبة',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.grey.shade50, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Column(
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Column(
                children: [
                  Icon(Icons.emergency, size: 48, color: Colors.red.shade600),
                  const SizedBox(height: 12),
                  Text(
                    'في حالة الطوارئ',
                    style: GoogleFonts.cairo(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.red.shade800,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'إذا كنت تعاني من أعراض شديدة أو رد فعل تحسسي، اتصل بالطوارئ فوراً',
                    textAlign: TextAlign.center,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.red.shade700,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _emergencyContacts.length,
                itemBuilder: (context, index) {
                  final contact = _emergencyContacts[index];
                  return _buildEmergencyCard(contact);
                },
              ),
            ),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue.shade600, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'معلومات مهمة',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '• احتفظ بقائمة الأدوية التي تتناولها\n'
                    '• أخبر الطبيب عن إصابتك بالسيلياك\n'
                    '• احمل بطاقة طبية تشير لحالتك\n'
                    '• تجنب الأطعمة المشكوك فيها',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.blue.shade700,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: ElevatedButton.icon(
                onPressed: () => _showFirstAidDialog(),
                icon: const Icon(Icons.medical_services, color: Colors.white),
                label: Text(
                  'دليل الإسعافات الأولية للسيلياك',
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade600,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmergencyCard(Map<String, dynamic> contact) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: (contact['color'] as Color).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            contact['icon'] as IconData,
            color: contact['color'] as Color,
            size: 28,
          ),
        ),
        title: Text(
          contact['name'] as String,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade800,
          ),
        ),
        subtitle: Text(
          contact['number'] as String,
          style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade600),
        ),
        trailing: ElevatedButton.icon(
          onPressed: () async {
            await _makePhoneCall(contact['number'] as String);
          },
          icon: const Icon(Icons.phone, size: 18),
          label: Text(
            'اتصال',
            style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: contact['color'] as Color,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);

    try {
      if (await canLaunchUrl(launchUri)) {
        await launchUrl(launchUri);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'لا يمكن إجراء المكالمة. تأكد من أن جهازك يدعم المكالمات.',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء محاولة الاتصال: $e',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showFirstAidDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.medical_services, color: Colors.green.shade600),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'الإسعافات الأولية للسيلياك',
                style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: _celiacFirstAidGuides.length,
            itemBuilder: (context, index) {
              final guide = _celiacFirstAidGuides[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                child: ExpansionTile(
                  leading: Icon(guide['icon'], color: guide['color']),
                  title: Text(
                    guide['title'],
                    style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                  ),
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            guide['description'],
                            style: GoogleFonts.cairo(fontSize: 14),
                          ),
                          const SizedBox(height: 12),
                          ...guide['steps']
                              .map<Widget>(
                                (step) => Padding(
                                  padding: const EdgeInsets.only(bottom: 8),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: 6,
                                        height: 6,
                                        margin: const EdgeInsets.only(
                                          top: 6,
                                          right: 8,
                                        ),
                                        decoration: BoxDecoration(
                                          color: guide['color'],
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          step,
                                          style: GoogleFonts.cairo(
                                            fontSize: 13,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                              .toList(),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }
}

class EmergencyContact {
  final String name;
  final String number;
  final IconData icon;
  final Color color;

  EmergencyContact({
    required this.name,
    required this.number,
    required this.icon,
    required this.color,
  });
}

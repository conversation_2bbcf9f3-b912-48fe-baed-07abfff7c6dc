// lib/widgets/add_post_widget.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../models/forum_post.dart';
import '../providers/forum_provider.dart';
import '../providers/auth_provider.dart' as app_auth;
import '../utils/app_colors.dart';

class AddPostWidget extends StatefulWidget {
  final VoidCallback? onPostAdded;

  const AddPostWidget({super.key, this.onPostAdded});

  @override
  State<AddPostWidget> createState() => _AddPostWidgetState();
}

class _AddPostWidgetState extends State<AddPostWidget>
    with TickerProviderStateMixin {
  final TextEditingController _contentController = TextEditingController();
  final List<File> _selectedImages = [];
  final List<File> _selectedVideos = [];
  final List<String> _tags = [];
  final TextEditingController _tagController = TextEditingController();

  PostType _selectedType = PostType.text;
  PostCategory _selectedCategory = PostCategory.general;
  bool _isSubmitting = false;
  bool _showAdvancedOptions = false;

  final ImagePicker _imagePicker = ImagePicker();
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _contentController.dispose();
    _tagController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _pickImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage();
      if (images.isNotEmpty) {
        setState(() {
          for (final image in images) {
            if (_selectedImages.length < 10) {
              _selectedImages.add(File(image.path));
            }
          }
          if (_selectedImages.isNotEmpty) {
            _selectedType = PostType.image;
          }
        });
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء اختيار الصور');
    }
  }

  Future<void> _pickVideos() async {
    try {
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
      );
      if (video != null) {
        setState(() {
          if (_selectedVideos.length < 3) {
            _selectedVideos.add(File(video.path));
            _selectedType = PostType.video;
          }
        });
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء اختيار الفيديو');
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
      if (_selectedImages.isEmpty && _selectedVideos.isEmpty) {
        _selectedType = PostType.text;
      }
    });
  }

  void _removeVideo(int index) {
    setState(() {
      _selectedVideos.removeAt(index);
      if (_selectedImages.isEmpty && _selectedVideos.isEmpty) {
        _selectedType = PostType.text;
      }
    });
  }

  void _addTag() {
    final tag = _tagController.text.trim();
    if (tag.isNotEmpty && !_tags.contains(tag) && _tags.length < 10) {
      setState(() {
        _tags.add(tag);
        _tagController.clear();
      });
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  Future<void> _submitPost() async {
    if (_contentController.text.trim().isEmpty) {
      _showErrorSnackBar('يرجى كتابة محتوى المنشور');
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final authProvider = Provider.of<app_auth.AuthProvider>(
        context,
        listen: false,
      );
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);

      if (authProvider.currentUser == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      // إضافة المنشور
      await forumProvider.addPost(
        content: _contentController.text.trim(),
        type: _selectedType,
        category: _selectedCategory,
        tags: _tags,
        images: _selectedImages,
        videos: _selectedVideos,
      );

      // مسح الحقول
      _contentController.clear();
      setState(() {
        _selectedImages.clear();
        _selectedVideos.clear();
        _tags.clear();
        _selectedType = PostType.text;
        _selectedCategory = PostCategory.general;
        _showAdvancedOptions = false;
      });

      _showSuccessSnackBar('تم نشر المنشور بنجاح');
      widget.onPostAdded?.call();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء نشر المنشور: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: AppColors.error),
      );
    }
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: AppColors.success),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<app_auth.AuthProvider>(context);

    if (authProvider.currentUser == null) {
      return Container(
        padding: const EdgeInsets.all(20),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.login, size: 48, color: AppColors.textSecondary),
              const SizedBox(height: 16),
              Text(
                'يجب تسجيل الدخول لإضافة منشور',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, 1),
        end: Offset.zero,
      ).animate(_slideAnimation),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, -4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // رأس النافذة
            _buildHeader(),

            const SizedBox(height: 20),

            // حقل المحتوى
            _buildContentField(),

            const SizedBox(height: 16),

            // أزرار الوسائط
            _buildMediaButtons(),

            // عرض الصور المختارة
            if (_selectedImages.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildSelectedImages(),
            ],

            // عرض الفيديوهات المختارة
            if (_selectedVideos.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildSelectedVideos(),
            ],

            const SizedBox(height: 16),

            // خيارات متقدمة
            _buildAdvancedOptions(),

            // العلامات
            if (_tags.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildTagsDisplay(),
            ],

            const SizedBox(height: 20),

            // زر النشر
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        // صورة المستخدم
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: AppColors.border, width: 2),
          ),
          child: ClipOval(
            child: Consumer<app_auth.AuthProvider>(
              builder: (context, authProvider, _) {
                final profileImageUrl =
                    authProvider.userProfile?.profileImageUrl;
                if (profileImageUrl != null && profileImageUrl.isNotEmpty) {
                  return Image.network(
                    profileImageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      child: Text(
                        authProvider.userProfile?.initials ?? 'م',
                        style: GoogleFonts.cairo(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  );
                } else {
                  return Container(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    child: Text(
                      authProvider.userProfile?.initials ?? 'م',
                      style: GoogleFonts.cairo(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  );
                }
              },
            ),
          ),
        ),

        const SizedBox(width: 12),

        // عنوان النافذة
        Expanded(
          child: Text(
            'إنشاء منشور جديد',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ),

        // زر الإغلاق
        IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Icon(Icons.close, color: AppColors.textSecondary),
        ),
      ],
    );
  }

  Widget _buildContentField() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.border),
      ),
      child: TextField(
        controller: _contentController,
        maxLines: 6,
        style: GoogleFonts.cairo(fontSize: 16, color: AppColors.textPrimary),
        decoration: InputDecoration(
          hintText: 'ما الذي تريد مشاركته؟',
          hintStyle: GoogleFonts.cairo(color: AppColors.textHint, fontSize: 16),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
      ),
    );
  }

  Widget _buildMediaButtons() {
    return Row(
      children: [
        // زر الصور
        _buildMediaButton(
          icon: Icons.image_outlined,
          label: 'صور',
          color: AppColors.success,
          onTap: _pickImages,
        ),

        const SizedBox(width: 12),

        // زر الفيديو
        _buildMediaButton(
          icon: Icons.videocam_outlined,
          label: 'فيديو',
          color: AppColors.primary,
          onTap: _pickVideos,
        ),

        const SizedBox(width: 12),

        // زر الاستطلاع
        _buildMediaButton(
          icon: Icons.poll_outlined,
          label: 'استطلاع',
          color: AppColors.warning,
          onTap: () {
            // إضافة استطلاع (مؤقتاً: رسالة تأكيد)
          },
        ),

        const Spacer(),

        // زر الخيارات المتقدمة
        IconButton(
          onPressed: () {
            setState(() {
              _showAdvancedOptions = !_showAdvancedOptions;
            });
          },
          icon: Icon(
            _showAdvancedOptions ? Icons.expand_less : Icons.expand_more,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildMediaButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 18, color: color),
            const SizedBox(width: 6),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedImages() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الصور المختارة (${_selectedImages.length})',
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _selectedImages.length,
            itemBuilder: (context, index) {
              return Container(
                width: 100,
                margin: EdgeInsets.only(
                  left: index < _selectedImages.length - 1 ? 8 : 0,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.border),
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.file(
                        _selectedImages[index],
                        width: 100,
                        height: 100,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Positioned(
                      top: 4,
                      right: 4,
                      child: GestureDetector(
                        onTap: () => _removeImage(index),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: AppColors.error,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.close,
                            size: 14,
                            color: AppColors.textOnPrimary,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSelectedVideos() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الفيديوهات المختارة (${_selectedVideos.length})',
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _selectedVideos.length,
            itemBuilder: (context, index) {
              return Container(
                width: 100,
                margin: EdgeInsets.only(
                  left: index < _selectedVideos.length - 1 ? 8 : 0,
                ),
                decoration: BoxDecoration(
                  color: AppColors.background,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.border),
                ),
                child: Stack(
                  children: [
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.play_circle_filled,
                            size: 32,
                            color: AppColors.primary,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'فيديو',
                            style: GoogleFonts.cairo(
                              fontSize: 10,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Positioned(
                      top: 4,
                      right: 4,
                      child: GestureDetector(
                        onTap: () => _removeVideo(index),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: AppColors.error,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.close,
                            size: 14,
                            color: AppColors.textOnPrimary,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAdvancedOptions() {
    if (!_showAdvancedOptions) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // اختيار الفئة
        Text(
          'الفئة',
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            color: AppColors.background,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.border),
          ),
          child: DropdownButton<PostCategory>(
            value: _selectedCategory,
            isExpanded: true,
            underline: const SizedBox.shrink(),
            style: GoogleFonts.cairo(color: AppColors.textPrimary),
            items: PostCategory.values.map((category) {
              return DropdownMenuItem(
                value: category,
                child: Text(_getCategoryDisplayName(category)),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedCategory = value;
                });
              }
            },
          ),
        ),

        const SizedBox(height: 16),

        // إضافة علامات
        Text(
          'العلامات',
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.background,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.border),
                ),
                child: TextField(
                  controller: _tagController,
                  style: GoogleFonts.cairo(fontSize: 14),
                  decoration: InputDecoration(
                    hintText: 'أضف علامة...',
                    hintStyle: GoogleFonts.cairo(color: AppColors.textHint),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  onSubmitted: (_) => _addTag(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: _addTag,
              icon: Icon(Icons.add, color: AppColors.primary),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTagsDisplay() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'العلامات (${_tags.length})',
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: _tags.map((tag) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '#$tag',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 4),
                  GestureDetector(
                    onTap: () => _removeTag(tag),
                    child: Icon(
                      Icons.close,
                      size: 14,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isSubmitting ? null : _submitPost,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
        ),
        child: _isSubmitting
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: AppColors.textOnPrimary,
                      strokeWidth: 2,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'جاري النشر...',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              )
            : Text(
                'نشر المنشور',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  String _getCategoryDisplayName(PostCategory category) {
    switch (category) {
      case PostCategory.general:
        return 'عام';
      case PostCategory.questions:
        return 'أسئلة';
      case PostCategory.tips:
        return 'نصائح';
      case PostCategory.experiences:
        return 'تجارب';
      case PostCategory.discussions:
        return 'مناقشات';
      case PostCategory.health:
        return 'صحة';
      case PostCategory.nutrition:
        return 'تغذية';
      case PostCategory.recipes:
        return 'وصفات';
      case PostCategory.medications:
        return 'أدوية';
      case PostCategory.emergency:
        return 'طوارئ';
    }
  }
}

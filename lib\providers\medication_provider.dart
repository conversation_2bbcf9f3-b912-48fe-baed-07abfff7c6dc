import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:yassincil/models/medication.dart';
import 'package:yassincil/models/comment.dart';
import 'package:yassincil/services/firestore_service.dart';
import 'package:yassincil/services/storage_service.dart';
import 'package:yassincil/utils/app_constants.dart';

import 'package:yassincil/utils/database_helper.dart';

class MedicationProvider with ChangeNotifier {
  final FirestoreService _firestoreService;
  final StorageService _storageService;
  final DatabaseHelper _dbHelper;

  FirestoreService get firestoreService => _firestoreService;
  List<Medication> _medications = [];
  bool _isLoading = false;
  String? _errorMessage;

  List<Medication> get medications => _medications;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  MedicationProvider(
    this._firestoreService,
    this._storageService,
    this._dbHelper,
  );

  // دالة إضافية لتحديث البيانات
  Future<void> loadMedications() async {
    await fetchMedications();
  }

  Future<void> fetchMedications() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      // Get data from local database first
      final localMedications = await _dbHelper.getMedications();
      if (localMedications.isNotEmpty) {
        _medications = localMedications;
        _isLoading = false;
        notifyListeners();
      }

      // Get data from Firestore
      debugPrint("Fetching medications from Firestore...");
      QuerySnapshot snapshot = await _firestoreService.getCollection(
        AppConstants.medicationsCollection,
        orderBy: 'name',
      );

      debugPrint("Firestore returned ${snapshot.docs.length} medications");

      final firestoreMedications = snapshot.docs
          .map((doc) => Medication.fromFirestore(doc))
          .toList();

      // Update local database
      await _dbHelper.clearMedications();
      for (final med in firestoreMedications) {
        await _dbHelper.insertMedication(med);
      }

      _medications = firestoreMedications;
      _errorMessage = null; // Clear any previous error
      debugPrint("Successfully loaded ${_medications.length} medications");
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء جلب الأدوية: $e";
      debugPrint("Error fetching medications: $e");

      // If Firestore fails, try to use local data
      if (_medications.isEmpty) {
        try {
          final localMedications = await _dbHelper.getMedications();
          if (localMedications.isNotEmpty) {
            _medications = localMedications;
            _errorMessage = "تم تحميل البيانات من التخزين المحلي";
            debugPrint(
              "Loaded ${_medications.length} medications from local database",
            );
          }
        } catch (localError) {
          debugPrint("Error loading from local database: $localError");
        }
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> deleteMedication(String medicationId) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    try {
      await _firestoreService.deleteDocument(
        AppConstants.medicationsCollection,
        medicationId,
      );
      await fetchMedications();
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء حذف الدواء: $e";
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> approveMedication(String medicationId) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    try {
      final user = FirebaseAuth.instance.currentUser;
      await _firestoreService
          .updateDocument(AppConstants.medicationsCollection, medicationId, {
            'isApproved': true,
            'approvalStatus': MedicationApprovalStatus.approved.value,
            'reviewerId': user?.uid,
            'reviewerName': user?.displayName ?? 'مشرف',
            'reviewedAt': FieldValue.serverTimestamp(),
          });
      await fetchMedications();
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء الموافقة على الدواء: $e";
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> rejectMedication(String medicationId, String reason) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    try {
      final user = FirebaseAuth.instance.currentUser;
      await _firestoreService
          .updateDocument(AppConstants.medicationsCollection, medicationId, {
            'isApproved': false,
            'approvalStatus': MedicationApprovalStatus.rejected.value,
            'reviewerComment': reason,
            'reviewerId': user?.uid,
            'reviewerName': user?.displayName ?? 'مشرف',
            'reviewedAt': FieldValue.serverTimestamp(),
          });
      await fetchMedications();
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء رفض الدواء: $e";
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> requestMedicationRevision(
    String medicationId,
    String comment,
  ) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    try {
      final user = FirebaseAuth.instance.currentUser;
      await _firestoreService
          .updateDocument(AppConstants.medicationsCollection, medicationId, {
            'isApproved': false,
            'approvalStatus': MedicationApprovalStatus.needsRevision.value,
            'reviewerComment': comment,
            'reviewerId': user?.uid,
            'reviewerName': user?.displayName ?? 'مشرف',
            'reviewedAt': FieldValue.serverTimestamp(),
          });
      await fetchMedications();
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء طلب التعديل: $e";
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // دالة إضافة دواء باستخدام كائن Medication (للنظام الجديد)
  Future<void> addMedication(
    Medication medication, {
    List<File>? imageFiles,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      List<String> finalImageUrls = List.from(medication.imageUrls);

      // رفع الصور إذا كانت موجودة
      if (imageFiles != null && imageFiles.isNotEmpty) {
        for (var imageFile in imageFiles) {
          final imageUrl = await _storageService.uploadFile(
            imageFile,
            'medication_images/${DateTime.now().millisecondsSinceEpoch}.jpg',
          );
          if (imageUrl != null) {
            finalImageUrls.add(imageUrl);
          }
        }
      }

      // إنشاء الدواء النهائي مع الصور المرفوعة
      final finalMedication = Medication(
        name: medication.name,
        company: medication.company,
        ingredients: medication.ingredients,
        isAllowed: medication.isAllowed,
        notes: medication.notes,
        imageUrls: finalImageUrls,
        category: medication.category,
        calories: medication.calories,
        protein: medication.protein,
        carbohydrates: medication.carbohydrates,
        fat: medication.fat,
        allergens: medication.allergens,
        likes: medication.likes,
        likesCount: medication.likesCount,
        commentsCount: medication.commentsCount,
        ratingsCount: medication.ratingsCount,
        averageRating: medication.averageRating,
        activeIngredient: medication.activeIngredient,
        dosage: medication.dosage,
        sideEffects: medication.sideEffects,
        contraindications: medication.contraindications,
        interactions: medication.interactions,
        alternatives: medication.alternatives,
        prescriptionRequired: medication.prescriptionRequired,
        ageGroup: medication.ageGroup,
        pregnancyCategory: medication.pregnancyCategory,
        storageConditions: medication.storageConditions,
        expiryDate: medication.expiryDate,
        barcode: medication.barcode,
        source: medication.source,
        isApproved: medication.isApproved,
        approvalStatus: medication.approvalStatus,
        reviewerComment: medication.reviewerComment,
        reviewerId: medication.reviewerId,
        reviewerName: medication.reviewerName,
        reviewedAt: medication.reviewedAt,
        isFeatured: medication.isFeatured,
        userId: medication.userId ?? user?.uid,
        username: medication.username ?? user?.displayName,
        createdAt: medication.createdAt,
        updatedAt: medication.updatedAt,
        tags: medication.tags,
      );

      await _firestoreService.addDocument(
        AppConstants.medicationsCollection,
        finalMedication.toMap(),
      );
      await fetchMedications();
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء إضافة الدواء: ${e.toString()}";
      rethrow;
    }
  }

  // دالة إضافة دواء بالطريقة القديمة (للتوافق مع الكود الموجود)
  Future<void> addMedicationLegacy({
    required String name,
    required String company,
    required String ingredients,
    required String notes,
    required bool isAllowed,
    String category = 'مسكنات الألم',
    double calories = 0.0,
    double protein = 0.0,
    double carbohydrates = 0.0,
    double fat = 0.0,
    List<String>? allergens,
    String? activeIngredient,
    String? dosage,
    String? sideEffects,
    String? contraindications,
    String? interactions,
    List<String> alternatives = const [],
    String? prescriptionRequired,
    String? ageGroup,
    String? pregnancyCategory,
    String? storageConditions,
    String? expiryDate,
    String? barcode,
    String? source,
    bool isApproved = true,
    bool isFeatured = false,
    List<String> tags = const [],
    List<File>? imageFiles,
    List<String>? imageUrls,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      List<String> finalImageUrls = imageUrls ?? [];

      if (imageFiles != null) {
        for (var imageFile in imageFiles) {
          final imageUrl = await _storageService.uploadFile(
            imageFile,
            'medication_images/${DateTime.now().millisecondsSinceEpoch}.jpg',
          );
          if (imageUrl != null) {
            finalImageUrls.add(imageUrl);
          }
        }
      }

      final medication = Medication(
        name: name,
        company: company,
        ingredients: ingredients,
        isAllowed: isAllowed,
        notes: notes,
        imageUrls: finalImageUrls,
        category: category,
        calories: calories,
        protein: protein,
        carbohydrates: carbohydrates,
        fat: fat,
        allergens: allergens,
        likes: [],
        likesCount: 0,
        commentsCount: 0,
        ratingsCount: 0,
        averageRating: 0.0,
        activeIngredient: activeIngredient,
        dosage: dosage,
        sideEffects: sideEffects,
        contraindications: contraindications,
        interactions: interactions,
        alternatives: alternatives,
        prescriptionRequired: prescriptionRequired,
        ageGroup: ageGroup,
        pregnancyCategory: pregnancyCategory,
        storageConditions: storageConditions,
        expiryDate: expiryDate,
        barcode: barcode,
        source: source,
        isApproved: isApproved,
        approvalStatus: isApproved
            ? MedicationApprovalStatus.approved
            : MedicationApprovalStatus.pending,
        isFeatured: isFeatured,
        userId: user?.uid,
        username: user?.displayName,
        createdAt: DateTime.now(),
        tags: tags,
      );

      await _firestoreService.addDocument(
        AppConstants.medicationsCollection,
        medication.toMap(),
      );
      await fetchMedications();
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء إضافة الدواء: ${e.toString()}";
      rethrow;
    }
  }

  // دالة تحديث دواء باستخدام كائن Medication (للنظام الجديد)
  Future<void> updateMedication(
    Medication medication, {
    List<File>? imageFiles,
  }) async {
    try {
      if (medication.id == null) {
        throw Exception("معرف الدواء مطلوب للتحديث");
      }

      List<String> finalImageUrls = List.from(medication.imageUrls);

      // رفع الصور الجديدة إذا كانت موجودة
      if (imageFiles != null && imageFiles.isNotEmpty) {
        for (var imageFile in imageFiles) {
          final imageUrl = await _storageService.uploadFile(
            imageFile,
            'medication_images/${DateTime.now().millisecondsSinceEpoch}.jpg',
          );
          if (imageUrl != null) {
            finalImageUrls.add(imageUrl);
          }
        }
      }

      // تحديث الدواء مع كل الحقول الجديدة
      final updatedMedication = Medication(
        id: medication.id,
        name: medication.name,
        company: medication.company,
        ingredients: medication.ingredients,
        isAllowed: medication.isAllowed,
        notes: medication.notes,
        imageUrls: finalImageUrls,
        category: medication.category,
        calories: medication.calories,
        protein: medication.protein,
        carbohydrates: medication.carbohydrates,
        fat: medication.fat,
        allergens: medication.allergens,
        likes: medication.likes,
        likesCount: medication.likesCount,
        commentsCount: medication.commentsCount,
        ratingsCount: medication.ratingsCount,
        averageRating: medication.averageRating,
        activeIngredient: medication.activeIngredient,
        dosage: medication.dosage,
        sideEffects: medication.sideEffects,
        contraindications: medication.contraindications,
        interactions: medication.interactions,
        alternatives: medication.alternatives,
        prescriptionRequired: medication.prescriptionRequired,
        ageGroup: medication.ageGroup,
        pregnancyCategory: medication.pregnancyCategory,
        storageConditions: medication.storageConditions,
        expiryDate: medication.expiryDate,
        barcode: medication.barcode,
        source: medication.source,
        isApproved: medication.isApproved,
        approvalStatus: medication.approvalStatus,
        reviewerComment: medication.reviewerComment,
        reviewerId: medication.reviewerId,
        reviewerName: medication.reviewerName,
        reviewedAt: medication.reviewedAt,
        isFeatured: medication.isFeatured,
        userId: medication.userId,
        username: medication.username,
        createdAt: medication.createdAt,
        updatedAt: DateTime.now(),
        tags: medication.tags,
      );

      await _firestoreService.updateDocument(
        AppConstants.medicationsCollection,
        medication.id!,
        updatedMedication.toMap(),
      );
      await fetchMedications();
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء تحديث الدواء: ${e.toString()}";
      rethrow;
    }
  }

  // دالة تحديث دواء بالطريقة القديمة (للتوافق مع الكود الموجود)
  Future<void> updateMedicationLegacy({
    required String medicationId,
    required String name,
    required String company,
    required String ingredients,
    required String notes,
    required bool isAllowed,
    String? category,
    double? calories,
    double? protein,
    double? carbohydrates,
    double? fat,
    List<String>? allergens,
    String? activeIngredient,
    String? dosage,
    String? sideEffects,
    String? contraindications,
    String? interactions,
    List<String>? alternatives,
    String? prescriptionRequired,
    String? ageGroup,
    String? pregnancyCategory,
    String? storageConditions,
    String? expiryDate,
    String? barcode,
    String? source,
    bool? isApproved,
    bool? isFeatured,
    List<String>? tags,
    List<File>? imageFiles,
    List<String>? imageUrls,
  }) async {
    try {
      List<String> finalImageUrls = imageUrls ?? [];

      if (imageFiles != null) {
        for (var imageFile in imageFiles) {
          final imageUrl = await _storageService.uploadFile(
            imageFile,
            'medication_images/${DateTime.now().millisecondsSinceEpoch}.jpg',
          );
          if (imageUrl != null) {
            finalImageUrls.add(imageUrl);
          }
        }
      }

      final updateData = {
        'name': name,
        'company': company,
        'ingredients': ingredients,
        'notes': notes,
        'isAllowed': isAllowed,
        'category': category,
        'calories': calories,
        'protein': protein,
        'carbohydrates': carbohydrates,
        'fat': fat,
        'allergens': allergens,
        'activeIngredient': activeIngredient,
        'dosage': dosage,
        'sideEffects': sideEffects,
        'contraindications': contraindications,
        'interactions': interactions,
        'alternatives': alternatives,
        'prescriptionRequired': prescriptionRequired,
        'ageGroup': ageGroup,
        'pregnancyCategory': pregnancyCategory,
        'storageConditions': storageConditions,
        'expiryDate': expiryDate,
        'barcode': barcode,
        'source': source,
        'isApproved': isApproved,
        'approvalStatus': isApproved == null
            ? null
            : (isApproved
                  ? MedicationApprovalStatus.approved.value
                  : MedicationApprovalStatus.pending.value),
        'isFeatured': isFeatured,
        'tags': tags,
        'imageUrls': finalImageUrls,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // Remove null values to avoid overwriting existing fields with null
      updateData.removeWhere((key, value) => value == null);

      await _firestoreService.updateDocument(
        AppConstants.medicationsCollection,
        medicationId,
        updateData,
      );
      await fetchMedications();
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء تحديث الدواء: ${e.toString()}";
      rethrow;
    }
  }

  Future<Medication?> searchMedicationByBarcode(String barcode) async {
    try {
      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.medicationsCollection)
          .where('barcode', isEqualTo: barcode)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return Medication.fromFirestore(querySnapshot.docs.first);
      }
      return null;
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء البحث بالباركود: $e";
      debugPrint("Error searching by barcode: $e");
      notifyListeners();
      return null;
    }
  }

  // دوال الإعجابات والتعليقات
  Future<bool> hasLikedMedication(String medicationId, String userId) async {
    try {
      final doc = await _firestoreService.getDocument(
        '${AppConstants.medicationsCollection}/$medicationId/likes',
        userId,
      );
      return doc.exists;
    } catch (e) {
      debugPrint("Error checking like status: $e");
      return false;
    }
  }

  Future<void> toggleLikeOnMedication(
    String medicationId,
    String userId,
  ) async {
    final likeRef = _firestoreService.db
        .collection(AppConstants.medicationsCollection)
        .doc(medicationId)
        .collection('likes')
        .doc(userId);
    final medicationRef = _firestoreService.db
        .collection(AppConstants.medicationsCollection)
        .doc(medicationId);

    await _firestoreService.db.runTransaction((transaction) async {
      final medicationSnapshot = await transaction.get(medicationRef);
      if (!medicationSnapshot.exists) {
        throw Exception("Medication does not exist!");
      }

      int currentLikes =
          (medicationSnapshot.data()?['likesCount'] as int?) ?? 0;
      final likeDoc = await transaction.get(likeRef);

      if (likeDoc.exists) {
        transaction.delete(likeRef);
        transaction.update(medicationRef, {'likesCount': currentLikes - 1});
      } else {
        transaction.set(likeRef, {
          'userId': userId,
          'timestamp': FieldValue.serverTimestamp(),
        });
        transaction.update(medicationRef, {'likesCount': currentLikes + 1});
      }
    });
    await fetchMedications();
  }

  Future<void> addCommentToMedication(
    String medicationId,
    Comment comment,
  ) async {
    final medicationRef = _firestoreService.db
        .collection(AppConstants.medicationsCollection)
        .doc(medicationId);

    await _firestoreService.db.runTransaction((transaction) async {
      final medicationSnapshot = await transaction.get(medicationRef);
      if (!medicationSnapshot.exists) {
        throw Exception("Medication does not exist!");
      }
      int currentComments =
          (medicationSnapshot.data()?['commentsCount'] as int?) ?? 0;

      transaction.set(
        medicationRef.collection(AppConstants.commentsSubcollection).doc(),
        comment.toMap(),
      );
      transaction.update(medicationRef, {'commentsCount': currentComments + 1});
    });
    await fetchMedications();
  }

  // ==================== دوال التعليقات المتقدمة ====================

  /// الحصول على تعليقات دواء معين
  Stream<List<Comment>> getCommentsForMedication(String medicationId) {
    return _firestoreService.db
        .collection(AppConstants.medicationsCollection)
        .doc(medicationId)
        .collection(AppConstants.commentsSubcollection)
        .orderBy('createdAt', descending: false)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Comment.fromFirestore(doc)).toList(),
        );
  }

  /// إضافة رد على تعليق
  Future<void> addReplyToComment({
    required String medicationId,
    required String parentCommentId,
    required Comment reply,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception("يجب تسجيل الدخول أولاً");

      final replyData = reply
          .copyWith(
            userId: user.uid,
            username: user.displayName ?? user.email ?? 'مستخدم',
            parentCommentId: parentCommentId,
            createdAt: DateTime.now(),
          )
          .toMap();

      final medicationRef = _firestoreService.db
          .collection(AppConstants.medicationsCollection)
          .doc(medicationId);

      // إضافة الرد
      await medicationRef
          .collection(AppConstants.commentsSubcollection)
          .add(replyData);

      // تحديث عداد الردود للتعليق الأصلي
      final parentCommentRef = medicationRef
          .collection(AppConstants.commentsSubcollection)
          .doc(parentCommentId);

      await _firestoreService.db.runTransaction((transaction) async {
        final parentDoc = await transaction.get(parentCommentRef);
        if (parentDoc.exists) {
          final currentReplies = parentDoc.data()?['repliesCount'] ?? 0;
          transaction.update(parentCommentRef, {
            'repliesCount': currentReplies + 1,
          });
        }
      });

      // تحديث عداد التعليقات للدواء
      await _firestoreService.db.runTransaction((transaction) async {
        final medicationDoc = await transaction.get(medicationRef);
        if (medicationDoc.exists) {
          final currentComments = medicationDoc.data()?['commentsCount'] ?? 0;
          transaction.update(medicationRef, {
            'commentsCount': currentComments + 1,
          });
        }
      });

      await fetchMedications();
    } catch (e) {
      debugPrint('خطأ في إضافة الرد: $e');
      rethrow;
    }
  }

  /// الحصول على ردود تعليق معين
  Stream<List<Comment>> getRepliesForComment({
    required String medicationId,
    required String parentCommentId,
  }) {
    try {
      return _firestoreService.db
          .collection(AppConstants.medicationsCollection)
          .doc(medicationId)
          .collection(AppConstants.commentsSubcollection)
          .where('parentCommentId', isEqualTo: parentCommentId)
          .snapshots()
          .map((snapshot) {
            final comments = snapshot.docs
                .map((doc) => Comment.fromFirestore(doc))
                .toList();
            // ترتيب الردود محلياً بدلاً من في Firestore
            comments.sort((a, b) => a.createdAt.compareTo(b.createdAt));
            return comments;
          });
    } catch (e) {
      debugPrint("Error getting replies: $e");
      return Stream.value([]);
    }
  }

  /// تعديل تعليق
  Future<void> updateComment({
    required String medicationId,
    required String commentId,
    required String newContent,
    List<String>? newImageUrls,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception("يجب تسجيل الدخول أولاً");

      final commentRef = _firestoreService.db
          .collection(AppConstants.medicationsCollection)
          .doc(medicationId)
          .collection(AppConstants.commentsSubcollection)
          .doc(commentId);

      final updateData = {
        'content': newContent,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
        'isEdited': true,
      };

      if (newImageUrls != null) {
        updateData['imageUrls'] = newImageUrls;
      }

      await commentRef.update(updateData);
    } catch (e) {
      debugPrint('خطأ في تعديل التعليق: $e');
      rethrow;
    }
  }

  /// حذف تعليق
  Future<void> deleteComment({
    required String medicationId,
    required String commentId,
    bool isAdmin = false,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception("يجب تسجيل الدخول أولاً");

      final commentRef = _firestoreService.db
          .collection(AppConstants.medicationsCollection)
          .doc(medicationId)
          .collection(AppConstants.commentsSubcollection)
          .doc(commentId);

      // التحقق من الصلاحية
      if (!isAdmin) {
        final commentDoc = await commentRef.get();
        if (commentDoc.exists) {
          final commentData = commentDoc.data();
          if (commentData?['userId'] != user.uid) {
            throw Exception("لا يمكنك حذف تعليق شخص آخر");
          }
        }
      }

      // حذف التعليق والردود
      await _deleteCommentAndReplies(medicationId, commentId);

      // تحديث عداد التعليقات
      final medicationRef = _firestoreService.db
          .collection(AppConstants.medicationsCollection)
          .doc(medicationId);

      await _firestoreService.db.runTransaction((transaction) async {
        final medicationDoc = await transaction.get(medicationRef);
        if (medicationDoc.exists) {
          final currentComments = medicationDoc.data()?['commentsCount'] ?? 0;
          transaction.update(medicationRef, {
            'commentsCount': (currentComments - 1)
                .clamp(0, double.infinity)
                .toInt(),
          });
        }
      });

      await fetchMedications();
    } catch (e) {
      debugPrint('خطأ في حذف التعليق: $e');
      rethrow;
    }
  }

  /// حذف تعليق وجميع ردوده
  Future<void> _deleteCommentAndReplies(
    String medicationId,
    String commentId,
  ) async {
    final batch = _firestoreService.db.batch();
    final commentsRef = _firestoreService.db
        .collection(AppConstants.medicationsCollection)
        .doc(medicationId)
        .collection(AppConstants.commentsSubcollection);

    // حذف التعليق الأصلي
    batch.delete(commentsRef.doc(commentId));

    // البحث عن الردود وحذفها
    final repliesSnapshot = await commentsRef
        .where('parentCommentId', isEqualTo: commentId)
        .get();

    for (final replyDoc in repliesSnapshot.docs) {
      batch.delete(replyDoc.reference);
    }

    await batch.commit();
  }

  // ==================== دوال الإعجابات للتعليقات ====================

  /// الإعجاب بتعليق أو إلغاء الإعجاب
  Future<void> toggleCommentLike({
    required String medicationId,
    required String commentId,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception("يجب تسجيل الدخول أولاً");

      final commentRef = _firestoreService.db
          .collection(AppConstants.medicationsCollection)
          .doc(medicationId)
          .collection(AppConstants.commentsSubcollection)
          .doc(commentId);

      final likesRef = commentRef.collection('likes').doc(user.uid);

      await _firestoreService.db.runTransaction((transaction) async {
        final likeDoc = await transaction.get(likesRef);
        final commentDoc = await transaction.get(commentRef);

        if (!commentDoc.exists) return;

        final currentLikes = commentDoc.data()?['likesCount'] ?? 0;

        if (likeDoc.exists) {
          // إلغاء الإعجاب
          transaction.delete(likesRef);
          transaction.update(commentRef, {
            'likesCount': (currentLikes - 1).clamp(0, double.infinity).toInt(),
          });
        } else {
          // إضافة إعجاب
          transaction.set(likesRef, {
            'userId': user.uid,
            'createdAt': Timestamp.fromDate(DateTime.now()),
          });
          transaction.update(commentRef, {'likesCount': currentLikes + 1});
        }
      });
    } catch (e) {
      debugPrint('خطأ في تبديل الإعجاب: $e');
      rethrow;
    }
  }

  /// فحص ما إذا كان المستخدم معجب بالتعليق
  Future<bool> hasLikedComment({
    required String medicationId,
    required String commentId,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return false;

      final likeDoc = await _firestoreService.db
          .collection(AppConstants.medicationsCollection)
          .doc(medicationId)
          .collection(AppConstants.commentsSubcollection)
          .doc(commentId)
          .collection('likes')
          .doc(user.uid)
          .get();

      return likeDoc.exists;
    } catch (e) {
      debugPrint('خطأ في فحص الإعجاب: $e');
      return false;
    }
  }
}

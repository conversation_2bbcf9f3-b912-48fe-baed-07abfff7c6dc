class Product {
  final String id;
  final String name;
  final bool isGlutenFree;
  final String ingredients;
  final String notes;

  Product({
    required this.id,
    required this.name,
    required this.isGlutenFree,
    required this.ingredients,
    required this.notes,
  });

  factory Product.fromMap(String id, Map<String, dynamic> data) {
    return Product(
      id: id,
      name: data['name'] ?? '',
      isGlutenFree: data['isGlutenFree'] ?? false,
      ingredients: data['ingredients'] ?? '',
      notes: data['notes'] ?? '',
    );
  }
}

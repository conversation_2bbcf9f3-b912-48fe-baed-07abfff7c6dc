import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../models/doctor_appointment.dart';
import '../services/firestore_service.dart';
import '../utils/app_constants.dart';

class DoctorAppointmentProvider extends ChangeNotifier {
  final FirestoreService _firestoreService;
  
  List<DoctorAppointment> _appointments = [];
  Map<String, List<DoctorAppointment>> _doctorAppointments = {};
  Map<String, List<DoctorAppointment>> _patientAppointments = {};
  bool _isLoading = false;
  String? _errorMessage;

  DoctorAppointmentProvider(this._firestoreService);

  // Getters
  List<DoctorAppointment> get appointments => _appointments;
  Map<String, List<DoctorAppointment>> get doctorAppointments => _doctorAppointments;
  Map<String, List<DoctorAppointment>> get patientAppointments => _patientAppointments;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Get appointments for specific doctor
  List<DoctorAppointment> getAppointmentsForDoctor(String doctorId) {
    return _doctorAppointments[doctorId] ?? [];
  }

  // Get appointments for specific patient
  List<DoctorAppointment> getAppointmentsForPatient(String patientId) {
    return _patientAppointments[patientId] ?? [];
  }

  // Get upcoming appointments for doctor
  List<DoctorAppointment> getUpcomingAppointments(String doctorId) {
    final doctorAppts = getAppointmentsForDoctor(doctorId);
    return doctorAppts.where((apt) => apt.isUpcoming).toList();
  }

  // Get available time slots for doctor on specific date
  List<String> getAvailableTimeSlots(String doctorId, DateTime date) {
    // Default time slots (can be customized per doctor)
    final allSlots = [
      '9:00 ص - 9:30 ص',
      '9:30 ص - 10:00 ص',
      '10:00 ص - 10:30 ص',
      '10:30 ص - 11:00 ص',
      '11:00 ص - 11:30 ص',
      '11:30 ص - 12:00 م',
      '2:00 م - 2:30 م',
      '2:30 م - 3:00 م',
      '3:00 م - 3:30 م',
      '3:30 م - 4:00 م',
      '4:00 م - 4:30 م',
      '4:30 م - 5:00 م',
    ];

    // Get booked slots for this date
    final doctorAppts = getAppointmentsForDoctor(doctorId);
    final bookedSlots = doctorAppts
        .where((apt) => 
            apt.appointmentDate.year == date.year &&
            apt.appointmentDate.month == date.month &&
            apt.appointmentDate.day == date.day &&
            (apt.status == AppointmentStatus.confirmed || 
             apt.status == AppointmentStatus.pending))
        .map((apt) => apt.timeSlot)
        .toList();

    // Return available slots
    return allSlots.where((slot) => !bookedSlots.contains(slot)).toList();
  }

  // Load appointments for specific doctor
  Future<void> loadDoctorAppointments(String doctorId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.doctorAppointmentsCollection)
          .where('doctorId', isEqualTo: doctorId)
          .orderBy('appointmentDate', descending: false)
          .get();

      final appointments = querySnapshot.docs
          .map((doc) => DoctorAppointment.fromFirestore(doc))
          .toList();

      _doctorAppointments[doctorId] = appointments;
    } catch (e) {
      _errorMessage = 'خطأ في تحميل المواعيد: $e';
      debugPrint('Error loading doctor appointments: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load appointments for specific patient
  Future<void> loadPatientAppointments(String patientId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.doctorAppointmentsCollection)
          .where('patientId', isEqualTo: patientId)
          .orderBy('appointmentDate', descending: true)
          .get();

      final appointments = querySnapshot.docs
          .map((doc) => DoctorAppointment.fromFirestore(doc))
          .toList();

      _patientAppointments[patientId] = appointments;
    } catch (e) {
      _errorMessage = 'خطأ في تحميل المواعيد: $e';
      debugPrint('Error loading patient appointments: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load all appointments (admin only)
  Future<void> loadAllAppointments() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.doctorAppointmentsCollection)
          .orderBy('appointmentDate', descending: true)
          .get();

      _appointments = querySnapshot.docs
          .map((doc) => DoctorAppointment.fromFirestore(doc))
          .toList();

      // Group appointments by doctor and patient
      _doctorAppointments.clear();
      _patientAppointments.clear();
      
      for (final appointment in _appointments) {
        // Group by doctor
        if (!_doctorAppointments.containsKey(appointment.doctorId)) {
          _doctorAppointments[appointment.doctorId] = [];
        }
        _doctorAppointments[appointment.doctorId]!.add(appointment);

        // Group by patient
        if (!_patientAppointments.containsKey(appointment.patientId)) {
          _patientAppointments[appointment.patientId] = [];
        }
        _patientAppointments[appointment.patientId]!.add(appointment);
      }
    } catch (e) {
      _errorMessage = 'خطأ في تحميل المواعيد: $e';
      debugPrint('Error loading all appointments: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Book new appointment
  Future<void> bookAppointment({
    required String doctorId,
    required String doctorName,
    required String patientId,
    required String patientName,
    String? patientPhone,
    String? patientEmail,
    required DateTime appointmentDate,
    required String timeSlot,
    AppointmentType type = AppointmentType.consultation,
    String? reason,
    String? patientNotes,
    double? fee,
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Check if slot is still available
      final availableSlots = getAvailableTimeSlots(doctorId, appointmentDate);
      if (!availableSlots.contains(timeSlot)) {
        throw Exception('الموعد المحدد غير متاح');
      }

      final appointment = DoctorAppointment(
        doctorId: doctorId,
        doctorName: doctorName,
        patientId: patientId,
        patientName: patientName,
        patientPhone: patientPhone,
        patientEmail: patientEmail,
        appointmentDate: appointmentDate,
        timeSlot: timeSlot,
        type: type,
        reason: reason,
        patientNotes: patientNotes,
        fee: fee,
        createdAt: DateTime.now(),
      );

      await _firestoreService.addDocument(
        AppConstants.doctorAppointmentsCollection,
        appointment.toMap(),
      );

      // Reload appointments
      await loadDoctorAppointments(doctorId);
      await loadPatientAppointments(patientId);
    } catch (e) {
      _errorMessage = 'خطأ في حجز الموعد: $e';
      debugPrint('Error booking appointment: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update appointment
  Future<void> updateAppointment(String appointmentId, DoctorAppointment updatedAppointment) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await _firestoreService.updateDocument(
        AppConstants.doctorAppointmentsCollection,
        appointmentId,
        updatedAppointment.copyWith(updatedAt: DateTime.now()).toMap(),
      );

      // Reload appointments
      await loadDoctorAppointments(updatedAppointment.doctorId);
      await loadPatientAppointments(updatedAppointment.patientId);
    } catch (e) {
      _errorMessage = 'خطأ في تحديث الموعد: $e';
      debugPrint('Error updating appointment: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Cancel appointment
  Future<void> cancelAppointment(String appointmentId, String cancelReason, String cancelledBy) async {
    try {
      final appointment = _appointments.firstWhere((a) => a.id == appointmentId);
      
      final updatedAppointment = appointment.copyWith(
        status: AppointmentStatus.cancelled,
        cancelReason: cancelReason,
        cancelledAt: DateTime.now(),
        cancelledBy: cancelledBy,
        updatedAt: DateTime.now(),
      );

      await updateAppointment(appointmentId, updatedAppointment);
    } catch (e) {
      _errorMessage = 'خطأ في إلغاء الموعد: $e';
      debugPrint('Error cancelling appointment: $e');
    }
  }

  // Confirm appointment
  Future<void> confirmAppointment(String appointmentId) async {
    try {
      final appointment = _appointments.firstWhere((a) => a.id == appointmentId);
      
      final updatedAppointment = appointment.copyWith(
        status: AppointmentStatus.confirmed,
        updatedAt: DateTime.now(),
      );

      await updateAppointment(appointmentId, updatedAppointment);
    } catch (e) {
      _errorMessage = 'خطأ في تأكيد الموعد: $e';
      debugPrint('Error confirming appointment: $e');
    }
  }

  // Complete appointment
  Future<void> completeAppointment(String appointmentId, String? notes) async {
    try {
      final appointment = _appointments.firstWhere((a) => a.id == appointmentId);
      
      final updatedAppointment = appointment.copyWith(
        status: AppointmentStatus.completed,
        notes: notes,
        updatedAt: DateTime.now(),
      );

      await updateAppointment(appointmentId, updatedAppointment);
    } catch (e) {
      _errorMessage = 'خطأ في إكمال الموعد: $e';
      debugPrint('Error completing appointment: $e');
    }
  }

  // Get appointment statistics
  Map<String, dynamic> getAppointmentStatistics() {
    final totalAppointments = _appointments.length;
    final confirmedAppointments = _appointments.where((a) => a.status == AppointmentStatus.confirmed).length;
    final completedAppointments = _appointments.where((a) => a.status == AppointmentStatus.completed).length;
    final cancelledAppointments = _appointments.where((a) => a.status == AppointmentStatus.cancelled).length;

    final statusDistribution = <String, int>{};
    for (final status in AppointmentStatus.values) {
      statusDistribution[status.name] = _appointments.where((a) => a.status == status).length;
    }

    return {
      'totalAppointments': totalAppointments,
      'confirmedAppointments': confirmedAppointments,
      'completedAppointments': completedAppointments,
      'cancelledAppointments': cancelledAppointments,
      'statusDistribution': statusDistribution,
    };
  }

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Reset provider
  void reset() {
    _appointments = [];
    _doctorAppointments = {};
    _patientAppointments = {};
    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }
}

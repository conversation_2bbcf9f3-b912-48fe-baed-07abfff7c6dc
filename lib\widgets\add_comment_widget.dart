// lib/widgets/add_comment_widget.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';

import '../models/comment.dart';
import '../providers/food_provider.dart';
import '../providers/auth_provider.dart';
import '../utils/app_colors.dart';

class AddCommentWidget extends StatefulWidget {
  final String foodItemId;
  final String? parentCommentId;
  final String? replyToUsername;
  final VoidCallback? onCommentAdded;

  const AddCommentWidget({
    super.key,
    required this.foodItemId,
    this.parentCommentId,
    this.replyToUsername,
    this.onCommentAdded,
  });

  @override
  State<AddCommentWidget> createState() => _AddCommentWidgetState();
}

class _AddCommentWidgetState extends State<AddCommentWidget> {
  final TextEditingController _commentController = TextEditingController();
  final List<String> _selectedImages = [];
  bool _isLoading = false;

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);

    if (authProvider.currentUser == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text(
            'يجب تسجيل الدخول لإضافة التعليقات',
            style: TextStyle(color: Colors.grey),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الرد إذا كان رد على تعليق
          if (widget.replyToUsername != null)
            Container(
              padding: const EdgeInsets.all(8),
              margin: const EdgeInsets.only(bottom: 8),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.reply, size: 16, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Text(
                    'رد على ${widget.replyToUsername}',
                    style: const TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),

          // حقل النص
          Row(
            children: [
              // صورة المستخدم
              CircleAvatar(
                radius: 20,
                backgroundImage: authProvider.currentUser?.photoURL != null
                    ? NetworkImage(authProvider.currentUser!.photoURL!)
                    : null,
                child: authProvider.currentUser?.photoURL == null
                    ? Text(
                        authProvider.currentUser?.displayName?.isNotEmpty ==
                                true
                            ? authProvider.currentUser!.displayName![0]
                                  .toUpperCase()
                            : 'م',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )
                    : null,
              ),

              const SizedBox(width: 12),

              // حقل الإدخال
              Expanded(
                child: TextField(
                  controller: _commentController,
                  maxLines: null,
                  decoration: InputDecoration(
                    hintText: widget.parentCommentId != null
                        ? 'اكتب ردك...'
                        : 'اكتب تعليقك...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade100,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    suffixIcon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // زر رفع الصور
                        IconButton(
                          onPressed: _pickImage,
                          icon: const Icon(
                            Icons.image,
                            color: AppColors.primary,
                          ),
                        ),

                        // زر الإرسال
                        IconButton(
                          onPressed: _isLoading ? null : _addComment,
                          icon: _isLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Icon(
                                  Icons.send,
                                  color: AppColors.primary,
                                ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          // عرض الصور المختارة
          if (_selectedImages.isNotEmpty)
            Container(
              margin: const EdgeInsets.only(top: 12),
              height: 80,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.only(left: 8),
                    width: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                        image: NetworkImage(_selectedImages[index]),
                        fit: BoxFit.cover,
                      ),
                    ),
                    child: Stack(
                      children: [
                        Positioned(
                          top: 4,
                          left: 4,
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedImages.removeAt(index);
                              });
                            },
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.close,
                                size: 16,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _pickImage() async {
    try {
      final foodProvider = Provider.of<FoodProvider>(context, listen: false);
      final imageUrl = await foodProvider.uploadCommentImage(context);

      if (imageUrl != null) {
        setState(() {
          _selectedImages.add(imageUrl);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في رفع الصورة: $e')));
      }
    }
  }

  Future<void> _addComment() async {
    if (_commentController.text.trim().isEmpty && _selectedImages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى كتابة تعليق أو إضافة صورة')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final foodProvider = Provider.of<FoodProvider>(context, listen: false);

      final comment = Comment(
        postId: widget.foodItemId,
        content: _commentController.text.trim(),
        userId: authProvider.currentUser!.uid,
        username:
            authProvider.userProfile?.displayName ??
            authProvider.userProfile?.username ??
            'مستخدم',
        userAvatar: authProvider.userProfile?.profileImageUrl,
        createdAt: DateTime.now(),
        imageUrls: _selectedImages,
        parentCommentId: widget.parentCommentId,
      );

      if (widget.parentCommentId != null) {
        // إضافة رد
        await foodProvider.addReplyToComment(
          foodItemId: widget.foodItemId,
          parentCommentId: widget.parentCommentId!,
          reply: comment,
        );
      } else {
        // إضافة تعليق جديد
        await foodProvider.addCommentToFoodItem(widget.foodItemId, comment);
      }

      // مسح الحقول
      _commentController.clear();
      setState(() {
        _selectedImages.clear();
      });

      // إشعار بالنجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.parentCommentId != null
                  ? 'تم إضافة الرد بنجاح'
                  : 'تم إضافة التعليق بنجاح',
            ),
          ),
        );

        // استدعاء callback إذا كان موجود
        widget.onCommentAdded?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.parentCommentId != null
                  ? 'خطأ في إضافة الرد: $e'
                  : 'خطأ في إضافة التعليق: $e',
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

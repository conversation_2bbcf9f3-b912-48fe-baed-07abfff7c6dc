import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../providers/medication_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/medication.dart';
import '../../utils/app_colors.dart';
import '../../utils/error_handler.dart';
import '../../widgets/loading_widget.dart' hide EmptyStateWidget;
import '../../widgets/empty_state_widget.dart';
import '../medications/add_edit_medication_screen_enhanced.dart';
import '../medications/medication_detail_screen.dart';
import 'medication_migration_screen.dart';

class MedicationsManagementScreen extends StatefulWidget {
  const MedicationsManagementScreen({super.key});

  @override
  State<MedicationsManagementScreen> createState() =>
      _MedicationsManagementScreenState();
}

class _MedicationsManagementScreenState
    extends State<MedicationsManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  String _selectedFilter = 'الكل';
  String _selectedCategory = 'الكل';
  bool _isLoading = false;

  final List<String> _filters = [
    'الكل',
    'الأحدث',
    'الأقدم',
    'الأعلى تقييماً',
    'الأقل تقييماً',
    'الأكثر تفاعلاً',
    'مسموح',
    'غير مسموح',
    'يحتاج وصفة',
    'بدون وصفة',
  ];

  final List<String> _categoryFilters = [
    'الكل',
    'مسكنات الألم',
    'مضادات الالتهاب',
    'مضادات الحموضة',
    'فيتامينات ومعادن',
    'مضادات الحساسية',
    'أدوية الجهاز الهضمي',
    'أدوية القلب والأوعية',
    'مضادات حيوية',
    'أدوية الجهاز التنفسي',
    'أدوية الجلد',
    'أدوية العيون',
    'أدوية الأذن',
    'مكملات غذائية',
    'أدوية الأطفال',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadMedications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadMedications() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final medicationProvider = Provider.of<MedicationProvider>(
        context,
        listen: false,
      );
      await medicationProvider.fetchMedications();
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(context, e);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildTabBar(),
          Expanded(
            child: _isLoading
                ? const LoadingWidget(message: 'جاري تحميل الأدوية...')
                : _buildTabBarView(),
          ),
        ],
      ),
      floatingActionButton: _buildAddMedicationFAB(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'إدارة الأدوية',
        style: GoogleFonts.cairo(fontWeight: FontWeight.bold, fontSize: 20),
      ),
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadMedications,
          tooltip: 'تحديث',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'statistics':
                _showStatistics();
                break;
              case 'export':
                _exportMedications();
                break;
              case 'bulk_actions':
                _showBulkActionsDialog();
                break;
              case 'categories':
                _manageCategoriesDialog();
                break;
              case 'suggestions':
                _showSuggestionsScreen();
                break;
              case 'interactions':
                _showInteractionsScreen();
                break;
              case 'migration':
                _showMigrationScreen();
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'statistics',
              child: Row(
                children: [
                  const Icon(Icons.analytics, size: 20),
                  const SizedBox(width: 8),
                  Text('الإحصائيات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'suggestions',
              child: Row(
                children: [
                  const Icon(Icons.lightbulb, size: 20),
                  const SizedBox(width: 8),
                  Text('اقتراحات المستخدمين', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'interactions',
              child: Row(
                children: [
                  const Icon(Icons.warning, size: 20),
                  const SizedBox(width: 8),
                  Text('التفاعلات الدوائية', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  const Icon(Icons.download, size: 20),
                  const SizedBox(width: 8),
                  Text('تصدير البيانات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'bulk_actions',
              child: Row(
                children: [
                  const Icon(Icons.checklist, size: 20),
                  const SizedBox(width: 8),
                  Text('إجراءات جماعية', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'categories',
              child: Row(
                children: [
                  const Icon(Icons.category, size: 20),
                  const SizedBox(width: 8),
                  Text('إدارة الفئات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'migration',
              child: Row(
                children: [
                  const Icon(Icons.sync_alt, size: 20),
                  const SizedBox(width: 8),
                  Text('ترحيل البيانات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          Container(
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث في الأدوية...',
                hintStyle: GoogleFonts.cairo(color: Colors.grey.shade600),
                prefixIcon: Icon(Icons.search, color: Colors.grey.shade600),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(Icons.clear, color: Colors.grey.shade600),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {});
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              style: GoogleFonts.cairo(),
              onChanged: (value) {
                setState(() {});
              },
            ),
          ),
          const SizedBox(height: 12),
          // فلاتر
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: _selectedFilter,
                      isExpanded: true,
                      icon: Icon(
                        Icons.arrow_drop_down,
                        color: AppColors.primary,
                      ),
                      style: GoogleFonts.cairo(color: AppColors.textPrimary),
                      onChanged: (value) {
                        setState(() {
                          _selectedFilter = value!;
                        });
                      },
                      items: _filters.map((filter) {
                        return DropdownMenuItem(
                          value: filter,
                          child: Text(filter),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: _selectedCategory,
                      isExpanded: true,
                      icon: Icon(
                        Icons.arrow_drop_down,
                        color: AppColors.primary,
                      ),
                      style: GoogleFonts.cairo(color: AppColors.textPrimary),
                      onChanged: (value) {
                        setState(() {
                          _selectedCategory = value!;
                        });
                      },
                      items: _categoryFilters.map((category) {
                        return DropdownMenuItem(
                          value: category,
                          child: Text(category),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: Colors.grey.shade600,
        labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        unselectedLabelStyle: GoogleFonts.cairo(fontWeight: FontWeight.normal),
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        isScrollable: true,
        tabs: const [
          Tab(text: 'جميع الأدوية'),
          Tab(text: 'في انتظار المراجعة'),
          Tab(text: 'المميزة'),
          Tab(text: 'المرفوضة'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildMedicationsList('all'),
        _buildMedicationsList('pending'),
        _buildMedicationsList('featured'),
        _buildMedicationsList('rejected'),
      ],
    );
  }

  Widget _buildMedicationsList(String type) {
    return Consumer<MedicationProvider>(
      builder: (context, medicationProvider, child) {
        final medications = _getFilteredMedications(
          medicationProvider.medications,
          type,
        );

        if (medications.isEmpty) {
          return EmptyStateWidget(
            icon: Icons.medication,
            title: 'لا توجد أدوية',
            subtitle: _getEmptyMessage(type),
            action: ElevatedButton.icon(
              onPressed: _loadMedications,
              icon: const Icon(Icons.refresh),
              label: Text('إعادة التحميل', style: GoogleFonts.cairo()),
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _loadMedications,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: medications.length,
            itemBuilder: (context, index) {
              final medication = medications[index];
              return _buildMedicationManagementCard(medication);
            },
          ),
        );
      },
    );
  }

  List<Medication> _getFilteredMedications(
    List<Medication> medications,
    String type,
  ) {
    var filtered = medications.where((medication) {
      // فلترة حسب البحث
      if (_searchController.text.isNotEmpty) {
        final query = _searchController.text.toLowerCase();
        if (!medication.name.toLowerCase().contains(query) &&
            !medication.company.toLowerCase().contains(query) &&
            !medication.ingredients.toLowerCase().contains(query) &&
            !medication.notes.toLowerCase().contains(query)) {
          return false;
        }
      }

      // فلترة حسب الفئة
      if (_selectedCategory != 'الكل') {
        if (medication.category != _selectedCategory) {
          return false;
        }
      }

      // فلترة حسب النوع
      switch (type) {
        case 'pending':
          return !medication.isApproved;
        case 'featured':
          return medication.isFeatured;
        case 'rejected':
          return !medication.isApproved; // الأدوية غير المعتمدة (مؤقتاً)
        default:
          return medication.isApproved;
      }
    }).toList();

    // ترتيب حسب الفلتر المختار
    switch (_selectedFilter) {
      case 'الأحدث':
        filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'الأقدم':
        filtered.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case 'الأعلى تقييماً':
        filtered.sort((a, b) => b.averageRating.compareTo(a.averageRating));
        break;
      case 'الأقل تقييماً':
        filtered.sort((a, b) => a.averageRating.compareTo(b.averageRating));
        break;
      case 'الأكثر تفاعلاً':
        filtered.sort(
          (a, b) => (b.likesCount + b.commentsCount).compareTo(
            a.likesCount + a.commentsCount,
          ),
        );
        break;
      case 'مسموح':
        filtered = filtered
            .where((medication) => medication.isAllowed)
            .toList();
        break;
      case 'غير مسموح':
        filtered = filtered
            .where((medication) => !medication.isAllowed)
            .toList();
        break;
      case 'يحتاج وصفة':
        filtered = filtered
            .where((medication) => medication.prescriptionRequired == 'نعم')
            .toList();
        break;
      case 'بدون وصفة':
        filtered = filtered
            .where((medication) => medication.prescriptionRequired == 'لا')
            .toList();
        break;
    }

    return filtered;
  }

  String _getEmptyMessage(String type) {
    switch (type) {
      case 'pending':
        return 'لا توجد أدوية في انتظار المراجعة';
      case 'featured':
        return 'لا توجد أدوية مميزة';
      case 'rejected':
        return 'لا توجد أدوية مرفوضة';
      default:
        return 'لم يتم العثور على أدوية تطابق البحث';
    }
  }

  Widget _buildMedicationManagementCard(Medication medication) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة الدواء مع شارة الحالة
          Stack(
            children: [
              _buildMedicationImage(medication),
              Positioned(
                top: 12,
                right: 12,
                child: _buildStatusBadge(medication),
              ),
              if (medication.isFeatured)
                Positioned(
                  top: 12,
                  left: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.star, size: 16, color: Colors.white),
                        const SizedBox(width: 4),
                        Text(
                          'مميز',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              if (!medication.isAllowed)
                Positioned(
                  bottom: 12,
                  right: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.warning,
                          size: 16,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'غير مسموح',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
          // معلومات الدواء
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // اسم الدواء والتقييم
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        medication.name,
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                    _buildRatingWidget(medication.averageRating),
                  ],
                ),
                const SizedBox(height: 8),
                // الشركة والفئة
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        medication.company,
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                    _buildCategoryChip(medication.category),
                  ],
                ),
                const SizedBox(height: 8),
                // المكونات
                Text(
                  'المكونات: ${medication.ingredients}',
                  style: GoogleFonts.cairo(fontSize: 14),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),
                // إحصائيات سريعة
                Row(
                  children: [
                    _buildStatItem(
                      Icons.favorite,
                      '${medication.likesCount}',
                      'إعجاب',
                    ),
                    const SizedBox(width: 16),
                    _buildStatItem(
                      Icons.comment,
                      '${medication.commentsCount}',
                      'تعليق',
                    ),
                    const SizedBox(width: 16),
                    _buildStatItem(
                      Icons.star,
                      '${medication.ratingsCount}',
                      'تقييم',
                    ),
                    const Spacer(),
                    if (medication.prescriptionRequired == 'نعم')
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.orange.shade200),
                        ),
                        child: Text(
                          'يحتاج وصفة',
                          style: GoogleFonts.cairo(
                            fontSize: 10,
                            color: Colors.orange.shade700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                // أزرار الإدارة
                Row(
                  children: [
                    Expanded(
                      child: _buildActionButton(
                        icon: Icons.visibility,
                        label: 'عرض',
                        color: Colors.blue,
                        onTap: () => _viewMedication(medication),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildActionButton(
                        icon: Icons.edit,
                        label: 'تعديل',
                        color: Colors.orange,
                        onTap: () => _editMedication(medication),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildActionButton(
                        icon: Icons.delete,
                        label: 'حذف',
                        color: Colors.red,
                        onTap: () => _deleteMedication(medication),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // الدوال المساعدة
  Widget _buildMedicationImage(Medication medication) {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        color: Colors.grey.shade200,
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        child: medication.imageUrls.isNotEmpty
            ? CachedNetworkImage(
                imageUrl: medication.imageUrls.first,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey.shade200,
                  child: const Center(child: CircularProgressIndicator()),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey.shade200,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.medication,
                        size: 48,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'لا توجد صورة',
                        style: GoogleFonts.cairo(color: Colors.grey.shade600),
                      ),
                    ],
                  ),
                ),
              )
            : Container(
                color: Colors.grey.shade200,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.medication,
                      size: 48,
                      color: Colors.grey.shade400,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لا توجد صورة',
                      style: GoogleFonts.cairo(color: Colors.grey.shade600),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildStatusBadge(Medication medication) {
    Color color;
    String text;

    if (!medication.isApproved) {
      color = Colors.orange;
      text = 'في انتظار المراجعة';
    } else if (!medication.isAllowed) {
      color = Colors.red;
      text = 'غير مسموح';
    } else {
      color = Colors.green;
      text = 'معتمد';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: GoogleFonts.cairo(
          fontSize: 10,
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildRatingWidget(double rating) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.amber.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.amber.shade200),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.star, size: 16, color: Colors.amber.shade600),
          const SizedBox(width: 4),
          Text(
            rating.toStringAsFixed(1),
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.amber.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(String category) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Text(
        category,
        style: GoogleFonts.cairo(
          fontSize: 10,
          color: Colors.blue.shade700,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String value, String label) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: Colors.grey.shade600),
        const SizedBox(width: 4),
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(width: 2),
        Text(
          label,
          style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddMedicationFAB() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: FloatingActionButton.extended(
        onPressed: _showAddMedicationDialog,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        icon: const Icon(Icons.add),
        label: Text(
          'إضافة دواء',
          style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  // دوال الإجراءات
  void _viewMedication(Medication medication) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MedicationDetailScreen(medication: medication),
      ),
    );
  }

  void _editMedication(Medication medication) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            EnhancedAddEditMedicationScreen(medication: medication),
      ),
    );
  }

  void _deleteMedication(Medication medication) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف الدواء', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف دواء "${medication.name}"؟ لا يمكن التراجع عن هذا الإجراء.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _performDeleteMedication(medication);
            },
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _performDeleteMedication(Medication medication) async {
    try {
      final medicationProvider = Provider.of<MedicationProvider>(
        context,
        listen: false,
      );
      await medicationProvider.deleteMedication(medication.id!);
      if (mounted) {
        ErrorHandler.showSuccessSnackBar(context, 'تم حذف الدواء بنجاح');
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(context, 'فشل في حذف الدواء: $e');
      }
    }
  }

  void _showAddMedicationDialog() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const EnhancedAddEditMedicationScreen(),
      ),
    );
  }

  void _showStatistics() {
    // عرض إحصائيات الأدوية
    final medicationProvider = Provider.of<MedicationProvider>(
      context,
      listen: false,
    );
    final medications = medicationProvider.medications;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إحصائيات الأدوية',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatisticRow('إجمالي الأدوية', '${medications.length}'),
            _buildStatisticRow(
              'الأدوية المعتمدة',
              '${medications.where((m) => m.isApproved).length}',
            ),
            _buildStatisticRow(
              'في انتظار المراجعة',
              '${medications.where((m) => !m.isApproved).length}',
            ),
            _buildStatisticRow(
              'الأدوية المميزة',
              '${medications.where((m) => m.isFeatured).length}',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: GoogleFonts.cairo()),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  void _exportMedications() {
    // تصدير الأدوية
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تصدير الأدوية بنجاح', style: GoogleFonts.cairo()),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showBulkActionsDialog() {
    // إجراءات جماعية
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم تطبيق الإجراءات الجماعية',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _manageCategoriesDialog() {
    // إدارة الفئات
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم فتح إدارة الفئات', style: GoogleFonts.cairo()),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showSuggestionsScreen() {
    // عرض اقتراحات المستخدمين
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم فتح اقتراحات المستخدمين', style: GoogleFonts.cairo()),
        backgroundColor: Colors.purple,
      ),
    );
  }

  void _showInteractionsScreen() {
    // عرض التفاعلات الدوائية
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'التفاعلات الدوائية',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: 5,
            itemBuilder: (context, index) => ListTile(
              leading: Icon(Icons.warning, color: Colors.orange),
              title: Text(
                'تفاعل دوائي ${index + 1}',
                style: GoogleFonts.cairo(),
              ),
              subtitle: Text(
                'تفاعل مع دواء آخر - مستوى خطورة متوسط',
                style: GoogleFonts.cairo(),
              ),
              trailing: Icon(Icons.info_outline, color: Colors.blue),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _showMigrationScreen() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const MedicationMigrationScreen(),
      ),
    );
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:yassincil/models/medication.dart';
import 'package:yassincil/utils/app_constants.dart';

/// سكريبت لتحديث الأدوية الموجودة لإضافة حالة الموافقة
/// يجب تشغيل هذا السكريبت مرة واحدة فقط بعد تطبيق التحديث
class UpdateExistingMedications {
  static Future<void> updateAllMedications() async {
    try {
      final firestore = FirebaseFirestore.instance;
      final collection = firestore.collection(
        AppConstants.medicationsCollection,
      );

      // جلب جميع الأدوية
      final snapshot = await collection.get();

      print('بدء تحديث ${snapshot.docs.length} دواء...');

      int updatedCount = 0;
      int skippedCount = 0;

      for (final doc in snapshot.docs) {
        final data = doc.data();

        // التحقق من وجود حقل approvalStatus
        if (data['approvalStatus'] == null) {
          // تحديد حالة الموافقة بناءً على isApproved
          final isApproved = data['isApproved'] ?? true;
          final approvalStatus = isApproved
              ? MedicationApprovalStatus.approved.value
              : MedicationApprovalStatus.pending.value;

          // تحديث الوثيقة
          await doc.reference.update({
            'approvalStatus': approvalStatus,
            'updatedAt': FieldValue.serverTimestamp(),
          });

          updatedCount++;
          print('تم تحديث الدواء: ${data['name']} - الحالة: $approvalStatus');
        } else {
          skippedCount++;
          print(
            'تم تخطي الدواء: ${data['name']} - يحتوي على approvalStatus بالفعل',
          );
        }
      }

      print('تم الانتهاء من التحديث:');
      print('- تم تحديث: $updatedCount دواء');
      print('- تم تخطي: $skippedCount دواء');
    } catch (e) {
      print('حدث خطأ أثناء تحديث الأدوية: $e');
      rethrow;
    }
  }

  /// تحديث دواء واحد فقط (للاختبار)
  static Future<void> updateSingleMedication(String medicationId) async {
    try {
      final firestore = FirebaseFirestore.instance;
      final doc = await firestore
          .collection(AppConstants.medicationsCollection)
          .doc(medicationId)
          .get();

      if (!doc.exists) {
        print('الدواء غير موجود: $medicationId');
        return;
      }

      final data = doc.data()!;

      if (data['approvalStatus'] == null) {
        final isApproved = data['isApproved'] ?? true;
        final approvalStatus = isApproved
            ? MedicationApprovalStatus.approved.value
            : MedicationApprovalStatus.pending.value;

        await doc.reference.update({
          'approvalStatus': approvalStatus,
          'updatedAt': FieldValue.serverTimestamp(),
        });

        print('تم تحديث الدواء: ${data['name']} - الحالة: $approvalStatus');
      } else {
        print('الدواء يحتوي على approvalStatus بالفعل: ${data['name']}');
      }
    } catch (e) {
      print('حدث خطأ أثناء تحديث الدواء: $e');
      rethrow;
    }
  }

  /// إعادة تعيين جميع الأدوية لحالة "في انتظار المراجعة" (للاختبار فقط)
  static Future<void> resetAllMedicationsToPending() async {
    try {
      final firestore = FirebaseFirestore.instance;
      final collection = firestore.collection(
        AppConstants.medicationsCollection,
      );

      final snapshot = await collection.get();

      print(
        'بدء إعادة تعيين ${snapshot.docs.length} دواء إلى حالة "في انتظار المراجعة"...',
      );

      for (final doc in snapshot.docs) {
        await doc.reference.update({
          'isApproved': false,
          'approvalStatus': MedicationApprovalStatus.pending.value,
          'reviewerComment': null,
          'reviewerId': null,
          'reviewerName': null,
          'reviewedAt': null,
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      print('تم إعادة تعيين جميع الأدوية بنجاح');
    } catch (e) {
      print('حدث خطأ أثناء إعادة التعيين: $e');
      rethrow;
    }
  }
}

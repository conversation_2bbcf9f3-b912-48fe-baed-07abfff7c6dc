import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:yassincil/providers/slider_provider.dart';
import 'package:yassincil/models/slider_item.dart';
import 'package:yassincil/screens/admin/add_edit_slider_item_screen.dart';

// تم حذف أي سطر return خارج الكلاس
class ManageSliderItemsScreen extends StatefulWidget {
  const ManageSliderItemsScreen({super.key});

  @override
  State<ManageSliderItemsScreen> createState() =>
      _ManageSliderItemsScreenState();
}

class _ManageSliderItemsScreenState extends State<ManageSliderItemsScreen> {
  final String _searchText = '';
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _imageUrlController = TextEditingController();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _targetScreenController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    _imageUrlController.dispose();
    _titleController.dispose();
    _targetScreenController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => SliderProvider()..fetchSliderItems(),
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F6FA),
        appBar: AppBar(
          title: const Text(
            'إدارة السلايدر',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontWeight: FontWeight.bold,
              fontSize: 22,
              color: Colors.white,
            ),
          ),
          backgroundColor: const Color(0xFF512DA8),
          elevation: 6,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(bottom: Radius.circular(18)),
          ),
          actions: [
            Tooltip(
              message: 'إضافة عنصر جديد',
              child: IconButton(
                icon: const Icon(
                  Icons.add_circle,
                  color: Colors.white,
                  size: 28,
                ),
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18),
                      ),
                      title: const Text(
                        'إضافة عنصر جديد للسلايدر',
                        style: TextStyle(fontFamily: 'Cairo'),
                      ),
                      content: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 400),
                              curve: Curves.easeInOut,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.deepPurple.withValues(
                                      alpha: 0.12,
                                    ),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: _imageUrlController.text.trim().isNotEmpty
                                  ? Image.network(
                                      _imageUrlController.text.trim(),
                                      height: 120,
                                      width: 300,
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) =>
                                              Container(
                                                height: 120,
                                                width: 300,
                                                color: Colors.grey[200],
                                                child: const Icon(
                                                  Icons.broken_image,
                                                  size: 40,
                                                  color: Colors.deepPurple,
                                                ),
                                              ),
                                    )
                                  : Container(
                                      height: 120,
                                      width: 300,
                                      color: Colors.grey[200],
                                      child: Icon(
                                        Icons.image,
                                        size: 40,
                                        color: Colors.deepPurple[200],
                                      ),
                                    ),
                            ),
                            const SizedBox(height: 14),
                            TextFormField(
                              controller: _imageUrlController,
                              style: const TextStyle(fontFamily: 'Cairo'),
                              decoration: InputDecoration(
                                labelText: 'رابط صورة السلايدر',
                                labelStyle: const TextStyle(
                                  fontFamily: 'Cairo',
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: const Icon(
                                  Icons.link,
                                  color: Color(0xFF512DA8),
                                ),
                                filled: true,
                                fillColor: Colors.grey[100],
                              ),
                              onChanged: (value) {
                                (context as Element).markNeedsBuild();
                              },
                            ),
                            const SizedBox(height: 12),
                            TextFormField(
                              controller: _titleController,
                              style: const TextStyle(fontFamily: 'Cairo'),
                              decoration: InputDecoration(
                                labelText: 'عنوان السلايدر',
                                labelStyle: const TextStyle(
                                  fontFamily: 'Cairo',
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: const Icon(
                                  Icons.title,
                                  color: Color(0xFF512DA8),
                                ),
                                filled: true,
                                fillColor: Colors.grey[100],
                              ),
                            ),
                            const SizedBox(height: 12),
                            TextFormField(
                              controller: _targetScreenController,
                              style: const TextStyle(fontFamily: 'Cairo'),
                              decoration: InputDecoration(
                                labelText: 'القسم المستهدف (مثال: home)',
                                labelStyle: const TextStyle(
                                  fontFamily: 'Cairo',
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: const Icon(
                                  Icons.dashboard,
                                  color: Color(0xFF512DA8),
                                ),
                                filled: true,
                                fillColor: Colors.grey[100],
                              ),
                            ),
                          ],
                        ),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text(
                            'إلغاء',
                            style: TextStyle(fontFamily: 'Cairo'),
                          ),
                        ),
                        ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF512DA8),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          icon: const Icon(Icons.save, color: Colors.white),
                          label: const Text(
                            'حفظ',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              color: Colors.white,
                            ),
                          ),
                          onPressed: () async {
                            final imageUrl = _imageUrlController.text.trim();
                            final title = _titleController.text.trim();
                            final targetScreen =
                                _targetScreenController.text.trim().isEmpty
                                ? 'home'
                                : _targetScreenController.text.trim();
                            if (imageUrl.isEmpty || title.isEmpty) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'يرجى إدخال جميع الحقول المطلوبة',
                                    style: TextStyle(fontFamily: 'Cairo'),
                                  ),
                                ),
                              );
                              return;
                            }
                            final newItem = SliderItem(
                              imageUrl: imageUrl,
                              title: title,
                              targetScreen: targetScreen,
                              createdAt: Timestamp.fromDate(DateTime.now()),
                              order: 0,
                              isActive: true,
                            );
                            final provider = Provider.of<SliderProvider>(
                              context,
                              listen: false,
                            );
                            await provider.addSliderItem(newItem);
                            _imageUrlController.clear();
                            _titleController.clear();
                            _targetScreenController.clear();
                            if (context.mounted) {
                              Navigator.of(context).pop();
                            }
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'تمت إضافة العنصر بنجاح',
                                    style: TextStyle(fontFamily: 'Cairo'),
                                  ),
                                ),
                              );
                            }
                          },
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        body: Consumer<SliderProvider>(
          builder: (context, provider, _) {
            final filteredItems = provider.sliderItems.where((item) {
              final search = _searchText.trim().toLowerCase();
              if (search.isEmpty) return true;
              return (item.title.toLowerCase().contains(search) ||
                  item.targetScreen.toLowerCase().contains(search));
            }).toList();
            return Column(
              children: [
                // ...existing code...
                Expanded(
                  child: provider.sliderItems.isEmpty
                      ? const Center(
                          child: Text(
                            'لا توجد نتائج مطابقة.',
                            style: TextStyle(fontFamily: 'Cairo'),
                          ),
                        )
                      : ReorderableListView(
                          padding: const EdgeInsets.all(16),
                          onReorder: (oldIndex, newIndex) async {
                            if (newIndex > oldIndex) newIndex -= 1;
                            final items = List.of(filteredItems);
                            final item = items.removeAt(oldIndex);
                            items.insert(newIndex, item);
                            await provider.reorderAll(items);
                            if (oldIndex != newIndex && context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'تم تحديث ترتيب السلايدر بنجاح',
                                    style: TextStyle(fontFamily: 'Cairo'),
                                  ),
                                ),
                              );
                            }
                          },
                          children: [
                            for (
                              int index = 0;
                              index < filteredItems.length;
                              index++
                            )
                              Card(
                                key: ValueKey(filteredItems[index].id),
                                elevation: 4,
                                margin: const EdgeInsets.symmetric(vertical: 8),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: ListTile(
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                  leading: ClipRRect(
                                    borderRadius: BorderRadius.circular(12),
                                    child: Image.network(
                                      filteredItems[index].imageUrl,
                                      width: 60,
                                      height: 60,
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) =>
                                              const Icon(
                                                Icons.broken_image,
                                                color: Colors.deepPurple,
                                              ),
                                    ),
                                  ),
                                  title: Text(
                                    filteredItems[index].title,
                                    style: const TextStyle(
                                      fontFamily: 'Cairo',
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  subtitle: Row(
                                    children: [
                                      Text(
                                        filteredItems[index].targetScreen,
                                        style: const TextStyle(
                                          fontFamily: 'Cairo',
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      if (!filteredItems[index].isActive)
                                        const Chip(
                                          label: Text(
                                            'معطل',
                                            style: TextStyle(
                                              fontFamily: 'Cairo',
                                              color: Colors.white,
                                            ),
                                          ),
                                          backgroundColor: Colors.red,
                                        )
                                      else ...[
                                        if (filteredItems[index].startDate !=
                                                null &&
                                            DateTime.now().isBefore(
                                              filteredItems[index].startDate!
                                                  .toDate(),
                                            ))
                                          const Chip(
                                            label: Text(
                                              'خارج الجدول الزمني (لم يبدأ بعد)',
                                              style: TextStyle(
                                                fontFamily: 'Cairo',
                                                color: Colors.white,
                                              ),
                                            ),
                                            backgroundColor: Colors.orange,
                                          )
                                        else if (filteredItems[index].endDate !=
                                                null &&
                                            DateTime.now().isAfter(
                                              filteredItems[index].endDate!
                                                  .toDate(),
                                            ))
                                          const Chip(
                                            label: Text(
                                              'خارج الجدول الزمني (انتهى)',
                                              style: TextStyle(
                                                fontFamily: 'Cairo',
                                                color: Colors.white,
                                              ),
                                            ),
                                            backgroundColor: Colors.grey,
                                          )
                                        else
                                          const Chip(
                                            label: Text(
                                              'مفعل',
                                              style: TextStyle(
                                                fontFamily: 'Cairo',
                                                color: Colors.white,
                                              ),
                                            ),
                                            backgroundColor: Colors.green,
                                          ),
                                      ],
                                    ],
                                  ),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Tooltip(
                                        message: filteredItems[index].isActive
                                            ? 'تعطيل العنصر'
                                            : 'تفعيل العنصر',
                                        child: IconButton(
                                          icon: Icon(
                                            filteredItems[index].isActive
                                                ? Icons.visibility
                                                : Icons.visibility_off,
                                            color: filteredItems[index].isActive
                                                ? Colors.green
                                                : Colors.grey,
                                          ),
                                          onPressed: () async {
                                            await provider.setSliderActive(
                                              filteredItems[index].id!,
                                              !filteredItems[index].isActive,
                                            );
                                            if (context.mounted) {
                                              ScaffoldMessenger.of(
                                                context,
                                              ).showSnackBar(
                                                SnackBar(
                                                  content: Text(
                                                    filteredItems[index]
                                                            .isActive
                                                        ? 'تم تفعيل العنصر.'
                                                        : 'تم تعطيل العنصر.',
                                                    style: const TextStyle(
                                                      fontFamily: 'Cairo',
                                                    ),
                                                  ),
                                                ),
                                              );
                                            }
                                          },
                                        ),
                                      ),
                                      Tooltip(
                                        message: 'تعديل',
                                        child: IconButton(
                                          icon: const Icon(
                                            Icons.edit,
                                            color: Colors.blue,
                                          ),
                                          onPressed: () {
                                            Navigator.of(context).push(
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    AddEditSliderItemScreen(
                                                      sliderItem:
                                                          filteredItems[index],
                                                    ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                      Tooltip(
                                        message: 'حذف',
                                        child: IconButton(
                                          icon: const Icon(
                                            Icons.delete,
                                            color: Colors.red,
                                          ),
                                          onPressed: () async {
                                            final confirm = await showDialog<bool>(
                                              context: context,
                                              builder: (context) => AlertDialog(
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(18),
                                                ),
                                                title: const Text(
                                                  'تأكيد الحذف',
                                                  style: TextStyle(
                                                    fontFamily: 'Cairo',
                                                  ),
                                                ),
                                                content: const Text(
                                                  'هل أنت متأكد من حذف هذا العنصر؟',
                                                  style: TextStyle(
                                                    fontFamily: 'Cairo',
                                                  ),
                                                ),
                                                actions: [
                                                  TextButton(
                                                    onPressed: () =>
                                                        Navigator.of(
                                                          context,
                                                        ).pop(false),
                                                    child: const Text(
                                                      'إلغاء',
                                                      style: TextStyle(
                                                        fontFamily: 'Cairo',
                                                      ),
                                                    ),
                                                  ),
                                                  TextButton(
                                                    onPressed: () =>
                                                        Navigator.of(
                                                          context,
                                                        ).pop(true),
                                                    child: const Text(
                                                      'حذف',
                                                      style: TextStyle(
                                                        fontFamily: 'Cairo',
                                                        color: Colors.red,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            );
                                            if (confirm == true) {
                                              await provider.deleteSliderItem(
                                                filteredItems[index].id!,
                                              );
                                              if (context.mounted) {
                                                ScaffoldMessenger.of(
                                                  context,
                                                ).showSnackBar(
                                                  const SnackBar(
                                                    content: Text(
                                                      'تم حذف العنصر بنجاح',
                                                      style: TextStyle(
                                                        fontFamily: 'Cairo',
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              }
                                            }
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                          ],
                        ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

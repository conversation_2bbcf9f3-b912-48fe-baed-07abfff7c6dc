import 'package:cloud_firestore/cloud_firestore.dart';
import 'sample_safe_stores.dart';

class AddSampleSafeStores {
  static Future<void> addSampleStores() async {
    try {
      final firestore = FirebaseFirestore.instance;
      final sampleStores = SampleSafeStores.getSampleStores();

      // Check if stores already exist
      final existingStores = await firestore.collection('safe_stores').get();

      if (existingStores.docs.isNotEmpty) {
        print('Sample stores already exist. Skipping...');
        return;
      }

      // Add sample stores
      for (final store in sampleStores) {
        await firestore.collection('safe_stores').add(store.toMap());
        print('Added store: ${store.name}');
      }

      print('Successfully added ${sampleStores.length} sample stores!');
    } catch (e) {
      print('Error adding sample stores: $e');
    }
  }

  static Future<void> clearAllStores() async {
    try {
      final firestore = FirebaseFirestore.instance;
      final stores = await firestore.collection('safe_stores').get();

      for (final doc in stores.docs) {
        await doc.reference.delete();
      }

      print('Cleared all stores successfully!');
    } catch (e) {
      print('Error clearing stores: $e');
    }
  }
}

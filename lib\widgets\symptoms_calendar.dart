import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:yassincil/models/symptom_entry.dart';
import 'package:yassincil/utils/app_colors.dart';

class SymptomsCalendar extends StatefulWidget {
  final List<SymptomEntry> symptomEntries;
  final Function(DateTime) onDateSelected;
  final DateTime? selectedDate;

  const SymptomsCalendar({
    super.key,
    required this.symptomEntries,
    required this.onDateSelected,
    this.selectedDate,
  });

  @override
  State<SymptomsCalendar> createState() => _SymptomsCalendarState();
}

class _SymptomsCalendarState extends State<SymptomsCalendar> {
  late DateTime _focusedDay;
  DateTime? _selectedDay;
  CalendarFormat _calendarFormat = CalendarFormat.month;

  @override
  void initState() {
    super.initState();
    _focusedDay = DateTime.now();
    _selectedDay = widget.selectedDate ?? DateTime.now();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildCalendarHeader(),
            const SizedBox(height: 16),
            _buildCalendar(),
            const SizedBox(height: 16),
            _buildLegend(),
          ],
        ),
      ),
    );
  }

  Widget _buildCalendarHeader() {
    return Row(
      children: [
        Icon(
          Icons.calendar_today,
          color: Colors.purple.shade600,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'تقويم الأعراض',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        PopupMenuButton<CalendarFormat>(
          onSelected: (format) {
            setState(() {
              _calendarFormat = format;
            });
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: CalendarFormat.month,
              child: Text('شهري', style: GoogleFonts.cairo()),
            ),
            PopupMenuItem(
              value: CalendarFormat.twoWeeks,
              child: Text('أسبوعين', style: GoogleFonts.cairo()),
            ),
            PopupMenuItem(
              value: CalendarFormat.week,
              child: Text('أسبوعي', style: GoogleFonts.cairo()),
            ),
          ],
          child: Icon(
            Icons.view_module,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildCalendar() {
    return TableCalendar<SymptomEntry>(
      firstDay: DateTime.utc(2020, 1, 1),
      lastDay: DateTime.utc(2030, 12, 31),
      focusedDay: _focusedDay,
      calendarFormat: _calendarFormat,
      eventLoader: _getEventsForDay,
      startingDayOfWeek: StartingDayOfWeek.saturday,
      selectedDayPredicate: (day) {
        return isSameDay(_selectedDay, day);
      },
      onDaySelected: (selectedDay, focusedDay) {
        if (!isSameDay(_selectedDay, selectedDay)) {
          setState(() {
            _selectedDay = selectedDay;
            _focusedDay = focusedDay;
          });
          widget.onDateSelected(selectedDay);
        }
      },
      onFormatChanged: (format) {
        if (_calendarFormat != format) {
          setState(() {
            _calendarFormat = format;
          });
        }
      },
      onPageChanged: (focusedDay) {
        _focusedDay = focusedDay;
      },
      calendarStyle: CalendarStyle(
        outsideDaysVisible: false,
        weekendTextStyle: GoogleFonts.cairo(color: Colors.red.shade600),
        holidayTextStyle: GoogleFonts.cairo(color: Colors.red.shade600),
        defaultTextStyle: GoogleFonts.cairo(),
        selectedTextStyle: GoogleFonts.cairo(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
        todayTextStyle: GoogleFonts.cairo(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
        selectedDecoration: BoxDecoration(
          color: Colors.purple.shade600,
          shape: BoxShape.circle,
        ),
        todayDecoration: BoxDecoration(
          color: Colors.purple.shade400,
          shape: BoxShape.circle,
        ),
        markerDecoration: BoxDecoration(
          color: Colors.red.shade600,
          shape: BoxShape.circle,
        ),
        markersMaxCount: 3,
        canMarkersOverflow: true,
      ),
      headerStyle: HeaderStyle(
        formatButtonVisible: false,
        titleCentered: true,
        titleTextStyle: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
        leftChevronIcon: Icon(
          Icons.chevron_left,
          color: Colors.grey.shade600,
        ),
        rightChevronIcon: Icon(
          Icons.chevron_right,
          color: Colors.grey.shade600,
        ),
      ),
      daysOfWeekStyle: DaysOfWeekStyle(
        weekdayStyle: GoogleFonts.cairo(
          color: AppColors.textSecondary,
          fontWeight: FontWeight.w600,
        ),
        weekendStyle: GoogleFonts.cairo(
          color: Colors.red.shade600,
          fontWeight: FontWeight.w600,
        ),
      ),
      calendarBuilders: CalendarBuilders(
        markerBuilder: (context, day, events) {
          if (events.isNotEmpty) {
            return _buildDayMarker(day, events);
          }
          return null;
        },
      ),
    );
  }

  Widget _buildDayMarker(DateTime day, List<SymptomEntry> events) {
    if (events.isEmpty) return const SizedBox.shrink();

    // حساب متوسط الشدة لليوم
    final averageSeverity = events.map((e) => e.severity).reduce((a, b) => a + b) / events.length;
    
    Color markerColor;
    if (averageSeverity <= 2) {
      markerColor = Colors.green;
    } else if (averageSeverity <= 3) {
      markerColor = Colors.orange;
    } else {
      markerColor = Colors.red;
    }

    return Positioned(
      bottom: 1,
      child: Container(
        width: 6,
        height: 6,
        decoration: BoxDecoration(
          color: markerColor,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  Widget _buildLegend() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مفتاح الألوان:',
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 16.0,
            runSpacing: 8.0,
            children: [
              _buildLegendItem('أعراض خفيفة', Colors.green),
              _buildLegendItem('أعراض متوسطة', Colors.orange),
              _buildLegendItem('أعراض شديدة', Colors.red),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  List<SymptomEntry> _getEventsForDay(DateTime day) {
    return widget.symptomEntries.where((entry) {
      return isSameDay(entry.timestamp, day);
    }).toList();
  }
}

class SelectedDateEntries extends StatelessWidget {
  final DateTime selectedDate;
  final List<SymptomEntry> entries;
  final Function(SymptomEntry) onEntryTap;

  const SelectedDateEntries({
    super.key,
    required this.selectedDate,
    required this.entries,
    required this.onEntryTap,
  });

  @override
  Widget build(BuildContext context) {
    final dayEntries = entries.where((entry) {
      return isSameDay(entry.timestamp, selectedDate);
    }).toList();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.event_note,
                  color: Colors.purple.shade600,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'أعراض ${DateFormat('dd MMMM yyyy', 'ar').format(selectedDate)}',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (dayEntries.isEmpty)
              _buildEmptyState()
            else
              ...dayEntries.map((entry) => _buildEntryCard(entry)),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(32),
      alignment: Alignment.center,
      child: Column(
        children: [
          Icon(
            Icons.sentiment_satisfied,
            size: 48,
            color: Colors.green.shade400,
          ),
          const SizedBox(height: 12),
          Text(
            'لا توجد أعراض في هذا اليوم',
            style: GoogleFonts.cairo(
              color: Colors.green.shade600,
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          Text(
            'يوم رائع!',
            style: GoogleFonts.cairo(
              color: AppColors.textSecondary,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEntryCard(SymptomEntry entry) {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => onEntryTap(entry),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: entry.severityColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      entry.symptomName,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${entry.severityText} • ${DateFormat('HH:mm').format(entry.timestamp)}',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey.shade400,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

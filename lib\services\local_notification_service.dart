import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;

class LocalNotificationService {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static bool _isInitialized = false;

  /// تهيئة خدمة الإشعارات المحلية
  static Future<void> initialize() async {
    if (_isInitialized) return;

    // تهيئة المناطق الزمنية
    tz.initializeTimeZones();

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        );

    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _isInitialized = true;
  }

  /// معالجة النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('تم النقر على الإشعار: ${response.payload}');
    // يمكن إضافة منطق التنقل هنا
  }

  /// طلب صلاحيات الإشعارات
  static Future<bool> requestPermissions() async {
    final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
        _notificationsPlugin
            .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin
            >();

    if (androidImplementation != null) {
      final bool? granted = await androidImplementation
          .requestNotificationsPermission();
      return granted ?? false;
    }

    // iOS implementation would go here
    // For now, return true for iOS

    return false;
  }

  /// عرض إشعار فوري
  static Future<void> showInstantNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
          'instant_notifications',
          'الإشعارات الفورية',
          channelDescription: 'إشعارات فورية من التطبيق',
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
        );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _notificationsPlugin.show(
      id,
      title,
      body,
      platformChannelSpecifics,
      payload: payload,
    );
  }

  /// جدولة إشعار
  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
          'scheduled_notifications',
          'الإشعارات المجدولة',
          channelDescription: 'إشعارات مجدولة للتذكيرات',
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
        );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _notificationsPlugin.zonedSchedule(
      id,
      title,
      body,
      tz.TZDateTime.from(scheduledDate, tz.local),
      platformChannelSpecifics,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      payload: payload,
    );
  }

  /// جدولة إشعار يومي
  static Future<void> scheduleDailyNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
          'daily_notifications',
          'التذكيرات اليومية',
          channelDescription: 'تذكيرات يومية لتتبع الأعراض',
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
        );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _notificationsPlugin.periodicallyShow(
      id,
      title,
      body,
      RepeatInterval.daily,
      platformChannelSpecifics,
      payload: payload,
    );
  }

  /// إلغاء إشعار محدد
  static Future<void> cancelNotification(int id) async {
    await _notificationsPlugin.cancel(id);
  }

  /// إلغاء جميع الإشعارات
  static Future<void> cancelAllNotifications() async {
    await _notificationsPlugin.cancelAll();
  }

  /// الحصول على الإشعارات المعلقة
  static Future<List<PendingNotificationRequest>>
  getPendingNotifications() async {
    return await _notificationsPlugin.pendingNotificationRequests();
  }

  /// إشعارات خاصة بتتبع الأعراض
  static Future<void> scheduleSymptomReminder({
    required String time, // "HH:mm"
    bool enabled = true,
  }) async {
    const int symptomReminderId = 1001;

    if (!enabled) {
      await cancelNotification(symptomReminderId);
      return;
    }

    // تحليل الوقت للاستخدام المستقبلي
    debugPrint('تم تعيين تذكير في الوقت: $time');

    await scheduleDailyNotification(
      id: symptomReminderId,
      title: 'تذكير تتبع الأعراض',
      body: 'لا تنس تسجيل أعراضك اليوم للحصول على تحليل أفضل',
      payload: 'symptom_reminder',
    );
  }

  /// إشعار عند اكتشاف منتج جديد
  static Future<void> showNewProductNotification(String productName) async {
    await showInstantNotification(
      id: 2001,
      title: 'منتج جديد مكتشف!',
      body: 'تم العثور على معلومات عن: $productName',
      payload: 'new_product',
    );
  }

  /// إشعار عند إضافة مطعم جديد
  static Future<void> showNewRestaurantNotification(
    String restaurantName,
  ) async {
    await showInstantNotification(
      id: 2002,
      title: 'مطعم جديد متاح!',
      body: 'تم إضافة مطعم جديد: $restaurantName',
      payload: 'new_restaurant',
    );
  }

  /// إشعار عند رد جديد في المنتدى
  static Future<void> showForumReplyNotification(String postTitle) async {
    await showInstantNotification(
      id: 2003,
      title: 'رد جديد في المنتدى',
      body: 'تم الرد على منشورك: $postTitle',
      payload: 'forum_reply',
    );
  }

  /// تحديث شارة التطبيق (iOS)
  static Future<void> updateBadgeCount(int count) async {
    // iOS badge update would go here
    debugPrint('Badge count updated to: $count');
  }
}

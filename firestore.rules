rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // ===== دوال مساعدة =====
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // ===== قواعد المستخدمين =====
    match /users/{userId} {
      allow read, write: if isOwner(userId) || isAdmin();
      allow create: if isOwner(userId);
      
      // الإعدادات الشخصية
      match /preferences/{document} {
        allow read, write: if isOwner(userId);
      }

      // الإشعارات
      match /notifications/{document} {
        allow read, write: if isOwner(userId);
      }

      // المفضلة الخاصة بالمستخدم
      match /favorites/{favoriteId} {
        allow read, write: if isOwner(userId);
      }
    }

    // ===== قواعد الأدوية - قراءة مفتوحة للجميع =====
    match /medications/{medicationId} {
      allow read: if true;  // قراءة مفتوحة
      
      // المشرفون يمكنهم التعديل والحذف
      allow update, delete: if isAdmin();
      
      // المستخدمون المسجلون يمكنهم إضافة أدوية للمراجعة فقط
      allow create: if isAuthenticated() && 
                     request.resource.data.approvalStatus == 'pending';
      
      // المشرفون يمكنهم إنشاء أدوية مع أي حالة موافقة
      allow create: if isAdmin();
      
      // التعليقات على الأدوية
      match /comments/{commentId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() && 
                                 (request.auth.uid == resource.data.userId || isAdmin());

        // الإعجابات على التعليقات
        match /likes/{userId} {
          allow read: if true;
          allow write: if isOwner(userId);
        }
        
        // الردود على التعليقات
        match /replies/{replyId} {
          allow read: if true;
          allow create: if isAuthenticated();
          allow update, delete: if isAuthenticated() &&
            (request.auth.uid == resource.data.userId || isAdmin());
        }
      }

      // التقييمات على الأدوية
      match /ratings/{userId} {
        allow read: if true;
        allow write: if isOwner(userId);
      }

      // المفضلة للأدوية
      match /favorites/{userId} {
        allow read, write: if isOwner(userId);
      }
    }

    // ===== قواعد الأطعمة - قراءة مفتوحة =====
    match /foodItems/{foodId} {
      allow read: if true;
      allow write: if isAdmin();

      // التعليقات على الأطعمة
      match /comments/{commentId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() &&
          (request.auth.uid == resource.data.userId || isAdmin());
        
        // الإعجابات على التعليقات
        match /likes/{userId} {
          allow read: if true;
          allow write: if isOwner(userId);
        }

        // الردود على التعليقات
        match /replies/{replyId} {
          allow read: if true;
          allow create: if isAuthenticated();
          allow update, delete: if isAuthenticated() &&
            (request.auth.uid == resource.data.userId || isAdmin());
        }
      }

      // التقييمات على الأطعمة
      match /ratings/{userId} {
        allow read: if true;
        allow write: if isOwner(userId);
      }

      // المفضلة للأطعمة
      match /favorites/{userId} {
        allow read, write: if isOwner(userId);
      }
    }

    // ===== قواعد المطاعم - قراءة مفتوحة =====
    match /restaurants/{restaurantId} {
      allow read: if true;
      allow write: if isAdmin();

      // التعليقات على المطاعم
      match /comments/{commentId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() &&
          (request.auth.uid == resource.data.userId || isAdmin());
        
        // الإعجابات على التعليقات
        match /likes/{userId} {
          allow read: if true;
          allow write: if isOwner(userId);
        }
      }

      // التقييمات على المطاعم
      match /reviews/{reviewId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() &&
          (request.auth.uid == resource.data.userId || isAdmin());
      }
    }

    // ===== قواعد المقالات - قراءة مفتوحة =====
    match /articles/{articleId} {
      allow read: if true;
      allow write: if isAdmin();

      // التعليقات على المقالات
      match /comments/{commentId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() &&
          (request.auth.uid == resource.data.userId || isAdmin());

        // الإعجابات على التعليقات
        match /likes/{userId} {
          allow read: if true;
          allow write: if isOwner(userId);
        }
      }

      // التقييمات على المقالات
      match /ratings/{userId} {
        allow read: if true;
        allow write: if isOwner(userId);
      }

      // المفضلة للمقالات
      match /favorites/{userId} {
        allow read, write: if isOwner(userId);
      }

      // قراءة لاحقاً للمقالات
      match /readLater/{userId} {
        allow read, write: if isOwner(userId);
      }
    }

    // ===== قواعد الوصفات =====
    match /recipes/{recipeId} {
      allow read: if true;
      allow write: if isAdmin();

      // التعليقات على الوصفات
      match /comments/{commentId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() &&
          (request.auth.uid == resource.data.userId || isAdmin());

        // الردود على التعليقات
        match /replies/{replyId} {
          allow read: if true;
          allow create: if isAuthenticated();
          allow update, delete: if isAuthenticated() &&
            (request.auth.uid == resource.data.userId || isAdmin());
        }
        
        // الإعجابات على التعليقات
        match /likes/{userId} {
          allow read: if true;
          allow write: if isOwner(userId);
        }
      }

      // التقييمات على الوصفات
      match /ratings/{userId} {
        allow read: if true;
        allow write: if isOwner(userId);
      }

      // المفضلة للوصفات
      match /favorites/{userId} {
        allow read, write: if isOwner(userId);
      }
    }

    // ===== قواعد الصيدليات =====
    match /pharmacies/{pharmacyId} {
      allow read: if true;
      allow write: if isAdmin();

      // التعليقات على الصيدليات
      match /comments/{commentId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() &&
          (request.auth.uid == resource.data.userId || isAdmin());
      }

      // التقييمات على الصيدليات
      match /reviews/{reviewId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() &&
          (request.auth.uid == resource.data.userId || isAdmin());
      }
    }

    // ===== قواعد المتاجر =====
    match /stores/{storeId} {
      allow read: if true;
      allow write: if isAdmin();

      // التعليقات على المتاجر
      match /comments/{commentId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() &&
          (request.auth.uid == resource.data.userId || isAdmin());
      }

      // التقييمات على المتاجر
      match /reviews/{reviewId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() &&
          (request.auth.uid == resource.data.userId || isAdmin());
      }
    }

    // ===== قواعد المستشفيات =====
    match /hospitals/{hospitalId} {
      allow read: if true;
      allow write: if isAdmin();
    }

    // ===== قواعد الأطباء =====
    match /doctors/{doctorId} {
      allow read: if true;
      allow write: if isAdmin();
    }

    // ===== اقتراحات الأطعمة =====
    match /foodSuggestions/{suggestionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
        request.auth.uid == request.resource.data.userId;
      allow update, delete: if isAuthenticated() &&
        (request.auth.uid == resource.data.userId || isAdmin());
    }

    // ===== اقتراحات الأدوية =====
    match /medicationSuggestions/{suggestionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
        request.auth.uid == request.resource.data.userId;
      allow update, delete: if isAuthenticated() &&
        (request.auth.uid == resource.data.userId || isAdmin());
    }

    // ===== مساهمات المقالات =====
    match /articleContributions/{contributionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
        request.auth.uid == request.resource.data.userId;
      allow update, delete: if isAuthenticated() &&
        (request.auth.uid == resource.data.userId || isAdmin());
    }

    // ===== المنتدى =====
    match /forum_posts/{postId} {
      allow read: if true;
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() &&
        (request.auth.uid == resource.data.userId || isAdmin());

      // التعليقات على المنشورات
      match /comments/{commentId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() &&
          (request.auth.uid == resource.data.userId || isAdmin());

        // الإعجابات على التعليقات
        match /likes/{userId} {
          allow read: if true;
          allow write: if isOwner(userId);
        }
      }
    }

    // ===== المنشورات العامة =====
    match /posts/{postId} {
      allow read: if true;
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() &&
        (request.auth.uid == resource.data.userId || isAdmin());

      // التعليقات على المنشورات
      match /comments/{commentId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() &&
          (request.auth.uid == resource.data.userId || isAdmin());

        // الإعجابات على التعليقات
        match /likes/{userId} {
          allow read: if true;
          allow write: if isOwner(userId);
        }
      }
    }

    // ===== التقييمات العامة =====
    match /reviews/{reviewId} {
      allow read: if true;
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() &&
        (request.auth.uid == resource.data.userId || isAdmin());

      // الردود على التقييمات
      match /replies/{replyId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() &&
          (request.auth.uid == resource.data.userId || isAdmin());
      }
    }

    // ===== البيانات الطبية الشخصية =====
    match /symptom_entries/{entryId} {
      allow read, write: if isAuthenticated() &&
        request.auth.uid == resource.data.userId;
    }

    match /medication_reminders/{reminderId} {
      allow read, write: if isAuthenticated() &&
        request.auth.uid == resource.data.userId;
    }

    match /nutrition_entries/{entryId} {
      allow read, write: if isAuthenticated() &&
        request.auth.uid == resource.data.userId;
    }

    match /nutrition_goals/{goalId} {
      allow read, write: if isAuthenticated() &&
        request.auth.uid == resource.data.userId;
    }

    // ===== البيانات العامة =====
    match /emergency_contacts/{contactId} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }

    // ===== مساهمات الأطباء =====
    match /doctorContributions/{contributionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
        request.auth.uid == request.resource.data.userId;
      allow update, delete: if isAuthenticated() &&
        (request.auth.uid == resource.data.userId || isAdmin());
    }

    // ===== الرسائل والمحادثات =====
    match /messages/{messageId} {
      allow read, write: if isAuthenticated() &&
        (request.auth.uid == resource.data.senderId ||
         request.auth.uid == resource.data.receiverId ||
         isAdmin());
    }

    // المحادثات
    match /conversations/{conversationId} {
      allow read, write: if isAuthenticated() &&
        (request.auth.uid in resource.data.participants || isAdmin());

      // رسائل المحادثة
      match /messages/{messageId} {
        allow read, write: if isAuthenticated() &&
          (request.auth.uid in get(/databases/$(database)/documents/conversations/$(conversationId)).data.participants || isAdmin());
      }
    }

    // ===== السلايدر والإعلانات =====
    match /sliderItems/{sliderId} {
      allow read: if true;
      allow write: if isAdmin();
    }

    // البانرات
    match /banners/{bannerId} {
      allow read: if true;
      allow write: if isAdmin();
    }

    // ===== الإعدادات والتكوين =====
    match /app_settings/{settingId} {
      allow read: if true;
      allow write: if isAdmin();
    }

    // إعدادات المستخدم الشخصية
    match /user_preferences/{userId} {
      allow read, write: if isOwner(userId);
    }

    // ===== الإحصائيات والتقارير =====
    match /analytics/{document} {
      allow read, write: if isAdmin();
    }

    // تقارير المستخدمين
    match /user_reports/{reportId} {
      allow read: if isAdmin();
      allow create: if isAuthenticated();
      allow update, delete: if isAdmin();
    }

    // ===== الإدارة =====
    match /admin/{document} {
      allow read, write: if isAdmin();
    }

    // سجل العمليات
    match /audit_logs/{logId} {
      allow read: if isAdmin();
      allow create: if isAuthenticated();
    }

    // ===== البيانات العامة =====
    match /public_data/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }

    // الأسئلة الشائعة
    match /faqs/{faqId} {
      allow read: if true;
      allow write: if isAdmin();
    }

    // شروط الاستخدام وسياسة الخصوصية
    match /legal/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }

    // ===== قاعدة عامة للمشرفين =====
    match /{document=**} {
      allow read, write: if isAdmin();
    }
  }
}

import 'package:flutter/foundation.dart';
import 'package:yassincil/services/firestore_service.dart';
import 'package:yassincil/utils/app_constants.dart';
import 'package:yassincil/models/food_item.dart';
import 'package:yassincil/models/medication.dart';
import 'package:yassincil/models/recipe.dart';
import 'package:yassincil/models/restaurant.dart';
import 'package:yassincil/models/article.dart';
import 'package:yassincil/models/product.dart';
import 'package:yassincil/models/post.dart';

class AdvancedSearchService {
  static final FirestoreService _firestoreService = FirestoreService();

  /// البحث الشامل في جميع المحتويات
  static Future<SearchResults> searchAll(
    String query, {
    List<SearchCategory>? categories,
    SearchFilters? filters,
  }) async {
    try {
      final results = SearchResults();
      final searchQuery = query.toLowerCase().trim();

      if (searchQuery.isEmpty) return results;

      // تحديد الفئات للبحث فيها
      final searchCategories = categories ?? SearchCategory.values;

      // البحث في كل فئة
      for (final category in searchCategories) {
        switch (category) {
          case SearchCategory.foods:
            results.foods = await _searchFoods(searchQuery, filters);
            break;
          case SearchCategory.medications:
            results.medications = await _searchMedications(
              searchQuery,
              filters,
            );
            break;
          case SearchCategory.recipes:
            results.recipes = await _searchRecipes(searchQuery, filters);
            break;
          case SearchCategory.restaurants:
            results.restaurants = await _searchRestaurants(
              searchQuery,
              filters,
            );
            break;
          case SearchCategory.articles:
            results.articles = await _searchArticles(searchQuery, filters);
            break;
          case SearchCategory.products:
            results.products = await _searchProducts(searchQuery, filters);
            break;
          case SearchCategory.forumPosts:
            results.forumPosts = await _searchForumPosts(searchQuery, filters);
            break;
        }
      }

      return results;
    } catch (e) {
      debugPrint('خطأ في البحث الشامل: $e');
      throw Exception('فشل في البحث: $e');
    }
  }

  /// البحث في الأطعمة
  static Future<List<FoodItem>> _searchFoods(
    String query,
    SearchFilters? filters,
  ) async {
    try {
      final snapshot = await _firestoreService.getCollection(
        AppConstants.foodItemsCollection,
        orderBy: 'name',
      );

      final foods = snapshot.docs
          .map((doc) => FoodItem.fromFirestore(doc))
          .toList();

      return foods.where((food) {
        final matchesQuery =
            food.name.toLowerCase().contains(query) ||
            food.details.toLowerCase().contains(query) ||
            (food.ingredients?.toLowerCase().contains(query) ?? false);

        if (!matchesQuery) return false;

        // تطبيق الفلاتر
        if (filters != null) {
          if (filters.glutenFreeOnly && !food.isGlutenFree) {
            return false;
          }
          if (filters.category != null && food.category != filters.category) {
            return false;
          }
        }

        return true;
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الأطعمة: $e');
      return [];
    }
  }

  /// البحث في الأدوية
  static Future<List<Medication>> _searchMedications(
    String query,
    SearchFilters? filters,
  ) async {
    try {
      final snapshot = await _firestoreService.getCollection(
        AppConstants.medicationsCollection,
        orderBy: 'name',
      );

      final medications = snapshot.docs
          .map((doc) => Medication.fromFirestore(doc))
          .toList();

      return medications.where((medication) {
        final matchesQuery =
            medication.name.toLowerCase().contains(query) ||
            medication.notes.toLowerCase().contains(query) ||
            medication.ingredients.toLowerCase().contains(query);

        if (!matchesQuery) return false;

        // تطبيق الفلاتر
        if (filters != null) {
          if (filters.glutenFreeOnly && !medication.isAllowed) return false;
        }

        return true;
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الأدوية: $e');
      return [];
    }
  }

  /// البحث في الوصفات
  static Future<List<Recipe>> _searchRecipes(
    String query,
    SearchFilters? filters,
  ) async {
    try {
      final snapshot = await _firestoreService.getCollection(
        AppConstants.recipesCollection,
        orderBy: 'title',
      );

      final recipes = snapshot.docs
          .map((doc) => Recipe.fromFirestore(doc))
          .toList();

      return recipes.where((recipe) {
        final matchesQuery =
            recipe.title.toLowerCase().contains(query) ||
            recipe.description.toLowerCase().contains(query) ||
            recipe.ingredients.any(
              (ingredient) => ingredient.toLowerCase().contains(query),
            );

        if (!matchesQuery) return false;

        // تطبيق الفلاتر
        if (filters != null) {
          if (filters.glutenFreeOnly && !recipe.isGlutenFree) {
            return false;
          }
          if (filters.category != null && recipe.category != filters.category) {
            return false;
          }
        }

        return true;
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الوصفات: $e');
      return [];
    }
  }

  /// البحث في المطاعم
  static Future<List<Restaurant>> _searchRestaurants(
    String query,
    SearchFilters? filters,
  ) async {
    try {
      final snapshot = await _firestoreService.getCollection(
        AppConstants.restaurantsCollection,
        orderBy: 'name',
      );

      final restaurants = snapshot.docs
          .map((doc) => Restaurant.fromFirestore(doc))
          .toList();

      return restaurants.where((restaurant) {
        final matchesQuery =
            restaurant.name.toLowerCase().contains(query) ||
            restaurant.description.toLowerCase().contains(query) ||
            restaurant.address.toLowerCase().contains(query);

        if (!matchesQuery) return false;

        // تطبيق الفلاتر
        if (filters != null) {
          if (filters.glutenFreeOnly && !restaurant.hasGlutenFreeOptions) {
            return false;
          }
        }

        return true;
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في المطاعم: $e');
      return [];
    }
  }

  /// البحث في المقالات
  static Future<List<Article>> _searchArticles(
    String query,
    SearchFilters? filters,
  ) async {
    try {
      final snapshot = await _firestoreService.getCollection(
        AppConstants.articlesCollection,
        orderBy: 'title',
      );

      final articles = snapshot.docs
          .map((doc) => Article.fromFirestore(doc))
          .toList();

      return articles.where((article) {
        final matchesQuery =
            article.title.toLowerCase().contains(query) ||
            article.content.toLowerCase().contains(query) ||
            article.tags.any((tag) => tag.toLowerCase().contains(query));

        if (!matchesQuery) {
          return false;
        }

        // تطبيق الفلاتر
        if (filters != null) {
          if (filters.category != null &&
              article.category != filters.category) {
            return false;
          }
        }

        return true;
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في المقالات: $e');
      return [];
    }
  }

  /// البحث في المنتجات
  static Future<List<Product>> _searchProducts(
    String query,
    SearchFilters? filters,
  ) async {
    try {
      final snapshot = await _firestoreService.getCollection(
        AppConstants.productsCollection,
        orderBy: 'name',
      );

      final products = snapshot.docs
          .map((doc) => Product.fromFirestore(doc))
          .toList();

      return products.where((product) {
        final matchesQuery =
            product.name.toLowerCase().contains(query) ||
            product.brand.toLowerCase().contains(query) ||
            product.description.toLowerCase().contains(query) ||
            product.barcode.contains(query) ||
            product.ingredients.any(
              (ingredient) => ingredient.toLowerCase().contains(query),
            );

        if (!matchesQuery) {
          return false;
        }

        // تطبيق الفلاتر
        if (filters != null) {
          if (filters.glutenFreeOnly && product.isGlutenFree != true) {
            return false;
          }
          if (filters.category != null &&
              product.category != filters.category) {
            return false;
          }
        }

        return true;
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في المنتجات: $e');
      return [];
    }
  }

  /// البحث في منشورات المنتدى
  static Future<List<Post>> _searchForumPosts(
    String query,
    SearchFilters? filters,
  ) async {
    try {
      final snapshot = await _firestoreService.getCollection(
        AppConstants.postsCollection,
        orderBy: 'title',
      );

      final posts = snapshot.docs
          .map((doc) => Post.fromFirestore(doc))
          .toList();

      return posts.where((post) {
        final matchesQuery =
            post.title.toLowerCase().contains(query) ||
            post.content.toLowerCase().contains(query);

        return matchesQuery;
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في المنتدى: $e');
      return [];
    }
  }

  /// البحث الصوتي (يمكن تطويره لاحقاً)
  static Future<SearchResults> voiceSearch(String audioQuery) async {
    // يمكن دمج خدمة تحويل الصوت إلى نص هنا
    // مثل Google Speech-to-Text أو Azure Speech Services
    debugPrint('البحث الصوتي: $audioQuery');
    return await searchAll(audioQuery);
  }

  /// اقتراحات البحث
  static Future<List<String>> getSearchSuggestions(String query) async {
    try {
      final suggestions = <String>[];

      if (query.length < 2) return suggestions;

      // يمكن تطوير هذا لاحقاً لجلب اقتراحات من قاعدة البيانات
      // أو استخدام خدمة اقتراحات خارجية

      return suggestions;
    } catch (e) {
      debugPrint('خطأ في جلب اقتراحات البحث: $e');
      return [];
    }
  }
}

/// فئات البحث
enum SearchCategory {
  foods,
  medications,
  recipes,
  restaurants,
  articles,
  products,
  forumPosts,
}

/// فلاتر البحث
class SearchFilters {
  final bool glutenFreeOnly;
  final String? category;
  final DateTime? dateFrom;
  final DateTime? dateTo;
  final double? minRating;
  final bool verifiedOnly;

  SearchFilters({
    this.glutenFreeOnly = false,
    this.category,
    this.dateFrom,
    this.dateTo,
    this.minRating,
    this.verifiedOnly = false,
  });
}

/// نتائج البحث
class SearchResults {
  List<FoodItem> foods = [];
  List<Medication> medications = [];
  List<Recipe> recipes = [];
  List<Restaurant> restaurants = [];
  List<Article> articles = [];
  List<Product> products = [];
  List<Post> forumPosts = [];

  /// إجمالي عدد النتائج
  int get totalCount =>
      foods.length +
      medications.length +
      recipes.length +
      restaurants.length +
      articles.length +
      products.length +
      forumPosts.length;

  /// التحقق من وجود نتائج
  bool get hasResults => totalCount > 0;

  /// مسح جميع النتائج
  void clear() {
    foods.clear();
    medications.clear();
    recipes.clear();
    restaurants.clear();
    articles.clear();
    products.clear();
    forumPosts.clear();
  }
}

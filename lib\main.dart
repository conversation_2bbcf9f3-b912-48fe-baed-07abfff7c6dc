import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'package:intl/date_symbol_data_local.dart';

// Utils
import 'package:yassincil/utils/timeago_config.dart';
import 'package:yassincil/utils/app_theme.dart' as app_theme;
import 'package:yassincil/utils/theme_manager.dart';

// Firebase Options - This file will be generated by `flutterfire configure`
import 'package:yassincil/firebase_options.dart';

// Services
import 'package:yassincil/services/auth_service.dart';
import 'package:yassincil/services/firestore_service.dart';
import 'package:yassincil/services/storage_service.dart';

// Providers
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/providers/medication_provider.dart';
import 'package:yassincil/providers/favorites_provider.dart';
import 'package:yassincil/providers/food_provider.dart';
import 'package:yassincil/providers/recipe_provider.dart';

import 'package:yassincil/providers/restaurant_provider.dart';
import 'package:yassincil/providers/doctor_provider.dart';
import 'package:yassincil/providers/article_provider.dart';
import 'package:yassincil/providers/post_provider.dart';
import 'package:yassincil/providers/forum_provider.dart';
import 'package:yassincil/providers/user_management_provider.dart';
import 'package:yassincil/providers/slider_provider.dart';
import 'package:yassincil/providers/notification_provider.dart';
import 'package:yassincil/providers/product_provider.dart';
import 'package:yassincil/providers/search_provider.dart';
import 'package:yassincil/providers/nutrition_provider.dart';
import 'package:yassincil/providers/pharmacy_provider.dart';
import 'package:yassincil/providers/symptoms_provider.dart';
import 'package:yassincil/providers/review_provider.dart';
import 'package:yassincil/providers/safe_store_provider.dart';
import 'package:yassincil/services/notification_service.dart';

import 'package:yassincil/utils/database_helper.dart';

// Screens
import 'package:yassincil/auth/screens/login_screen.dart';
import 'package:yassincil/screens/home_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized(); // Ensure Flutter widgets are initialized

  // تهيئة البيانات المحلية للتواريخ
  await initializeDateFormatting('ar', null);

  // تهيئة timeago للعربية
  TimeagoConfig.initialize();

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform, // Initialize Firebase
  );

  // تهيئة خدمة الإشعارات
  await NotificationService.initialize();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Services provided as singletons (not ChangeNotifier)
        Provider<AuthService>(create: (_) => AuthService()),
        Provider<FirestoreService>(create: (_) => FirestoreService()),
        Provider<StorageService>(create: (_) => StorageService()),
        Provider<DatabaseHelper>(create: (_) => DatabaseHelper.instance),

        // Change Notifier Providers (for state management)
        ChangeNotifierProvider<AuthProvider>(
          create: (context) => AuthProvider(
            Provider.of<AuthService>(context, listen: false),
            Provider.of<FirestoreService>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider<MedicationProvider>(
          create: (context) => MedicationProvider(
            Provider.of<FirestoreService>(context, listen: false),
            Provider.of<StorageService>(context, listen: false),
            Provider.of<DatabaseHelper>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider<FavoritesProvider>(
          create: (context) => FavoritesProvider(
            Provider.of<FirestoreService>(context, listen: false),
            Provider.of<DatabaseHelper>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider<FoodProvider>(
          create: (context) => FoodProvider(
            Provider.of<FirestoreService>(context, listen: false),
            Provider.of<StorageService>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider<RecipeProvider>(
          create: (context) => RecipeProvider(
            Provider.of<FirestoreService>(context, listen: false),
          ),
        ),

        ChangeNotifierProvider<RestaurantProvider>(
          create: (context) => RestaurantProvider(
            Provider.of<FirestoreService>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider<DoctorProvider>(
          create: (context) => DoctorProvider(
            Provider.of<FirestoreService>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider<ArticleProvider>(
          create: (context) => ArticleProvider(
            Provider.of<FirestoreService>(context, listen: false),
            Provider.of<StorageService>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider<PostProvider>(
          create: (context) => PostProvider(
            Provider.of<FirestoreService>(context, listen: false),
            Provider.of<StorageService>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider<ForumProvider>(
          create: (context) => ForumProvider(
            Provider.of<FirestoreService>(context, listen: false),
            Provider.of<StorageService>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider<UserManagementProvider>(
          create: (context) => UserManagementProvider(
            Provider.of<AuthService>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider<SliderProvider>(
          create: (context) => SliderProvider(),
        ),
        ChangeNotifierProvider<NotificationProvider>(
          create: (context) => NotificationProvider(),
        ),
        ChangeNotifierProvider<ProductProvider>(
          create: (context) => ProductProvider(),
        ),
        ChangeNotifierProvider<SearchProvider>(
          create: (context) => SearchProvider(),
        ),
        ChangeNotifierProvider<NutritionProvider>(
          create: (context) => NutritionProvider(
            Provider.of<FirestoreService>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider<PharmacyProvider>(
          create: (context) => PharmacyProvider(
            Provider.of<FirestoreService>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider<SymptomsProvider>(
          create: (context) => SymptomsProvider(
            Provider.of<FirestoreService>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider<ReviewProvider>(
          create: (context) => ReviewProvider(
            Provider.of<FirestoreService>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider<SafeStoreProvider>(
          create: (context) => SafeStoreProvider(),
        ),
        ChangeNotifierProvider<ThemeManager>(
          create: (context) => ThemeManager()..initialize(),
        ),
      ],
      child: Consumer<ThemeManager>(
        builder: (context, themeManager, _) {
          return MaterialApp(
            title: 'رفيق السيلياك',
            debugShowCheckedModeBanner: false,
            theme: app_theme.AppTheme.lightTheme,
            darkTheme: app_theme.AppTheme.darkTheme,
            themeMode: themeManager.themeMode,
            // This part decides which screen to show first based on authentication status
            home: Consumer<AuthProvider>(
              builder: (context, authProvider, _) {
                // فقط عرض شاشة التحميل عند بدء التطبيق
                if (authProvider.isLoadingAuth) {
                  return const Scaffold(
                    body: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('جاري التحقق من حالة تسجيل الدخول...'),
                        ],
                      ),
                    ),
                  );
                }
                // إذا كان المستخدم مسجل دخول، اعرض الشاشة الرئيسية
                if (authProvider.currentUser != null) {
                  return const HomeScreen();
                }
                // إذا لم يكن مسجل دخول، اعرض شاشة تسجيل الدخول
                return const LoginScreen();
              },
            ),
            // Define named routes for easier navigation (optional but good practice)
            routes: {
              '/login': (context) => const LoginScreen(),
              '/home': (context) => const HomeScreen(),
              // Add routes for other screens if you use named routes extensively
            },
          );
        },
      ),
    );
  }
}

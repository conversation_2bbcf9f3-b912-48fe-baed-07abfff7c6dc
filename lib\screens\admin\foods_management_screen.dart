import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../providers/food_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/food_item.dart';
import '../../utils/app_colors.dart';
import '../../utils/error_handler.dart';
import '../../widgets/loading_widget.dart' hide EmptyStateWidget;
import '../../widgets/empty_state_widget.dart';
import '../foods/add_edit_food_screen.dart';
import '../foods/food_detail_screen.dart';

class FoodsManagementScreen extends StatefulWidget {
  const FoodsManagementScreen({super.key});

  @override
  State<FoodsManagementScreen> createState() => _FoodsManagementScreenState();
}

class _FoodsManagementScreenState extends State<FoodsManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  String _selectedFilter = 'الكل';
  String _selectedCategory = 'الكل';
  bool _isLoading = false;

  final List<String> _filters = [
    'الكل',
    'الأحدث',
    'الأقدم',
    'الأعلى تقييماً',
    'الأقل تقييماً',
    'الأكثر تفاعلاً',
    'خالي من الجلوتين',
    'يحتوي على جلوتين',
  ];

  final List<String> _categoryFilters = [
    'الكل',
    'حبوب ومخبوزات',
    'ألبان ومنتجاتها',
    'خضروات',
    'فواكه',
    'لحوم وأسماك',
    'حلويات',
    'مشروبات',
    'توابل وبهارات',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadFoods();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadFoods() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final foodProvider = Provider.of<FoodProvider>(context, listen: false);
      foodProvider.fetchFoodItems();
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(context, e);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildTabBar(),
          Expanded(
            child: _isLoading
                ? const LoadingWidget(message: 'جاري تحميل الأطعمة...')
                : _buildTabBarView(),
          ),
        ],
      ),
      floatingActionButton: _buildAddFoodFAB(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'إدارة الأطعمة',
        style: GoogleFonts.cairo(fontWeight: FontWeight.bold, fontSize: 20),
      ),
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadFoods,
          tooltip: 'تحديث',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'statistics':
                _showStatistics();
                break;
              case 'export':
                _exportFoods();
                break;
              case 'bulk_actions':
                _showBulkActionsDialog();
                break;
              case 'categories':
                _manageCategoriesDialog();
                break;
              case 'suggestions':
                _showSuggestionsScreen();
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'statistics',
              child: Row(
                children: [
                  const Icon(Icons.analytics, size: 20),
                  const SizedBox(width: 8),
                  Text('الإحصائيات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'suggestions',
              child: Row(
                children: [
                  const Icon(Icons.lightbulb, size: 20),
                  const SizedBox(width: 8),
                  Text('اقتراحات المستخدمين', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  const Icon(Icons.download, size: 20),
                  const SizedBox(width: 8),
                  Text('تصدير البيانات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'bulk_actions',
              child: Row(
                children: [
                  const Icon(Icons.checklist, size: 20),
                  const SizedBox(width: 8),
                  Text('إجراءات جماعية', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'categories',
              child: Row(
                children: [
                  const Icon(Icons.category, size: 20),
                  const SizedBox(width: 8),
                  Text('إدارة الفئات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          Container(
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث في الأطعمة...',
                hintStyle: GoogleFonts.cairo(color: Colors.grey.shade600),
                prefixIcon: Icon(Icons.search, color: Colors.grey.shade600),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(Icons.clear, color: Colors.grey.shade600),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {});
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              style: GoogleFonts.cairo(),
              onChanged: (value) {
                setState(() {});
              },
            ),
          ),
          const SizedBox(height: 12),
          // فلاتر
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: _selectedFilter,
                      isExpanded: true,
                      icon: Icon(
                        Icons.arrow_drop_down,
                        color: AppColors.primary,
                      ),
                      style: GoogleFonts.cairo(color: AppColors.textPrimary),
                      onChanged: (value) {
                        setState(() {
                          _selectedFilter = value!;
                        });
                      },
                      items: _filters.map((filter) {
                        return DropdownMenuItem(
                          value: filter,
                          child: Text(filter),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: _selectedCategory,
                      isExpanded: true,
                      icon: Icon(
                        Icons.arrow_drop_down,
                        color: AppColors.primary,
                      ),
                      style: GoogleFonts.cairo(color: AppColors.textPrimary),
                      onChanged: (value) {
                        setState(() {
                          _selectedCategory = value!;
                        });
                      },
                      items: _categoryFilters.map((category) {
                        return DropdownMenuItem(
                          value: category,
                          child: Text(category),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: Colors.grey.shade600,
        labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        unselectedLabelStyle: GoogleFonts.cairo(fontWeight: FontWeight.normal),
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        isScrollable: true,
        tabs: const [
          Tab(text: 'جميع الأطعمة'),
          Tab(text: 'في انتظار المراجعة'),
          Tab(text: 'المميزة'),
          Tab(text: 'المرفوضة'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildFoodsList('all'),
        _buildFoodsList('pending'),
        _buildFoodsList('featured'),
        _buildFoodsList('rejected'),
      ],
    );
  }

  Widget _buildFoodsList(String type) {
    return Consumer<FoodProvider>(
      builder: (context, foodProvider, child) {
        final foods = _getFilteredFoods(foodProvider.foodItems, type);

        if (foods.isEmpty) {
          return EmptyStateWidget(
            icon: Icons.fastfood,
            title: 'لا توجد أطعمة',
            subtitle: _getEmptyMessage(type),
            action: ElevatedButton.icon(
              onPressed: _loadFoods,
              icon: const Icon(Icons.refresh),
              label: Text('إعادة التحميل', style: GoogleFonts.cairo()),
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _loadFoods,
          child: GridView.builder(
            padding: const EdgeInsets.all(16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.75,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: foods.length,
            itemBuilder: (context, index) {
              final food = foods[index];
              return _buildFoodManagementCard(food);
            },
          ),
        );
      },
    );
  }

  List<FoodItem> _getFilteredFoods(List<FoodItem> foods, String type) {
    var filtered = foods.where((food) {
      // فلترة حسب البحث
      if (_searchController.text.isNotEmpty) {
        final query = _searchController.text.toLowerCase();
        if (!food.name.toLowerCase().contains(query) &&
            !food.details.toLowerCase().contains(query) &&
            !food.category.toLowerCase().contains(query) &&
            !(food.ingredients?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // فلترة حسب الفئة
      if (_selectedCategory != 'الكل') {
        if (food.category != _selectedCategory) {
          return false;
        }
      }

      // فلترة حسب النوع
      switch (type) {
        case 'pending':
          return !food.isApproved;
        case 'featured':
          return food.isFeatured;
        case 'rejected':
          return !food.isApproved; // الأطعمة غير المعتمدة (مؤقتاً)
        default:
          return food.isApproved;
      }
    }).toList();

    // ترتيب حسب الفلتر المختار
    switch (_selectedFilter) {
      case 'الأحدث':
        filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'الأقدم':
        filtered.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case 'الأعلى تقييماً':
        filtered.sort((a, b) => b.averageRating.compareTo(a.averageRating));
        break;
      case 'الأقل تقييماً':
        filtered.sort((a, b) => a.averageRating.compareTo(b.averageRating));
        break;
      case 'الأكثر تفاعلاً':
        filtered.sort(
          (a, b) => (b.likesCount + b.commentsCount).compareTo(
            a.likesCount + a.commentsCount,
          ),
        );
        break;
      case 'خالي من الجلوتين':
        filtered = filtered.where((food) => food.isGlutenFree).toList();
        break;
      case 'يحتوي على جلوتين':
        filtered = filtered.where((food) => !food.isGlutenFree).toList();
        break;
    }

    return filtered;
  }

  String _getEmptyMessage(String type) {
    switch (type) {
      case 'pending':
        return 'لا توجد أطعمة في انتظار المراجعة';
      case 'featured':
        return 'لا توجد أطعمة مميزة';
      case 'rejected':
        return 'لا توجد أطعمة مرفوضة';
      default:
        return 'لم يتم العثور على أطعمة تطابق البحث';
    }
  }

  Widget _buildFoodManagementCard(FoodItem food) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة الطعام مع شارة الحالة
          Expanded(
            flex: 3,
            child: Stack(
              children: [
                _buildFoodImage(food),
                Positioned(top: 8, right: 8, child: _buildStatusBadge(food)),
                if (food.isFeatured)
                  Positioned(
                    top: 8,
                    left: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 3,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.amber,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.star, size: 12, color: Colors.white),
                          const SizedBox(width: 2),
                          Text(
                            'مميز',
                            style: GoogleFonts.cairo(
                              fontSize: 10,
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                if (!food.isGlutenFree)
                  Positioned(
                    bottom: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 3,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.warning,
                            size: 12,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            'جلوتين',
                            style: GoogleFonts.cairo(
                              fontSize: 10,
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
          // معلومات الطعام
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // اسم الطعام والتقييم
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          food.name,
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      _buildRatingWidget(food.averageRating),
                    ],
                  ),
                  const SizedBox(height: 4),
                  // الفئة
                  Text(
                    food.category,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const Spacer(),
                  // إحصائيات سريعة
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildStatItem(
                        Icons.favorite,
                        food.likesCount.toString(),
                      ),
                      _buildStatItem(
                        Icons.comment,
                        food.commentsCount.toString(),
                      ),
                      _buildStatItem(Icons.star, food.ratingsCount.toString()),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // أزرار الإدارة
                  Row(
                    children: [
                      Expanded(
                        child: _buildActionButton(
                          icon: Icons.visibility,
                          color: Colors.blue,
                          onTap: () => _viewFood(food),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: _buildActionButton(
                          icon: Icons.edit,
                          color: Colors.orange,
                          onTap: () => _editFood(food),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: _buildActionButton(
                          icon: Icons.delete,
                          color: Colors.red,
                          onTap: () => _deleteFood(food),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // الدوال المساعدة
  Widget _buildFoodImage(FoodItem food) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        color: Colors.grey.shade200,
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        child: food.imageUrls.isNotEmpty
            ? CachedNetworkImage(
                imageUrl: food.imageUrls.first,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey.shade200,
                  child: const Center(child: CircularProgressIndicator()),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey.shade200,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.fastfood,
                        size: 32,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'لا توجد صورة',
                        style: GoogleFonts.cairo(
                          fontSize: 10,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            : Container(
                color: Colors.grey.shade200,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.fastfood, size: 32, color: Colors.grey.shade400),
                    const SizedBox(height: 4),
                    Text(
                      'لا توجد صورة',
                      style: GoogleFonts.cairo(
                        fontSize: 10,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildStatusBadge(FoodItem food) {
    Color color;
    String text;

    if (!food.isApproved) {
      color = Colors.orange;
      text = 'مراجعة';
    } else {
      color = Colors.green;
      text = 'معتمد';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        text,
        style: GoogleFonts.cairo(
          fontSize: 10,
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildRatingWidget(double rating) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: Colors.amber.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.amber.shade200),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.star, size: 12, color: Colors.amber.shade600),
          const SizedBox(width: 2),
          Text(
            rating.toStringAsFixed(1),
            style: GoogleFonts.cairo(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: Colors.amber.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String value) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 12, color: Colors.grey.shade600),
        const SizedBox(width: 2),
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 10,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(6),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Icon(icon, size: 14, color: color),
      ),
    );
  }

  Widget _buildAddFoodFAB() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: FloatingActionButton.extended(
        onPressed: _showAddFoodDialog,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        icon: const Icon(Icons.add),
        label: Text(
          'إضافة طعام',
          style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  // دوال الإجراءات
  void _viewFood(FoodItem food) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => FoodDetailScreen(foodItem: food)),
    );
  }

  void _editFood(FoodItem food) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditFoodScreen(foodItem: food),
      ),
    );
  }

  void _deleteFood(FoodItem food) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف الطعام', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف "${food.name}"؟ لا يمكن التراجع عن هذا الإجراء.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _performDeleteFood(food);
            },
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _performDeleteFood(FoodItem food) async {
    try {
      final foodProvider = Provider.of<FoodProvider>(context, listen: false);
      await foodProvider.deleteFoodItem(food.id!);
      if (mounted) {
        ErrorHandler.showSuccessSnackBar(context, 'تم حذف الطعام بنجاح');
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(context, 'فشل في حذف الطعام: $e');
      }
    }
  }

  void _showAddFoodDialog() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddEditFoodScreen()),
    );
  }

  void _showStatistics() {
    // عرض إحصائيات الأطعمة
    final foodProvider = Provider.of<FoodProvider>(context, listen: false);
    final foods = foodProvider.foodItems;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إحصائيات الأطعمة',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatisticRow('إجمالي الأطعمة', '${foods.length}'),
            _buildStatisticRow(
              'الأطعمة المعتمدة',
              '${foods.where((f) => f.isApproved).length}',
            ),
            _buildStatisticRow(
              'في انتظار المراجعة',
              '${foods.where((f) => !f.isApproved).length}',
            ),
            _buildStatisticRow(
              'الأطعمة المميزة',
              '${foods.where((f) => f.isFeatured).length}',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: GoogleFonts.cairo()),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  void _exportFoods() {
    // تصدير الأطعمة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تصدير الأطعمة بنجاح', style: GoogleFonts.cairo()),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showBulkActionsDialog() {
    // إجراءات جماعية
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم تطبيق الإجراءات الجماعية',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _manageCategoriesDialog() {
    // إدارة الفئات
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم فتح إدارة الفئات', style: GoogleFonts.cairo()),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showSuggestionsScreen() {
    // عرض اقتراحات المستخدمين
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم فتح اقتراحات المستخدمين', style: GoogleFonts.cairo()),
        backgroundColor: Colors.purple,
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AddCommentWidget extends StatefulWidget {
  final Function(String) onCommentAdded;
  final String? parentCommentId;
  final String? replyToUsername;

  const AddCommentWidget({
    super.key,
    required this.onCommentAdded,
    this.parentCommentId,
    this.replyToUsername,
  });

  @override
  State<AddCommentWidget> createState() => _AddCommentWidgetState();
}

class _AddCommentWidgetState extends State<AddCommentWidget> {
  final TextEditingController _controller = TextEditingController();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _submitComment() async {
    final content = _controller.text.trim();
    if (content.isEmpty) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      await widget.onCommentAdded(content);
      _controller.clear();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.parentCommentId != null ? 'تم إضافة الرد' : 'تم إضافة التعليق',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في إضافة التعليق',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.replyToUsername != null) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'رد على ${widget.replyToUsername}',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 12),
          ],
          
          TextField(
            controller: _controller,
            decoration: InputDecoration(
              hintText: widget.parentCommentId != null 
                  ? 'اكتب ردك...' 
                  : 'اكتب تعليقك...',
              hintStyle: GoogleFonts.cairo(color: Colors.grey.shade600),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.green.shade600),
              ),
              contentPadding: const EdgeInsets.all(12),
            ),
            style: GoogleFonts.cairo(),
            maxLines: 3,
            enabled: !_isSubmitting,
          ),
          
          const SizedBox(height: 12),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (widget.parentCommentId != null)
                TextButton(
                  onPressed: _isSubmitting ? null : () {
                    _controller.clear();
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    'إلغاء',
                    style: GoogleFonts.cairo(color: Colors.grey.shade600),
                  ),
                ),
              
              const SizedBox(width: 8),
              
              ElevatedButton(
                onPressed: _isSubmitting ? null : _submitComment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade600,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isSubmitting
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        widget.parentCommentId != null ? 'رد' : 'نشر',
                        style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                      ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

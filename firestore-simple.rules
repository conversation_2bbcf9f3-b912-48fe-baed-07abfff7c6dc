rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // قواعد مبسطة للاختبار - تسمح بالوصول لجميع المستخدمين المسجلين
    
    // المستخدمون
    match /users/{userId} {
      allow read, write: if request.auth != null;
    }
    
    // الأطعمة والمكونات
    match /foodItems/{foodId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
      
      // التعليقات على الأطعمة
      match /comments/{commentId} {
        allow read, write: if request.auth != null;
      }
      
      // الإعجابات على الأطعمة
      match /likes/{userId} {
        allow read, write: if request.auth != null;
      }
    }
    
    // الأدوية
    match /medications/{medicationId} {
      allow read, write: if request.auth != null;
      
      // التعليقات على الأدوية
      match /comments/{commentId} {
        allow read, write: if request.auth != null;
      }
      
      // الإعجابات على الأدوية
      match /likes/{userId} {
        allow read, write: if request.auth != null;
      }
    }
    
    // الوصفات
    match /recipes/{recipeId} {
      allow read, write: if request.auth != null;
      
      // التعليقات على الوصفات
      match /comments/{commentId} {
        allow read, write: if request.auth != null;
      }
      
      // الإعجابات على الوصفات
      match /likes/{userId} {
        allow read, write: if request.auth != null;
      }
    }
    
    // المطاعم
    match /restaurants/{restaurantId} {
      allow read, write: if request.auth != null;
      
      // التعليقات على المطاعم
      match /comments/{commentId} {
        allow read, write: if request.auth != null;
      }
      
      // الإعجابات على المطاعم
      match /likes/{userId} {
        allow read, write: if request.auth != null;
      }
      
      // التقييمات على المطاعم
      match /reviews/{reviewId} {
        allow read, write: if request.auth != null;
      }
    }
    
    // الصيدليات
    match /pharmacies/{pharmacyId} {
      allow read, write: if request.auth != null;
      
      // التعليقات على الصيدليات
      match /comments/{commentId} {
        allow read, write: if request.auth != null;
      }
      
      // التقييمات على الصيدليات
      match /reviews/{reviewId} {
        allow read, write: if request.auth != null;
      }
    }
    
    // المتاجر
    match /stores/{storeId} {
      allow read, write: if request.auth != null;
      
      // التعليقات على المتاجر
      match /comments/{commentId} {
        allow read, write: if request.auth != null;
      }
      
      // التقييمات على المتاجر
      match /reviews/{reviewId} {
        allow read, write: if request.auth != null;
      }
    }
    
    // المقالات
    match /articles/{articleId} {
      allow read, write: if request.auth != null;
      
      // التعليقات على المقالات
      match /comments/{commentId} {
        allow read, write: if request.auth != null;
      }
      
      // الإعجابات على المقالات
      match /likes/{userId} {
        allow read, write: if request.auth != null;
      }
    }
    
    // المنتدى
    match /posts/{postId} {
      allow read, write: if request.auth != null;
      
      // التعليقات على المنشورات
      match /comments/{commentId} {
        allow read, write: if request.auth != null;
      }
      
      // الإعجابات على المنشورات
      match /likes/{userId} {
        allow read, write: if request.auth != null;
      }
    }
    
    // التقييمات العامة
    match /reviews/{reviewId} {
      allow read, write: if request.auth != null;
    }
    
    // البيانات الشخصية
    match /symptom_entries/{entryId} {
      allow read, write: if request.auth != null;
    }
    
    match /medication_reminders/{reminderId} {
      allow read, write: if request.auth != null;
    }
    
    match /nutrition_entries/{entryId} {
      allow read, write: if request.auth != null;
    }
    
    match /nutrition_goals/{goalId} {
      allow read, write: if request.auth != null;
    }
    
    // الرسائل
    match /messages/{messageId} {
      allow read, write: if request.auth != null;
    }
    
    match /conversations/{conversationId} {
      allow read, write: if request.auth != null;
      
      match /messages/{messageId} {
        allow read, write: if request.auth != null;
      }
    }
    
    // الإعدادات
    match /app_settings/{settingId} {
      allow read, write: if request.auth != null;
    }
    
    match /user_preferences/{userId} {
      allow read, write: if request.auth != null;
    }
    
    // السلايدر
    match /sliderItems/{sliderId} {
      allow read, write: if request.auth != null;
    }
    
    // البيانات العامة
    match /emergency_contacts/{contactId} {
      allow read, write: if request.auth != null;
    }
    
    match /hospitals/{hospitalId} {
      allow read, write: if request.auth != null;
    }
    
    match /doctors/{doctorId} {
      allow read, write: if request.auth != null;
    }
    
    // الإدارة
    match /admin/{document} {
      allow read, write: if request.auth != null;
    }
    
    match /analytics/{document} {
      allow read, write: if request.auth != null;
    }
    
    // البيانات العامة
    match /public_data/{document} {
      allow read, write: if request.auth != null;
    }
    
    match /faqs/{faqId} {
      allow read, write: if request.auth != null;
    }
    
    match /legal/{document} {
      allow read, write: if request.auth != null;
    }
    
    // منع الوصول لأي مجموعة غير محددة
    match /{document=**} {
      allow read, write: if false;
    }
  }
}

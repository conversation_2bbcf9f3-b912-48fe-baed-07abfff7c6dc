import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../providers/safe_store_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/safe_store.dart';
import '../../utils/app_colors.dart';
import 'add_edit_safe_store_screen.dart';
import 'safe_store_detail_screen.dart';

class ManageSafeStoresScreen extends StatefulWidget {
  const ManageSafeStoresScreen({super.key});

  @override
  State<ManageSafeStoresScreen> createState() => _ManageSafeStoresScreenState();
}

class _ManageSafeStoresScreenState extends State<ManageSafeStoresScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  String _selectedCategory = 'الكل';

  final List<String> _categories = [
    'الكل',
    'سوبر ماركت',
    'متاجر صحية',
    'مخابز خاصة',
    'متاجر أونلاين',
    'صيدليات',
    'مطاعم',
    'كافيهات',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<SafeStoreProvider>(context, listen: false).fetchStores();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  List<SafeStore> _getFilteredStores(List<SafeStore> stores) {
    var filtered = stores;

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((store) {
        return store.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            store.description.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            store.location.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // Filter by category
    if (_selectedCategory != 'الكل') {
      filtered = filtered
          .where((store) => store.category == _selectedCategory)
          .toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'إدارة المتاجر الآمنة',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          unselectedLabelStyle: GoogleFonts.cairo(),
          tabs: const [
            Tab(text: 'جميع المتاجر'),
            Tab(text: 'في انتظار التحقق'),
            Tab(text: 'المتاجر المميزة'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildSearchAndFilter(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllStoresTab(),
                _buildPendingVerificationTab(),
                _buildFeaturedStoresTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddEditSafeStoreScreen(),
            ),
          );
        },
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          // Search bar
          TextField(
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
            style: GoogleFonts.cairo(),
            decoration: InputDecoration(
              hintText: 'البحث في المتاجر...',
              hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500),
              prefixIcon: Icon(Icons.search, color: Colors.grey.shade600),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary, width: 2),
              ),
              filled: true,
              fillColor: Colors.grey.shade50,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
          ),
          const SizedBox(height: 12),
          // Category filter
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = category == _selectedCategory;

                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(
                      category,
                      style: GoogleFonts.cairo(
                        color: isSelected ? Colors.white : AppColors.primary,
                        fontWeight: isSelected
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = category;
                      });
                    },
                    backgroundColor: Colors.white,
                    selectedColor: AppColors.primary,
                    checkmarkColor: Colors.white,
                    side: BorderSide(color: AppColors.primary),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllStoresTab() {
    return Consumer<SafeStoreProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 16),
                Text(
                  provider.error!,
                  style: GoogleFonts.cairo(color: Colors.grey.shade600),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => provider.fetchStores(),
                  child: Text('إعادة المحاولة', style: GoogleFonts.cairo()),
                ),
              ],
            ),
          );
        }

        final filteredStores = _getFilteredStores(provider.stores);

        if (filteredStores.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.store_outlined,
                  size: 64,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 16),
                Text(
                  'لا توجد متاجر',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => provider.fetchStores(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredStores.length,
            itemBuilder: (context, index) {
              final store = filteredStores[index];
              return _buildStoreCard(store);
            },
          ),
        );
      },
    );
  }

  Widget _buildPendingVerificationTab() {
    return Consumer<SafeStoreProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final pendingStores = _getFilteredStores(
          provider.stores.where((store) => !store.isVerified).toList(),
        );

        if (pendingStores.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.verified_outlined,
                  size: 64,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 16),
                Text(
                  'لا توجد متاجر في انتظار التحقق',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => provider.fetchStores(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: pendingStores.length,
            itemBuilder: (context, index) {
              final store = pendingStores[index];
              return _buildStoreCard(store, showVerificationActions: true);
            },
          ),
        );
      },
    );
  }

  Widget _buildFeaturedStoresTab() {
    return Consumer<SafeStoreProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final featuredStores = _getFilteredStores(
          provider.stores.where((store) => store.isFeatured).toList(),
        );

        if (featuredStores.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.star_outline, size: 64, color: Colors.grey.shade400),
                const SizedBox(height: 16),
                Text(
                  'لا توجد متاجر مميزة',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => provider.fetchStores(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: featuredStores.length,
            itemBuilder: (context, index) {
              final store = featuredStores[index];
              return _buildStoreCard(store);
            },
          ),
        );
      },
    );
  }

  Widget _buildStoreCard(
    SafeStore store, {
    bool showVerificationActions = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Store image and basic info
          Container(
            height: 120,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
              image: store.image != null
                  ? DecorationImage(
                      image: NetworkImage(store.image!),
                      fit: BoxFit.cover,
                    )
                  : null,
              color: store.image == null ? Colors.grey.shade200 : null,
            ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(16),
                ),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                ),
              ),
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          store.name,
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      if (store.isVerified)
                        const Icon(
                          Icons.verified,
                          color: Colors.green,
                          size: 20,
                        ),
                      if (store.isFeatured)
                        const Icon(Icons.star, color: Colors.amber, size: 20),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          store.category,
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Icon(Icons.location_on, color: Colors.white, size: 14),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          store.location,
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.white,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Store details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  store.description,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                    height: 1.4,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),

                // Stats row
                Row(
                  children: [
                    _buildStatItem(
                      Icons.star,
                      store.rating.toStringAsFixed(1),
                      Colors.amber,
                    ),
                    const SizedBox(width: 16),
                    _buildStatItem(
                      Icons.favorite,
                      '${store.likesCount}',
                      Colors.red,
                    ),
                    const SizedBox(width: 16),
                    _buildStatItem(
                      Icons.comment,
                      '${store.reviewsCount}',
                      Colors.blue,
                    ),
                    const SizedBox(width: 16),
                    _buildStatItem(
                      Icons.visibility,
                      '${store.visitsCount}',
                      Colors.green,
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  SafeStoreDetailScreen(store: store),
                            ),
                          );
                        },
                        icon: const Icon(Icons.visibility),
                        label: Text('عرض', style: GoogleFonts.cairo()),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.primary,
                          side: BorderSide(color: AppColors.primary),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  AddEditSafeStoreScreen(store: store),
                            ),
                          );
                        },
                        icon: const Icon(Icons.edit),
                        label: Text('تعديل', style: GoogleFonts.cairo()),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.orange,
                          side: const BorderSide(color: Colors.orange),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _deleteStore(store),
                        icon: const Icon(Icons.delete),
                        label: Text('حذف', style: GoogleFonts.cairo()),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red,
                          side: const BorderSide(color: Colors.red),
                        ),
                      ),
                    ),
                  ],
                ),

                // Verification actions
                if (showVerificationActions) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _verifyStore(store),
                          icon: const Icon(Icons.verified),
                          label: Text('تحقق', style: GoogleFonts.cairo()),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _rejectStore(store),
                          icon: const Icon(Icons.close),
                          label: Text('رفض', style: GoogleFonts.cairo()),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String value, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade700,
          ),
        ),
      ],
    );
  }

  void _deleteStore(SafeStore store) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد الحذف', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف متجر "${store.name}"؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              navigator.pop();

              if (store.id != null) {
                final success = await Provider.of<SafeStoreProvider>(
                  context,
                  listen: false,
                ).deleteStore(store.id!);

                if (success) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text(
                        'تم حذف المتجر بنجاح',
                        style: GoogleFonts.cairo(),
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              }
            },
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _verifyStore(SafeStore store) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final updatedStore = store.copyWith(isVerified: true);
    final success = await Provider.of<SafeStoreProvider>(
      context,
      listen: false,
    ).updateStore(updatedStore);

    if (success) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('تم تحقق المتجر بنجاح', style: GoogleFonts.cairo()),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _rejectStore(SafeStore store) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('رفض المتجر', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من رفض متجر "${store.name}"؟ سيتم حذفه نهائياً.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              navigator.pop();

              if (store.id != null) {
                final success = await Provider.of<SafeStoreProvider>(
                  context,
                  listen: false,
                ).deleteStore(store.id!);

                if (success) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text(
                        'تم رفض المتجر وحذفه',
                        style: GoogleFonts.cairo(),
                      ),
                      backgroundColor: Colors.orange,
                    ),
                  );
                }
              }
            },
            child: Text(
              'رفض وحذف',
              style: GoogleFonts.cairo(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yassincil/models/medication_reminder.dart';

class ReminderProvider with ChangeNotifier {
  static const _remindersKey = 'medication_reminders';
  List<MedicationReminder> _reminders = [];

  List<MedicationReminder> get reminders => _reminders;

  ReminderProvider() {
    loadReminders();
  }

  Future<void> loadReminders() async {
    final prefs = await SharedPreferences.getInstance();
    final remindersString = prefs.getString(_remindersKey);
    if (remindersString != null) {
      final List<dynamic> remindersJson = jsonDecode(remindersString);
      _reminders = remindersJson
          .map((json) => MedicationReminder.fromJson(json))
          .toList();
      notifyListeners();
    }
  }

  Future<void> _saveReminders() async {
    final prefs = await SharedPreferences.getInstance();
    final remindersJson = _reminders
        .map((reminder) => reminder.toJson())
        .toList();
    await prefs.setString(_remindersKey, jsonEncode(remindersJson));
  }

  Future<void> addReminder(MedicationReminder reminder) async {
    // Ensure the ID is unique. A simple timestamp-based ID.
    final newReminder = MedicationReminder(
      id: DateTime.now().millisecondsSinceEpoch,
      medicationId: reminder.medicationId,
      medicationName: reminder.medicationName,
      medicationDosage: reminder.medicationDosage,
      time: reminder.time,
      isActive: reminder.isActive,
    );

    _reminders.add(newReminder);
    await _saveReminders();

    // TODO: Call NotificationService to schedule the notification

    notifyListeners();
  }

  Future<void> deleteReminder(int reminderId) async {
    _reminders.removeWhere((reminder) => reminder.id == reminderId);
    await _saveReminders();

    // TODO: Call NotificationService to cancel the notification

    notifyListeners();
  }

  Future<void> updateReminder(MedicationReminder updatedReminder) async {
    final index = _reminders.indexWhere((r) => r.id == updatedReminder.id);
    if (index != -1) {
      _reminders[index] = updatedReminder;
      await _saveReminders();

      // TODO: Cancel the old notification and schedule a new one

      notifyListeners();
    }
  }
}

import 'package:flutter/material.dart';
import 'package:yassincil/models/user_profile.dart';
import 'package:yassincil/services/auth_service.dart';

class UserManagementProvider extends ChangeNotifier {
  final AuthService _authService;
  List<UserProfile> _users = [];
  bool _isLoading = true;
  String? _errorMessage;

  UserManagementProvider(this._authService) {
    _authService.getAllUserProfiles().listen(
      (users) {
        _users = users;
        _isLoading = false;
        _errorMessage = null;
        notifyListeners();
      },
      onError: (error) {
        _errorMessage = 'حدث خطأ أثناء جلب المستخدمين: $error';
        _isLoading = false;
        notifyListeners();
      },
    );
  }

  List<UserProfile> get users => _users;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  Future<void> updateUserRole(String uid, String newRole) async {
    _isLoading = true;
    notifyListeners();
    try {
      await _authService.updateUserRole(uid, newRole);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = 'فشل تحديث الدور: e.toString()}';
      _isLoading = false;
      notifyListeners();
      rethrow;
    }
  }

  Future<void> deleteUser(String uid) async {
    _isLoading = true;
    notifyListeners();
    try {
      await _authService.deleteUserFirestoreProfile(uid);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = 'فشل حذف المستخدم: e.toString()}';
      _isLoading = false;
      notifyListeners();
      rethrow;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../../providers/safe_store_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/safe_store.dart';
import '../../utils/app_colors.dart';

class AddEditSafeStoreScreen extends StatefulWidget {
  final SafeStore? store; // null for add, non-null for edit

  const AddEditSafeStoreScreen({super.key, this.store});

  @override
  State<AddEditSafeStoreScreen> createState() => _AddEditSafeStoreScreenState();
}

class _AddEditSafeStoreScreenState extends State<AddEditSafeStoreScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  final _phoneController = TextEditingController();
  final _websiteController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _openingHoursController = TextEditingController();
  final _productsController = TextEditingController();
  final _specialServicesController = TextEditingController();

  String _selectedCategory = 'سوبر ماركت';
  bool _hasGlutenFreeSection = true;
  bool _isOpen = true;
  bool _hasDelivery = false;
  bool _acceptsInsurance = false;
  bool _isFeatured = false;
  bool _isVerified = false;
  double? _latitude;
  double? _longitude;
  File? _selectedImage;
  bool _isLoading = false;

  final List<String> _categories = [
    'سوبر ماركت',
    'متاجر صحية',
    'مخابز خاصة',
    'متاجر أونلاين',
    'صيدليات',
    'مطاعم',
    'كافيهات',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.store != null) {
      _populateFields();
    }
  }

  void _populateFields() {
    final store = widget.store!;
    _nameController.text = store.name;
    _descriptionController.text = store.description;
    _locationController.text = store.location;
    _phoneController.text = store.phone;
    _websiteController.text = store.website ?? '';
    _emailController.text = store.email ?? '';
    _addressController.text = store.address ?? '';
    _openingHoursController.text = store.openingHours;
    _productsController.text = store.products.join(', ');
    _specialServicesController.text = store.specialServices.join(', ');

    _selectedCategory = store.category;
    _hasGlutenFreeSection = store.hasGlutenFreeSection;
    _isOpen = store.isOpen;
    _hasDelivery = store.hasDelivery;
    _acceptsInsurance = store.acceptsInsurance;
    _isFeatured = store.isFeatured;
    _isVerified = store.isVerified;
    _latitude = store.latitude;
    _longitude = store.longitude;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _phoneController.dispose();
    _websiteController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _openingHoursController.dispose();
    _productsController.dispose();
    _specialServicesController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      setState(() {
        _selectedImage = File(pickedFile.path);
      });
    }
  }

  Future<void> _saveStore() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final safeStoreProvider = Provider.of<SafeStoreProvider>(
        context,
        listen: false,
      );

      if (authProvider.currentUser == null) {
        _showErrorSnackBar('يجب تسجيل الدخول أولاً');
        return;
      }

      final products = _productsController.text
          .split(',')
          .map((e) => e.trim())
          .where((e) => e.isNotEmpty)
          .toList();

      final specialServices = _specialServicesController.text
          .split(',')
          .map((e) => e.trim())
          .where((e) => e.isNotEmpty)
          .toList();

      final store = SafeStore(
        id: widget.store?.id,
        name: _nameController.text.trim(),
        category: _selectedCategory,
        description: _descriptionController.text.trim(),
        location: _locationController.text.trim(),
        phone: _phoneController.text.trim(),
        hasGlutenFreeSection: _hasGlutenFreeSection,
        rating: widget.store?.rating ?? 0.0,
        products: products,
        website: _websiteController.text.trim().isEmpty
            ? null
            : _websiteController.text.trim(),
        image: widget.store?.image, // Will be updated if new image is selected
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        address: _addressController.text.trim().isEmpty
            ? null
            : _addressController.text.trim(),
        latitude: _latitude,
        longitude: _longitude,
        isOpen: _isOpen,
        openingHours: _openingHoursController.text.trim().isEmpty
            ? '24 ساعة'
            : _openingHoursController.text.trim(),
        hasDelivery: _hasDelivery,
        acceptsInsurance: _acceptsInsurance,
        specialServices: specialServices,
        createdAt: widget.store?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
        isFeatured: _isFeatured,
        isVerified: _isVerified,
        likesCount: widget.store?.likesCount ?? 0,
        reviewsCount: widget.store?.reviewsCount ?? 0,
        visitsCount: widget.store?.visitsCount ?? 0,
      );

      bool success;
      if (widget.store == null) {
        success = await safeStoreProvider.addStore(store);
      } else {
        success = await safeStoreProvider.updateStore(store);
      }

      if (success) {
        if (mounted) {
          final navigator = Navigator.of(context);
          final scaffoldMessenger = ScaffoldMessenger.of(context);
          navigator.pop();
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(
                widget.store == null
                    ? 'تم إضافة المتجر بنجاح'
                    : 'تم تحديث المتجر بنجاح',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        _showErrorSnackBar('حدث خطأ أثناء حفظ المتجر');
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ غير متوقع');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.cairo()),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          widget.store == null ? 'إضافة متجر آمن' : 'تعديل المتجر',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveStore,
              child: Text(
                'حفظ',
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(),
              const SizedBox(height: 24),
              _buildContactInfoSection(),
              const SizedBox(height: 24),
              _buildProductsSection(),
              const SizedBox(height: 24),
              _buildSettingsSection(),
              const SizedBox(height: 24),
              _buildImageSection(),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return _buildSection(
      title: 'المعلومات الأساسية',
      icon: Icons.info_outline,
      children: [
        _buildTextField(
          controller: _nameController,
          label: 'اسم المتجر',
          hint: 'أدخل اسم المتجر',
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'اسم المتجر مطلوب';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        _buildDropdownField(
          label: 'الفئة',
          value: _selectedCategory,
          items: _categories,
          onChanged: (value) {
            setState(() {
              _selectedCategory = value!;
            });
          },
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _descriptionController,
          label: 'الوصف',
          hint: 'أدخل وصف المتجر',
          maxLines: 3,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'الوصف مطلوب';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _locationController,
          label: 'الموقع',
          hint: 'أدخل موقع المتجر',
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'الموقع مطلوب';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _addressController,
          label: 'العنوان التفصيلي',
          hint: 'أدخل العنوان التفصيلي (اختياري)',
          maxLines: 2,
        ),
      ],
    );
  }

  Widget _buildContactInfoSection() {
    return _buildSection(
      title: 'معلومات الاتصال',
      icon: Icons.contact_phone,
      children: [
        _buildTextField(
          controller: _phoneController,
          label: 'رقم الهاتف',
          hint: 'أدخل رقم الهاتف',
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'رقم الهاتف مطلوب';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _emailController,
          label: 'البريد الإلكتروني',
          hint: 'أدخل البريد الإلكتروني (اختياري)',
          keyboardType: TextInputType.emailAddress,
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _websiteController,
          label: 'الموقع الإلكتروني',
          hint: 'أدخل رابط الموقع الإلكتروني (اختياري)',
          keyboardType: TextInputType.url,
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _openingHoursController,
          label: 'ساعات العمل',
          hint: 'مثال: 8:00 ص - 10:00 م',
        ),
      ],
    );
  }

  Widget _buildProductsSection() {
    return _buildSection(
      title: 'المنتجات والخدمات',
      icon: Icons.shopping_basket,
      children: [
        _buildTextField(
          controller: _productsController,
          label: 'المنتجات',
          hint: 'أدخل المنتجات مفصولة بفاصلة',
          maxLines: 3,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'المنتجات مطلوبة';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _specialServicesController,
          label: 'الخدمات الخاصة',
          hint: 'أدخل الخدمات الخاصة مفصولة بفاصلة (اختياري)',
          maxLines: 2,
        ),
      ],
    );
  }

  Widget _buildSettingsSection() {
    return _buildSection(
      title: 'الإعدادات',
      icon: Icons.settings,
      children: [
        _buildSwitchTile(
          title: 'يحتوي على قسم خالي من الجلوتين',
          value: _hasGlutenFreeSection,
          onChanged: (value) {
            setState(() {
              _hasGlutenFreeSection = value;
            });
          },
        ),
        _buildSwitchTile(
          title: 'مفتوح حالياً',
          value: _isOpen,
          onChanged: (value) {
            setState(() {
              _isOpen = value;
            });
          },
        ),
        _buildSwitchTile(
          title: 'يوفر خدمة التوصيل',
          value: _hasDelivery,
          onChanged: (value) {
            setState(() {
              _hasDelivery = value;
            });
          },
        ),
        _buildSwitchTile(
          title: 'يقبل التأمين الطبي',
          value: _acceptsInsurance,
          onChanged: (value) {
            setState(() {
              _acceptsInsurance = value;
            });
          },
        ),
        _buildSwitchTile(
          title: 'متجر مميز',
          value: _isFeatured,
          onChanged: (value) {
            setState(() {
              _isFeatured = value;
            });
          },
        ),
        _buildSwitchTile(
          title: 'متجر موثق',
          value: _isVerified,
          onChanged: (value) {
            setState(() {
              _isVerified = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildImageSection() {
    return _buildSection(
      title: 'صورة المتجر',
      icon: Icons.image,
      children: [
        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: _selectedImage != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.file(_selectedImage!, fit: BoxFit.cover),
                )
              : widget.store?.image != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    widget.store!.image!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return _buildImagePlaceholder();
                    },
                  ),
                )
              : _buildImagePlaceholder(),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _pickImage,
            icon: const Icon(Icons.camera_alt),
            label: Text('اختيار صورة', style: GoogleFonts.cairo()),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              side: BorderSide(color: AppColors.primary),
              foregroundColor: AppColors.primary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImagePlaceholder() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.image, size: 64, color: Colors.grey.shade400),
        const SizedBox(height: 8),
        Text(
          'لا توجد صورة',
          style: GoogleFonts.cairo(color: Colors.grey.shade600, fontSize: 16),
        ),
      ],
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: AppColors.primary, size: 20),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
      style: GoogleFonts.cairo(),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        labelStyle: GoogleFonts.cairo(color: Colors.grey.shade700),
        hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String value,
    required List<String> items,
    required void Function(String?) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      onChanged: onChanged,
      items: items.map((item) {
        return DropdownMenuItem<String>(
          value: item,
          child: Text(item, style: GoogleFonts.cairo()),
        );
      }).toList(),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: GoogleFonts.cairo(color: Colors.grey.shade700),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),
      style: GoogleFonts.cairo(),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required bool value,
    required void Function(bool) onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: Colors.grey.shade800,
              ),
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../providers/safe_store_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/safe_store.dart';
import '../../models/comment.dart';
import '../../utils/app_colors.dart';
import '../../widgets/advanced_safe_store_comment_widget.dart';
import '../../widgets/add_safe_store_comment_widget.dart';

class SafeStoreDetailScreen extends StatefulWidget {
  final SafeStore store;

  const SafeStoreDetailScreen({super.key, required this.store});

  @override
  State<SafeStoreDetailScreen> createState() => _SafeStoreDetailScreenState();
}

class _SafeStoreDetailScreenState extends State<SafeStoreDetailScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLiked = false;
  bool _isFavorite = false;
  double _userRating = 0.0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Fetch comments and check if liked
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadStoreData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadStoreData() async {
    final safeStoreProvider = Provider.of<SafeStoreProvider>(
      context,
      listen: false,
    );
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // Fetch comments
    await safeStoreProvider.fetchStoreComments(widget.store.id!);

    // Check if user liked this store
    if (authProvider.currentUser != null) {
      final isLiked = await safeStoreProvider.isStoreLiked(
        widget.store.id!,
        authProvider.currentUser!.uid,
      );
      setState(() {
        _isLiked = isLiked;
      });
    }

    // Increment visits count
    await safeStoreProvider.incrementStoreVisits(widget.store.id!);
  }

  /// عرض معاينة الصورة في شاشة كاملة
  void _showImagePreview(String imageUrl) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            // خلفية شفافة قابلة للنقر للإغلاق
            GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.black.withOpacity(0.8),
              ),
            ),
            // الصورة
            Center(
              child: Container(
                margin: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.5),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: InteractiveViewer(
                    panEnabled: true,
                    boundaryMargin: const EdgeInsets.all(20),
                    minScale: 0.5,
                    maxScale: 4.0,
                    child: Image.network(
                      imageUrl,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 300,
                          height: 300,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade800,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.error_outline,
                                size: 60,
                                color: Colors.grey.shade400,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'فشل في تحميل الصورة',
                                style: GoogleFonts.cairo(
                                  color: Colors.grey.shade400,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
            // زر الإغلاق
            Positioned(
              top: 50,
              right: 20,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white, size: 24),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final safeStoreProvider = Provider.of<SafeStoreProvider>(context);
    final isAdmin = authProvider.isAdmin;

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: CustomScrollView(
        slivers: [
          _buildModernSliverAppBar(isAdmin),
          SliverToBoxAdapter(
            child: Column(
              children: [
                _buildModernStoreInfo(),
                _buildModernInteractionSection(
                  safeStoreProvider,
                  authProvider.currentUser?.uid,
                ),
                _buildModernCommentsSection(
                  safeStoreProvider,
                  authProvider.currentUser?.uid,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernSliverAppBar(bool isAdmin) {
    return SliverAppBar(
      expandedHeight: 350,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF00BFA5), Color(0xFF00796B), Color(0xFF004D40)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(35),
              bottomRight: Radius.circular(35),
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF00BFA5).withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Stack(
            children: [
              // Background pattern
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(35),
                      bottomRight: Radius.circular(35),
                    ),
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withOpacity(0.15),
                        Colors.transparent,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                ),
              ),
              // Decorative circles
              Positioned(
                top: -50,
                right: -30,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.1),
                  ),
                ),
              ),
              Positioned(
                bottom: -40,
                left: -20,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.08),
                  ),
                ),
              ),
              // Content
              Positioned(
                bottom: 60,
                left: 20,
                right: 20,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Store image/icon - أكبر مع إمكانية المعاينة
                    GestureDetector(
                      onTap: () {
                        if (widget.store.image != null &&
                            widget.store.image!.isNotEmpty) {
                          _showImagePreview(widget.store.image!);
                        }
                      },
                      child: Container(
                        width: 140,
                        height: 140,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.white.withOpacity(0.3),
                              Colors.white.withOpacity(0.1),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(30),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.4),
                            width: 3,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.white.withOpacity(0.2),
                              blurRadius: 15,
                              offset: const Offset(0, 5),
                            ),
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(22),
                          child: widget.store.image != null &&
                                  widget.store.image!.isNotEmpty
                              ? Stack(
                                  children: [
                                    Image.network(
                                      widget.store.image!,
                                      width: double.infinity,
                                      height: double.infinity,
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                        return const Icon(
                                          Icons.store_rounded,
                                          size: 60,
                                          color: Colors.white,
                                        );
                                      },
                                      loadingBuilder:
                                          (context, child, loadingProgress) {
                                        if (loadingProgress == null) {
                                          return child;
                                        }
                                        return const Center(
                                          child: SizedBox(
                                            width: 40,
                                            height: 40,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 3,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                Colors.white,
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                    // أيقونة المعاينة
                                    Positioned(
                                      bottom: 8,
                                      right: 8,
                                      child: Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          color: Colors.black.withOpacity(
                                            0.6,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        child: const Icon(
                                          Icons.zoom_in,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              : const Icon(
                                  Icons.store_rounded,
                                  size: 60,
                                  color: Colors.white,
                                ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Store name
                    Text(
                      widget.store.name,
                      style: GoogleFonts.cairo(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 8.0,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Category name
                    Text(
                      widget.store.category,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        color: Colors.white.withOpacity(0.9),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      actions: [
        // زر المفضلة
        Container(
          margin: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.white.withOpacity(0.3),
                Colors.white.withOpacity(0.1),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: Colors.white.withOpacity(0.4),
              width: 1.5,
            ),
          ),
          child: IconButton(
            icon: Icon(
              _isFavorite ? Icons.favorite : Icons.favorite_border,
              color: Colors.white,
              size: 22,
            ),
            onPressed: _toggleFavorite,
          ),
        ),
        // زر المشاركة
        Container(
          margin: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.white.withOpacity(0.3),
                Colors.white.withOpacity(0.1),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: Colors.white.withOpacity(0.4),
              width: 1.5,
            ),
          ),
          child: IconButton(
            icon: const Icon(
              Icons.share_rounded,
              color: Colors.white,
              size: 22,
            ),
            onPressed: _shareStore,
          ),
        ),
        if (isAdmin)
          Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.white.withOpacity(0.3),
                  Colors.white.withOpacity(0.1),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: Colors.white.withOpacity(0.4),
                width: 1.5,
              ),
            ),
            child: IconButton(
              icon: const Icon(
                Icons.edit_rounded,
                color: Colors.white,
                size: 22,
              ),
              onPressed: () {
                // Navigator.of(context).push(
                //   MaterialPageRoute(
                //     builder: (context) =>
                //         AddEditSafeStoreScreen(store: widget.store),
                //   ),
                // );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildModernStoreInfo() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Status Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: widget.store.isOpen
                    ? [const Color(0xFF4CAF50), const Color(0xFF388E3C)]
                    : [const Color(0xFFE53E3E), const Color(0xFFC53030)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: (widget.store.isOpen
                          ? const Color(0xFF4CAF50)
                          : const Color(0xFFE53E3E))
                      .withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Icon(
                    widget.store.isOpen
                        ? Icons.store_mall_directory_rounded
                        : Icons.store_mall_directory_outlined,
                    size: 30,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.store.isOpen ? 'مفتوح الآن' : 'مغلق',
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.store.openingHours,
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // Information Cards
          _buildModernInfoCard(
            Icons.location_on_rounded,
            'الموقع',
            widget.store.location,
            const [Color(0xFF6366F1), Color(0xFF4F46E5)],
          ),
          const SizedBox(height: 16),

          _buildModernInfoCard(
            Icons.phone_rounded,
            'الهاتف',
            widget.store.phone,
            const [Color(0xFF8B5CF6), Color(0xFF7C3AED)],
          ),
          const SizedBox(height: 16),

          if (widget.store.website != null &&
              widget.store.website!.isNotEmpty) ...[
            _buildModernInfoCard(
              Icons.language_rounded,
              'الموقع الإلكتروني',
              widget.store.website!,
              const [Color(0xFFF59E0B), Color(0xFFD97706)],
            ),
            const SizedBox(height: 16),
          ],

          if (widget.store.products.isNotEmpty) ...[
            _buildModernInfoCard(
              Icons.inventory_2_rounded,
              'المنتجات',
              widget.store.products.join(', '),
              const [Color(0xFF10B981), Color(0xFF059669)],
            ),
            const SizedBox(height: 16),
          ],

          if (widget.store.description.isNotEmpty) ...[
            _buildModernInfoCard(
              Icons.note_rounded,
              'الوصف',
              widget.store.description,
              const [Color(0xFFEC4899), Color(0xFFDB2777)],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildModernInfoCard(
    IconData icon,
    String title,
    String content,
    List<Color> gradientColors,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: gradientColors,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: Colors.white, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF1F2937),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            content,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: const Color(0xFF6B7280),
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernInteractionSection(
    SafeStoreProvider safeStoreProvider,
    String? currentUserId,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            // الصف الأول - الأزرار الأساسية
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildModernActionButton(
                  icon: Icons.favorite_rounded,
                  label: 'إعجاب',
                  count: widget.store.likesCount,
                  isActive: _isLiked,
                  onTap: () => _handleLike(safeStoreProvider, currentUserId),
                  activeColor: const Color(0xFFE53E3E),
                  inactiveColor: const Color(0xFF6B7280),
                ),
                _buildModernActionButton(
                  icon: Icons.comment_rounded,
                  label: 'تعليق',
                  count: widget.store.commentsCount,
                  isActive: false,
                  onTap: () => _scrollToComments(),
                  activeColor: const Color(0xFF3B82F6),
                  inactiveColor: const Color(0xFF6B7280),
                ),
                _buildModernActionButton(
                  icon: Icons.share_rounded,
                  label: 'مشاركة',
                  count: 0,
                  isActive: false,
                  onTap: _shareStore,
                  activeColor: const Color(0xFF10B981),
                  inactiveColor: const Color(0xFF6B7280),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // الصف الثاني - التقييم والمفضلة
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildModernActionButton(
                  icon: Icons.star_rounded,
                  label: 'تقييم',
                  count: _userRating.toInt(),
                  isActive: _userRating > 0,
                  onTap: _showRatingDialog,
                  activeColor: const Color(0xFFFBBF24),
                  inactiveColor: const Color(0xFF6B7280),
                ),
                _buildModernActionButton(
                  icon: _isFavorite ? Icons.bookmark : Icons.bookmark_border,
                  label: 'مفضلة',
                  count: 0,
                  isActive: _isFavorite,
                  onTap: _toggleFavorite,
                  activeColor: const Color(0xFF8B5CF6),
                  inactiveColor: const Color(0xFF6B7280),
                ),
                _buildModernActionButton(
                  icon: Icons.info_outline_rounded,
                  label: 'تفاصيل',
                  count: 0,
                  isActive: false,
                  onTap: _showStoreDetails,
                  activeColor: const Color(0xFF00BFA5),
                  inactiveColor: const Color(0xFF6B7280),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernActionButton({
    required IconData icon,
    required String label,
    required int count,
    required bool isActive,
    required VoidCallback onTap,
    required Color activeColor,
    required Color inactiveColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isActive
              ? activeColor.withOpacity(0.1)
              : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isActive
                ? activeColor.withOpacity(0.3)
                : Colors.grey.shade200,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: isActive ? activeColor : inactiveColor, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: isActive ? activeColor : inactiveColor,
              ),
            ),
            if (count > 0) ...[
              const SizedBox(height: 2),
              Text(
                count.toString(),
                style: GoogleFonts.cairo(
                  fontSize: 11,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _handleLike(
    SafeStoreProvider safeStoreProvider,
    String? currentUserId,
  ) {
    if (currentUserId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يجب تسجيل الدخول أولاً', style: GoogleFonts.cairo()),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    safeStoreProvider.toggleStoreLike(
      widget.store.id!,
      currentUserId,
    );
  }

  void _scrollToComments() {
    // يمكن إضافة منطق للتمرير إلى قسم التعليقات
  }

  Widget _buildModernCommentsSection(
    SafeStoreProvider safeStoreProvider,
    String? currentUserId,
  ) {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Comments Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: const [Color(0xFF6366F1), Color(0xFF4F46E5)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.comment_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'التعليقات والمراجعات',
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        '${widget.store.commentsCount} تعليق',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // Add Comment Widget
          if (currentUserId != null)
            Container(
              margin: const EdgeInsets.only(bottom: 20),
              child: AddSafeStoreCommentWidget(
                store: widget.store,
                onCommentAdded: () {
                  safeStoreProvider.fetchStoreComments(widget.store.id!);
                },
              ),
            ),

          // Comments List
          StreamBuilder<List<Comment>>(
            stream: safeStoreProvider.getCommentsForStore(
              widget.store.id!,
            ),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Container(
                  padding: const EdgeInsets.all(40),
                  child: const Center(child: CircularProgressIndicator()),
                );
              }

              if (snapshot.hasError) {
                return Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'خطأ في تحميل التعليقات: ${snapshot.error}',
                    style: GoogleFonts.cairo(color: Colors.red.shade700),
                  ),
                );
              }

              final comments = snapshot.data ?? [];

              if (comments.isEmpty) {
                return Container(
                  padding: const EdgeInsets.all(40),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.comment_outlined,
                        size: 48,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد تعليقات بعد',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'كن أول من يشارك تجربته مع هذا المتجر',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey.shade500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                );
              }

              return Column(
                children: comments.map((comment) {
                  return Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: AdvancedSafeStoreCommentWidget(
                      comment: comment,
                      store: widget.store,
                      onReply: () => _showReplyDialog(comment),
                    ),
                  );
                }).toList(),
              );
            },
          ),
        ],
      ),
    );
  }

  void _showReplyDialog(Comment parentComment) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.75,
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.textSecondary.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // عنوان الحوار
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'الرد على ${parentComment.username}',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ),

            // التعليق الأصلي
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.background,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    parentComment.username,
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    parentComment.content,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),

            // widget إضافة الرد
            Expanded(
              child: AddSafeStoreCommentWidget(
                store: widget.store,
                parentComment: parentComment,
                onCommentAdded: () {
                  Navigator.pop(context);
                  setState(() {});
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _shareStore() async {
    try {
      final shareText =
          '''
🛍️ ${widget.store.name}

📍 الموقع: ${widget.store.location}
⭐ التقييم: ${widget.store.rating}
${widget.store.isOpen ? '✅ مفتوح الآن' : '⚠️ مغلق'}

تطبيق ياسين سيل - دليلك للمتاجر الآمنة
      ''';

      await Share.share(shareText);
    } catch (e) {
      debugPrint('خطأ في المشاركة: $e');
    }
  }

  /// تبديل حالة المفضلة
  void _toggleFavorite() {
    setState(() {
      _isFavorite = !_isFavorite;
    });

    // TODO: حفظ حالة المفضلة في قاعدة البيانات
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _isFavorite
              ? 'تم إضافة المتجر للمفضلة'
              : 'تم إزالة المتجر من المفضلة',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: _isFavorite ? Colors.green : Colors.grey,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// عرض حوار التقييم
  void _showRatingDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تقييم المتجر',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('ما تقييمك لهذا المتجر؟', style: GoogleFonts.cairo()),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(5, (index) {
                return IconButton(
                  onPressed: () {
                    setState(() {
                      _userRating = index + 1.0;
                    });
                    Navigator.of(context).pop();
                    // TODO: حفظ التقييم في قاعدة البيانات
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'تم حفظ تقييمك: ${_userRating.toInt()} نجوم',
                          style: GoogleFonts.cairo(),
                        ),
                        backgroundColor: Colors.green,
                      ),
                    );
                  },
                  icon: Icon(
                    index < _userRating ? Icons.star : Icons.star_border,
                    color: Colors.amber,
                    size: 30,
                  ),
                );
              }),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  /// عرض تفاصيل إضافية عن المتجر
  void _showStoreDetails() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 50,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(3),
              ),
            ),
            // العنوان
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF00BFA5), Color(0xFF00796B)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: const Icon(
                      Icons.store_rounded,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'تفاصيل المتجر',
                          style: GoogleFonts.cairo(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF1F2937),
                          ),
                        ),
                        Text(
                          'معلومات شاملة ومفصلة',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close_rounded),
                  ),
                ],
              ),
            ),
            // المحتوى
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailSection('معلومات أساسية', Icons.info_rounded, [
                      _buildDetailItem('اسم المتجر', widget.store.name),
                      _buildDetailItem(
                        'الفئة',
                        widget.store.category,
                      ),
                      _buildDetailItem(
                        'الموقع',
                        widget.store.location,
                      ),
                      _buildDetailItem('الهاتف', widget.store.phone),
                    ]),
                    const SizedBox(height: 20),
                    _buildDetailSection(
                      'معلومات إضافية',
                      Icons.more_horiz_rounded,
                      [
                        _buildDetailItem(
                          'ساعات العمل',
                          widget.store.openingHours,
                        ),
                        if (widget.store.website != null &&
                            widget.store.website!.isNotEmpty)
                          _buildDetailItem(
                            'الموقع الإلكتروني',
                            widget.store.website!,
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailSection(String title, IconData icon, List<Widget> items) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: const Color(0xFF00BFA5), size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...items,
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: valueColor ?? const Color(0xFF1F2937),
                fontWeight: valueColor != null
                    ? FontWeight.w600
                    : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter_test/flutter_test.dart';
import 'package:yassincil/models/comment.dart';

void main() {
  group('Comment Model Tests', () {
    test('should create a comment with required fields', () {
      // Arrange
      final comment = Comment(
        postId: 'post-1',
        content: 'تعليق تجريبي',
        userId: 'user-1',
        username: 'مستخدم تجريبي',
        createdAt: DateTime.now(),
      );

      // Assert
      expect(comment.postId, 'post-1');
      expect(comment.content, 'تعليق تجريبي');
      expect(comment.userId, 'user-1');
      expect(comment.username, 'مستخدم تجريبي');
      expect(comment.createdAt, isA<DateTime>());
    });

    test('should create comment with default values', () {
      // Arrange
      final comment = Comment(
        postId: 'post-1',
        content: 'تعليق تجريبي',
        userId: 'user-1',
        username: 'مستخدم تجريبي',
        createdAt: DateTime.now(),
      );

      // Assert
      expect(comment.imageUrls, isEmpty);
      expect(comment.likesCount, 0);
      expect(comment.repliesCount, 0);
      expect(comment.isEdited, false);
      expect(comment.isDeleted, false);
      expect(comment.parentCommentId, isNull);
      expect(comment.userAvatar, isNull);
      expect(comment.updatedAt, isNull);
    });

    test('should create comment with optional fields', () {
      // Arrange
      final comment = Comment(
        postId: 'post-1',
        content: 'تعليق تجريبي',
        userId: 'user-1',
        username: 'مستخدم تجريبي',
        userAvatar: 'https://example.com/avatar.jpg',
        createdAt: DateTime.now(),
        imageUrls: ['https://example.com/image.jpg'],
        likesCount: 2,
        repliesCount: 1,
        parentCommentId: 'parent-comment-id',
        isEdited: true,
      );

      // Assert
      expect(comment.userAvatar, 'https://example.com/avatar.jpg');
      expect(comment.imageUrls.length, 1);
      expect(comment.likesCount, 2);
      expect(comment.repliesCount, 1);
      expect(comment.parentCommentId, 'parent-comment-id');
      expect(comment.isEdited, true);
    });

    test('should copy comment with new values', () {
      // Arrange
      final originalComment = Comment(
        postId: 'post-1',
        content: 'تعليق أصلي',
        userId: 'user-1',
        username: 'مستخدم تجريبي',
        createdAt: DateTime.now(),
        likesCount: 5,
      );

      // Act
      final copiedComment = originalComment.copyWith(
        content: 'تعليق محدث',
        likesCount: 10,
        isEdited: true,
      );

      // Assert
      expect(copiedComment.content, 'تعليق محدث');
      expect(copiedComment.likesCount, 10);
      expect(copiedComment.isEdited, true);
      expect(copiedComment.userId, originalComment.userId);
      expect(copiedComment.username, originalComment.username);
      expect(copiedComment.createdAt, originalComment.createdAt);
    });

    test('should convert to map correctly', () {
      // Arrange
      final comment = Comment(
        postId: 'post-1',
        content: 'تعليق تجريبي',
        userId: 'user-1',
        username: 'مستخدم تجريبي',
        createdAt: DateTime.now(),
        likesCount: 3,
        repliesCount: 1,
      );

      // Act
      final map = comment.toMap();

      // Assert
      expect(map['content'], 'تعليق تجريبي');
      expect(map['userId'], 'user-1');
      expect(map['username'], 'مستخدم تجريبي');
      expect(map['likesCount'], 3);
      expect(map['repliesCount'], 1);
      expect(map['isEdited'], false);
      expect(map['isDeleted'], false);
      expect(map['createdAt'], isA<dynamic>());
    });
  });
}
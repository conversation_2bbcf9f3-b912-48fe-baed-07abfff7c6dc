import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:yassincil/providers/medication_provider.dart';
import 'package:yassincil/providers/food_provider.dart';
import 'package:yassincil/providers/recipe_provider.dart';
import 'package:yassincil/providers/restaurant_provider.dart';
import 'package:yassincil/providers/doctor_provider.dart';
import 'package:yassincil/screens/barcode/barcode_scanner_screen.dart';
import 'package:yassincil/screens/medications/medication_detail_screen.dart';
import 'package:yassincil/screens/foods/food_detail_screen.dart';
import 'package:yassincil/screens/recipes/recipe_detail_screen.dart';
import 'package:yassincil/screens/restaurants/restaurant_detail_screen_enhanced.dart';

class UnifiedSearchScreen extends StatefulWidget {
  const UnifiedSearchScreen({super.key});

  @override
  State<UnifiedSearchScreen> createState() => _UnifiedSearchScreenState();
}

class _UnifiedSearchScreenState extends State<UnifiedSearchScreen>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late TabController _tabController;
  late FocusNode _searchFocusNode;

  String _searchQuery = '';
  bool _isSearching = false;

  final List<String> _searchCategories = [
    'الكل',
    'الأدوية',
    'الأطعمة',
    'الوصفات',
    'المطاعم',
    'الأطباء',
  ];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _tabController = TabController(
      length: _searchCategories.length,
      vsync: this,
    );
    _searchFocusNode = FocusNode();

    // Focus on search field when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: CustomScrollView(
        slivers: [
          _buildModernSliverAppBar(),
          SliverToBoxAdapter(
            child: Column(
              children: [
                _buildModernSearchHeader(),
                _buildModernQuickActions(),
                _buildModernCategoryTabs(),
              ],
            ),
          ),
          _buildModernSearchResults(),
        ],
      ),
    );
  }

  Widget _buildModernSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: const [Color(0xFF9C27B0), Color(0xFFBA68C8)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(30),
              bottomRight: Radius.circular(30),
            ),
          ),
          child: Stack(
            children: [
              // Background pattern
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(30),
                      bottomRight: Radius.circular(30),
                    ),
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withValues(alpha: 0.1),
                        Colors.transparent,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                ),
              ),
              // Content
              Positioned(
                bottom: 60,
                left: 20,
                right: 20,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Search icon
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 2,
                        ),
                      ),
                      child: const Icon(
                        Icons.search_rounded,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Title
                    Text(
                      'البحث الشامل',
                      style: GoogleFonts.cairo(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Subtitle
                    Text(
                      'ابحث في جميع أقسام التطبيق',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(
              Icons.qr_code_scanner_rounded,
              color: Colors.white,
            ),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const BarcodeScannerScreen(),
                ),
              );
            },
            tooltip: 'مسح الباركود',
          ),
        ),
      ],
    );
  }

  Widget _buildModernSearchHeader() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: TextField(
          controller: _searchController,
          focusNode: _searchFocusNode,
          decoration: InputDecoration(
            hintText: 'ابحث في الأدوية، الأطعمة، الوصفات...',
            hintStyle: GoogleFonts.cairo(
              color: Colors.grey.shade500,
              fontSize: 14,
            ),
            prefixIcon: Container(
              margin: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF9C27B0).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.search_rounded,
                color: Color(0xFF9C27B0),
                size: 20,
              ),
            ),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear_rounded),
                    onPressed: () {
                      _searchController.clear();
                      _updateSearchQuery('');
                    },
                  )
                : null,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          style: GoogleFonts.cairo(fontSize: 14),
          onChanged: _updateSearchQuery,
        ),
      ),
    );
  }

  void _updateSearchQuery(String query) {
    setState(() {
      _searchQuery = query;
      _isSearching = query.isNotEmpty;
    });
  }

  Widget _buildModernQuickActions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إجراءات سريعة',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF374151),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildQuickActionCard(
                  'مسح الباركود',
                  Icons.qr_code_scanner_rounded,
                  const Color(0xFF059669),
                  () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const BarcodeScannerScreen(),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickActionCard(
                  'البحث المتقدم',
                  Icons.tune_rounded,
                  const Color(0xFF7C3AED),
                  () {
                    // يمكن إضافة البحث المتقدم هنا
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.2)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF374151),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernCategoryTabs() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Container(
        height: 50,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicator: BoxDecoration(
            color: const Color(0xFF9C27B0),
            borderRadius: BorderRadius.circular(12),
          ),
          labelColor: Colors.white,
          unselectedLabelColor: const Color(0xFF6B7280),
          labelStyle: GoogleFonts.cairo(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          unselectedLabelStyle: GoogleFonts.cairo(
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
          indicatorPadding: const EdgeInsets.all(4),
          tabs: _searchCategories.map((category) {
            return Tab(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(category),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildModernSearchResults() {
    if (!_isSearching) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.search_rounded, size: 80, color: Colors.grey.shade400),
              const SizedBox(height: 16),
              Text(
                'ابدأ البحث',
                style: GoogleFonts.cairo(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'اكتب في شريط البحث للعثور على ما تحتاجه',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey.shade500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return SliverPadding(
      padding: const EdgeInsets.all(20),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              child: _buildSearchResultCard(index),
            );
          },
          childCount: 5, // عدد النتائج الوهمية
        ),
      ),
    );
  }

  Widget _buildSearchResultCard(int index) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: const Color(0xFF9C27B0).withValues(alpha: 0.1),
          child: const Icon(Icons.medication, color: Color(0xFF9C27B0)),
        ),
        title: Text(
          'نتيجة البحث ${index + 1}',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          'وصف النتيجة هنا...',
          style: GoogleFonts.cairo(fontSize: 12),
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          // معالجة النقر على النتيجة
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, color: Colors.purple.shade600, size: 20),
          const SizedBox(width: 8),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMedicationResults({int? limit}) {
    return Consumer<MedicationProvider>(
      builder: (context, medicationProvider, child) {
        final medications = medicationProvider.medications
            .where(
              (med) =>
                  med.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                  med.company.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ) ||
                  med.ingredients.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ),
            )
            .toList();

        final displayMedications = limit != null && medications.length > limit
            ? medications.take(limit).toList()
            : medications;

        if (displayMedications.isEmpty) {
          return _buildEmptyResults('لا توجد أدوية مطابقة');
        }

        return Column(
          children: displayMedications.map((medication) {
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: medication.isAllowed
                      ? Colors.green.shade100
                      : Colors.red.shade100,
                  child: Icon(
                    Icons.medication,
                    color: medication.isAllowed
                        ? Colors.green.shade600
                        : Colors.red.shade600,
                  ),
                ),
                title: Text(
                  medication.name,
                  style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                ),
                subtitle: Text(
                  medication.company,
                  style: GoogleFonts.cairo(fontSize: 12),
                ),
                trailing: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: medication.isAllowed
                        ? Colors.green.shade50
                        : Colors.red.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: medication.isAllowed
                          ? Colors.green.shade300
                          : Colors.red.shade300,
                    ),
                  ),
                  child: Text(
                    medication.isAllowed ? 'مسموح' : 'غير مسموح',
                    style: GoogleFonts.cairo(
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      color: medication.isAllowed
                          ? Colors.green.shade600
                          : Colors.red.shade600,
                    ),
                  ),
                ),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) =>
                          MedicationDetailScreen(medication: medication),
                    ),
                  );
                },
              ),
            );
          }).toList(),
        );
      },
    );
  }

  Widget _buildFoodResults({int? limit}) {
    return Consumer<FoodProvider>(
      builder: (context, foodProvider, child) {
        final foods = foodProvider.foodItems
            .where(
              (food) =>
                  food.name.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ) ||
                  food.category.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ) ||
                  (food.ingredients?.toLowerCase().contains(
                        _searchQuery.toLowerCase(),
                      ) ??
                      false),
            )
            .toList();

        final displayFoods = limit != null && foods.length > limit
            ? foods.take(limit).toList()
            : foods;

        if (displayFoods.isEmpty) {
          return _buildEmptyResults('لا توجد أطعمة مطابقة');
        }

        return Column(
          children: displayFoods.map((food) {
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: food.isGlutenFree
                      ? Colors.green.shade100
                      : Colors.red.shade100,
                  child: Icon(
                    Icons.restaurant_menu,
                    color: food.isGlutenFree
                        ? Colors.green.shade600
                        : Colors.red.shade600,
                  ),
                ),
                title: Text(
                  food.name,
                  style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                ),
                subtitle: Text(
                  food.category,
                  style: GoogleFonts.cairo(fontSize: 12),
                ),
                trailing: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: food.isGlutenFree
                        ? Colors.green.shade50
                        : Colors.red.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: food.isGlutenFree
                          ? Colors.green.shade300
                          : Colors.red.shade300,
                    ),
                  ),
                  child: Text(
                    food.isGlutenFree ? 'خالي من الجلوتين' : 'يحتوي على جلوتين',
                    style: GoogleFonts.cairo(
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      color: food.isGlutenFree
                          ? Colors.green.shade600
                          : Colors.red.shade600,
                    ),
                  ),
                ),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => FoodDetailScreen(foodItem: food),
                    ),
                  );
                },
              ),
            );
          }).toList(),
        );
      },
    );
  }

  Widget _buildRecipeResults({int? limit}) {
    return Consumer<RecipeProvider>(
      builder: (context, recipeProvider, child) {
        final recipes = recipeProvider.recipes
            .where(
              (recipe) =>
                  recipe.title.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ) ||
                  recipe.description.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ) ||
                  recipe.ingredients.any(
                    (ingredient) => ingredient.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ),
                  ),
            )
            .toList();

        final displayRecipes = limit != null && recipes.length > limit
            ? recipes.take(limit).toList()
            : recipes;

        if (displayRecipes.isEmpty) {
          return _buildEmptyResults('لا توجد وصفات مطابقة');
        }

        return Column(
          children: displayRecipes.map((recipe) {
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: recipe.isGlutenFree
                      ? Colors.green.shade100
                      : Colors.orange.shade100,
                  child: Icon(
                    Icons.menu_book,
                    color: recipe.isGlutenFree
                        ? Colors.green.shade600
                        : Colors.orange.shade600,
                  ),
                ),
                title: Text(
                  recipe.title,
                  style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                ),
                subtitle: Text(
                  recipe.description,
                  style: GoogleFonts.cairo(fontSize: 12),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (recipe.isGlutenFree)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green.shade300),
                        ),
                        child: Text(
                          'خالي من الجلوتين',
                          style: GoogleFonts.cairo(
                            fontSize: 8,
                            fontWeight: FontWeight.w600,
                            color: Colors.green.shade600,
                          ),
                        ),
                      ),
                    const SizedBox(width: 8),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.favorite,
                          size: 14,
                          color: Colors.red.shade400,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          '${recipe.likesCount}',
                          style: GoogleFonts.cairo(fontSize: 12),
                        ),
                      ],
                    ),
                  ],
                ),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => RecipeDetailScreen(recipe: recipe),
                    ),
                  );
                },
              ),
            );
          }).toList(),
        );
      },
    );
  }

  Widget _buildRestaurantResults({int? limit}) {
    return Consumer<RestaurantProvider>(
      builder: (context, restaurantProvider, child) {
        final restaurants = restaurantProvider.restaurants
            .where(
              (restaurant) =>
                  restaurant.name.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ) ||
                  restaurant.description.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ) ||
                  restaurant.address.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ),
            )
            .toList();

        final displayRestaurants = limit != null && restaurants.length > limit
            ? restaurants.take(limit).toList()
            : restaurants;

        if (displayRestaurants.isEmpty) {
          return _buildEmptyResults('لا توجد مطاعم مطابقة');
        }

        return Column(
          children: displayRestaurants.map((restaurant) {
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: restaurant.hasGlutenFreeOptions
                      ? Colors.green.shade100
                      : Colors.orange.shade100,
                  child: Icon(
                    Icons.restaurant,
                    color: restaurant.hasGlutenFreeOptions
                        ? Colors.green.shade600
                        : Colors.orange.shade600,
                  ),
                ),
                title: Text(
                  restaurant.name,
                  style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                ),
                subtitle: Text(
                  restaurant.address,
                  style: GoogleFonts.cairo(fontSize: 12),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (restaurant.hasGlutenFreeOptions)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green.shade300),
                        ),
                        child: Text(
                          'خالي من الجلوتين',
                          style: GoogleFonts.cairo(
                            fontSize: 8,
                            fontWeight: FontWeight.w600,
                            color: Colors.green.shade600,
                          ),
                        ),
                      ),
                    const SizedBox(width: 8),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.star,
                          size: 14,
                          color: Colors.amber.shade600,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          (restaurant.rating ?? 0).toStringAsFixed(1),
                          style: GoogleFonts.cairo(fontSize: 12),
                        ),
                      ],
                    ),
                  ],
                ),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => RestaurantDetailScreenEnhanced(
                        restaurant: restaurant,
                      ),
                    ),
                  );
                },
              ),
            );
          }).toList(),
        );
      },
    );
  }

  Widget _buildDoctorResults({int? limit}) {
    return Consumer<DoctorProvider>(
      builder: (context, doctorProvider, child) {
        final doctors = doctorProvider.doctors
            .where(
              (doctor) =>
                  doctor.name.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ) ||
                  doctor.specialty.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ) ||
                  doctor.clinic.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ),
            )
            .toList();

        final displayDoctors = limit != null && doctors.length > limit
            ? doctors.take(limit).toList()
            : doctors;

        if (displayDoctors.isEmpty) {
          return _buildEmptyResults('لا يوجد أطباء مطابقون');
        }

        return Column(
          children: displayDoctors.map((doctor) {
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.teal.shade100,
                  backgroundImage:
                      doctor.imageUrl != null && doctor.imageUrl!.isNotEmpty
                      ? NetworkImage(doctor.imageUrl!)
                      : null,
                  child: doctor.imageUrl == null || doctor.imageUrl!.isEmpty
                      ? Icon(Icons.person, color: Colors.teal.shade600)
                      : null,
                ),
                title: Text(
                  'د. ${doctor.name}',
                  style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      doctor.specialty,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.teal.shade600,
                      ),
                    ),
                    Text(
                      doctor.clinic,
                      style: GoogleFonts.cairo(
                        fontSize: 11,
                        color: Colors.grey.shade600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.star, size: 14, color: Colors.amber.shade600),
                    const SizedBox(width: 2),
                    Text(
                      doctor.rating.toStringAsFixed(1),
                      style: GoogleFonts.cairo(fontSize: 12),
                    ),
                  ],
                ),
                onTap: () {
                  // Navigate to doctor detail screen when available
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'تفاصيل الطبيب قيد التطوير',
                        style: GoogleFonts.cairo(),
                      ),
                      backgroundColor: Colors.orange,
                    ),
                  );
                },
              ),
            );
          }).toList(),
        );
      },
    );
  }

  Widget _buildEmptyResults(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(Icons.search_off, size: 60, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              message,
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

// حالات موافقة الدواء
enum MedicationApprovalStatus {
  pending('pending', 'في انتظار المراجعة', '⏳'),
  approved('approved', 'معتمد', '✅'),
  rejected('rejected', 'مرفوض', '❌'),
  needsRevision('needs_revision', 'يحتاج تعديل', '🔄');

  const MedicationApprovalStatus(this.value, this.arabicName, this.icon);

  final String value;
  final String arabicName;
  final String icon;

  static MedicationApprovalStatus fromString(String value) {
    return MedicationApprovalStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => MedicationApprovalStatus.pending,
    );
  }
}

class Medication {
  final String? id; // Optional, set when reading from Firestore
  final String name;
  final String company;
  final String ingredients;
  final bool isAllowed;
  final String notes;
  final List<String> imageUrls; // قائمة الصور
  final String category; // فئة الدواء
  final double? calories;
  final double? protein;
  final double? carbohydrates;
  final double? fat;
  final List<String>? allergens;
  final List<String> likes; // قائمة معرفات المستخدمين الذين أعجبوا
  final int likesCount;
  final int commentsCount;
  final int ratingsCount; // عدد التقييمات
  final double averageRating; // متوسط التقييمات
  final String? activeIngredient; // المادة الفعالة
  final String? dosage; // الجرعة
  final String? sideEffects; // الآثار الجانبية
  final String? contraindications; // موانع الاستعمال
  final String? interactions; // التفاعلات الدوائية
  final List<String> alternatives; // البدائل الآمنة
  final String? prescriptionRequired; // يحتاج وصفة طبية
  final String? ageGroup; // الفئة العمرية
  final String? pregnancyCategory; // آمن لمرضى السيلياك
  final String? storageConditions; // ظروف التخزين
  final String? expiryDate; // تاريخ الانتهاء
  final String? barcode; // الباركود
  final String? source; // مصدر المعلومات
  final bool isApproved; // هل الدواء معتمد (للتوافق مع الكود القديم)
  final MedicationApprovalStatus approvalStatus; // حالة الموافقة الجديدة
  final String? reviewerComment; // تعليق المراجع
  final String? reviewerId; // معرف المراجع
  final String? reviewerName; // اسم المراجع
  final DateTime? reviewedAt; // تاريخ المراجعة
  final bool isFeatured; // هل الدواء مميز
  final String? userId; // معرف المستخدم الذي أضاف الدواء
  final String? username; // اسم المستخدم الذي أضاف الدواء
  final DateTime createdAt; // تاريخ الإنشاء
  final DateTime? updatedAt; // تاريخ التحديث
  final List<String> tags; // العلامات

  // للتوافق مع الكود القديم
  String? get imageUrl => imageUrls.isNotEmpty ? imageUrls.first : null;

  Medication({
    this.id,
    required this.name,
    required this.company,
    required this.ingredients,
    required this.isAllowed,
    this.notes = '',
    this.imageUrls = const [],
    this.category = 'مسكنات الألم',
    this.calories,
    this.protein,
    this.carbohydrates,
    this.fat,
    this.allergens,
    this.likes = const [],
    this.likesCount = 0,
    this.commentsCount = 0,
    this.ratingsCount = 0,
    this.averageRating = 0.0,
    this.activeIngredient,
    this.dosage,
    this.sideEffects,
    this.contraindications,
    this.interactions,
    this.alternatives = const [],
    this.prescriptionRequired,
    this.ageGroup,
    this.pregnancyCategory,
    this.storageConditions,
    this.expiryDate,
    this.barcode,
    this.source,
    this.isApproved =
        false, // للتوافق مع الكود القديم - الأدوية الجديدة تحتاج موافقة
    this.approvalStatus =
        MedicationApprovalStatus.pending, // الأدوية الجديدة في انتظار المراجعة
    this.reviewerComment,
    this.reviewerId,
    this.reviewerName,
    this.reviewedAt,
    this.isFeatured = false,
    this.userId,
    this.username,
    required this.createdAt,
    this.updatedAt,
    this.tags = const [],
  });

  Map<String, dynamic> toMap({bool forDb = false}) {
    // عندما نخزن في قاعدة البيانات المحلية (SQLite)، نتجنب null بسبب قيود NOT NULL
    return {
      'id': id,
      'name': name,
      'company': company,
      'ingredients': ingredients,
      'isAllowed': forDb ? (isAllowed ? 1 : 0) : isAllowed,
      'notes': notes,
      'imageUrls': forDb ? imageUrls.join(',') : imageUrls,
      'category': category,
      'calories': calories,
      'protein': protein,
      'carbohydrates': carbohydrates,
      'fat': fat,
      'allergens': forDb
          ? (allergens == null ? '' : allergens!.join(','))
          : allergens,
      'likes': forDb ? likes.join(',') : likes,
      'likesCount': likesCount,
      'commentsCount': commentsCount,
      'ratingsCount': ratingsCount,
      'averageRating': averageRating,
      'activeIngredient': forDb ? (activeIngredient ?? '') : activeIngredient,
      'dosage': forDb ? (dosage ?? '') : dosage,
      'sideEffects': forDb ? (sideEffects ?? '') : sideEffects,
      'contraindications': forDb
          ? (contraindications ?? '')
          : contraindications,
      'interactions': forDb ? (interactions ?? '') : interactions,
      'alternatives': forDb ? alternatives.join(',') : alternatives,
      'prescriptionRequired': forDb
          ? (prescriptionRequired ?? '')
          : prescriptionRequired,
      'ageGroup': forDb ? (ageGroup ?? '') : ageGroup,
      'pregnancyCategory': forDb
          ? (pregnancyCategory ?? '')
          : pregnancyCategory,
      'storageConditions': forDb
          ? (storageConditions ?? '')
          : storageConditions,
      'expiryDate': forDb ? (expiryDate ?? '') : expiryDate,
      'barcode': forDb ? (barcode ?? '') : barcode,
      'source': forDb ? (source ?? '') : source,
      'isApproved': forDb ? (isApproved ? 1 : 0) : isApproved,
      'approvalStatus': forDb ? approvalStatus.value : approvalStatus.value,
      'reviewerComment': forDb ? (reviewerComment ?? '') : reviewerComment,
      'reviewerId': forDb ? (reviewerId ?? '') : reviewerId,
      'reviewerName': forDb ? (reviewerName ?? '') : reviewerName,
      'reviewedAt': forDb
          ? (reviewedAt?.toIso8601String() ?? '')
          : (reviewedAt != null ? Timestamp.fromDate(reviewedAt!) : null),
      'isFeatured': forDb ? (isFeatured ? 1 : 0) : isFeatured,
      'userId': forDb ? (userId ?? '') : userId,
      'username': forDb ? (username ?? '') : username,
      'createdAt': forDb
          ? createdAt.toIso8601String()
          : Timestamp.fromDate(createdAt),
      'updatedAt': forDb
          ? (updatedAt?.toIso8601String() ?? '')
          : (updatedAt != null ? Timestamp.fromDate(updatedAt!) : null),
      'tags': forDb ? tags.join(',') : tags,
    };
  }

  factory Medication.fromMap(Map<String, dynamic> map) {
    return Medication(
      id: map['id'],
      name: map['name'] ?? '',
      company: map['company'] ?? '',
      ingredients: map['ingredients'] ?? '',
      isAllowed: (map['isAllowed'] ?? 0) == 1,
      notes: map['notes'] ?? '',
      imageUrls: _splitString(map['imageUrls']),
      category: map['category'] ?? 'مسكنات الألم',
      calories: (map['calories'] ?? 0.0).toDouble(),
      protein: (map['protein'] ?? 0.0).toDouble(),
      carbohydrates: (map['carbohydrates'] ?? 0.0).toDouble(),
      fat: (map['fat'] ?? 0.0).toDouble(),
      allergens: _splitString(map['allergens']),
      likes: _splitString(map['likes']),
      likesCount: map['likesCount'] ?? 0,
      commentsCount: map['commentsCount'] ?? 0,
      ratingsCount: map['ratingsCount'] ?? 0,
      averageRating: (map['averageRating'] ?? 0.0).toDouble(),
      activeIngredient: map['activeIngredient'],
      dosage: map['dosage'],
      sideEffects: map['sideEffects'],
      contraindications: map['contraindications'],
      interactions: map['interactions'],
      alternatives: _splitString(map['alternatives']),
      prescriptionRequired: map['prescriptionRequired'],
      ageGroup: map['ageGroup'],
      pregnancyCategory: map['pregnancyCategory'],
      storageConditions: map['storageConditions'],
      expiryDate: map['expiryDate'],
      barcode: map['barcode'],
      source: map['source'],
      isApproved: (map['isApproved'] ?? 1) == 1,
      approvalStatus: MedicationApprovalStatus.fromString(
        map['approvalStatus'] ?? 'approved',
      ),
      reviewerComment: map['reviewerComment'],
      reviewerId: map['reviewerId'],
      reviewerName: map['reviewerName'],
      reviewedAt: map['reviewedAt'] != null && map['reviewedAt'] != ''
          ? _parseDateTime(map['reviewedAt'])
          : null,
      isFeatured: (map['isFeatured'] ?? 0) == 1,
      userId: map['userId'],
      username: map['username'],
      createdAt: _parseDateTime(map['createdAt']) ?? DateTime.now(),
      updatedAt: map['updatedAt'] != null && map['updatedAt'] != ''
          ? _parseDateTime(map['updatedAt'])
          : null,
      tags: _splitString(map['tags']),
    );
  }

  // دالة مساعدة لتقسيم النصوص بأمان
  static List<String> _splitString(String? value) {
    if (value == null || value.isEmpty) {
      return [];
    }
    return value.split(',').where((item) => item.isNotEmpty).toList();
  }

  // دالة مساعدة لتحويل التاريخ بأمان
  static DateTime? _parseDateTime(String? dateString) {
    if (dateString == null || dateString.isEmpty) {
      return null;
    }

    try {
      return DateTime.parse(dateString);
    } catch (e) {
      // محاولة تحويل تنسيقات أخرى
      try {
        // إذا كان التاريخ بتنسيق timestamp
        final timestamp = int.tryParse(dateString);
        if (timestamp != null) {
          return DateTime.fromMillisecondsSinceEpoch(timestamp);
        }
      } catch (e2) {
        // تجاهل الخطأ
      }

      debugPrint("Failed to parse date: $dateString, error: $e");
      return null;
    }
  }

  Medication copyWith({
    String? id,
    String? name,
    String? company,
    String? ingredients,
    bool? isAllowed,
    String? notes,
    List<String>? imageUrls,
    String? category,
    double? calories,
    double? protein,
    double? carbohydrates,
    double? fat,
    List<String>? allergens,
    List<String>? likes,
    int? likesCount,
    int? commentsCount,
    int? ratingsCount,
    double? averageRating,
    String? activeIngredient,
    String? dosage,
    String? sideEffects,
    String? contraindications,
    String? interactions,
    List<String>? alternatives,
    String? prescriptionRequired,
    String? ageGroup,
    String? pregnancyCategory,
    String? storageConditions,
    String? expiryDate,
    String? barcode,
    String? source,
    bool? isApproved,
    bool? isFeatured,
    String? userId,
    String? username,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? tags,
  }) {
    return Medication(
      id: id ?? this.id,
      name: name ?? this.name,
      company: company ?? this.company,
      ingredients: ingredients ?? this.ingredients,
      isAllowed: isAllowed ?? this.isAllowed,
      notes: notes ?? this.notes,
      imageUrls: imageUrls ?? this.imageUrls,
      category: category ?? this.category,
      calories: calories ?? this.calories,
      protein: protein ?? this.protein,
      carbohydrates: carbohydrates ?? this.carbohydrates,
      fat: fat ?? this.fat,
      allergens: allergens ?? this.allergens,
      likes: likes ?? this.likes,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      ratingsCount: ratingsCount ?? this.ratingsCount,
      averageRating: averageRating ?? this.averageRating,
      activeIngredient: activeIngredient ?? this.activeIngredient,
      dosage: dosage ?? this.dosage,
      sideEffects: sideEffects ?? this.sideEffects,
      contraindications: contraindications ?? this.contraindications,
      interactions: interactions ?? this.interactions,
      alternatives: alternatives ?? this.alternatives,
      prescriptionRequired: prescriptionRequired ?? this.prescriptionRequired,
      ageGroup: ageGroup ?? this.ageGroup,
      pregnancyCategory: pregnancyCategory ?? this.pregnancyCategory,
      storageConditions: storageConditions ?? this.storageConditions,
      expiryDate: expiryDate ?? this.expiryDate,
      barcode: barcode ?? this.barcode,
      source: source ?? this.source,
      isApproved: isApproved ?? this.isApproved,
      isFeatured: isFeatured ?? this.isFeatured,
      userId: userId ?? this.userId,
      username: username ?? this.username,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      tags: tags ?? this.tags,
    );
  }

  factory Medication.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // تحويل التاريخ بأمان
    DateTime createdAt;
    try {
      createdAt = (data['createdAt'] as Timestamp).toDate();
    } catch (e) {
      createdAt = DateTime.now();
    }

    DateTime? updatedAt;
    try {
      updatedAt = data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : null;
    } catch (e) {
      updatedAt = null;
    }

    return Medication(
      id: doc.id,
      name: data['name'] ?? '',
      company: data['company'] ?? '',
      ingredients: data['ingredients'] ?? '',
      isAllowed: data['isAllowed'] ?? false,
      notes: data['notes'] ?? '',
      imageUrls: data['imageUrls'] != null
          ? List<String>.from(data['imageUrls'])
          : (data['imageUrl'] != null
                ? [data['imageUrl']]
                : []), // للتوافق مع البيانات القديمة
      category: data['category'] ?? 'مسكنات الألم',
      calories: (data['calories'] ?? 0.0).toDouble(),
      protein: (data['protein'] ?? 0.0).toDouble(),
      carbohydrates: (data['carbohydrates'] ?? 0.0).toDouble(),
      fat: (data['fat'] ?? 0.0).toDouble(),
      allergens: data['allergens'] != null
          ? List<String>.from(data['allergens'] as List)
          : <String>[],
      likes: List<String>.from(data['likes'] ?? []),
      likesCount: data['likesCount'] ?? 0,
      commentsCount: data['commentsCount'] ?? 0,
      ratingsCount: data['ratingsCount'] ?? 0,
      averageRating: (data['averageRating'] ?? 0.0).toDouble(),
      activeIngredient: data['activeIngredient'],
      dosage: data['dosage'],
      sideEffects: data['sideEffects'],
      contraindications: data['contraindications'],
      interactions: data['interactions'],
      alternatives: List<String>.from(data['alternatives'] ?? []),
      prescriptionRequired: data['prescriptionRequired'],
      ageGroup: data['ageGroup'],
      pregnancyCategory: data['pregnancyCategory'],
      storageConditions: data['storageConditions'],
      expiryDate: data['expiryDate'],
      barcode: data['barcode'],
      source: data['source'],
      isApproved: data['isApproved'] ?? true,
      approvalStatus: MedicationApprovalStatus.fromString(
        data['approvalStatus'] ?? 'approved',
      ),
      reviewerComment: data['reviewerComment'],
      reviewerId: data['reviewerId'],
      reviewerName: data['reviewerName'],
      reviewedAt: data['reviewedAt'] != null
          ? (data['reviewedAt'] as Timestamp).toDate()
          : null,
      isFeatured: data['isFeatured'] ?? false,
      userId: data['userId'],
      username: data['username'],
      createdAt: createdAt,
      updatedAt: updatedAt,
      tags: List<String>.from(data['tags'] ?? []),
    );
  }
}

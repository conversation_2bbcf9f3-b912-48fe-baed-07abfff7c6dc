import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:share_plus/share_plus.dart';
import 'package:cached_network_image/cached_network_image.dart';

import 'package:yassincil/providers/medication_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/providers/favorites_provider.dart';
import 'package:yassincil/models/medication.dart';
import 'package:yassincil/models/comment.dart';
import 'package:yassincil/screens/medications/add_edit_medication_screen_enhanced.dart';
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/widgets/advanced_medication_comment_widget.dart';
import 'package:yassincil/widgets/add_medication_comment_widget.dart';
import 'package:yassincil/widgets/social_interactions_widget.dart';
import 'package:yassincil/auth/screens/login_screen.dart';

class EnhancedMedicationDetailScreen extends StatefulWidget {
  final Medication medication;
  final bool isForVerification;

  const EnhancedMedicationDetailScreen({
    super.key,
    required this.medication,
    this.isForVerification = false,
  });

  @override
  State<EnhancedMedicationDetailScreen> createState() =>
      _EnhancedMedicationDetailScreenState();
}

class _EnhancedMedicationDetailScreenState
    extends State<EnhancedMedicationDetailScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _fabController;
  late Animation<double> _fadeAnimation;
  late PageController _imagePageController;

  int _selectedImageIndex = 0;
  int _selectedTabIndex = 0;

  // مفتاح للتمرير إلى قسم التعليقات
  final GlobalKey _commentsKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _imagePageController = PageController();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    _fabController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController.forward();
      _fabController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _fabController.dispose();
    _imagePageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: CustomScrollView(
          slivers: [
            _buildEnhancedSliverAppBar(),
            SliverToBoxAdapter(
              child: Column(
                children: [
                  _buildImageGallery(),
                  _buildMedicationInfo(),
                  _buildDetailsTabs(),
                  _buildTabContent(),
                  _buildSocialInteractions(),
                  _buildCommentsSection(),
                  const SizedBox(height: 100), // مساحة للـ FAB
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  Widget _buildEnhancedSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: AppColors.primary,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          widget.medication.name,
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontSize: 16,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [AppColors.primary, AppColors.primary.withOpacity(0.8)],
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                right: -50,
                top: -50,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.1),
                  ),
                ),
              ),
              Positioned(
                left: -30,
                bottom: -30,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.05),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        Consumer<FavoritesProvider>(
          builder: (context, favProvider, child) {
            final isFavorite = favProvider.isFavorite(widget.medication.id!);
            return IconButton(
              icon: Icon(
                isFavorite ? Icons.favorite : Icons.favorite_border,
                color: Colors.white,
              ),
              onPressed: () async {
                final user = Provider.of<AuthProvider>(
                  context,
                  listen: false,
                ).currentUser;
                if (user == null) {
                  showDialog(
                    context: context,
                    builder: (ctx) => AlertDialog(
                      title: Text(
                        'تسجيل الدخول مطلوب',
                        style: GoogleFonts.cairo(),
                      ),
                      content: Text(
                        'يرجى تسجيل الدخول لإدارة المفضلة.',
                        style: GoogleFonts.cairo(),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(ctx).pop(),
                          child: Text('إلغاء', style: GoogleFonts.cairo()),
                        ),
                        ElevatedButton(
                          onPressed: () {
                            Navigator.of(ctx).pop();
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => LoginScreen(
                                  onLoginSuccess: () async {
                                    final wasFavorite = favProvider.isFavorite(
                                      widget.medication.id!,
                                    );
                                    await favProvider.toggleFavorite(
                                      widget.medication,
                                    );
                                    HapticFeedback.lightImpact();
                                    if (!mounted) return;
                                    ScaffoldMessenger.of(
                                      context,
                                    ).hideCurrentSnackBar();
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          wasFavorite
                                              ? 'تمت الإزالة من المفضلة'
                                              : 'تمت الإضافة إلى المفضلة',
                                          style: GoogleFonts.cairo(
                                            color: Colors.white,
                                          ),
                                        ),
                                        action: SnackBarAction(
                                          label: 'تراجع',
                                          textColor: Colors.white,
                                          onPressed: () {
                                            favProvider.toggleFavorite(
                                              widget.medication,
                                            );
                                          },
                                        ),
                                        backgroundColor: wasFavorite
                                            ? Colors.red
                                            : Colors.green,
                                        behavior: SnackBarBehavior.floating,
                                      ),
                                    );
                                  },
                                ),
                              ),
                            );
                          },
                          child: Text(
                            'تسجيل الدخول',
                            style: GoogleFonts.cairo(),
                          ),
                        ),
                      ],
                    ),
                  );
                  return;
                }
                final wasFavorite = favProvider.isFavorite(
                  widget.medication.id!,
                );
                await favProvider.toggleFavorite(widget.medication);
                HapticFeedback.lightImpact();
                if (!mounted) return;
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      wasFavorite
                          ? 'تمت الإزالة من المفضلة'
                          : 'تمت الإضافة إلى المفضلة',
                      style: GoogleFonts.cairo(color: Colors.white),
                    ),
                    action: SnackBarAction(
                      label: 'تراجع',
                      textColor: Colors.white,
                      onPressed: () {
                        favProvider.toggleFavorite(widget.medication);
                      },
                    ),
                    backgroundColor: wasFavorite ? Colors.red : Colors.green,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
              tooltip: isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة',
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.share_rounded, color: Colors.white),
          onPressed: _shareMedication,
          tooltip: 'مشاركة',
        ),
        Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            final isAdmin = authProvider.isAdmin;

            return PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert, color: Colors.white),
              onSelected: _handleMenuAction,
              itemBuilder: (context) => [
                if (!widget.isForVerification) ...[
                  // زر التعديل - للمشرفين فقط
                  if (isAdmin)
                    PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          const Icon(Icons.edit_outlined),
                          const SizedBox(width: 8),
                          Text('تعديل', style: GoogleFonts.cairo()),
                        ],
                      ),
                    ),
                  // زر الإبلاغ - للجميع
                  PopupMenuItem(
                    value: 'report',
                    child: Row(
                      children: [
                        const Icon(Icons.report_outlined, color: Colors.red),
                        const SizedBox(width: 8),
                        Text(
                          'إبلاغ',
                          style: GoogleFonts.cairo(color: Colors.red),
                        ),
                      ],
                    ),
                  ),
                ],
                PopupMenuItem(
                  value: 'copy_link',
                  child: Row(
                    children: [
                      const Icon(Icons.link_outlined),
                      const SizedBox(width: 8),
                      Text('نسخ الرابط', style: GoogleFonts.cairo()),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildImageGallery() {
    final images = widget.medication.imageUrls;

    if (images.isEmpty) {
      return Container(
        margin: const EdgeInsets.all(16),
        height: 250,
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.medical_services_rounded,
                size: 80,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد صور متاحة',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // الصورة الرئيسية
          Container(
            height: 300,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: PageView.builder(
                controller: _imagePageController,
                onPageChanged: (index) {
                  setState(() {
                    _selectedImageIndex = index;
                  });
                },
                itemCount: images.length,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () => _showImageViewer(images[index]),
                    child: Hero(
                      tag: 'medication-image-$index',
                      child: CachedNetworkImage(
                        imageUrl: images[index],
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: Colors.grey.shade200,
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey.shade200,
                          child: Icon(
                            Icons.error_outline,
                            size: 50,
                            color: Colors.grey.shade400,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          if (images.length > 1) ...[
            const SizedBox(height: 16),
            // مؤشرات الصور
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: images.asMap().entries.map((entry) {
                final index = entry.key;
                final isSelected = index == _selectedImageIndex;

                return GestureDetector(
                  onTap: () {
                    _imagePageController.animateToPage(
                      index,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: isSelected ? 24 : 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.primary
                          : Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                );
              }).toList(),
            ),

            const SizedBox(height: 16),

            // صور مصغرة
            SizedBox(
              height: 80,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: images.length,
                itemBuilder: (context, index) {
                  final isSelected = index == _selectedImageIndex;

                  return GestureDetector(
                    onTap: () {
                      _imagePageController.animateToPage(
                        index,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    },
                    child: Container(
                      margin: const EdgeInsets.only(right: 12),
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected
                              ? AppColors.primary
                              : Colors.grey.shade300,
                          width: isSelected ? 3 : 1,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: CachedNetworkImage(
                          imageUrl: images[index],
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            color: Colors.grey.shade200,
                            child: const Center(
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: Colors.grey.shade200,
                            child: Icon(
                              Icons.error_outline,
                              color: Colors.grey.shade400,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMedicationInfo() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // اسم الدواء والشركة
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.medication.name,
                      style: GoogleFonts.cairo(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.medication.company,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              _buildStatusBadge(),
            ],
          ),

          const SizedBox(height: 16),

          // معلومات سريعة
          Row(
            children: [
              Expanded(
                child: _buildInfoChip(
                  Icons.category_rounded,
                  'الفئة',
                  widget.medication.category,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInfoChip(
                  Icons.star_rounded,
                  'التقييم',
                  widget.medication.averageRating.toStringAsFixed(1),
                  Colors.amber,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildInfoChip(
                  Icons.favorite_rounded,
                  'الإعجابات',
                  widget.medication.likesCount.toString(),
                  Colors.red,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInfoChip(
                  Icons.comment_rounded,
                  'التعليقات',
                  widget.medication.commentsCount.toString(),
                  Colors.green,
                ),
              ),
            ],
          ),

          if (widget.medication.barcode?.isNotEmpty == true) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.qr_code, color: Colors.grey.shade600),
                  const SizedBox(width: 8),
                  Text(
                    'الباركود: ${widget.medication.barcode}',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () {
                      Clipboard.setData(
                        ClipboardData(text: widget.medication.barcode!),
                      );
                      _showSnackBar('تم نسخ الباركود');
                    },
                    icon: Icon(
                      Icons.copy,
                      size: 18,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoChip(
    IconData icon,
    String label,
    String value,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          Text(
            label,
            style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge() {
    Color color = widget.medication.isAllowed ? Colors.green : Colors.red;
    String text = widget.medication.isAllowed
        ? 'آمن لمرضى السيلياك (خالٍ من الجلوتين)'
        : 'غير آمن لمرضى السيلياك (قد يحتوي على الجلوتين)';
    IconData icon = widget.medication.isAllowed
        ? Icons.check_circle
        : Icons.cancel;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 6),
          Text(
            text,
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsTabs() {
    final tabs = [
      {'title': 'الوصف', 'icon': Icons.description_outlined},
      {'title': 'المعلومات الطبية', 'icon': Icons.medical_services_outlined},
      {'title': 'التحذيرات', 'icon': Icons.warning_amber_outlined},
      {'title': 'معلومات إضافية', 'icon': Icons.info_outlined},
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: tabs.asMap().entries.map((entry) {
            final index = entry.key;
            final tab = entry.value;
            final isSelected = _selectedTabIndex == index;

            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedTabIndex = index;
                });
                HapticFeedback.lightImpact();
              },
              child: Container(
                margin: const EdgeInsets.only(right: 12),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primary : Colors.white,
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: isSelected
                        ? AppColors.primary
                        : Colors.grey.shade300,
                  ),
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: AppColors.primary.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      tab['icon'] as IconData,
                      size: 18,
                      color: isSelected ? Colors.white : Colors.grey.shade600,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      tab['title'] as String,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: isSelected
                            ? FontWeight.w600
                            : FontWeight.normal,
                        color: isSelected ? Colors.white : Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildTabContent() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: _getTabContent(_selectedTabIndex),
      ),
    );
  }

  Widget _getTabContent(int index) {
    switch (index) {
      case 0:
        return _buildDescriptionTab();
      case 1:
        return _buildMedicalInfoTab();
      case 2:
        return _buildWarningsTab();
      case 3:
        return _buildAdditionalInfoTab();
      default:
        return _buildDescriptionTab();
    }
  }

  Widget _buildDescriptionTab() {
    return Column(
      key: const ValueKey('description'),
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.description_outlined, color: AppColors.primary),
            const SizedBox(width: 8),
            Text(
              'وصف الدواء',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        if (widget.medication.ingredients.isNotEmpty) ...[
          _buildInfoSection(
            'المكونات الفعالة',
            widget.medication.ingredients,
            Icons.science_outlined,
          ),
          const SizedBox(height: 16),
        ],

        if (widget.medication.notes.isNotEmpty) ...[
          _buildInfoSection(
            'ملاحظات',
            widget.medication.notes,
            Icons.note_outlined,
          ),
        ] else ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.grey.shade500),
                const SizedBox(width: 12),
                Text(
                  'لا توجد معلومات إضافية متاحة',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildMedicalInfoTab() {
    return Column(
      key: const ValueKey('medical'),
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.medical_services_outlined, color: AppColors.primary),
            const SizedBox(width: 8),
            Text(
              'المعلومات الطبية',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        if (widget.medication.activeIngredient?.isNotEmpty == true)
          _buildMedicalInfoItem(
            'المادة الفعالة الرئيسية',
            widget.medication.activeIngredient!,
            Icons.science,
          ),

        if (widget.medication.dosage?.isNotEmpty == true)
          _buildMedicalInfoItem(
            'الجرعة المقترحة',
            widget.medication.dosage!,
            Icons.medication_liquid,
          ),

        if (widget.medication.prescriptionRequired?.isNotEmpty == true)
          _buildMedicalInfoItem(
            'يحتاج وصفة طبية',
            widget.medication.prescriptionRequired!,
            Icons.receipt_long,
          ),

        if (widget.medication.ageGroup?.isNotEmpty == true)
          _buildMedicalInfoItem(
            'الفئة العمرية',
            widget.medication.ageGroup!,
            Icons.people,
          ),

        if (widget.medication.pregnancyCategory?.isNotEmpty == true)
          _buildMedicalInfoItem(
            'آمن لمرضى السيلياك',
            widget.medication.pregnancyCategory!,
            Icons.health_and_safety,
          ),

        if (widget.medication.storageConditions?.isNotEmpty == true)
          _buildMedicalInfoItem(
            'ظروف التخزين',
            widget.medication.storageConditions!,
            Icons.storage,
          ),
      ],
    );
  }

  Widget _buildWarningsTab() {
    return Column(
      key: const ValueKey('warnings'),
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.warning_amber_outlined, color: Colors.orange),
            const SizedBox(width: 8),
            Text(
              'التحذيرات والآثار الجانبية',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        if (widget.medication.sideEffects?.isNotEmpty == true)
          _buildWarningItem(
            'الآثار الجانبية',
            widget.medication.sideEffects!,
            Icons.warning,
            Colors.orange,
          ),

        if (widget.medication.contraindications?.isNotEmpty == true)
          _buildWarningItem(
            'موانع الاستعمال',
            widget.medication.contraindications!,
            Icons.block,
            Colors.red,
          ),

        if (widget.medication.interactions?.isNotEmpty == true)
          _buildWarningItem(
            'التفاعلات الدوائية',
            widget.medication.interactions!,
            Icons.merge_type,
            Colors.purple,
          ),

        if (widget.medication.allergens?.isNotEmpty == true)
          _buildWarningItem(
            'مسببات الحساسية',
            widget.medication.allergens!.join(', '),
            Icons.coronavirus,
            Colors.red,
          ),
      ],
    );
  }

  Widget _buildAdditionalInfoTab() {
    return Column(
      key: const ValueKey('additional'),
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.info_outlined, color: AppColors.primary),
            const SizedBox(width: 8),
            Text(
              'معلومات إضافية',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        if (widget.medication.alternatives.isNotEmpty)
          _buildAdditionalInfoItem(
            'البدائل الآمنة',
            widget.medication.alternatives.join(', '),
            Icons.swap_horiz,
          ),

        if (widget.medication.tags.isNotEmpty) _buildTagsSection(),

        if (widget.medication.source?.isNotEmpty == true)
          _buildAdditionalInfoItem(
            'المصدر',
            widget.medication.source!,
            Icons.source,
          ),

        if (widget.medication.expiryDate?.isNotEmpty == true)
          _buildAdditionalInfoItem(
            'تاريخ انتهاء الصلاحية',
            widget.medication.expiryDate!,
            Icons.event,
          ),

        _buildAdditionalInfoItem(
          'تاريخ الإضافة',
          _formatDate(widget.medication.createdAt),
          Icons.calendar_today,
        ),

        _buildAdditionalInfoItem(
          'آخر تحديث',
          _formatDate(widget.medication.updatedAt),
          Icons.update,
        ),
      ],
    );
  }

  Widget _buildInfoSection(String title, String content, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 18, color: Colors.grey.shade600),
            const SizedBox(width: 8),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Text(
            content,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.grey.shade800,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMedicalInfoItem(String title, String content, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: Colors.blue.shade600, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue.shade800,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  content,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWarningItem(
    String title,
    String content,
    IconData icon,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  content,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoItem(String title, String content, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Icon(icon, size: 18, color: Colors.grey.shade600),
          const SizedBox(width: 12),
          Text(
            '$title: ',
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          Expanded(
            child: Text(
              content,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade800,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTagsSection() {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.tag, size: 18, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              Text(
                'العلامات',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: widget.medication.tags.map((tag) {
              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: AppColors.primary.withOpacity(0.3)),
                ),
                child: Text(
                  tag,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: AppColors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSocialInteractions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: SocialInteractionsWidget(
        medication: widget.medication,
        onCommentButtonPressed: _scrollToCommentsSection,
      ),
    );
  }

  void _scrollToCommentsSection() {
    // التمرير إلى قسم التعليقات
    Scrollable.ensureVisible(
      _commentsKey.currentContext!,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  Widget _buildCommentsSection() {
    return Container(
      key: _commentsKey,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // قسم إضافة التعليق
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.comment_outlined, color: AppColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'التعليقات والتقييمات',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${widget.medication.commentsCount} تعليق',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                if (widget.medication.id != null)
                  AddMedicationCommentWidget(
                    medicationId: widget.medication.id!,
                  )
                else
                  Text(
                    'لا يمكن إضافة تعليقات قبل حفظ الدواء (لا يوجد معرف).',
                    style: GoogleFonts.cairo(color: Colors.grey.shade600),
                  ),
              ],
            ),
          ),

          // قائمة التعليقات
          if (widget.medication.id != null) _buildCommentsList(),
        ],
      ),
    );
  }

  Widget _buildCommentsList() {
    return StreamBuilder<List<Comment>>(
      stream: Provider.of<MedicationProvider>(
        context,
        listen: false,
      ).getCommentsForMedication(widget.medication.id!),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasError) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: Center(
              child: Column(
                children: [
                  Icon(Icons.error_outline, size: 48, color: AppColors.error),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ في تحميل التعليقات',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.error,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        final comments = snapshot.data ?? [];

        if (comments.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: Center(
              child: Text(
                'لا توجد تعليقات. كن أول من يعلق!',
                style: GoogleFonts.cairo(color: AppColors.textSecondary),
              ),
            ),
          );
        }

        // عرض التعليقات الرئيسية فقط (بدون الردود)
        final mainComments = comments
            .where((comment) => comment.parentCommentId == null)
            .toList();

        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: mainComments.length,
          itemBuilder: (context, index) {
            return Container(
              margin: const EdgeInsets.only(top: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: AdvancedMedicationCommentWidget(
                  comment: mainComments[index],
                  medicationId: widget.medication.id!,
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildFloatingActionButtons() {
    return ScaleTransition(
      scale: _fabController,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (!widget.isForVerification) ...[
            FloatingActionButton(
              heroTag: "edit_medication",
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => EnhancedAddEditMedicationScreen(
                      medication: widget.medication,
                    ),
                  ),
                );
              },
              backgroundColor: Colors.blue,
              child: const Icon(Icons.edit_rounded, color: Colors.white),
            ),
            const SizedBox(height: 16),
          ],
          FloatingActionButton(
            heroTag: "share_medication",
            onPressed: _shareMedication,
            backgroundColor: AppColors.primary,
            child: const Icon(Icons.share_rounded, color: Colors.white),
          ),
        ],
      ),
    );
  }

  // Helper Methods
  void _showImageViewer(String imageUrl) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _ImageViewerScreen(imageUrl: imageUrl),
      ),
    );
  }

  void _shareMedication() {
    final text =
        '''
${widget.medication.name}
الشركة: ${widget.medication.company}
الفئة: ${widget.medication.category}
${widget.medication.isAllowed ? 'آمن لمرضى السيلياك (خالٍ من الجلوتين)' : 'غير آمن لمرضى السيلياك (قد يحتوي على الجلوتين)'}

${widget.medication.notes.isNotEmpty ? widget.medication.notes : 'لا توجد ملاحظات إضافية'}
''';

    Share.share(text, subject: 'معلومات الدواء: ${widget.medication.name}');
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) =>
                EnhancedAddEditMedicationScreen(medication: widget.medication),
          ),
        );
        break;
      case 'report':
        _showReportDialog();
        break;
      case 'copy_link':
        Clipboard.setData(
          ClipboardData(text: 'medication://${widget.medication.id}'),
        );
        _showSnackBar('تم نسخ الرابط');
        break;
    }
  }

  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إبلاغ عن مشكلة', style: GoogleFonts.cairo()),
        content: Text(
          'هل تريد الإبلاغ عن مشكلة في هذا الدواء؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showSnackBar('تم إرسال البلاغ بنجاح');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('إبلاغ', style: GoogleFonts.cairo(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.cairo()),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return '-';
    return '${date.day}/${date.month}/${date.year}';
  }
}

// شاشة عرض الصور بملء الشاشة
class _ImageViewerScreen extends StatelessWidget {
  final String imageUrl;

  const _ImageViewerScreen({required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // الخلفية مع تأثير ضبابي
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.9),
                  Colors.black.withOpacity(0.7),
                  Colors.black.withOpacity(0.9),
                ],
              ),
            ),
            child: GestureDetector(onTap: () => Navigator.pop(context)),
          ),

          // الصورة مع تأثيرات متقدمة
          Center(
            child: Hero(
              tag: 'medication-image-viewer',
              child: Container(
                margin: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.5),
                      blurRadius: 30,
                      offset: const Offset(0, 15),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(25),
                  child: InteractiveViewer(
                    panEnabled: true,
                    boundaryMargin: const EdgeInsets.all(20),
                    minScale: 0.5,
                    maxScale: 4.0,
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      fit: BoxFit.contain,
                      placeholder: (context, url) => Container(
                        width: 200,
                        height: 200,
                        color: Colors.grey.shade800,
                        child: const Center(
                          child: CircularProgressIndicator(color: Colors.white),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        width: 200,
                        height: 200,
                        color: Colors.grey.shade800,
                        child: const Icon(
                          Icons.error_outline,
                          color: Colors.white,
                          size: 50,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),

          // شريط علوي مع أزرار
          Positioned(
            top: 50,
            left: 20,
            right: 20,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildGlassButton(
                  icon: Icons.close_rounded,
                  onTap: () => Navigator.pop(context),
                ),
                _buildGlassButton(
                  icon: Icons.download_rounded,
                  onTap: () {
                    // سيتم تنفيذ تحميل الصورة لاحقاً
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('سيتم إضافة ميزة التحميل قريباً'),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGlassButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.white.withOpacity(0.3)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Icon(icon, color: Colors.white, size: 24),
      ),
    );
  }
}

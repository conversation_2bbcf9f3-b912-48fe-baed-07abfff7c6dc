import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import 'package:yassincil/providers/pharmacy_provider.dart';
import 'package:yassincil/models/pharmacy.dart';

class AddEditPharmacyScreen extends StatefulWidget {
  final Pharmacy? pharmacy;

  const AddEditPharmacyScreen({super.key, this.pharmacy});

  @override
  State<AddEditPharmacyScreen> createState() => _AddEditPharmacyScreenState();
}

class _AddEditPharmacyScreenState extends State<AddEditPharmacyScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();
  final _openingHoursController = TextEditingController();
  final _imageUrlController = TextEditingController();

  bool _isOpen = true;
  bool _hasDelivery = false;
  bool _hasGlutenFree = false;
  File? _selectedImage;
  bool _isLoading = false;
  bool _useImageUrl = false;

  @override
  void initState() {
    super.initState();
    if (widget.pharmacy != null) {
      _nameController.text = widget.pharmacy!.name;
      _addressController.text = widget.pharmacy!.address;
      _phoneController.text = widget.pharmacy!.phone;
      _openingHoursController.text = widget.pharmacy!.openingHours;
      _isOpen = widget.pharmacy!.isOpen;
      _hasDelivery = widget.pharmacy!.hasDelivery;
      _hasGlutenFree = widget.pharmacy!.hasGlutenFreeProducts;
      if (widget.pharmacy!.imageUrl != null) {
        _imageUrlController.text = widget.pharmacy!.imageUrl!;
        _useImageUrl = true;
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _openingHoursController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 800,
      maxHeight: 600,
      imageQuality: 80,
    );

    if (pickedFile != null) {
      setState(() {
        _selectedImage = File(pickedFile.path);
        _useImageUrl = false;
        _imageUrlController.clear();
      });
    }
  }

  Future<void> _savePharmacy() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final pharmacyProvider = Provider.of<PharmacyProvider>(
        context,
        listen: false,
      );

      String? finalImageUrl;
      if (_useImageUrl && _imageUrlController.text.trim().isNotEmpty) {
        finalImageUrl = _imageUrlController.text.trim();
      }

      final pharmacy = Pharmacy(
        id: widget.pharmacy?.id,
        name: _nameController.text.trim(),
        address: _addressController.text.trim(),
        phone: _phoneController.text.trim(),
        openingHours: _openingHoursController.text.trim(),
        isOpen: _isOpen,
        hasDelivery: _hasDelivery,
        hasGlutenFreeProducts: _hasGlutenFree,
        imageUrl: finalImageUrl,
        latitude: widget.pharmacy?.latitude ?? 0.0,
        longitude: widget.pharmacy?.longitude ?? 0.0,
        rating: widget.pharmacy?.rating ?? 0.0,
        createdAt: widget.pharmacy?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (widget.pharmacy == null) {
        await pharmacyProvider.addPharmacy(pharmacy);
      } else {
        await pharmacyProvider.updatePharmacy(pharmacy);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.pharmacy == null
                  ? 'تم إضافة الصيدلية بنجاح'
                  : 'تم تحديث الصيدلية بنجاح',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEditing = widget.pharmacy != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isEditing ? 'تعديل صيدلية' : 'إضافة صيدلية جديدة',
        ),
        centerTitle: true,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.save_rounded),
              onPressed: _savePharmacy,
              tooltip: 'حفظ',
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildImageSection(),
            const SizedBox(height: 20),
            _buildNameField(),
            const SizedBox(height: 16),
            _buildAddressField(),
            const SizedBox(height: 16),
            _buildPhoneField(),
            const SizedBox(height: 16),
            _buildOpeningHoursField(),
            const SizedBox(height: 16),
            _buildSwitches(),
            const SizedBox(height: 32),
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    final theme = Theme.of(context);
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        children: [
          Container(
            height: 200,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
              gradient: LinearGradient(
                colors: [theme.primaryColor.withOpacity(0.2), theme.primaryColor.withOpacity(0.05)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Stack(
              children: [
                if (_selectedImage != null)
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                    child: Image.file(
                      _selectedImage!,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  )
                else if (_useImageUrl && _imageUrlController.text.isNotEmpty)
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                    child: Image.network(
                      _imageUrlController.text,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildImagePlaceholder(),
                    ),
                  )
                else if (widget.pharmacy?.imageUrl != null && !_useImageUrl)
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                    child: Image.network(
                      widget.pharmacy!.imageUrl!,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildImagePlaceholder(),
                    ),
                  )
                else
                  _buildImagePlaceholder(),
                Positioned(
                  bottom: 16,
                  right: 16,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      FloatingActionButton.small(
                        onPressed: _pickImage,
                        heroTag: "pharmacy_camera",
                        child: const Icon(Icons.camera_alt),
                      ),
                      const SizedBox(width: 8),
                      FloatingActionButton.small(
                        onPressed: () {
                          setState(() {
                            _useImageUrl = !_useImageUrl;
                            if (_useImageUrl) {
                              _selectedImage = null;
                            } else {
                              _imageUrlController.clear();
                            }
                          });
                        },
                        heroTag: "pharmacy_link",
                        child: const Icon(Icons.link),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          if (_useImageUrl)
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextFormField(
                controller: _imageUrlController,
                decoration: const InputDecoration(
                  labelText: 'رابط الصورة',
                  prefixIcon: Icon(Icons.link),
                ),
                onChanged: (value) => setState(() {}),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildImagePlaceholder() {
    final theme = Theme.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.local_pharmacy, size: 60, color: theme.primaryColor.withOpacity(0.5)),
          const SizedBox(height: 8),
          Text(
            'اضغط لإضافة صورة',
            style: theme.textTheme.titleLarge?.copyWith(color: theme.primaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: const InputDecoration(
        labelText: 'اسم الصيدلية',
        prefixIcon: Icon(Icons.business),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال اسم الصيدلية';
        }
        return null;
      },
    );
  }

  Widget _buildAddressField() {
    return TextFormField(
      controller: _addressController,
      decoration: const InputDecoration(
        labelText: 'العنوان',
        prefixIcon: Icon(Icons.location_on),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال العنوان';
        }
        return null;
      },
    );
  }

  Widget _buildPhoneField() {
    return TextFormField(
      controller: _phoneController,
      keyboardType: TextInputType.phone,
      decoration: const InputDecoration(
        labelText: 'رقم الهاتف',
        prefixIcon: Icon(Icons.phone),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال رقم الهاتف';
        }
        return null;
      },
    );
  }

  Widget _buildOpeningHoursField() {
    return TextFormField(
      controller: _openingHoursController,
      decoration: const InputDecoration(
        labelText: 'ساعات العمل',
        prefixIcon: Icon(Icons.access_time),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال ساعات العمل';
        }
        return null;
      },
    );
  }

  Widget _buildSwitches() {
    final theme = Theme.of(context);
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            SwitchListTile(
              title: Text('مفتوح حالياً', style: theme.textTheme.titleLarge),
              value: _isOpen,
              onChanged: (value) {
                setState(() {
                  _isOpen = value;
                });
              },
              activeColor: theme.primaryColor,
            ),
            SwitchListTile(
              title: Text('خدمة توصيل', style: theme.textTheme.titleLarge),
              value: _hasDelivery,
              onChanged: (value) {
                setState(() {
                  _hasDelivery = value;
                });
              },
              activeColor: theme.primaryColor,
            ),
            SwitchListTile(
              title: Text('منتجات خالية من الجلوتين', style: theme.textTheme.titleLarge),
              value: _hasGlutenFree,
              onChanged: (value) {
                setState(() {
                  _hasGlutenFree = value;
                });
              },
              activeColor: theme.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return ElevatedButton.icon(
      onPressed: _isLoading ? null : _savePharmacy,
      icon: _isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
              ),
            )
          : const Icon(Icons.save),
      label: Text(
        _isLoading
            ? 'جاري الحفظ...'
            : (widget.pharmacy == null
                ? 'إضافة الصيدلية'
                : 'حفظ التغييرات'),
      ),
    );
  }
}
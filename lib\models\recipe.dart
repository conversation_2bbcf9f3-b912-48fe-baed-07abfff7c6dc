import 'package:cloud_firestore/cloud_firestore.dart';

class Recipe {
  final String? id;
  final String title;
  final String description;
  final List<String> ingredients;
  final List<String> instructions;
  final String category; // e.g., 'فطور', 'غداء', 'عشاء', 'حلويات'
  final List<String> imageUrls; // قائمة الصور
  final String userId; // ID of the user who submitted the recipe
  final String username; // Name of the user who submitted the recipe
  final String? userAvatar; // صورة المستخدم
  final List<String> likes; // قائمة معرفات المستخدمين الذين أعجبوا
  final int likesCount;
  final int commentsCount;
  final int favoriteCount; // عدد الإضافات إلى المفضلة
  final int ratingsCount;
  final double averageRating;
  final bool isGlutenFree; // هل الوصفة خالية من الغلوتين
  final bool isLactoseFree; // هل الوصفة خالية من اللاكتوز
  final bool isApproved; // هل الوصفة معتمدة
  final bool isFeatured; // هل الوصفة مميزة
  final int prepTime; // وقت التحضير بالدقائق
  final int cookTime; // وقت الطبخ بالدقائق
  final int servings; // عدد الحصص
  final String difficulty; // مستوى الصعوبة
  final double? calories; // السعرات الحرارية
  final double? protein; // البروتين
  final double? carbs; // الكربوهيدرات
  final double? fat; // الدهون
  final List<String> tags; // العلامات
  final DateTime createdAt;
  final DateTime? updatedAt;

  Recipe({
    this.id,
    required this.title,
    required this.description,
    required this.ingredients,
    required this.instructions,
    required this.category,
    this.imageUrls = const [],
    required this.userId,
    required this.username,
    this.userAvatar,
    this.likes = const [],
    this.likesCount = 0,
    this.commentsCount = 0,
    this.favoriteCount = 0,
    this.ratingsCount = 0,
    this.averageRating = 0.0,
    this.isGlutenFree = false,
    this.isLactoseFree = false,
    this.isApproved = false,
    this.isFeatured = false,
    this.prepTime = 30,
    this.cookTime = 30,
    this.servings = 4,
    this.difficulty = 'متوسط',
    this.calories,
    this.protein,
    this.carbs,
    this.fat,
    this.tags = const [],
    required this.createdAt,
    this.updatedAt,
  });

  // للتوافق مع الكود القديم
  String? get imageUrl => imageUrls.isNotEmpty ? imageUrls.first : null;

  Recipe copyWith({
    String? id,
    String? title,
    String? description,
    List<String>? ingredients,
    List<String>? instructions,
    String? category,
    List<String>? imageUrls,
    String? userId,
    String? username,
    String? userAvatar,
    List<String>? likes,
    int? likesCount,
    int? commentsCount,
    int? favoriteCount,
    int? ratingsCount,
    double? averageRating,
    bool? isGlutenFree,
    bool? isLactoseFree,
    bool? isApproved,
    bool? isFeatured,
    int? prepTime,
    int? cookTime,
    int? servings,
    String? difficulty,
    double? calories,
    double? protein,
    double? carbs,
    double? fat,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Recipe(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      ingredients: ingredients ?? this.ingredients,
      instructions: instructions ?? this.instructions,
      category: category ?? this.category,
      imageUrls: imageUrls ?? this.imageUrls,
      userId: userId ?? this.userId,
      username: username ?? this.username,
      userAvatar: userAvatar ?? this.userAvatar,
      likes: likes ?? this.likes,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      favoriteCount: favoriteCount ?? this.favoriteCount,
      ratingsCount: ratingsCount ?? this.ratingsCount,
      averageRating: averageRating ?? this.averageRating,
      isGlutenFree: isGlutenFree ?? this.isGlutenFree,
      isLactoseFree: isLactoseFree ?? this.isLactoseFree,
      isApproved: isApproved ?? this.isApproved,
      isFeatured: isFeatured ?? this.isFeatured,
      prepTime: prepTime ?? this.prepTime,
      cookTime: cookTime ?? this.cookTime,
      servings: servings ?? this.servings,
      difficulty: difficulty ?? this.difficulty,
      calories: calories ?? this.calories,
      protein: protein ?? this.protein,
      carbs: carbs ?? this.carbs,
      fat: fat ?? this.fat,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'description': description,
      'ingredients': ingredients,
      'instructions': instructions,
      'category': category,
      'imageUrls': imageUrls,
      'userId': userId,
      'username': username,
      'userAvatar': userAvatar,
      'likes': likes,
      'likesCount': likesCount,
      'commentsCount': commentsCount,
      'favoriteCount': favoriteCount,
      'ratingsCount': ratingsCount,
      'averageRating': averageRating,
      'isGlutenFree': isGlutenFree,
      'isLactoseFree': isLactoseFree,
      'isApproved': isApproved,
      'isFeatured': isFeatured,
      'prepTime': prepTime,
      'cookTime': cookTime,
      'servings': servings,
      'difficulty': difficulty,
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fat': fat,
      'tags': tags,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  factory Recipe.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // تحويل التاريخ بأمان
    DateTime createdAt;
    try {
      createdAt = (data['createdAt'] as Timestamp).toDate();
    } catch (e) {
      createdAt = DateTime.now();
    }

    DateTime? updatedAt;
    try {
      updatedAt = data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : null;
    } catch (e) {
      updatedAt = null;
    }

    return Recipe(
      id: doc.id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      ingredients: List<String>.from(data['ingredients'] ?? []),
      instructions: List<String>.from(data['instructions'] ?? []),
      category: data['category'] ?? '',
      imageUrls: data['imageUrls'] != null
          ? List<String>.from(data['imageUrls'])
          : (data['imageUrl'] != null
                ? [data['imageUrl']]
                : []), // للتوافق مع البيانات القديمة
      userId: data['userId'] ?? '',
      username: data['username'] ?? 'مستخدم',
      userAvatar: data['userAvatar'],
      likes: List<String>.from(data['likes'] ?? []),
      likesCount: data['likesCount'] ?? 0,
      commentsCount: data['commentsCount'] ?? 0,
      favoriteCount: data['favoriteCount'] ?? 0,
      ratingsCount: data['ratingsCount'] ?? 0,
      averageRating: (data['averageRating'] ?? 0.0).toDouble(),
      isGlutenFree: data['isGlutenFree'] ?? false,
      isLactoseFree: data['isLactoseFree'] ?? false,
      isApproved: data['isApproved'] ?? true,
      isFeatured: data['isFeatured'] ?? false,
      prepTime: data['prepTime'] ?? 30,
      cookTime: data['cookTime'] ?? 30,
      servings: data['servings'] ?? 4,
      difficulty: data['difficulty'] ?? 'متوسط',
      calories: data['calories']?.toDouble(),
      protein: data['protein']?.toDouble(),
      carbs: data['carbs']?.toDouble(),
      fat: data['fat']?.toDouble(),
      tags: List<String>.from(data['tags'] ?? []),
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
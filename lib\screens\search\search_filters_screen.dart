import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/providers/search_provider.dart';
import 'package:yassincil/services/advanced_search_service.dart';

class SearchFiltersScreen extends StatefulWidget {
  const SearchFiltersScreen({super.key});

  @override
  State<SearchFiltersScreen> createState() => _SearchFiltersScreenState();
}

class _SearchFiltersScreenState extends State<SearchFiltersScreen> {
  late bool _glutenFreeOnly;
  String? _selectedCategory;
  DateTime? _dateFrom;
  DateTime? _dateTo;
  double? _minRating;
  bool _verifiedOnly = false;

  final List<String> _categories = [
    'مخبوزات',
    'ألبان',
    'حلويات',
    'مشروبات',
    'خضروات',
    'فواكه',
    'لحوم',
    'أسماك',
    'حبوب',
    'توابل',
  ];

  @override
  void initState() {
    super.initState();
    final searchProvider = Provider.of<SearchProvider>(context, listen: false);
    _glutenFreeOnly = searchProvider.filters.glutenFreeOnly;
    _selectedCategory = searchProvider.filters.category;
    _dateFrom = searchProvider.filters.dateFrom;
    _dateTo = searchProvider.filters.dateTo;
    _minRating = searchProvider.filters.minRating;
    _verifiedOnly = searchProvider.filters.verifiedOnly;
  }

  void _applyFilters() {
    final searchProvider = Provider.of<SearchProvider>(context, listen: false);
    
    final filters = SearchFilters(
      glutenFreeOnly: _glutenFreeOnly,
      category: _selectedCategory,
      dateFrom: _dateFrom,
      dateTo: _dateTo,
      minRating: _minRating,
      verifiedOnly: _verifiedOnly,
    );

    searchProvider.updateFilters(filters);
    Navigator.of(context).pop();
  }

  void _resetFilters() {
    setState(() {
      _glutenFreeOnly = false;
      _selectedCategory = null;
      _dateFrom = null;
      _dateTo = null;
      _minRating = null;
      _verifiedOnly = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'فلاتر البحث',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _resetFilters,
            child: Text(
              'إعادة تعيين',
              style: GoogleFonts.cairo(color: theme.primaryColor),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // فلتر خالي من الغلوتين
            _buildGlutenFreeFilter(theme),
            
            const SizedBox(height: 20),
            
            // فلتر الفئة
            _buildCategoryFilter(theme),
            
            const SizedBox(height: 20),
            
            // فلتر التاريخ
            _buildDateFilter(theme),
            
            const SizedBox(height: 20),
            
            // فلتر التقييم
            _buildRatingFilter(theme),
            
            const SizedBox(height: 20),
            
            // فلتر المحتوى المعتمد
            _buildVerifiedFilter(theme),
            
            const SizedBox(height: 32),
            
            // أزرار التطبيق والإلغاء
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: BorderSide(color: theme.primaryColor),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'إلغاء',
                      style: GoogleFonts.cairo(
                        color: theme.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _applyFilters,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.primaryColor,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'تطبيق الفلاتر',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGlutenFreeFilter(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.deepPurple.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.health_and_safety, color: Colors.green[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'خالي من الغلوتين',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          SwitchListTile(
            title: Text(
              'إظهار المنتجات الخالية من الغلوتين فقط',
              style: GoogleFonts.cairo(),
            ),
            value: _glutenFreeOnly,
            onChanged: (value) {
              setState(() {
                _glutenFreeOnly = value;
              });
            },
            activeColor: Colors.green[600],
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.deepPurple.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.category, color: theme.primaryColor, size: 20),
              const SizedBox(width: 8),
              Text(
                'الفئة',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          DropdownButtonFormField<String>(
            value: _selectedCategory,
            decoration: InputDecoration(
              hintText: 'اختر الفئة',
              hintStyle: GoogleFonts.cairo(),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            items: [
              DropdownMenuItem<String>(
                value: null,
                child: Text('جميع الفئات', style: GoogleFonts.cairo()),
              ),
              ..._categories.map((category) {
                return DropdownMenuItem<String>(
                  value: category,
                  child: Text(category, style: GoogleFonts.cairo()),
                );
              }),
            ],
            onChanged: (value) {
              setState(() {
                _selectedCategory = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDateFilter(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.deepPurple.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.date_range, color: Colors.blue[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'فترة زمنية',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDateField(
                  'من تاريخ',
                  _dateFrom,
                  (date) => setState(() => _dateFrom = date),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDateField(
                  'إلى تاريخ',
                  _dateTo,
                  (date) => setState(() => _dateTo = date),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateField(String label, DateTime? date, Function(DateTime?) onChanged) {
    return InkWell(
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: date ?? DateTime.now(),
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
        );
        onChanged(selectedDate);
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              date != null 
                  ? '${date.day}/${date.month}/${date.year}'
                  : 'اختر التاريخ',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: date != null ? Colors.black : Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingFilter(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.deepPurple.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.star, color: Colors.orange[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'التقييم الأدنى',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _minRating ?? 0,
                  min: 0,
                  max: 5,
                  divisions: 10,
                  label: _minRating?.toStringAsFixed(1) ?? '0.0',
                  onChanged: (value) {
                    setState(() {
                      _minRating = value == 0 ? null : value;
                    });
                  },
                ),
              ),
              Text(
                _minRating?.toStringAsFixed(1) ?? '0.0',
                style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVerifiedFilter(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.deepPurple.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.verified, color: Colors.blue[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'المحتوى المعتمد',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          SwitchListTile(
            title: Text(
              'إظهار المحتوى المعتمد فقط',
              style: GoogleFonts.cairo(),
            ),
            subtitle: Text(
              'المحتوى الذي تم التحقق منه من قبل المشرفين',
              style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey[600]),
            ),
            value: _verifiedOnly,
            onChanged: (value) {
              setState(() {
                _verifiedOnly = value;
              });
            },
            activeColor: Colors.blue[600],
          ),
        ],
      ),
    );
  }
}

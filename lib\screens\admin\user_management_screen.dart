import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:yassincil/models/user_profile.dart';
import 'package:yassincil/providers/user_management_provider.dart';
import 'package:yassincil/providers/auth_provider.dart'; // Needed to check current user's UID
import 'package:yassincil/utils/app_constants.dart';

class UserManagementScreen extends StatelessWidget {
  const UserManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المستخدمين'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: Consumer<UserManagementProvider>(
        builder: (context, userProvider, child) {
          if (userProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          if (userProvider.errorMessage != null) {
            return Center(child: Text(userProvider.errorMessage!));
          }
          if (userProvider.users.isEmpty) {
            return const Center(child: Text('لا يوجد مستخدمون مسجلون بعد.'));
          }

          return ListView.builder(
            itemCount: userProvider.users.length,
            itemBuilder: (context, index) {
              final user = userProvider.users[index];
              final currentUserId = Provider.of<AuthProvider>(
                context,
                listen: false,
              ).currentUser?.uid;

              // Prevent admin from changing their own role or deleting themselves
              final isCurrentUser = user.uid == currentUserId;

              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                child: ListTile(
                  title: Text(user.displayName),
                  subtitle: Text(
                    '${user.usernameDisplay} - الدور: ${user.role == AppConstants.adminRole ? 'مشرف' : 'مستخدم عادي'}',
                  ),
                  trailing: isCurrentUser
                      ? const Text('أنت', style: TextStyle(color: Colors.grey))
                      : PopupMenuButton<String>(
                          onSelected: (String value) {
                            if (value == 'toggle_role') {
                              _showRoleChangeDialog(
                                context,
                                userProvider,
                                user,
                              );
                            } else if (value == 'delete_user') {
                              _showDeleteUserDialog(
                                context,
                                userProvider,
                                user,
                              );
                            }
                          },
                          itemBuilder: (BuildContext context) =>
                              <PopupMenuEntry<String>>[
                                PopupMenuItem<String>(
                                  value: 'toggle_role',
                                  enabled:
                                      !isCurrentUser, // Disable if it's the current user
                                  child: Text(
                                    user.role == AppConstants.adminRole
                                        ? 'جعل مستخدم عادي'
                                        : 'جعل مشرف',
                                  ),
                                ),
                                PopupMenuItem<String>(
                                  value: 'delete_user',
                                  enabled:
                                      !isCurrentUser, // Disable if it's the current user
                                  child: const Text(
                                    'حذف المستخدم',
                                    style: TextStyle(color: Colors.red),
                                  ),
                                ),
                              ],
                        ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  void _showRoleChangeDialog(
    BuildContext context,
    UserManagementProvider userProvider,
    UserProfile user,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        String newRole = user.role == AppConstants.adminRole
            ? AppConstants.userRole
            : AppConstants.adminRole;
        return AlertDialog(
          title: const Text('تغيير دور المستخدم'),
          content: Text(
            'هل أنت متأكد أنك تريد تغيير دور ${user.username} إلى ${newRole == AppConstants.adminRole ? 'مشرف' : 'مستخدم عادي'}؟',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('إلغاء'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
            ),
            ElevatedButton(
              child: const Text('تأكيد'),
              onPressed: () async {
                Navigator.of(dialogContext).pop();
                try {
                  await userProvider.updateUserRole(user.uid, newRole);
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم تحديث دور ${user.username} بنجاح.'),
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('فشل تحديث الدور: ${e.toString()}'),
                      ),
                    );
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }

  void _showDeleteUserDialog(
    BuildContext context,
    UserManagementProvider userProvider,
    UserProfile user,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('حذف المستخدم'),
          content: Text(
            'هل أنت متأكد أنك تريد حذف المستخدم ${user.username}؟ هذا سيحذف ملفه الشخصي فقط من قاعدة البيانات، ولن يحذفه من مصادقة Firebase.',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('إلغاء'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
            ),
            ElevatedButton(
              child: const Text('حذف', style: TextStyle(color: Colors.white)),
              onPressed: () async {
                Navigator.of(dialogContext).pop();
                try {
                  await userProvider.deleteUser(user.uid);
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'تم حذف ملف ${user.username} الشخصي بنجاح.',
                        ),
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('فشل حذف المستخدم: ${e.toString()}'),
                      ),
                    );
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }
}

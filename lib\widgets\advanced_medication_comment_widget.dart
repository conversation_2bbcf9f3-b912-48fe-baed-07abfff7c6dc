// lib/widgets/advanced_medication_comment_widget.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../models/comment.dart';
import '../providers/medication_provider.dart';
import '../providers/auth_provider.dart' as app_auth;
import '../screens/medications/comment_replies_screen.dart';
import '../utils/app_colors.dart';

class AdvancedMedicationCommentWidget extends StatefulWidget {
  final Comment comment;
  final String medicationId;
  final VoidCallback? onReply;
  final bool showReplies;

  const AdvancedMedicationCommentWidget({
    super.key,
    required this.comment,
    required this.medicationId,
    this.onReply,
    this.showReplies = true,
  });

  @override
  State<AdvancedMedicationCommentWidget> createState() =>
      _AdvancedMedicationCommentWidgetState();
}

class _AdvancedMedicationCommentWidgetState
    extends State<AdvancedMedicationCommentWidget> {
  bool _isLiked = false;
  bool _isEditing = false;
  final TextEditingController _editController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _editController.text = widget.comment.content;
    _checkIfLiked();
  }

  @override
  void dispose() {
    _editController.dispose();
    super.dispose();
  }

  Future<void> _checkIfLiked() async {
    final medicationProvider = Provider.of<MedicationProvider>(
      context,
      listen: false,
    );
    final isLiked = await medicationProvider.hasLikedComment(
      medicationId: widget.medicationId,
      commentId: widget.comment.id!,
    );
    if (mounted) {
      setState(() {
        _isLiked = isLiked;
      });
    }
  }

  Future<void> _toggleLike(MedicationProvider medicationProvider) async {
    try {
      await medicationProvider.toggleCommentLike(
        medicationId: widget.medicationId,
        commentId: widget.comment.id!,
      );
      setState(() {
        _isLiked = !_isLiked;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحديث الإعجاب'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _saveEdit(MedicationProvider medicationProvider) async {
    if (_editController.text.trim().isEmpty) return;

    try {
      await medicationProvider.updateComment(
        medicationId: widget.medicationId,
        commentId: widget.comment.id!,
        newContent: _editController.text.trim(),
      );
      setState(() {
        _isEditing = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تعديل التعليق بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تعديل التعليق'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _deleteComment(
    MedicationProvider medicationProvider,
    bool isAdmin,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف التعليق', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف هذا التعليق؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'حذف',
              style: GoogleFonts.cairo(color: AppColors.error),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await medicationProvider.deleteComment(
          medicationId: widget.medicationId,
          commentId: widget.comment.id!,
          isAdmin: isAdmin,
        );
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف التعليق بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ أثناء حذف التعليق'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<app_auth.AuthProvider>(context);
    final medicationProvider = Provider.of<MedicationProvider>(context);
    final currentUser = authProvider.currentUser;
    final isOwner = currentUser?.uid == widget.comment.userId;
    // طباعة معلومات التصحيح
    debugPrint('Current User ID: ${currentUser?.uid}');
    debugPrint('Comment User ID: ${widget.comment.userId}');
    debugPrint('Is Owner: $isOwner');
    final isAdmin = authProvider.isAdmin;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس التعليق
              _buildCommentHeader(isOwner, isAdmin),

              const SizedBox(height: 8),

              // محتوى التعليق
              if (_isEditing)
                _buildEditingWidget(medicationProvider)
              else
                _buildCommentContent(),

              // صور التعليق
              if (widget.comment.imageUrls.isNotEmpty) _buildCommentImages(),

              const SizedBox(height: 8),

              // أزرار التفاعل
              _buildActionButtons(medicationProvider, isOwner, isAdmin),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCommentHeader(bool isOwner, bool isAdmin) {
    return Row(
      children: [
        // صورة المستخدم
        CircleAvatar(
          radius: 16,
          backgroundColor: AppColors.primary.withValues(alpha: 0.1),
          backgroundImage: widget.comment.userAvatar != null
              ? NetworkImage(widget.comment.userAvatar!)
              : null,
          child: widget.comment.userAvatar == null
              ? Text(
                  _getUserInitials(widget.comment.username),
                  style: GoogleFonts.cairo(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                )
              : null,
        ),
        const SizedBox(width: 8),

        // اسم المستخدم والوقت
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    widget.comment.username,
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  if (widget.comment.isEdited) ...[
                    const SizedBox(width: 4),
                    Text(
                      '(محرر)',
                      style: GoogleFonts.cairo(
                        fontSize: 10,
                        color: AppColors.textSecondary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
              Text(
                _formatTime(widget.comment.createdAt),
                style: GoogleFonts.cairo(
                  fontSize: 10,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),

        // قائمة الخيارات
        if (isOwner || isAdmin)
          PopupMenuButton<String>(
            icon: Icon(
              Icons.more_vert,
              size: 16,
              color: AppColors.textSecondary,
            ),
            onSelected: (value) {
              if (value == 'edit' && isOwner) {
                setState(() {
                  _isEditing = true;
                });
              } else if (value == 'delete') {
                _deleteComment(
                  Provider.of<MedicationProvider>(context, listen: false),
                  isAdmin,
                );
              }
            },
            itemBuilder: (context) {
              List<PopupMenuEntry<String>> items = [];

              // إضافة خيار التعديل إذا كان المستخدم هو صاحب التعليق
              if (isOwner) {
                items.add(
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 16, color: AppColors.primary),
                        const SizedBox(width: 8),
                        Text('تعديل', style: GoogleFonts.cairo()),
                      ],
                    ),
                  ),
                );
              }

              // إضافة خيار الحذف للمشرف أو صاحب التعليق
              items.add(
                PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 16, color: AppColors.error),
                      const SizedBox(width: 8),
                      Text('حذف', style: GoogleFonts.cairo()),
                    ],
                  ),
                ),
              );

              return items;
            },
          ),
      ],
    );
  }

  Widget _buildCommentContent() {
    return Text(
      widget.comment.content,
      style: GoogleFonts.cairo(fontSize: 14, color: AppColors.textPrimary),
    );
  }

  Widget _buildEditingWidget(MedicationProvider medicationProvider) {
    return Column(
      children: [
        TextField(
          controller: _editController,
          maxLines: null,
          style: GoogleFonts.cairo(),
          decoration: InputDecoration(
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.primary),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () {
                setState(() {
                  _isEditing = false;
                  _editController.text = widget.comment.content;
                });
              },
              child: Text('إلغاء', style: GoogleFonts.cairo()),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () => _saveEdit(medicationProvider),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
              ),
              child: Text(
                'حفظ',
                style: GoogleFonts.cairo(color: AppColors.textOnPrimary),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCommentImages() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.comment.imageUrls.length,
        itemBuilder: (context, index) {
          final imageUrl = widget.comment.imageUrls[index];

          return GestureDetector(
            onTap: () => _showFullScreenImage(imageUrl),
            child: Container(
              margin: const EdgeInsets.only(left: 8),
              width: 100,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.border),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  imageUrl,
                  fit: BoxFit.cover,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                  loadingProgress.expectedTotalBytes!
                            : null,
                        strokeWidth: 2,
                        color: AppColors.primary,
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) {
                    debugPrint('Error loading image: $error');
                    debugPrint('Image URL: $imageUrl');
                    return Container(
                      color: AppColors.background,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.broken_image,
                            color: AppColors.textSecondary,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'خطأ',
                            style: GoogleFonts.cairo(
                              fontSize: 10,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _showFullScreenImage(String imageUrl) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            iconTheme: const IconThemeData(color: Colors.white),
            title: Text(
              'عرض الصورة',
              style: GoogleFonts.cairo(color: Colors.white),
            ),
          ),
          body: Center(
            child: InteractiveViewer(
              minScale: 0.5,
              maxScale: 4.0,
              child: Image.network(
                imageUrl,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) => Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.broken_image, color: Colors.white, size: 64),
                    const SizedBox(height: 16),
                    Text(
                      'تعذر تحميل الصورة',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 18,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(
    MedicationProvider medicationProvider,
    bool isOwner,
    bool isAdmin,
  ) {
    return Row(
      children: [
        // زر الإعجاب
        InkWell(
          onTap: () => _toggleLike(medicationProvider),
          child: Row(
            children: [
              Icon(
                _isLiked ? Icons.favorite : Icons.favorite_border,
                size: 18,
                color: _isLiked ? AppColors.error : AppColors.textSecondary,
              ),
              const SizedBox(width: 4),
              Text(
                '${widget.comment.likesCount}',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: _isLiked ? AppColors.error : AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(width: 16),

        // زر التعديل المباشر (يظهر فقط لصاحب التعليق)
        if (isOwner)
          InkWell(
            onTap: () {
              setState(() {
                _isEditing = true;
              });
            },
            child: Row(
              children: [
                Icon(Icons.edit, size: 18, color: AppColors.primary),
                const SizedBox(width: 4),
                Text(
                  'تعديل',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),

        const SizedBox(width: 16),

        // زر الرد
        if (widget.comment.parentCommentId == null)
          InkWell(
            onTap: _navigateToReplies,
            child: Row(
              children: [
                Icon(Icons.reply, size: 18, color: AppColors.textSecondary),
                const SizedBox(width: 4),
                Text(
                  'رد',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

        const Spacer(),

        // زر عرض الردود
        if (widget.comment.parentCommentId == null &&
            widget.comment.repliesCount > 0)
          InkWell(
            onTap: _navigateToReplies,
            child: Row(
              children: [
                Icon(Icons.forum_outlined, size: 18, color: AppColors.primary),
                const SizedBox(width: 4),
                Text(
                  '${widget.comment.repliesCount} ${widget.comment.repliesCount == 1 ? 'رد' : 'ردود'}',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  void _navigateToReplies() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CommentRepliesScreen(
          medicationId: widget.medicationId,
          parentComment: widget.comment,
        ),
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  String _getUserInitials(String username) {
    if (username.isEmpty) return 'م';

    final nameParts = username.split(' ');
    if (nameParts.length > 1) {
      // إذا كان الاسم يحتوي على مسافة، نأخذ الحرف الأول من كل جزء
      return '${nameParts[0][0]}${nameParts[1][0]}';
    } else {
      // إذا كان الاسم مفرداً، نأخذ الحرف الأول فقط
      return username[0];
    }
  }
}

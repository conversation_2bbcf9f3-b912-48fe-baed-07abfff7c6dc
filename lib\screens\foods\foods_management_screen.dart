import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:yassincil/providers/food_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/models/food_item.dart';
import 'package:yassincil/screens/foods/add_edit_food_screen.dart';

class FoodsManagementScreen extends StatefulWidget {
  const FoodsManagementScreen({super.key});

  @override
  State<FoodsManagementScreen> createState() => _FoodsManagementScreenState();
}

class _FoodsManagementScreenState extends State<FoodsManagementScreen>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late TabController _tabController;
  String _searchQuery = '';
  String _selectedFilter = 'الكل';

  final List<String> _filters = ['الكل', 'خالي من الجلوتين', 'غير آمن'];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _tabController = TabController(length: _filters.length, vsync: this);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _refresh() async {
    if (!mounted) return;

    try {
      final foodProvider = Provider.of<FoodProvider>(context, listen: false);
      foodProvider.fetchFoodItems();
    } catch (e) {
      debugPrint('Error during refresh: $e');
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء تحديث البيانات',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<FoodItem> _getFilteredFoods(List<FoodItem> foods) {
    return foods.where((food) {
      final matchesSearch =
          _searchQuery.isEmpty ||
          food.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          food.category.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (food.brand?.toLowerCase().contains(_searchQuery.toLowerCase()) ??
              false);

      bool matchesFilter = true;
      switch (_selectedFilter) {
        case 'خالي من الجلوتين':
          matchesFilter = food.isGlutenFree;
          break;
        case 'غير آمن':
          matchesFilter = !food.isGlutenFree;
          break;
        default:
          matchesFilter = true;
      }

      return matchesSearch && matchesFilter;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final foodProvider = Provider.of<FoodProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final isAdmin = authProvider.isAdmin;

    final filteredFoods = _getFilteredFoods(foodProvider.foodItems);

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildFilterTabs(),
          _buildStatisticsCard(foodProvider.foodItems),
          Expanded(child: _buildContent(filteredFoods, foodProvider, isAdmin)),
        ],
      ),
      floatingActionButton: isAdmin ? _buildAddFAB() : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'إدارة الأطعمة',
        style: GoogleFonts.cairo(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: const Color(0xFF4CAF50),
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh, color: Colors.white),
          onPressed: _refresh,
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFF4CAF50),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TextField(
          controller: _searchController,
          style: GoogleFonts.cairo(),
          decoration: InputDecoration(
            hintText: 'ابحث عن طعام...',
            hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500),
            prefixIcon: const Icon(Icons.search, color: Color(0xFF4CAF50)),
            suffixIcon: _searchQuery.isNotEmpty
                ? IconButton(
                    icon: Icon(Icons.clear, color: Colors.grey.shade600),
                    onPressed: () {
                      _searchController.clear();
                      setState(() {
                        _searchQuery = '';
                      });
                    },
                  )
                : null,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 15,
            ),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
      ),
    );
  }

  Widget _buildFilterTabs() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: TabBar(
        controller: _tabController,
        indicatorColor: const Color(0xFF4CAF50),
        labelColor: const Color(0xFF4CAF50),
        unselectedLabelColor: Colors.grey.shade600,
        labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        unselectedLabelStyle: GoogleFonts.cairo(fontWeight: FontWeight.normal),
        onTap: (index) {
          setState(() {
            _selectedFilter = _filters[index];
          });
        },
        tabs: _filters.map((filter) => Tab(text: filter)).toList(),
      ),
    );
  }

  Widget _buildStatisticsCard(List<FoodItem> allFoods) {
    final totalFoods = allFoods.length;
    final glutenFreeFoods = allFoods.where((f) => f.isGlutenFree).length;
    final notGlutenFreeFoods = allFoods.where((f) => !f.isGlutenFree).length;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.analytics,
                    color: Color(0xFF4CAF50),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'إحصائيات الأطعمة',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'إجمالي الأطعمة',
                      totalFoods.toString(),
                      const Color(0xFF4CAF50),
                      Icons.restaurant_menu,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatItem(
                      'خالي من الجلوتين',
                      glutenFreeFoods.toString(),
                      Colors.green.shade600,
                      Icons.check_circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatItem(
                      'غير آمن',
                      notGlutenFreeFoods.toString(),
                      Colors.red.shade600,
                      Icons.cancel,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildContent(
    List<FoodItem> foods,
    FoodProvider foodProvider,
    bool isAdmin,
  ) {
    if (foodProvider.isLoading && foods.isEmpty) {
      return _buildLoadingState();
    }

    if (foodProvider.errorMessage != null && foods.isEmpty) {
      return _buildErrorState(foodProvider);
    }

    if (foods.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _refresh,
      color: const Color(0xFF4CAF50),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: foods.length,
        itemBuilder: (context, index) {
          return _buildFoodCard(foods[index], foodProvider, isAdmin);
        },
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(color: Color(0xFF4CAF50)),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل الأطعمة...',
            style: GoogleFonts.cairo(fontSize: 16, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(FoodProvider foodProvider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 80, color: Colors.red.shade300),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ أثناء تحميل البيانات',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            foodProvider.errorMessage ?? 'خطأ غير معروف',
            textAlign: TextAlign.center,
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: _refresh,
            icon: const Icon(Icons.refresh),
            label: Text('إعادة المحاولة', style: GoogleFonts.cairo()),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.fastfood_outlined, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد أطعمة مطابقة',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير معايير البحث أو الفلترة',
            textAlign: TextAlign.center,
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildFoodCard(
    FoodItem food,
    FoodProvider foodProvider,
    bool isAdmin,
  ) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () {
          _showFoodDetails(food);
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: LinearGradient(
                    colors: [Colors.green.shade100, Colors.green.shade50],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: food.imageUrl != null && food.imageUrl!.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.network(
                          food.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Icon(
                            Icons.restaurant,
                            color: Colors.green.shade300,
                            size: 30,
                          ),
                        ),
                      )
                    : Icon(
                        Icons.restaurant,
                        color: Colors.green.shade300,
                        size: 30,
                      ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            food.name,
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _buildStatusBadge(food.isGlutenFree),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      food.category,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              if (isAdmin) _buildAdminActions(food, foodProvider),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(bool isGlutenFree) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isGlutenFree ? Colors.green.shade50 : Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isGlutenFree ? Colors.green.shade300 : Colors.red.shade300,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isGlutenFree ? Icons.check_circle : Icons.cancel,
            size: 14,
            color: isGlutenFree ? Colors.green.shade600 : Colors.red.shade600,
          ),
          const SizedBox(width: 4),
          Text(
            isGlutenFree ? 'آمن' : 'غير آمن',
            style: GoogleFonts.cairo(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: isGlutenFree ? Colors.green.shade600 : Colors.red.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdminActions(FoodItem food, FoodProvider foodProvider) {
    return PopupMenuButton<String>(
      icon: Icon(Icons.more_vert, color: Colors.grey.shade600, size: 20),
      onSelected: (value) async {
        switch (value) {
          case 'edit':
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => AddEditFoodScreen(foodItem: food),
              ),
            );
            break;
          case 'toggle':
            await _toggleFoodStatus(food, foodProvider);
            break;
          case 'delete':
            await _deleteFood(food, foodProvider);
            break;
        }
      },
      itemBuilder: (context) => [
        PopupMenuItem<String>(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 16, color: Colors.green.shade600),
              const SizedBox(width: 8),
              Text('تعديل', style: GoogleFonts.cairo(fontSize: 12)),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'toggle',
          child: Row(
            children: [
              Icon(
                food.isGlutenFree ? Icons.block : Icons.check_circle,
                size: 16,
                color: food.isGlutenFree
                    ? Colors.red.shade600
                    : Colors.green.shade600,
              ),
              const SizedBox(width: 8),
              Text(
                food.isGlutenFree ? 'جعله غير آمن' : 'جعله آمناً',
                style: GoogleFonts.cairo(fontSize: 12),
              ),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 16, color: Colors.red.shade600),
              const SizedBox(width: 8),
              Text('حذف', style: GoogleFonts.cairo(fontSize: 12)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAddFAB() {
    return FloatingActionButton.extended(
      onPressed: () {
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const AddEditFoodScreen()),
        );
      },
      backgroundColor: const Color(0xFF4CAF50),
      foregroundColor: Colors.white,
      icon: const Icon(Icons.add),
      label: Text('إضافة طعام', style: GoogleFonts.cairo()),
    );
  }

  void _showFoodDetails(FoodItem food) {
    // Implementation can be similar to _showMedicationDetails if needed
  }

  Future<void> _toggleFoodStatus(
    FoodItem food,
    FoodProvider foodProvider,
  ) async {
    try {
      await foodProvider.updateFoodItem(
        foodId: food.id!,
        name: food.name,
        details: food.details,
        category: food.category,
        isGlutenFree: !food.isGlutenFree,
        imageUrl: food.imageUrl,
      );

      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تحديث حالة الطعام بنجاح',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في تحديث حالة الطعام',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteFood(FoodItem food, FoodProvider foodProvider) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(
          'تأكيد الحذف',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل أنت متأكد أنك تريد حذف "${food.name}"؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(false),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(ctx).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await foodProvider.deleteFoodItem(food.id!);
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف الطعام بنجاح', style: GoogleFonts.cairo()),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في حذف الطعام', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}

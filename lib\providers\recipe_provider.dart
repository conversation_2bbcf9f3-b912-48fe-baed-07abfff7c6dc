import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:yassincil/models/recipe.dart';
import 'package:yassincil/models/comment.dart';
import 'package:yassincil/services/firestore_service.dart';
import 'package:yassincil/utils/app_constants.dart';

class RecipeProvider with ChangeNotifier {
  final FirestoreService _firestoreService;
  static const int _pageSize = 10;

  List<Recipe> _recipes = [];
  bool _isLoading = false;
  String? _errorMessage;
  DocumentSnapshot? _lastDocument;
  bool _isFetchingMore = false;
  bool _hasMore = true;

  List<Recipe> get recipes => _recipes;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isFetchingMore => _isFetchingMore;
  bool get hasMore => _hasMore;

  RecipeProvider(this._firestoreService) {
    fetchInitialRecipes(approvedStatus: true);
  }

  Future<void> fetchInitialRecipes({bool? approvedStatus}) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      Query query = _firestoreService.db.collection(
        AppConstants.recipesCollection,
      );

      if (approvedStatus != null) {
        query = query.where('isApproved', isEqualTo: approvedStatus);
      }

      query = query.orderBy('createdAt', descending: true).limit(_pageSize);

      final snapshot = await query.get();

      _recipes = snapshot.docs
          .map((doc) {
            try {
              return Recipe.fromFirestore(doc);
            } catch (e) {
              debugPrint('خطأ في تحويل وصفة ${doc.id}: $e');
              return null;
            }
          })
          .where((recipe) => recipe != null)
          .cast<Recipe>()
          .toList();

      if (snapshot.docs.isNotEmpty) {
        _lastDocument = snapshot.docs.last;
        _hasMore = snapshot.docs.length == _pageSize;
      } else {
        _lastDocument = null;
        _hasMore = false;
      }
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء جلب الوصفات: $e";
      debugPrint("Error fetching initial recipes: $e");
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> fetchMoreRecipes({bool? approvedStatus}) async {
    if (_isFetchingMore || !_hasMore) return;

    _isFetchingMore = true;
    notifyListeners();

    try {
      Query query = _firestoreService.db.collection(
        AppConstants.recipesCollection,
      );

      if (approvedStatus != null) {
        query = query.where('isApproved', isEqualTo: approvedStatus);
      }

      query = query
          .orderBy('createdAt', descending: true)
          .startAfterDocument(_lastDocument!)
          .limit(_pageSize);

      final snapshot = await query.get();

      final newRecipes = snapshot.docs
          .map((doc) {
            try {
              return Recipe.fromFirestore(doc);
            } catch (e) {
              debugPrint('خطأ في تحويل وصفة ${doc.id}: $e');
              return null;
            }
          })
          .where((recipe) => recipe != null)
          .cast<Recipe>()
          .toList();

      _recipes.addAll(newRecipes);

      if (snapshot.docs.isNotEmpty) {
        _lastDocument = snapshot.docs.last;
        _hasMore = snapshot.docs.length == _pageSize;
      } else {
        _hasMore = false;
      }
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء جلب المزيد من الوصفات: $e";
      debugPrint("Error fetching more recipes: $e");
    } finally {
      _isFetchingMore = false;
      notifyListeners();
    }
  }

  Future<List<Recipe>> fetchRecipesByIds(List<String> ids) async {
    if (ids.isEmpty) return [];
    try {
      final List<Recipe> favoriteRecipes = [];
      // Firestore 'whereIn' query is limited to 10 elements.
      // We need to batch the requests.
      for (var i = 0; i < ids.length; i += 10) {
        final sublist = ids.sublist(
          i,
          i + 10 > ids.length ? ids.length : i + 10,
        );
        final snapshot = await _firestoreService.db
            .collection(AppConstants.recipesCollection)
            .where(FieldPath.documentId, whereIn: sublist)
            .get();
        favoriteRecipes.addAll(
          snapshot.docs.map((doc) => Recipe.fromFirestore(doc)).toList(),
        );
      }
      return favoriteRecipes;
    } catch (e) {
      debugPrint("Error fetching recipes by IDs: $e");
      return [];
    }
  }

  Future<void> addRecipe(Recipe recipe) async {
    try {
      await _firestoreService.addDocument(
        AppConstants.recipesCollection,
        recipe.toMap(),
      );
      await fetchInitialRecipes(approvedStatus: true);
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء إضافة الوصفة: $e";
      rethrow;
    }
  }

  Future<void> updateRecipe(Recipe recipe) async {
    try {
      if (recipe.id == null) {
        throw Exception("Recipe ID is required for update.");
      }
      await _firestoreService.updateDocument(
        AppConstants.recipesCollection,
        recipe.id!,
        recipe.toMap(),
      );
      await fetchInitialRecipes(approvedStatus: true);
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء تحديث الوصفة: $e";
      rethrow;
    }
  }

  Future<void> deleteRecipe(String recipeId) async {
    try {
      await _firestoreService.deleteDocument(
        AppConstants.recipesCollection,
        recipeId,
      );
      await fetchInitialRecipes(approvedStatus: true);
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء حذف الوصفة: $e";
      rethrow;
    }
  }

  Future<void> updateRecipeApprovalStatus(
    String recipeId,
    bool isApproved,
  ) async {
    try {
      await _firestoreService.updateDocument(
        AppConstants.recipesCollection,
        recipeId,
        {'isApproved': isApproved},
      );
      await fetchInitialRecipes(approvedStatus: true); // Refresh approved list
      await fetchInitialRecipes(
        approvedStatus: false,
      ); // Refresh unapproved list
    } catch (e) {
      debugPrint("Error updating recipe approval status: $e");
      rethrow;
    }
  }

  // --- Favorite Methods ---
  Future<void> incrementFavoriteCount(String recipeId) async {
    try {
      await _firestoreService.db
          .collection(AppConstants.recipesCollection)
          .doc(recipeId)
          .update({'favoriteCount': FieldValue.increment(1)});
    } catch (e) {
      debugPrint('Error incrementing favorite count: $e');
    }
  }

  Future<void> decrementFavoriteCount(String recipeId) async {
    try {
      await _firestoreService.db
          .collection(AppConstants.recipesCollection)
          .doc(recipeId)
          .update({'favoriteCount': FieldValue.increment(-1)});
    } catch (e) {
      debugPrint('Error decrementing favorite count: $e');
    }
  }

  // --- Like Methods ---
  Future<bool> isRecipeLiked(String recipeId, String userId) async {
    try {
      final doc = await _firestoreService.db
          .collection(AppConstants.recipesCollection)
          .doc(recipeId)
          .get();
      if (!doc.exists) return false;
      final data = doc.data() as Map<String, dynamic>;
      final List<dynamic> likes = (data['likes'] as List<dynamic>?) ?? [];
      return likes.contains(userId);
    } catch (e) {
      debugPrint('Error checking recipe like status: $e');
      return false;
    }
  }

  Future<void> toggleRecipeLike(String recipeId, String userId) async {
    try {
      final recipeRef = _firestoreService.db
          .collection(AppConstants.recipesCollection)
          .doc(recipeId);

      await _firestoreService.db.runTransaction((transaction) async {
        final snap = await transaction.get(recipeRef);
        if (!snap.exists) throw Exception('Recipe does not exist');
        final data = snap.data() as Map<String, dynamic>;
        final List<dynamic> likes = (data['likes'] as List<dynamic>?) ?? [];
        final int likesCount = (data['likesCount'] as int?) ?? 0;

        if (likes.contains(userId)) {
          // Unlike
          transaction.update(recipeRef, {
            'likes': FieldValue.arrayRemove([userId]),
            'likesCount': likesCount > 0 ? likesCount - 1 : 0,
            'updatedAt': FieldValue.serverTimestamp(),
          });

          // تحديث محلي للقائمة لسرعة الانعكاس على الواجهة
          final idx = _recipes.indexWhere((r) => r.id == recipeId);
          if (idx != -1) {
            final current = _recipes[idx];
            final newLikes = List<String>.from(current.likes)..remove(userId);
            _recipes[idx] = current.copyWith(
              likes: newLikes,
              likesCount: (current.likesCount - 1).clamp(0, 1 << 30),
              updatedAt: DateTime.now(),
            );
          }
        } else {
          // Like
          transaction.update(recipeRef, {
            'likes': FieldValue.arrayUnion([userId]),
            'likesCount': likesCount + 1,
            'updatedAt': FieldValue.serverTimestamp(),
          });

          final idx = _recipes.indexWhere((r) => r.id == recipeId);
          if (idx != -1) {
            final current = _recipes[idx];
            final newLikes = List<String>.from(current.likes)..add(userId);
            _recipes[idx] = current.copyWith(
              likes: newLikes,
              likesCount: current.likesCount + 1,
              updatedAt: DateTime.now(),
            );
          }
        }
      });

      notifyListeners();
    } catch (e) {
      debugPrint('Error toggling like on recipe: $e');
      rethrow;
    }
  }

  // --- Rating Methods ---
  Future<void> setRecipeRating({
    required String recipeId,
    required String userId,
    required int rating,
  }) async {
    assert(rating >= 1 && rating <= 5);
    try {
      final recipeRef = _firestoreService.db
          .collection(AppConstants.recipesCollection)
          .doc(recipeId);
      final ratingRef = recipeRef.collection('ratings').doc(userId);

      await _firestoreService.db.runTransaction((transaction) async {
        final recipeSnap = await transaction.get(recipeRef);
        if (!recipeSnap.exists) throw Exception('Recipe does not exist');

        final data = recipeSnap.data() ?? {};
        final int ratingsCount = (data['ratingsCount'] as int?) ?? 0;
        final double sumRatings =
            (data['sumRatings'] as num?)?.toDouble() ??
            ((data['averageRating'] as num?)?.toDouble() ?? 0.0) * ratingsCount;

        final prevRatingSnap = await transaction.get(ratingRef);
        int newCount = ratingsCount;
        double newSum = sumRatings;

        if (prevRatingSnap.exists) {
          final prev = (prevRatingSnap.data()?['value'] as num?)?.toInt() ?? 0;
          newSum = newSum - prev + rating;
          transaction.update(ratingRef, {
            'value': rating,
            'updatedAt': FieldValue.serverTimestamp(),
          });
        } else {
          newSum = newSum + rating;
          newCount = ratingsCount + 1;
          transaction.set(ratingRef, {
            'userId': userId,
            'value': rating,
            'createdAt': FieldValue.serverTimestamp(),
          });
        }

        final double newAverage = newCount > 0 ? newSum / newCount : 0.0;
        transaction.update(recipeRef, {
          'sumRatings': newSum,
          'ratingsCount': newCount,
          'averageRating': newAverage,
          'updatedAt': FieldValue.serverTimestamp(),
        });
      });
      await fetchInitialRecipes(approvedStatus: true);
    } catch (e) {
      debugPrint('خطأ في حفظ التقييم: $e');
      rethrow;
    }
  }

  Future<int?> getUserRatingForRecipe(String recipeId, String userId) async {
    try {
      final ratingDoc = await _firestoreService.db
          .collection(AppConstants.recipesCollection)
          .doc(recipeId)
          .collection('ratings')
          .doc(userId)
          .get();

      if (ratingDoc.exists) {
        return (ratingDoc.data()?['value'] as num?)?.toInt();
      }
      return null;
    } catch (e) {
      debugPrint('Error fetching user rating: $e');
      return null;
    }
  }

  // --- Comment Methods ---
  Stream<List<Comment>> getCommentsStream(String recipeId) {
    try {
      return _firestoreService.db
          .collection(AppConstants.recipesCollection)
          .doc(recipeId)
          .collection('comments')
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) {
            return snapshot.docs
                .map((doc) => Comment.fromFirestore(doc))
                .toList();
          });
    } catch (e) {
      debugPrint('خطأ في getCommentsStream: $e');
      return Stream.value([]);
    }
  }

  Future<void> addComment(String recipeId, Comment comment) async {
    try {
      final batch = _firestoreService.db.batch();
      final recipeRef = _firestoreService.db
          .collection(AppConstants.recipesCollection)
          .doc(recipeId);
      final commentRef = recipeRef.collection('comments').doc();

      batch.set(commentRef, comment.toMap());
      batch.update(recipeRef, {'commentsCount': FieldValue.increment(1)});

      await batch.commit();
    } catch (e) {
      debugPrint('خطأ في إضافة التعليق: $e');
      rethrow;
    }
  }

  Future<void> updateComment({
    required String recipeId,
    required String commentId,
    required String newContent,
  }) async {
    try {
      await _firestoreService.db
          .collection(AppConstants.recipesCollection)
          .doc(recipeId)
          .collection('comments')
          .doc(commentId)
          .update({
            'content': newContent,
            'isEdited': true,
            'updatedAt': FieldValue.serverTimestamp(),
          });
    } catch (e) {
      debugPrint('خطأ في تحديث التعليق: $e');
      rethrow;
    }
  }

  Future<void> deleteComment({
    required String recipeId,
    required String commentId,
  }) async {
    try {
      final batch = _firestoreService.db.batch();
      final recipeRef = _firestoreService.db
          .collection(AppConstants.recipesCollection)
          .doc(recipeId);
      final commentRef = recipeRef.collection('comments').doc(commentId);

      batch.delete(commentRef);
      batch.update(recipeRef, {'commentsCount': FieldValue.increment(-1)});

      await batch.commit();
    } catch (e) {
      debugPrint('خطأ في حذف التعليق: $e');
      rethrow;
    }
  }

  Future<void> toggleCommentLike({
    required String recipeId,
    required String commentId,
    required String userId,
  }) async {
    final likeRef = _firestoreService.db
        .collection(AppConstants.recipesCollection)
        .doc(recipeId)
        .collection('comments')
        .doc(commentId)
        .collection('likes')
        .doc(userId);

    final commentRef = _firestoreService.db
        .collection(AppConstants.recipesCollection)
        .doc(recipeId)
        .collection('comments')
        .doc(commentId);

    await _firestoreService.db.runTransaction((transaction) async {
      final likeDoc = await transaction.get(likeRef);
      final commentDoc = await transaction.get(commentRef);

      if (!commentDoc.exists) return;

      if (likeDoc.exists) {
        transaction.delete(likeRef);
      } else {
        transaction.set(likeRef, {
          'userId': userId,
          'createdAt': FieldValue.serverTimestamp(),
        });
      }
    });
  }

  // --- Reply Methods ---
  Stream<List<Comment>> getRepliesForComment({
    required String recipeId,
    required String parentCommentId,
  }) {
    try {
      return _firestoreService.db
          .collection(AppConstants.recipesCollection)
          .doc(recipeId)
          .collection('comments')
          .doc(parentCommentId)
          .collection('replies')
          .orderBy('createdAt', descending: false)
          .snapshots()
          .map((snapshot) {
            return snapshot.docs
                .map((doc) => Comment.fromFirestore(doc))
                .toList();
          });
    } catch (e) {
      debugPrint('Error in getRepliesForComment: $e');
      return Stream.error(e);
    }
  }

  Future<void> addReplyToComment({
    required String recipeId,
    required String parentCommentId,
    required Comment reply,
  }) async {
    try {
      final batch = _firestoreService.db.batch();
      final parentCommentRef = _firestoreService.db
          .collection(AppConstants.recipesCollection)
          .doc(recipeId)
          .collection('comments')
          .doc(parentCommentId);
      final replyRef = parentCommentRef.collection('replies').doc();

      // تأكد من تمرير parentCommentId في كائن الرد
      final fixedReply =
          reply.parentCommentId == null || reply.parentCommentId!.isEmpty
          ? reply.copyWith(parentCommentId: parentCommentId)
          : reply;

      batch.set(replyRef, fixedReply.toMap());
      batch.update(parentCommentRef, {'repliesCount': FieldValue.increment(1)});

      await batch.commit();
    } catch (e) {
      debugPrint('خطأ في إضافة رد على التعليق: $e');
      rethrow;
    }
  }

  // --- Report Methods ---
  Future<void> reportComment({
    required String recipeId,
    required String commentId,
    required String reason,
    required String reporterId,
    required String reporterName,
  }) async {
    try {
      await _firestoreService.db.collection('reports').add({
        'reporterId': reporterId,
        'reporterName': reporterName,
        'targetId': commentId,
        'targetType': 'comment',
        'recipeId': recipeId,
        'reason': reason,
        'status': 'pending',
        'createdAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('خطأ في إرسال البلاغ: $e');
      rethrow;
    }
  }

  // --- Sample Data Methods (for debugging/testing) ---
  Future<void> addSampleRepliesToComment({
    required String recipeId,
    required String parentCommentId,
  }) async {
    try {
      final sampleReplies = [
        Comment(
          postId: recipeId,
          content: 'شكراً لك على التعليق! أتفق معك تماماً.',
          userId: 'user_reply_1',
          username: 'سارة أحمد',
          userAvatar: 'https://i.pravatar.cc/150?img=1',
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
          parentCommentId: parentCommentId,
        ),
        Comment(
          postId: recipeId,
          content: 'هل يمكن استبدال هذا المكون بشيء آخر؟',
          userId: 'user_reply_2',
          username: 'محمد علي',
          userAvatar: 'https://i.pravatar.cc/150?img=2',
          createdAt: DateTime.now().subtract(const Duration(hours: 1)),
          parentCommentId: parentCommentId,
        ),
      ];

      for (final reply in sampleReplies) {
        await addReplyToComment(
          recipeId: recipeId,
          parentCommentId: parentCommentId,
          reply: reply,
        );
      }
    } catch (e) {
      debugPrint('خطأ في إضافة الردود التجريبية: $e');
    }
  }
}

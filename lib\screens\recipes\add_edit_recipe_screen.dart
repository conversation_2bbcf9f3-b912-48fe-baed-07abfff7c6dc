import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import 'package:yassincil/models/recipe.dart';
import 'package:yassincil/providers/recipe_provider.dart';
import 'package:yassincil/providers/auth_provider.dart' as app_auth;
import 'package:yassincil/services/storage_service.dart';
import 'package:yassincil/utils/app_colors.dart';

class AddEditRecipeScreen extends StatefulWidget {
  final Recipe? recipe;
  const AddEditRecipeScreen({super.key, this.recipe});

  @override
  State<AddEditRecipeScreen> createState() => _AddEditRecipeScreenState();
}

class _AddEditRecipeScreenState extends State<AddEditRecipeScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _ingredientsController;
  late TextEditingController _instructionsController;
  late TextEditingController _prepTimeController;
  late TextEditingController _cookTimeController;
  late TextEditingController _servingsController;
  late TextEditingController _imageUrlController;
  late TextEditingController _caloriesController;
  late TextEditingController _proteinController;
  late TextEditingController _carbsController;
  late TextEditingController _fatController;

  bool _isLoading = false;
  bool _useImageUrl = false;
  bool _isGlutenFree = true;
  bool _isLactoseFree = false;
  String _selectedDifficulty = 'سهل';
  String _selectedCategory = 'أخرى';

  final List<File> _selectedImages = [];
  List<String> _imageUrls = [];
  final ImagePicker _picker = ImagePicker();
  final StorageService _storageService = StorageService();

  final List<String> _categories = [
    'فطور',
    'غداء',
    'عشاء',
    'حلويات',
    'مشروبات',
    'سلطات',
    'مقبلات',
    'مخبوزات',
    'أخرى',
  ];

  final List<String> _difficulties = ['سهل', 'متوسط', 'صعب'];

  @override
  void initState() {
    super.initState();
    final recipe = widget.recipe;
    _titleController = TextEditingController(text: recipe?.title ?? '');
    _descriptionController = TextEditingController(
      text: recipe?.description ?? '',
    );
    _ingredientsController = TextEditingController(
      text: recipe?.ingredients.join('\n') ?? '',
    );
    _instructionsController = TextEditingController(
      text: recipe?.instructions.join('\n') ?? '',
    );
    _prepTimeController = TextEditingController(
      text: recipe?.prepTime.toString() ?? '',
    );
    _cookTimeController = TextEditingController(
      text: recipe?.cookTime.toString() ?? '',
    );
    _servingsController = TextEditingController(
      text: recipe?.servings.toString() ?? '',
    );
    _imageUrlController = TextEditingController();
    _caloriesController = TextEditingController(text: recipe?.calories?.toString() ?? '');
    _proteinController = TextEditingController(text: recipe?.protein?.toString() ?? '');
    _carbsController = TextEditingController(text: recipe?.carbs?.toString() ?? '');
    _fatController = TextEditingController(text: recipe?.fat?.toString() ?? '');

    if (recipe != null) {
      _isGlutenFree = recipe.isGlutenFree;
      _isLactoseFree = recipe.isLactoseFree;
      _selectedDifficulty = recipe.difficulty;
      _selectedCategory = recipe.category;
      _imageUrls = List.from(recipe.imageUrls);
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _ingredientsController.dispose();
    _instructionsController.dispose();
    _prepTimeController.dispose();
    _cookTimeController.dispose();
    _servingsController.dispose();
    _imageUrlController.dispose();
    _caloriesController.dispose();
    _proteinController.dispose();
    _carbsController.dispose();
    _fatController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final pickedFiles = await _picker.pickMultiImage(
      maxWidth: 1024,
      imageQuality: 85,
    );
    if (pickedFiles.isNotEmpty) {
      setState(() {
        _selectedImages.addAll(pickedFiles.map((file) => File(file.path)));
      });
    }
  }

  void _removeImage(int index, bool isUrl) {
    setState(() {
      if (isUrl) {
        _imageUrls.removeAt(index);
      } else {
        _selectedImages.removeAt(index);
      }
    });
  }

  void _addImageFromUrl() {
    final url = _imageUrlController.text.trim();
    if (url.isEmpty) return;

    final uri = Uri.tryParse(url);
    final isHttp =
        uri != null && (uri.scheme == 'http' || uri.scheme == 'https');
    if (uri == null || !uri.hasAbsolutePath || !isHttp) {
      _showErrorSnackBar('الرابط غير صالح. يرجى استخدام رابط http/https صحيح');
      return;
    }

    if (_imageUrls.contains(url)) {
      _showErrorSnackBar('تمت إضافة هذا الرابط مسبقاً');
      return;
    }

    setState(() {
      _imageUrls.add(url);
      _imageUrlController.clear();
      _useImageUrl = false;
    });
    _showSuccessSnackBar('تمت إضافة الصورة من الرابط');
  }

  Future<void> _saveRecipe() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final recipeProvider = Provider.of<RecipeProvider>(
        context,
        listen: false,
      );
      final authProvider = Provider.of<app_auth.AuthProvider>(
        context,
        listen: false,
      );

      List<String> uploadedImageUrls = [];
      for (File imageFile in _selectedImages) {
        final imageUrl = await _storageService.uploadFile(
          imageFile,
          'recipes/${DateTime.now().millisecondsSinceEpoch}',
        );
        if (imageUrl != null) {
          uploadedImageUrls.add(imageUrl);
        }
      }

      final allImageUrls = [..._imageUrls, ...uploadedImageUrls];
      final ingredients = _ingredientsController.text
          .split('\n')
          .where((e) => e.trim().isNotEmpty)
          .toList();
      final instructions = _instructionsController.text
          .split('\n')
          .where((e) => e.trim().isNotEmpty)
          .toList();

      final calories = double.tryParse(_caloriesController.text);
      final protein = double.tryParse(_proteinController.text);
      final carbs = double.tryParse(_carbsController.text);
      final fat = double.tryParse(_fatController.text);

      if (widget.recipe == null) {
        final user = authProvider.currentUser;
        final username = authProvider.userProfile?.username ?? 'مستخدم';
        final userAvatar = authProvider.userProfile?.profileImageUrl;

        final newRecipe = Recipe(
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          ingredients: ingredients,
          instructions: instructions,
          category: _selectedCategory,
          imageUrls: allImageUrls,
          userId: user?.uid ?? '',
          username: username,
          userAvatar: userAvatar,
          isGlutenFree: _isGlutenFree,
          isLactoseFree: _isLactoseFree,
          isApproved: authProvider.isAdmin, // Changed this line
          prepTime: int.tryParse(_prepTimeController.text) ?? 0,
          cookTime: int.tryParse(_cookTimeController.text) ?? 0,
          servings: int.tryParse(_servingsController.text) ?? 1,
          difficulty: _selectedDifficulty,
          calories: calories,
          protein: protein,
          carbs: carbs,
          fat: fat,
          createdAt: DateTime.now(),
        );
        await recipeProvider.addRecipe(newRecipe);
        _showSuccessSnackBar('تمت إضافة الوصفة بنجاح');
      } else {
        final updatedRecipe = widget.recipe!.copyWith(
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          ingredients: ingredients,
          instructions: instructions,
          category: _selectedCategory,
          imageUrls: allImageUrls,
          isGlutenFree: _isGlutenFree,
          isLactoseFree: _isLactoseFree,
          prepTime: int.tryParse(_prepTimeController.text) ?? 0,
          cookTime: int.tryParse(_cookTimeController.text) ?? 0,
          servings: int.tryParse(_servingsController.text) ?? 1,
          difficulty: _selectedDifficulty,
          calories: calories,
          protein: protein,
          carbs: carbs,
          fat: fat,
          updatedAt: DateTime.now(),
        );
        await recipeProvider.updateRecipe(updatedRecipe);
        _showSuccessSnackBar('تم تحديث الوصفة بنجاح');
      }
      if (mounted) Navigator.of(context).pop();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ: ${e.toString()}');
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.cairo()),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.cairo()),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.recipe != null;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          isEditing ? 'تعديل الوصفة' : 'إضافة وصفة جديدة',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: AppColors.textOnPrimary,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: AppColors.textOnPrimary,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          if (!_isLoading)
            IconButton(
              icon: const Icon(
                Icons.save_rounded,
                color: AppColors.textOnPrimary,
              ),
              onPressed: _saveRecipe,
              tooltip: 'حفظ',
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildImageSection(),
            const SizedBox(height: 20),
            _buildTextField(
              _titleController,
              'عنوان الوصفة',
              Icons.title_rounded,
            ),
            const SizedBox(height: 16),
            _buildTextField(
              _descriptionController,
              'وصف الوصفة',
              Icons.description_rounded,
              maxLines: 4,
            ),
            const SizedBox(height: 16),
            _buildCategoryDropdown(),
            const SizedBox(height: 16),
            _buildDetailsSection(),
            const SizedBox(height: 16),
            _buildNutritionSection(),
            const SizedBox(height: 16),
            _buildTextField(
              _ingredientsController,
              'المكونات',
              Icons.list_alt_rounded,
              maxLines: 8,
              hint: 'اكتب كل مكون في سطر منفصل',
            ),
            const SizedBox(height: 16),
            _buildTextField(
              _instructionsController,
              'طريقة التحضير',
              Icons.format_list_numbered_rounded,
              maxLines: 10,
              hint: 'اكتب كل خطوة في سطر منفصل',
            ),
            const SizedBox(height: 16),
            _buildGlutenFreeSwitch(),
            const SizedBox(height: 8),
            _buildLactoseFreeSwitch(),
            const SizedBox(height: 32),
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        children: [
          Container(
            height: 200,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
              gradient: LinearGradient(
                colors: [
                  AppColors.primary.withAlpha(51),
                  AppColors.primary.withAlpha(26),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Stack(
              children: [
                if (_selectedImages.isEmpty && _imageUrls.isEmpty)
                  _buildImagePlaceholder()
                else
                  PageView.builder(
                    itemCount: _selectedImages.length + _imageUrls.length,
                    itemBuilder: (context, index) {
                      Widget imageWidget;
                      bool isUrl = index >= _selectedImages.length;
                      if (isUrl) {
                        imageWidget = Image.network(
                          _imageUrls[index - _selectedImages.length],
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              _buildImagePlaceholder(),
                        );
                      } else {
                        imageWidget = Image.file(
                          _selectedImages[index],
                          fit: BoxFit.cover,
                        );
                      }
                      return Stack(
                        fit: StackFit.expand,
                        children: [
                          ClipRRect(
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(16),
                            ),
                            child: imageWidget,
                          ),
                          Positioned(
                            top: 8,
                            right: 8,
                            child: IconButton(
                              icon: const Icon(
                                Icons.close_rounded,
                                color: Colors.red,
                              ),
                              onPressed: () => _removeImage(
                                isUrl ? index - _selectedImages.length : index,
                                isUrl,
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                Positioned(
                  bottom: 16,
                  right: 16,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      FloatingActionButton.small(
                        onPressed: _pickImage,
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        heroTag: "recipe_camera",
                        child: const Icon(Icons.add_a_photo),
                      ),
                      const SizedBox(width: 8),
                      FloatingActionButton.small(
                        onPressed: () {
                          setState(() {
                            _useImageUrl = !_useImageUrl;
                            if (!_useImageUrl) {
                              _imageUrlController.clear();
                            }
                          });
                        },
                        backgroundColor: Colors.orange.shade600,
                        foregroundColor: Colors.white,
                        heroTag: "recipe_link",
                        child: const Icon(Icons.link),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          if (_useImageUrl)
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _imageUrlController,
                      style: GoogleFonts.cairo(),
                      decoration: InputDecoration(
                        labelText: 'رابط الصورة',
                        labelStyle: GoogleFonts.cairo(
                          color: Colors.orange.shade600,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        prefixIcon: Icon(
                          Icons.link,
                          color: Colors.orange.shade600,
                        ),
                        hintText: 'https://example.com/image.jpg',
                        hintStyle: GoogleFonts.cairo(
                          color: AppColors.textSecondary.withAlpha(153),
                        ),
                      ),
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final uri = Uri.tryParse(value);
                          final isHttp =
                              uri != null &&
                              (uri.scheme == 'http' || uri.scheme == 'https');
                          if (uri == null || !uri.hasAbsolutePath || !isHttp) {
                            return 'يرجى إدخال رابط صحيح (http/https)';
                          }
                        }
                        return null;
                      },
                      onChanged: (value) => setState(() {}),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _imageUrlController.text.trim().isNotEmpty
                        ? _addImageFromUrl
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange.shade600,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text('إضافة', style: GoogleFonts.cairo()),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildImagePlaceholder() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.restaurant_menu_rounded,
            size: 60,
            color: AppColors.primary.withAlpha(179),
          ),
          const SizedBox(height: 8),
          Text(
            'أضف صورًا للوصفة',
            style: GoogleFonts.cairo(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField(
    TextEditingController controller,
    String label,
    IconData icon, {
    int maxLines = 1,
    String? hint,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: TextFormField(
          controller: controller,
          style: GoogleFonts.cairo(),
          maxLines: maxLines,
          decoration: InputDecoration(
            labelText: label,
            hintText: hint,
            labelStyle: GoogleFonts.cairo(color: AppColors.primary),
            border: InputBorder.none,
            prefixIcon: Icon(icon, color: AppColors.primary),
            alignLabelWithHint: true,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال $label';
            }
            return null;
          },
        ),
      ),
    );
  }

  Widget _buildCategoryDropdown() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: DropdownButtonFormField<String>(
          value: _selectedCategory,
          style: GoogleFonts.cairo(color: AppColors.textPrimary, fontSize: 16),
          dropdownColor: AppColors.surface,
          decoration: InputDecoration(
            labelText: 'الفئة',
            labelStyle: GoogleFonts.cairo(color: AppColors.primary),
            border: InputBorder.none,
            prefixIcon: Icon(Icons.category_rounded, color: AppColors.primary),
          ),
          items: _categories.map((category) {
            return DropdownMenuItem(value: category, child: Text(category));
          }).toList(),
          onChanged: (value) {
            if (value != null) setState(() => _selectedCategory = value);
          },
        ),
      ),
    );
  }

  Widget _buildDetailsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _prepTimeController,
                    decoration: InputDecoration(
                      labelText: 'وقت التحضير (دقيقة)',
                      labelStyle: GoogleFonts.cairo(),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.timer_outlined),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'الحقل مطلوب';
                      }
                      if (int.tryParse(value.trim()) == null) {
                        return 'أدخل رقمًا صحيحًا';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _cookTimeController,
                    decoration: InputDecoration(
                      labelText: 'وقت الطهي (دقيقة)',
                      labelStyle: GoogleFonts.cairo(),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.whatshot_outlined),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'الحقل مطلوب';
                      }
                      if (int.tryParse(value.trim()) == null) {
                        return 'أدخل رقمًا صحيحًا';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _servingsController,
                    decoration: InputDecoration(
                      labelText: 'عدد الحصص',
                      labelStyle: GoogleFonts.cairo(),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.people_outline),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'الحقل مطلوب';
                      }
                      if (int.tryParse(value.trim()) == null) {
                        return 'أدخل رقمًا صحيحًا';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedDifficulty,
                    decoration: InputDecoration(
                      labelText: 'الصعوبة',
                      labelStyle: GoogleFonts.cairo(),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.leaderboard_outlined),
                    ),
                    items: _difficulties
                        .map((d) => DropdownMenuItem(value: d, child: Text(d)))
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _selectedDifficulty = value);
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNutritionSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الغذائية (اختياري)',
              style: GoogleFonts.cairo(fontSize: 16, fontWeight: FontWeight.bold, color: AppColors.textPrimary),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _caloriesController,
                    decoration: InputDecoration(
                      labelText: 'السعرات الحرارية',
                      labelStyle: GoogleFonts.cairo(),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                      prefixIcon: const Icon(Icons.local_fire_department_outlined),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _proteinController,
                    decoration: InputDecoration(
                      labelText: 'البروتين (جم)',
                      labelStyle: GoogleFonts.cairo(),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                      prefixIcon: const Icon(Icons.fitness_center_outlined),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _carbsController,
                    decoration: InputDecoration(
                      labelText: 'الكربوهيدرات (جم)',
                      labelStyle: GoogleFonts.cairo(),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                      prefixIcon: const Icon(Icons.rice_bowl_outlined),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _fatController,
                    decoration: InputDecoration(
                      labelText: 'الدهون (جم)',
                      labelStyle: GoogleFonts.cairo(),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                      prefixIcon: const Icon(Icons.fastfood_outlined),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGlutenFreeSwitch() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: SwitchListTile(
        title: Text(
          'خالية من الجلوتين',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        value: _isGlutenFree,
        onChanged: (value) => setState(() => _isGlutenFree = value),
        secondary: Icon(
          Icons.health_and_safety_outlined,
          color: _isGlutenFree ? Colors.green : Colors.grey,
        ),
        activeColor: Colors.green,
      ),
    );
  }

  Widget _buildLactoseFreeSwitch() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: SwitchListTile(
        title: Text(
          'خالية من اللاكتوز',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        value: _isLactoseFree,
        onChanged: (value) => setState(() => _isLactoseFree = value),
        secondary: Icon(
          Icons.no_food_outlined,
          color: _isLactoseFree ? Colors.blue : Colors.grey,
        ),
        activeColor: Colors.blue,
      ),
    );
  }

  Widget _buildSaveButton() {
    return ElevatedButton.icon(
      onPressed: _isLoading ? null : _saveRecipe,
      icon: _isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 2,
              ),
            )
          : const Icon(Icons.save),
      label: Text(
        _isLoading
            ? 'جاري الحفظ...'
            : (widget.recipe != null ? 'حفظ التغييرات' : 'إضافة الوصفة'),
        style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}
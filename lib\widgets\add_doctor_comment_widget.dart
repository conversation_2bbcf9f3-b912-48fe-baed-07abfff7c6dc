import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/models/comment.dart';
import 'package:yassincil/providers/doctor_provider.dart';
import 'package:yassincil/providers/auth_provider.dart' as app_auth;
import 'package:yassincil/utils/app_colors.dart';

class AddDoctorCommentWidget extends StatefulWidget {
  final String doctorId;
  final VoidCallback? onCommentAdded;

  const AddDoctorCommentWidget({
    super.key,
    required this.doctorId,
    this.onCommentAdded,
  });

  @override
  State<AddDoctorCommentWidget> createState() => _AddDoctorCommentWidgetState();
}

class _AddDoctorCommentWidgetState extends State<AddDoctorCommentWidget> {
  final _commentController = TextEditingController();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  Future<void> _submitComment() async {
    if (_commentController.text.trim().isEmpty) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final authProvider = Provider.of<app_auth.AuthProvider>(
        context,
        listen: false,
      );
      final doctorProvider = Provider.of<DoctorProvider>(
        context,
        listen: false,
      );

      final comment = Comment(
        postId: widget.doctorId,
        content: _commentController.text.trim(),
        userId: authProvider.currentUser!.uid,
        username: authProvider.currentUser!.displayName ?? 'مستخدم',
        createdAt: DateTime.now(),
      );

      await doctorProvider.addCommentToDoctor(widget.doctorId, comment);

      _commentController.clear();
      widget.onCommentAdded?.call();
    } catch (e) {
      // Handle error
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _commentController,
              decoration: InputDecoration(
                hintText: 'أضف تعليقك...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: _isSubmitting
                ? const CircularProgressIndicator()
                : const Icon(Icons.send),
            onPressed: _isSubmitting ? null : _submitComment,
          ),
        ],
      ),
    );
  }
}

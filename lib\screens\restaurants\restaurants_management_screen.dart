import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../providers/restaurant_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/restaurant.dart';
import 'add_edit_restaurant_screen.dart';

class RestaurantsManagementScreen extends StatefulWidget {
  const RestaurantsManagementScreen({super.key});

  @override
  State<RestaurantsManagementScreen> createState() =>
      _RestaurantsManagementScreenState();
}

class _RestaurantsManagementScreenState
    extends State<RestaurantsManagementScreen>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late TabController _tabController;
  String _searchQuery = '';
  String _selectedFilter = 'الكل';

  final List<String> _filters = ['الكل', 'يوفر خالي من الجلوتين', 'غير مؤكد'];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _tabController = TabController(length: _filters.length, vsync: this);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _refresh() async {
    if (!mounted) return;
    try {
      await Provider.of<RestaurantProvider>(context, listen: false)
          .fetchRestaurants();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحديث البيانات', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<Restaurant> _getFilteredRestaurants(List<Restaurant> restaurants) {
    return restaurants.where((restaurant) {
      final matchesSearch = _searchQuery.isEmpty ||
          restaurant.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          restaurant.address.toLowerCase().contains(_searchQuery.toLowerCase());

      bool matchesFilter;
      switch (_selectedFilter) {
        case 'يوفر خالي من الجلوتين':
          matchesFilter = restaurant.hasGlutenFreeOptions;
          break;
        case 'غير مؤكد':
          matchesFilter = !restaurant.hasGlutenFreeOptions;
          break;
        default:
          matchesFilter = true;
      }

      return matchesSearch && matchesFilter;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final restaurantProvider = Provider.of<RestaurantProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final isAdmin = authProvider.isAdmin;

    final filteredRestaurants =
        _getFilteredRestaurants(restaurantProvider.restaurants);

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildFilterTabs(),
          _buildStatisticsCard(restaurantProvider.restaurants),
          Expanded(
            child: _buildContent(
              filteredRestaurants,
              restaurantProvider,
              isAdmin,
            ),
          ),
        ],
      ),
      floatingActionButton: isAdmin ? _buildAddFAB() : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'إدارة المطاعم',
        style: GoogleFonts.cairo(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: const Color(0xFF00BFA5),
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh, color: Colors.white),
          onPressed: _refresh,
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFF00BFA5),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TextField(
          controller: _searchController,
          style: GoogleFonts.cairo(),
          decoration: InputDecoration(
            hintText: 'ابحث عن مطعم...',
            hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500),
            prefixIcon: const Icon(Icons.search, color: Color(0xFF00BFA5)),
            suffixIcon: _searchQuery.isNotEmpty
                ? IconButton(
                    icon: Icon(Icons.clear, color: Colors.grey.shade600),
                    onPressed: () {
                      _searchController.clear();
                      setState(() {
                        _searchQuery = '';
                      });
                    },
                  )
                : null,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 15,
            ),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
      ),
    );
  }

  Widget _buildFilterTabs() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: TabBar(
        controller: _tabController,
        indicatorColor: const Color(0xFF00BFA5),
        labelColor: const Color(0xFF00BFA5),
        unselectedLabelColor: Colors.grey.shade600,
        labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        unselectedLabelStyle: GoogleFonts.cairo(fontWeight: FontWeight.normal),
        onTap: (index) {
          setState(() {
            _selectedFilter = _filters[index];
          });
        },
        tabs: _filters.map((filter) => Tab(text: filter)).toList(),
      ),
    );
  }

  Widget _buildStatisticsCard(List<Restaurant> allRestaurants) {
    final total = allRestaurants.length;
    final glutenFree =
        allRestaurants.where((r) => r.hasGlutenFreeOptions).length;
    final notSure =
        allRestaurants.where((r) => !r.hasGlutenFreeOptions).length;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.analytics, color: Color(0xFF00BFA5), size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'إحصائيات المطاعم',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'الإجمالي',
                      total.toString(),
                      const Color(0xFF00BFA5),
                      Icons.restaurant,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatItem(
                      'يوفر خالي جلوتين',
                      glutenFree.toString(),
                      Colors.green.shade600,
                      Icons.check_circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatItem(
                      'غير مؤكد',
                      notSure.toString(),
                      Colors.orange.shade600,
                      Icons.help_outline,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildContent(
    List<Restaurant> restaurants,
    RestaurantProvider restaurantProvider,
    bool isAdmin,
  ) {
    if (restaurantProvider.isLoading && restaurants.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }
    if (restaurants.isEmpty) {
      return Center(
        child: Text('لا توجد مطاعم مطابقة', style: GoogleFonts.cairo()),
      );
    }

    return RefreshIndicator(
      onRefresh: _refresh,
      color: const Color(0xFF00BFA5),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: restaurants.length,
        itemBuilder: (context, index) {
          return _buildRestaurantCard(
            restaurants[index],
            restaurantProvider,
            isAdmin,
          );
        },
      ),
    );
  }

  Widget _buildRestaurantCard(
    Restaurant restaurant,
    RestaurantProvider restaurantProvider,
    bool isAdmin,
  ) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: const Color(0xFF00BFA5).withOpacity(0.1),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: restaurant.imageUrl != null &&
                        restaurant.imageUrl!.isNotEmpty
                    ? Image.network(
                        restaurant.imageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) =>
                            const Icon(Icons.restaurant_menu,
                                color: Color(0xFF00BFA5)),
                      )
                    : const Icon(Icons.restaurant_menu,
                        color: Color(0xFF00BFA5)),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    restaurant.name,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    restaurant.address,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            if (isAdmin)
              _buildAdminActions(restaurant, restaurantProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildAdminActions(
    Restaurant restaurant,
    RestaurantProvider restaurantProvider,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.edit, color: Color(0xFF00BFA5)),
          onPressed: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) =>
                    AddEditRestaurantScreen(restaurant: restaurant),
              ),
            );
          },
        ),
        IconButton(
          icon: Icon(Icons.delete, color: Colors.red.shade600),
          onPressed: () => _deleteRestaurant(restaurant, restaurantProvider),
        ),
      ],
    );
  }

  Future<void> _deleteRestaurant(
      Restaurant restaurant, RestaurantProvider provider) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text('تأكيد الحذف', style: GoogleFonts.cairo()),
        content: Text('هل أنت متأكد من حذف "${restaurant.name}"؟',
            style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(false),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(ctx).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await provider.deleteRestaurant(restaurant.id!);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف المطعم بنجاح', style: GoogleFonts.cairo()),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في حذف المطعم', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Widget _buildAddFAB() {
    return FloatingActionButton.extended(
      onPressed: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const AddEditRestaurantScreen(),
          ),
        );
      },
      backgroundColor: const Color(0xFF00BFA5),
      foregroundColor: Colors.white,
      icon: const Icon(Icons.add),
      label: Text('إضافة مطعم', style: GoogleFonts.cairo()),
    );
  }
}

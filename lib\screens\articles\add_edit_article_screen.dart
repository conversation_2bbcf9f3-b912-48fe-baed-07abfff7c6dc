import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import 'package:yassincil/providers/article_provider.dart';
import 'package:yassincil/models/article.dart';

class AddEditArticleScreen extends StatefulWidget {
  final Article? article;

  const AddEditArticleScreen({super.key, this.article});

  @override
  State<AddEditArticleScreen> createState() => _AddEditArticleScreenState();
}

class _AddEditArticleScreenState extends State<AddEditArticleScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _authorController = TextEditingController();
  final _tagsController = TextEditingController();
  final _imageUrlController = TextEditingController();

  String _selectedCategory = 'التغذية';
  File? _selectedImage;
  bool _isLoading = false;
  bool _useImageUrl = false;

  final List<String> _categories = [
    'التغذية',
    'الأدوية',
    'الأعراض',
    'نصائح طبية',
    'الوقاية',
    'العلاج',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.article != null) {
      _titleController.text = widget.article!.title;
      _contentController.text = widget.article!.content;
      _authorController.text = widget.article!.author;
      _selectedCategory = widget.article!.category;
      _tagsController.text = widget.article!.tags.join(', ');
      if (widget.article!.imageUrl != null) {
        _imageUrlController.text = widget.article!.imageUrl!;
        _useImageUrl = true;
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _authorController.dispose();
    _tagsController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 800,
      maxHeight: 600,
      imageQuality: 80,
    );

    if (pickedFile != null) {
      setState(() {
        _selectedImage = File(pickedFile.path);
        _useImageUrl = false;
        _imageUrlController.clear();
      });
    }
  }

  Future<void> _saveArticle() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final articleProvider = Provider.of<ArticleProvider>(
        context,
        listen: false,
      );

      String? finalImageUrl;
      if (_useImageUrl && _imageUrlController.text.trim().isNotEmpty) {
        finalImageUrl = _imageUrlController.text.trim();
      }

      final tags = _tagsController.text
          .split(',')
          .map((tag) => tag.trim())
          .where((tag) => tag.isNotEmpty)
          .toList();

      if (widget.article == null) {
        await articleProvider.addArticle(
          title: _titleController.text.trim(),
          content: _contentController.text.trim(),
          author: _authorController.text.trim(),
          category: _selectedCategory,
          tags: tags,
          imageFile: _selectedImage,
          imageUrl: finalImageUrl,
        );
      } else {
        await articleProvider.updateArticle(
          articleId: widget.article!.id!,
          title: _titleController.text.trim(),
          content: _contentController.text.trim(),
          author: _authorController.text.trim(),
          category: _selectedCategory,
          tags: tags,
          imageFile: _selectedImage,
          imageUrl: finalImageUrl,
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.article == null
                  ? 'تم إضافة المقال بنجاح'
                  : 'تم تحديث المقال بنجاح',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEditing = widget.article != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isEditing ? 'تعديل المقال' : 'إضافة مقال جديد',
        ),
        centerTitle: true,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.save_rounded),
              onPressed: _saveArticle,
              tooltip: 'حفظ',
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildImageSection(),
            const SizedBox(height: 20),
            _buildTitleField(),
            const SizedBox(height: 16),
            _buildAuthorField(),
            const SizedBox(height: 16),
            _buildCategoryDropdown(),
            const SizedBox(height: 16),
            _buildContentField(),
            const SizedBox(height: 16),
            _buildTagsField(),
            const SizedBox(height: 32),
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    final theme = Theme.of(context);
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        children: [
          Container(
            height: 200,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
              gradient: LinearGradient(
                colors: [theme.primaryColor.withOpacity(0.2), theme.primaryColor.withOpacity(0.05)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Stack(
              children: [
                if (_selectedImage != null)
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                    child: Image.file(
                      _selectedImage!,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  )
                else if (_useImageUrl && _imageUrlController.text.isNotEmpty)
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                    child: Image.network(
                      _imageUrlController.text,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildImagePlaceholder(),
                    ),
                  )
                else if (widget.article?.imageUrl != null && !_useImageUrl)
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                    child: Image.network(
                      widget.article!.imageUrl!,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildImagePlaceholder(),
                    ),
                  )
                else
                  _buildImagePlaceholder(),
                Positioned(
                  bottom: 16,
                  right: 16,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      FloatingActionButton.small(
                        onPressed: _pickImage,
                        heroTag: "article_camera",
                        child: const Icon(Icons.camera_alt),
                      ),
                      const SizedBox(width: 8),
                      FloatingActionButton.small(
                        onPressed: () {
                          setState(() {
                            _useImageUrl = !_useImageUrl;
                            if (_useImageUrl) {
                              _selectedImage = null;
                            } else {
                              _imageUrlController.clear();
                            }
                          });
                        },
                        heroTag: "article_link",
                        child: const Icon(Icons.link),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          if (_useImageUrl)
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextFormField(
                controller: _imageUrlController,
                decoration: const InputDecoration(
                  labelText: 'رابط الصورة',
                  prefixIcon: Icon(Icons.link),
                ),
                onChanged: (value) => setState(() {}),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildImagePlaceholder() {
    final theme = Theme.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.article, size: 60, color: theme.primaryColor.withOpacity(0.5)),
          const SizedBox(height: 8),
          Text(
            'اضغط لإضافة صورة',
            style: theme.textTheme.titleLarge?.copyWith(color: theme.primaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleField() {
    return TextFormField(
      controller: _titleController,
      decoration: const InputDecoration(
        labelText: 'عنوان المقال',
        prefixIcon: Icon(Icons.title),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال عنوان المقال';
        }
        return null;
      },
    );
  }

  Widget _buildAuthorField() {
    return TextFormField(
      controller: _authorController,
      decoration: const InputDecoration(
        labelText: 'اسم الكاتب',
        prefixIcon: Icon(Icons.person),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال اسم الكاتب';
        }
        return null;
      },
    );
  }

  Widget _buildCategoryDropdown() {
    final theme = Theme.of(context);
    return DropdownButtonFormField<String>(
      value: _selectedCategory,
      style: theme.textTheme.bodyLarge,
      decoration: InputDecoration(
        labelText: 'فئة المقال',
        prefixIcon: Icon(
          Icons.category_rounded,
          color: theme.primaryColor,
        ),
      ),
      items: _categories.map((category) {
        return DropdownMenuItem(
          value: category,
          child: Text(category),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedCategory = value!;
        });
      },
    );
  }

  Widget _buildContentField() {
    return TextFormField(
      controller: _contentController,
      maxLines: 8,
      decoration: const InputDecoration(
        labelText: 'محتوى المقال',
        prefixIcon: Icon(Icons.description),
        alignLabelWithHint: true,
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال محتوى المقال';
        }
        return null;
      },
    );
  }

  Widget _buildTagsField() {
    return TextFormField(
      controller: _tagsController,
      decoration: const InputDecoration(
        labelText: 'العلامات (مفصولة بفواصل)',
        prefixIcon: Icon(Icons.tag),
        hintText: 'مثال: تغذية, صحة, نصائح',
      ),
    );
  }

  Widget _buildSaveButton() {
    return ElevatedButton.icon(
      onPressed: _isLoading ? null : _saveArticle,
      icon: _isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
              ),
            )
          : const Icon(Icons.save),
      label: Text(
        _isLoading
            ? 'جاري الحفظ...'
            : (widget.article == null ? 'إضافة المقال' : 'حفظ التغييرات'),
      ),
    );
  }
}
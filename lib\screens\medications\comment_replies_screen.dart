import 'package:flutter/material.dart';
import 'package:yassincil/widgets/medications_app_bar.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:io';
import 'dart:async';

import 'package:yassincil/models/comment.dart';
import 'package:yassincil/providers/medication_provider.dart';
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/widgets/advanced_medication_comment_widget.dart';
import 'package:yassincil/widgets/add_medication_comment_widget.dart';

class CommentRepliesScreen extends StatefulWidget {
  final String medicationId;
  final Comment parentComment;

  const CommentRepliesScreen({
    super.key,
    required this.medicationId,
    required this.parentComment,
  });

  @override
  State<CommentRepliesScreen> createState() => _CommentRepliesScreenState();
}

class _CommentRepliesScreenState extends State<CommentRepliesScreen> {
  // Controller for the ScrollView
  final ScrollController _scrollController = ScrollController();
  // Focus node for the text field
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Add listener to focus node to detect keyboard visibility
    _focusNode.addListener(_onFocusChange);

    // Add listener to scroll controller to detect when user scrolls
    _scrollController.addListener(() {
      // If user is scrolling, hide keyboard
      FocusScope.of(context).unfocus();
    });
  }

  @override
  void dispose() {
    // Clean up resources
    _scrollController.dispose();
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    super.dispose();
  }

  // Handle focus changes to detect keyboard visibility
  void _onFocusChange() {
    if (_focusNode.hasFocus) {
      // Scroll to bottom when keyboard appears with a slight delay
      Future.delayed(const Duration(milliseconds: 300), () {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final medicationProvider = Provider.of<MedicationProvider>(
      context,
      listen: false,
    );

    // Check if keyboard is visible from MediaQuery
    final isKeyboardVisibleFromSystem =
        MediaQuery.of(context).viewInsets.bottom > 0;

    // Calculate the height of the keyboard
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    // Calculate the height of the input field container (approximately)
    const inputFieldHeight = 80.0;

    return GestureDetector(
      onTap: () => FocusScope.of(
        context,
      ).unfocus(), // إغلاق الكيبورد عند النقر خارج حقل النص
      child: Scaffold(
        backgroundColor: AppColors.background,
        // تعطيل التكيف التلقائي مع لوحة المفاتيح لنتحكم به يدوياً
        resizeToAvoidBottomInset: false,
        appBar: MedicationsAppBar(
          title: 'الردود على ${widget.parentComment.username}',
        ),
        body: Stack(
          children: [
            // منطقة التعليقات والردود القابلة للتمرير
            Positioned.fill(
              // ضبط المساحة السفلية لتجنب تداخل حقل الإدخال
              bottom: isKeyboardVisibleFromSystem
                  ? keyboardHeight + inputFieldHeight
                  : inputFieldHeight,
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  // Parent Comment - تصميم عصري
                  SliverToBoxAdapter(
                    child: Container(
                      margin: const EdgeInsets.all(16.0),
                      decoration: BoxDecoration(
                        // تأثير Glassmorphism عصري
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white.withOpacity(0.95),
                            Colors.white.withOpacity(0.85),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(24), // زوايا عصرية
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1.5,
                        ),
                        // ظلال ملونة عصرية
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF00BFA5).withOpacity(0.15),
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                            spreadRadius: 0,
                          ),
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: AdvancedMedicationCommentWidget(
                          comment: widget.parentComment,
                          medicationId: widget.medicationId,
                          showReplies:
                              false, // Don't show replies recursively here
                        ),
                      ),
                    ),
                  ),
                  // Replies List
                  StreamBuilder<List<Comment>>(
                    stream: medicationProvider.getRepliesForComment(
                      medicationId: widget.medicationId,
                      parentCommentId: widget.parentComment.id!,
                    ),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const SliverToBoxAdapter(
                          child: Center(child: CircularProgressIndicator()),
                        );
                      }
                      if (snapshot.hasError) {
                        return SliverToBoxAdapter(
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  size: 48,
                                  color: AppColors.error,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'حدث خطأ في تحميل الردود',
                                  style: GoogleFonts.cairo(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.error,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'يرجى المحاولة مرة أخرى',
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: () {
                                    // إعادة تحميل الصفحة
                                    Navigator.of(context).pushReplacement(
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            CommentRepliesScreen(
                                              medicationId: widget.medicationId,
                                              parentComment:
                                                  widget.parentComment,
                                            ),
                                      ),
                                    );
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.primary,
                                  ),
                                  child: Text(
                                    'إعادة المحاولة',
                                    style: GoogleFonts.cairo(
                                      color: AppColors.textOnPrimary,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }
                      final replies = snapshot.data ?? [];
                      if (replies.isEmpty) {
                        return SliverToBoxAdapter(
                          child: Center(
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Text(
                                'لا توجد ردود. كن أول من يرد!',
                                style: GoogleFonts.cairo(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ),
                          ),
                        );
                      }
                      return SliverList(
                        delegate: SliverChildBuilderDelegate((context, index) {
                          return Container(
                            margin: const EdgeInsets.only(
                              left: 32.0, // مسافة أكبر لإظهار الردود كفروع
                              right: 16.0,
                              bottom: 12.0,
                            ),
                            decoration: BoxDecoration(
                              // تأثير Glassmorphism للردود
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.white.withOpacity(0.9),
                                  Colors.white.withOpacity(0.7),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(
                                20,
                              ), // زوايا أقل دائرية للردود
                              border: Border.all(
                                color: const Color(0xFF00BFA5).withOpacity(0.2),
                                width: 1.0,
                              ),
                              // ظلال أخف للردود
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(
                                    0xFF00BFA5,
                                  ).withOpacity(0.08),
                                  blurRadius: 12,
                                  offset: const Offset(0, 4),
                                  spreadRadius: 0,
                                ),
                              ],
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // خط الربط البصري للرد
                                  Container(
                                    width: 3,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      gradient: const LinearGradient(
                                        colors: [
                                          Color(0xFF00BFA5),
                                          Color(0xFF00796B),
                                        ],
                                      ),
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  // محتوى الرد
                                  Expanded(
                                    child: AdvancedMedicationCommentWidget(
                                      comment: replies[index],
                                      medicationId: widget.medicationId,
                                      showReplies: false,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }, childCount: replies.length),
                      );
                    },
                  ),
                  // إضافة مساحة في الأسفل لتجنب تداخل حقل الإدخال مع المحتوى
                  const SliverToBoxAdapter(child: SizedBox(height: 20)),
                ],
              ),
            ),

            // Add Reply Widget - تصميم عصري ومحسن للكيبورد
            Positioned(
              left: 0,
              right: 0,
              bottom: isKeyboardVisibleFromSystem ? keyboardHeight : 0,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 12.0,
                ),
                decoration: BoxDecoration(
                  // تأثير Glassmorphism عصري
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.white.withOpacity(0.95),
                      Colors.white.withOpacity(0.85),
                    ],
                  ),
                  border: Border(
                    top: BorderSide(
                      color: const Color(0xFF00BFA5).withOpacity(0.2),
                      width: 1.5,
                    ),
                  ),
                  // ظلال عصرية
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF00BFA5).withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, -8),
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, -2),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: AddMedicationCommentWidget(
                  medicationId: widget.medicationId,
                  parentCommentId: widget.parentComment.id,
                  replyToUsername: widget.parentComment.username,
                  focusNode: _focusNode, // استخدام نقطة التركيز المخصصة
                  onCommentAdded: () {
                    // The stream builder will automatically update the list
                    // Scroll to bottom after adding comment
                    Future.delayed(const Duration(milliseconds: 300), () {
                      if (_scrollController.hasClients) {
                        _scrollController.animateTo(
                          _scrollController.position.maxScrollExtent,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      }
                    });
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

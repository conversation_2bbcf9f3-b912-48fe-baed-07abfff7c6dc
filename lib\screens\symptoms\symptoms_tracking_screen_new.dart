
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';

import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/providers/symptoms_provider.dart';
import 'package:yassincil/models/symptom_entry.dart';
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/utils/error_handler.dart';
import 'package:yassincil/screens/symptoms/add_symptom_dialog.dart';
import 'package:yassincil/widgets/symptoms_chart.dart';
import 'package:yassincil/widgets/symptoms_calendar.dart';
import 'package:yassincil/widgets/loading_widget.dart';

// --- THEME CONSTANTS ---
const _primaryColor = Color(0xFFD32F2F); // A deeper, more professional red
const _accentColor = Color(0xFFFF5252);
const _backgroundColor = Color(0xFFF5F5F5);
const _surfaceColor = Colors.white;
const _textColor = Color(0xFF212121);
const _textSecondaryColor = Color(0xFF757575);

class SymptomsTrackingScreen extends StatefulWidget {
  final bool showBackButton;

  const SymptomsTrackingScreen({super.key, this.showBackButton = true});

  @override
  State<SymptomsTrackingScreen> createState() => _SymptomsTrackingScreenState();
}

class _SymptomsTrackingScreenState extends State<SymptomsTrackingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  DateTime _selectedDate = DateTime.now();
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _fetchData();
  }

  void _fetchData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      if (authProvider.currentUser != null) {
        Provider.of<SymptomsProvider>(context, listen: false)
            .fetchSymptomEntries(authProvider.currentUser!.uid);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // --- DATA EXPORT & SHARING LOGIC ---
  Future<void> _exportReport() async {
    try {
      final symptomsProvider = Provider.of<SymptomsProvider>(context, listen: false);
      if (symptomsProvider.symptomEntries.isEmpty) {
        ErrorHandler.showWarningSnackBar(context, 'لا توجد أعراض لتصديرها');
        return;
      }
      final report = _generateReport(symptomsProvider.symptomEntries);
      final file = await _saveReportToFile(report);
      await Share.shareXFiles([XFile(file.path)], text: 'تقرير تتبع الأعراض');
      if (mounted) ErrorHandler.showSuccessSnackBar(context, 'تم تصدير التقرير بنجاح');
    } catch (e) {
      if (mounted) ErrorHandler.showErrorSnackBar(context, 'فشل تصدير التقرير: $e');
    }
  }

  Map<String, dynamic> _generateReport(List<SymptomEntry> symptoms) {
     final now = DateTime.now();
    final lastMonth = now.subtract(const Duration(days: 30));
    final recentSymptoms = symptoms.where((s) => s.timestamp.isAfter(lastMonth)).toList();
    final symptomCounts = <String, int>{};
    for (final symptom in recentSymptoms) {
      symptomCounts[symptom.symptomName] = (symptomCounts[symptom.symptomName] ?? 0) + 1;
    }
    return {
      'reportDate': now.toIso8601String(),
      'period': '30 يوم',
      'totalSymptoms': recentSymptoms.length,
      'symptomCounts': symptomCounts,
      'symptoms': recentSymptoms.map((s) => s.toMap()).toList(),
    };
  }

  Future<File> _saveReportToFile(Map<String, dynamic> report) async {
    final directory = await getApplicationDocumentsDirectory();
    final timestamp = DateFormat('yyyy-MM-dd_HH-mm').format(DateTime.now());
    final file = File('${directory.path}/symptom_report_$timestamp.json');
    await file.writeAsString(const JsonEncoder.withIndent('  ').convert(report));
    return file;
  }
  
  // --- UI BUILD METHODS ---

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _backgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFab(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'تتبع الأعراض',
        style: GoogleFonts.cairo(fontWeight: FontWeight.bold, color: _textColor),
      ),
      backgroundColor: _surfaceColor,
      foregroundColor: _textColor,
      elevation: 0,
      centerTitle: true,
      leading: widget.showBackButton
          ? IconButton(
              icon: const Icon(Icons.arrow_back_ios_new, size: 20),
              onPressed: () => Navigator.of(context).pop(),
            )
          : null,
      actions: [
        IconButton(
          icon: const Icon(Icons.more_vert, color: _textSecondaryColor),
          onPressed: () => _showMoreOptions(),
          tooltip: 'خيارات إضافية',
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        _buildHeader(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildSymptomListTab(),
              _buildAnalyticsTab(),
              _buildCalendarTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: _surfaceColor,
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            onChanged: (value) => setState(() => _searchQuery = value),
            decoration: InputDecoration(
              hintText: 'ابحث عن عرض, محفز, أو ملاحظة...',
              hintStyle: GoogleFonts.cairo(color: _textSecondaryColor),
              prefixIcon: const Icon(Icons.search, color: _textSecondaryColor),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear, color: _textSecondaryColor),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                      },
                    )
                  : null,
              filled: true,
              fillColor: _backgroundColor,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30.0),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(vertical: 14.0),
            ),
            style: GoogleFonts.cairo(color: _textColor),
          ),
          const SizedBox(height: 16),
          // Tabs
          TabBar(
            controller: _tabController,
            labelColor: _primaryColor,
            unselectedLabelColor: _textSecondaryColor,
            indicator: UnderlineTabIndicator(
              borderSide: const BorderSide(width: 3, color: _primaryColor),
              insets: const EdgeInsets.symmetric(horizontal: 16.0),
            ),
            labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.bold, fontSize: 15),
            unselectedLabelStyle: GoogleFonts.cairo(fontWeight: FontWeight.w600, fontSize: 14),
            tabs: const [
              Tab(text: 'نظرة عامة'),
              Tab(text: 'السجل'),
              Tab(text: 'تحليلات'),
              Tab(text: 'التقويم'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFab() {
    return FloatingActionButton.extended(
      onPressed: _showAddSymptomDialog,
      label: Text('إضافة عرض', style: GoogleFonts.cairo(fontWeight: FontWeight.bold)),
      icon: const Icon(Icons.add),
      backgroundColor: _accentColor,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    );
  }

  // --- TAB IMPLEMENTATIONS ---

  Widget _buildOverviewTab() {
    return Consumer<SymptomsProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) return const LoadingWidget();
        
        final stats = provider.getStatistics();
        final recentSymptoms = provider.getRecentSymptoms().take(5).toList();

        return RefreshIndicator(
          onRefresh: () async => _fetchData(),
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              _buildSectionTitle('ملخص اليوم', Icons.today),
              _buildTodaySummary(provider.todayEntries),
              const SizedBox(height: 24),
              _buildSectionTitle('إحصائيات عامة', Icons.pie_chart_outline),
              _buildStatsGrid(stats),
              const SizedBox(height: 24),
              _buildSectionTitle('آخر الأعراض المسجلة', Icons.history),
              if (recentSymptoms.isEmpty)
                _buildInfoCard('لا توجد أعراض حديثة', 'استمر في المحافظة على صحتك!', Icons.sentiment_very_satisfied, Colors.green)
              else
                ...recentSymptoms.map((e) => _buildSymptomListItem(e, provider, isMinimal: true)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSymptomListTab() {
    return Consumer<SymptomsProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) return const LoadingWidget();

        final filteredEntries = provider.searchSymptoms(_searchQuery);

        if (filteredEntries.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () async => _fetchData(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredEntries.length,
            itemBuilder: (context, index) {
              return _buildSymptomListItem(filteredEntries[index], provider);
            },
          ),
        );
      },
    );
  }

  Widget _buildAnalyticsTab() {
     return Consumer<SymptomsProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) return const LoadingWidget();
        if (provider.symptomEntries.isEmpty) {
          return _buildInfoCard('لا توجد بيانات كافية', 'سجل المزيد من الأعراض للحصول على تحليلات.', Icons.analytics, _primaryColor);
        }

        return ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildSectionTitle('اتجاهات الأعراض', Icons.show_chart),
            _buildChartCard(SymptomsChart(trends: provider.getSymptomTrends(), title: 'آخر 30 يوم')),
            const SizedBox(height: 16),
            _buildChartCard(SeverityDistributionChart(entries: provider.symptomEntries)),
            const SizedBox(height: 24),
            _buildSectionTitle('الأنماط الشائعة', Icons.star_border),
            _buildCommonPatternCard('الأعراض الأكثر شيوعاً', provider.getMostCommonSymptoms()),
            const SizedBox(height: 16),
            _buildCommonPatternCard('المحفزات الأكثر شيوعاً', provider.getMostCommonTriggers(), isTrigger: true),
          ],
        );
      },
    );
  }

  Widget _buildCalendarTab() {
    return Consumer<SymptomsProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) return const LoadingWidget();

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionTitle('تقويم الأعراض', Icons.calendar_today),
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: SymptomsCalendar(
                  symptomEntries: provider.symptomEntries,
                  selectedDate: _selectedDate,
                  onDateSelected: (date) => setState(() => _selectedDate = date),
                ),
              ),
              const SizedBox(height: 24),
              _buildSectionTitle('أعراض يوم ${DateFormat('d MMMM', 'ar').format(_selectedDate)}', Icons.list_alt),
              SelectedDateEntries(
                selectedDate: _selectedDate,
                entries: provider.symptomEntries,
                onEntryTap: (entry) => _showSymptomDetailsModal(entry),
              ),
            ],
          ),
        );
      },
    );
  }

  // --- UI HELPER WIDGETS ---

  Widget _buildSectionTitle(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        children: [
          Icon(icon, color: _textSecondaryColor, size: 20),
          const SizedBox(width: 8),
          Text(
            title,
            style: GoogleFonts.cairo(fontSize: 18, fontWeight: FontWeight.bold, color: _textColor),
          ),
        ],
      ),
    );
  }

  Widget _buildTodaySummary(List<SymptomEntry> todayEntries) {
    final severeCount = todayEntries.where((e) => e.severity >= 4).length;
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildSummaryItem('إجمالي الأعراض', '${todayEntries.length}', Icons.list_alt, Colors.blue),
            _buildSummaryItem('أعراض شديدة', '$severeCount', Icons.warning_amber_rounded, Colors.orange),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        CircleAvatar(
          radius: 24,
          backgroundColor: color.withOpacity(0.1),
          child: Icon(icon, size: 24, color: color),
        ),
        const SizedBox(height: 8),
        Text(value, style: GoogleFonts.cairo(fontSize: 22, fontWeight: FontWeight.bold, color: _textColor)),
        Text(label, style: GoogleFonts.cairo(fontSize: 13, color: _textSecondaryColor)),
      ],
    );
  }

  Widget _buildStatsGrid(Map<String, dynamic> stats) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 2.5,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      children: [
        _buildStatItem('متوسط الشدة', '${stats['averageSeverity'].toStringAsFixed(1)}/5', Icons.trending_up),
        _buildStatItem('هذا الأسبوع', '${stats['thisWeekCount']}', Icons.date_range),
        _buildStatItem('أعراض شديدة', '${stats['severeCount']}', Icons.favorite_border),
        _buildStatItem('إجمالي', '${stats['totalEntries']}', Icons.functions),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Icon(icon, color: _textSecondaryColor, size: 20),
          const SizedBox(width: 10),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(value, style: GoogleFonts.cairo(fontSize: 16, fontWeight: FontWeight.bold, color: _textColor)),
              Text(label, style: GoogleFonts.cairo(fontSize: 12, color: _textSecondaryColor)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSymptomListItem(SymptomEntry entry, SymptomsProvider provider, {bool isMinimal = false}) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: () => _showSymptomDetailsModal(entry),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 6,
                    backgroundColor: entry.severityColor,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      entry.symptomName,
                      style: GoogleFonts.cairo(fontSize: 17, fontWeight: FontWeight.bold, color: _textColor),
                    ),
                  ),
                  if (!isMinimal)
                    Text(
                      DateFormat('d MMM, HH:mm', 'ar').format(entry.timestamp),
                      style: GoogleFonts.cairo(fontSize: 13, color: _textSecondaryColor),
                    ),
                ],
              ),
              if (!isMinimal && (entry.notes != null && entry.notes!.isNotEmpty)) ...[
                const SizedBox(height: 10),
                Text(
                  entry.notes!,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: GoogleFonts.cairo(fontSize: 14, color: _textSecondaryColor),
                ),
              ],
              if (entry.triggers.isNotEmpty || entry.glutenExposure != null || entry.mealDetails != null) ...[
                const SizedBox(height: 10),
                Wrap(
                  spacing: 8,
                  runSpacing: 6,
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    ...entry.triggers.map((t) => _buildInfoChip(t, Icons.flash_on, Colors.orange)),
                    if (entry.glutenExposure != null)
                      const Tooltip(
                        message: 'تم تسجيل تعرض للغلوتين',
                        child: Icon(Icons.warning_amber_rounded, size: 18, color: Colors.amber),
                      ),
                    if (entry.mealDetails != null)
                      const Tooltip(
                        message: 'تم تسجيل تفاصيل وجبة',
                        child: Icon(Icons.restaurant_menu, size: 18, color: Colors.lightBlue),
                      ),
                  ],
                )
              ]
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(String text, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: GoogleFonts.cairo(fontSize: 12, color: color, fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.check_circle_outline, size: 80, color: Colors.green),
          const SizedBox(height: 16),
          Text('لا توجد أعراض!', style: GoogleFonts.cairo(fontSize: 22, fontWeight: FontWeight.bold)),
          Text(
            _searchQuery.isEmpty
                ? 'سجل رائع! لم يتم العثور على أعراض.'
                : 'لم يتم العثور على نتائج للبحث.',
            style: GoogleFonts.cairo(fontSize: 16, color: _textSecondaryColor),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  Widget _buildInfoCard(String title, String message, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            Icon(icon, size: 40, color: color),
            const SizedBox(height: 16),
            Text(title, style: GoogleFonts.cairo(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text(message, style: GoogleFonts.cairo(fontSize: 14, color: _textSecondaryColor), textAlign: TextAlign.center),
          ],
        ),
      ),
    );
  }

  Widget _buildChartCard(Widget chart) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: chart,
      ),
    );
  }

  Widget _buildCommonPatternCard(String title, Map<String, int> data, {bool isTrigger = false}) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: GoogleFonts.cairo(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            if (data.isEmpty)
              Text('لا توجد بيانات', style: GoogleFonts.cairo(color: _textSecondaryColor))
            else
              ...data.entries.map((entry) => Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Row(
                  children: [
                    Icon(isTrigger ? Icons.flash_on : Icons.coronavirus_outlined, color: isTrigger ? Colors.orange : _primaryColor, size: 18),
                    const SizedBox(width: 8),
                    Expanded(child: Text(entry.key, style: GoogleFonts.cairo(fontSize: 15, fontWeight: FontWeight.w600))),
                    Text('${entry.value} مرة', style: GoogleFonts.cairo(fontSize: 14, color: _textSecondaryColor, fontWeight: FontWeight.bold)),
                  ],
                ),
              )),
          ],
        ),
      ),
    );
  }

  // --- DIALOGS & MODALS ---

  void _showAddSymptomDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => const AddSymptomDialog(),
    );
    if (result == true) _fetchData();
  }

  void _showEditSymptomDialog(SymptomEntry entry) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AddSymptomDialog(existingEntry: entry),
    );
    if (result == true) _fetchData();
  }

  // --- Celiac-Specific Helpers ---
  String _translateMood(Mood mood) {
    switch (mood) {
      case Mood.happy: return 'سعيد';
      case Mood.neutral: return 'محايد';
      case Mood.sad: return 'حزين';
      case Mood.irritable: return 'منفعل';
      case Mood.foggy: return 'ضبابية الدماغ';
    }
  }

  String _translateGlutenExposureType(GlutenExposureType type) {
    switch (type) {
      case GlutenExposureType.suspected: return 'مشتبه به';
      case GlutenExposureType.confirmed: return 'مؤكد';
      case GlutenExposureType.crossContamination: return 'تلوث عرضي';
    }
  }

  String _translateMealSource(MealSource source) {
    switch (source) {
      case MealSource.home: return 'المنزل';
      case MealSource.restaurant: return 'مطعم';
      case MealSource.packed: return 'معلب';
    }
  }

  void _showSymptomDetailsModal(SymptomEntry entry) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          initialChildSize: 0.6,
          maxChildSize: 0.9,
          builder: (_, scrollController) => Container(
            padding: const EdgeInsets.all(20),
            child: ListView(
              controller: scrollController,
              children: [
                // Header
                Row(
                  children: [
                    CircleAvatar(backgroundColor: entry.severityColor, radius: 8),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        entry.symptomName,
                        style: GoogleFonts.cairo(fontSize: 22, fontWeight: FontWeight.bold),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.edit_outlined, color: _textSecondaryColor),
                      onPressed: () {
                        Navigator.pop(context);
                        _showEditSymptomDialog(entry);
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete_outline, color: _accentColor),
                      onPressed: () {
                        Navigator.pop(context);
                        _showDeleteConfirmation(entry);
                      },
                    ),
                  ],
                ),
                const Divider(height: 24),
                // Details
                _buildDetailRow('الشدة', entry.severityText, Icons.trending_up),
                _buildDetailRow('الوقت', DateFormat('EEEE, d MMMM yyyy, HH:mm', 'ar').format(entry.timestamp), Icons.access_time),
                if (entry.duration != null) _buildDetailRow('المدة', entry.durationText, Icons.timer),
                if (entry.notes != null && entry.notes!.isNotEmpty) _buildDetailRow('ملاحظات', entry.notes!, Icons.notes),
                if (entry.triggers.isNotEmpty) _buildDetailRow('المحفزات', entry.triggers.join(', '), Icons.flash_on),
                if (entry.medications.isNotEmpty) _buildDetailRow('الأدوية', entry.medications.join(', '), Icons.medical_services_outlined),
                if (entry.energyLevel != null) _buildDetailRow('مستوى الطاقة', '${entry.energyLevel}/5', Icons.battery_charging_full_rounded),
                if (entry.mood != null) _buildDetailRow('الحالة المزاجية', _translateMood(entry.mood!), Icons.sentiment_very_satisfied_rounded),
                if (entry.bristolStoolType != null) _buildDetailRow('مقياس بريستول للبراز', 'النوع ${entry.bristolStoolType}', Icons.water_drop_outlined),

                // Celiac-Specific Details
                if (entry.glutenExposure != null) ...[
                  const Divider(height: 24),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8.0),
                    child: _buildSectionTitle('تفاصيل التعرض للغلوتين', Icons.warning_amber_rounded),
                  ),
                  _buildDetailRow('النوع', _translateGlutenExposureType(entry.glutenExposure!.type), Icons.help_outline_rounded),
                  if (entry.glutenExposure!.suspectedSource != null && entry.glutenExposure!.suspectedSource!.isNotEmpty)
                    _buildDetailRow('المصدر المشتبه به', entry.glutenExposure!.suspectedSource!, Icons.food_bank_outlined),
                  if (entry.glutenExposure!.notes != null && entry.glutenExposure!.notes!.isNotEmpty)
                    _buildDetailRow('ملاحظات إضافية', entry.glutenExposure!.notes!, Icons.edit_note_rounded),
                ],

                if (entry.mealDetails != null) ...[
                  const Divider(height: 24),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8.0),
                    child: _buildSectionTitle('تفاصيل الوجبة', Icons.restaurant_menu_rounded),
                  ),
                  _buildDetailRow('الوجبة', entry.mealDetails!.mealName, Icons.fastfood_outlined),
                  _buildDetailRow('المصدر', _translateMealSource(entry.mealDetails!.source), Icons.storefront_outlined),
                  if (entry.mealDetails!.restaurantName != null && entry.mealDetails!.restaurantName!.isNotEmpty)
                    _buildDetailRow('اسم المطعم', entry.mealDetails!.restaurantName!, Icons.restaurant_rounded),
                  if (entry.mealDetails!.ingredients.isNotEmpty)
                    _buildDetailRow('المكونات', entry.mealDetails!.ingredients.join(', '), Icons.kitchen_rounded),
                  _buildDetailRow('معتمد خالي من الغلوتين', entry.mealDetails!.isCertifiedGlutenFree ? 'نعم' : 'لا', Icons.verified_user_outlined),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: _textSecondaryColor, size: 20),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label, style: GoogleFonts.cairo(fontSize: 13, color: _textSecondaryColor)),
                const SizedBox(height: 2),
                Text(value, style: GoogleFonts.cairo(fontSize: 15, fontWeight: FontWeight.w600, color: _textColor)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(SymptomEntry entry) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text('تأكيد الحذف', style: GoogleFonts.cairo()),
        content: Text('هل أنت متأكد من حذف هذا العرض؟ لا يمكن التراجع عن هذا الإجراء.', style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo(color: _textSecondaryColor)),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                final provider = Provider.of<SymptomsProvider>(context, listen: false);
                await provider.deleteSymptomEntry(entry.id!);
                if (mounted) ErrorHandler.showSuccessSnackBar(context, 'تم حذف العرض بنجاح');
              } catch (e) {
                if (mounted) ErrorHandler.showErrorSnackBar(context, 'فشل حذف العرض: $e');
              }
            },
            child: Text('حذف', style: GoogleFonts.cairo(color: _accentColor, fontWeight: FontWeight.bold)),
          ),
        ],
      ),
    );
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Wrap(
        children: [
          ListTile(
            leading: const Icon(Icons.analytics_outlined),
            title: Text('التحليل المتقدم', style: GoogleFonts.cairo()),
            onTap: () {
              Navigator.pop(context);
              // _showAdvancedAnalytics(); // Assuming this function exists and shows a dialog
            },
          ),
          ListTile(
            leading: const Icon(Icons.share_outlined),
            title: Text('مشاركة ملخص سريع', style: GoogleFonts.cairo()),
            onTap: () {
              Navigator.pop(context);
              // _shareQuickSummary();
            },
          ),
          ListTile(
            leading: const Icon(Icons.picture_as_pdf_outlined),
            title: Text('تصدير تقرير (JSON)', style: GoogleFonts.cairo()),
            onTap: () {
              Navigator.pop(context);
              _exportReport();
            },
          ),
           ListTile(
            leading: const Icon(Icons.backup_table_outlined),
            title: Text('تصدير بيانات (CSV)', style: GoogleFonts.cairo()),
            onTap: () {
              Navigator.pop(context);
              // _exportToCSV();
            },
          ),
          ListTile(
            leading: const Icon(Icons.cloud_upload_outlined),
            title: Text('إنشاء نسخة احتياطية', style: GoogleFonts.cairo()),
            onTap: () {
              Navigator.pop(context);
              // _createBackup();
            },
          ),
        ],
      ),
    );
  }
}

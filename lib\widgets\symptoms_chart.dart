import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/models/symptom_entry.dart';
import 'package:yassincil/utils/app_colors.dart';

class Symptoms<PERSON>hart extends StatelessWidget {
  final Map<String, List<int>> trends;
  final String title;

  const SymptomsChart({
    super.key,
    required this.trends,
    this.title = 'اتجاهات الأعراض',
  });

  @override
  Widget build(BuildContext context) {
    if (trends.isEmpty) {
      return _buildEmptyChart();
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const Sized<PERSON><PERSON>(height: 20),
            <PERSON><PERSON><PERSON><PERSON>(height: 250, child: Line<PERSON><PERSON>(_buildLineChartData())),
            const SizedBox(height: 16),
            _buildLegend(),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyChart() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        height: 200,
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.show_chart, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'لا توجد بيانات كافية لعرض الاتجاهات',
              style: GoogleFonts.cairo(
                color: AppColors.textSecondary,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  LineChartData _buildLineChartData() {
    final colors = [
      Colors.red,
      Colors.orange,
      Colors.blue,
      Colors.green,
      Colors.purple,
    ];

    final lineBarsData = <LineChartBarData>[];
    int colorIndex = 0;

    for (final entry in trends.entries) {
      final data = entry.value;

      final spots = <FlSpot>[];
      for (int i = 0; i < data.length; i++) {
        if (data[i] > 0) {
          spots.add(FlSpot(i.toDouble(), data[i].toDouble()));
        }
      }

      if (spots.isNotEmpty) {
        lineBarsData.add(
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: colors[colorIndex % colors.length],
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: const FlDotData(show: true),
            belowBarData: BarAreaData(
              show: true,
              color: colors[colorIndex % colors.length].withValues(alpha: 0.1),
            ),
          ),
        );
        colorIndex++;
      }
    }

    return LineChartData(
      lineBarsData: lineBarsData,
      titlesData: FlTitlesData(
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 40,
            getTitlesWidget: (value, meta) {
              return Text(
                value.toInt().toString(),
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: AppColors.textSecondary,
                ),
              );
            },
          ),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: 5,
            getTitlesWidget: (value, meta) {
              final dayIndex = value.toInt();
              if (dayIndex % 5 == 0) {
                final daysAgo = 29 - dayIndex;
                return Text(
                  '$daysAgo د',
                  style: GoogleFonts.cairo(
                    fontSize: 10,
                    color: AppColors.textSecondary,
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
      ),
      gridData: FlGridData(
        show: true,
        drawVerticalLine: false,
        horizontalInterval: 1,
        getDrawingHorizontalLine: (value) {
          return FlLine(color: Colors.grey.shade300, strokeWidth: 1);
        },
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: Colors.grey.shade300),
      ),
      minX: 0,
      maxX: 29,
      minY: 0,
      maxY: 5,
    );
  }

  Widget _buildLegend() {
    final colors = [
      Colors.red,
      Colors.orange,
      Colors.blue,
      Colors.green,
      Colors.purple,
    ];

    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: trends.keys.take(5).toList().asMap().entries.map((entry) {
        final index = entry.key;
        final symptomName = entry.value;
        final color = colors[index % colors.length];

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(color: color, shape: BoxShape.circle),
            ),
            const SizedBox(width: 6),
            Text(
              symptomName,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        );
      }).toList(),
    );
  }
}

class SeverityDistributionChart extends StatelessWidget {
  final List<SymptomEntry> entries;

  const SeverityDistributionChart({super.key, required this.entries});

  @override
  Widget build(BuildContext context) {
    if (entries.isEmpty) {
      return _buildEmptyChart();
    }

    final severityData = _calculateSeverityDistribution();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'توزيع شدة الأعراض',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              height: 200,
              child: PieChart(_buildPieChartData(severityData)),
            ),
            const SizedBox(height: 16),
            _buildSeverityLegend(severityData),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyChart() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        height: 200,
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.pie_chart, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'لا توجد بيانات لعرض التوزيع',
              style: GoogleFonts.cairo(
                color: AppColors.textSecondary,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Map<int, int> _calculateSeverityDistribution() {
    final distribution = <int, int>{};

    for (final entry in entries) {
      distribution[entry.severity] = (distribution[entry.severity] ?? 0) + 1;
    }

    return distribution;
  }

  PieChartData _buildPieChartData(Map<int, int> severityData) {
    final colors = [
      Colors.green, // شدة 1
      Colors.lightGreen, // شدة 2
      Colors.orange, // شدة 3
      Colors.deepOrange, // شدة 4
      Colors.red, // شدة 5
    ];

    final sections = severityData.entries.map((entry) {
      final severity = entry.key;
      final count = entry.value;
      final percentage = (count / entries.length) * 100;

      return PieChartSectionData(
        color: colors[severity - 1],
        value: count.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: GoogleFonts.cairo(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();

    return PieChartData(
      sections: sections,
      centerSpaceRadius: 40,
      sectionsSpace: 2,
    );
  }

  Widget _buildSeverityLegend(Map<int, int> severityData) {
    final colors = [
      Colors.green,
      Colors.lightGreen,
      Colors.orange,
      Colors.deepOrange,
      Colors.red,
    ];

    final severityTexts = [
      'خفيف',
      'خفيف إلى متوسط',
      'متوسط',
      'شديد',
      'شديد جداً',
    ];

    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: severityData.entries.map((entry) {
        final severity = entry.key;
        final count = entry.value;
        final color = colors[severity - 1];
        final text = severityTexts[severity - 1];

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(color: color, shape: BoxShape.circle),
            ),
            const SizedBox(width: 6),
            Text(
              '$text ($count)',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        );
      }).toList(),
    );
  }
}

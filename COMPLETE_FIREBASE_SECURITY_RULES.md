# قواعد الأمان الشاملة لتطبيق ياسين سيل 🔒

## 📋 **نسخ هذه القواعد إلى Firebase Console**

### 🔥 **قواعد Firestore Database**
انسخ هذا الكود إلى **Firestore Database > Rules**:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // ==================== قواعد المستخدمين ====================
    match /users/{userId} {
      // المستخدم يمكنه قراءة وتعديل بياناته فقط
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // المشرفون يمكنهم قراءة جميع المستخدمين
      allow read: if request.auth != null && 
                     exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    // ==================== قواعد المشرفين ====================
    match /admins/{adminId} {
      // فقط المشرفون يمكنهم قراءة قائمة المشرفين
      allow read: if request.auth != null && 
                     exists(/databases/$(database)/documents/admins/$(request.auth.uid));
      // لا يمكن لأحد إضافة مشرفين جدد من التطبيق (يدوياً فقط)
      allow write: if false;
    }
    
    // ==================== قواعد الأدوية ====================
    match /medications/{medicationId} {
      // الجميع يمكنهم القراءة
      allow read: if request.auth != null;
      
      // فقط المشرفون يمكنهم الكتابة
      allow create, update, delete: if request.auth != null && 
                                       exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    // تعليقات الأدوية
    match /medications/{medicationId}/comments/{commentId} {
      // الجميع يمكنهم القراءة
      allow read: if request.auth != null;
      
      // المستخدمون يمكنهم إضافة تعليقات
      allow create: if request.auth != null && 
                       request.auth.uid == resource.data.userId;
      
      // المستخدم يمكنه تعديل تعليقه، المشرف يمكنه تعديل/حذف أي تعليق
      allow update, delete: if request.auth != null && 
                               (request.auth.uid == resource.data.userId ||
                                exists(/databases/$(database)/documents/admins/$(request.auth.uid)));
    }
    
    // ردود التعليقات
    match /medications/{medicationId}/comments/{commentId}/replies/{replyId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
                       request.auth.uid == resource.data.userId;
      allow update, delete: if request.auth != null && 
                               (request.auth.uid == resource.data.userId ||
                                exists(/databases/$(database)/documents/admins/$(request.auth.uid)));
    }
    
    // ==================== قواعد الأطعمة ====================
    match /foods/{foodId} {
      allow read: if request.auth != null;
      allow create, update, delete: if request.auth != null && 
                                       exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    // تعليقات الأطعمة
    match /foods/{foodId}/comments/{commentId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
                       request.auth.uid == resource.data.userId;
      allow update, delete: if request.auth != null && 
                               (request.auth.uid == resource.data.userId ||
                                exists(/databases/$(database)/documents/admins/$(request.auth.uid)));
    }
    
    match /foods/{foodId}/comments/{commentId}/replies/{replyId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
                       request.auth.uid == resource.data.userId;
      allow update, delete: if request.auth != null && 
                               (request.auth.uid == resource.data.userId ||
                                exists(/databases/$(database)/documents/admins/$(request.auth.uid)));
    }
    
    // ==================== قواعد المطاعم ====================
    match /restaurants/{restaurantId} {
      allow read: if request.auth != null;
      allow create, update, delete: if request.auth != null && 
                                       exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    // تعليقات المطاعم
    match /restaurants/{restaurantId}/comments/{commentId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
                       request.auth.uid == resource.data.userId;
      allow update, delete: if request.auth != null && 
                               (request.auth.uid == resource.data.userId ||
                                exists(/databases/$(database)/documents/admins/$(request.auth.uid)));
    }
    
    match /restaurants/{restaurantId}/comments/{commentId}/replies/{replyId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
                       request.auth.uid == resource.data.userId;
      allow update, delete: if request.auth != null && 
                               (request.auth.uid == resource.data.userId ||
                                exists(/databases/$(database)/documents/admins/$(request.auth.uid)));
    }
    
    // ==================== قواعد المقالات ====================
    match /articles/{articleId} {
      allow read: if request.auth != null;
      allow create, update, delete: if request.auth != null && 
                                       exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    // تعليقات المقالات
    match /articles/{articleId}/comments/{commentId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
                       request.auth.uid == resource.data.userId;
      allow update, delete: if request.auth != null && 
                               (request.auth.uid == resource.data.userId ||
                                exists(/databases/$(database)/documents/admins/$(request.auth.uid)));
    }
    
    match /articles/{articleId}/comments/{commentId}/replies/{replyId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
                       request.auth.uid == resource.data.userId;
      allow update, delete: if request.auth != null && 
                               (request.auth.uid == resource.data.userId ||
                                exists(/databases/$(database)/documents/admins/$(request.auth.uid)));
    }
    
    // ==================== قواعد المفضلة ====================
    match /users/{userId}/favorites/{favoriteId} {
      // المستخدم يمكنه إدارة مفضلاته فقط
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // ==================== قواعد المنتدى ====================
    match /forum_posts/{postId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
                       request.auth.uid == resource.data.userId;
      allow update, delete: if request.auth != null && 
                               (request.auth.uid == resource.data.userId ||
                                exists(/databases/$(database)/documents/admins/$(request.auth.uid)));
    }
    
    // تعليقات المنتدى
    match /forum_posts/{postId}/comments/{commentId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
                       request.auth.uid == resource.data.userId;
      allow update, delete: if request.auth != null && 
                               (request.auth.uid == resource.data.userId ||
                                exists(/databases/$(database)/documents/admins/$(request.auth.uid)));
    }
    
    // ==================== قواعد الإشعارات ====================
    match /notifications/{userId}/messages/{messageId} {
      // المستخدم يمكنه قراءة إشعاراته فقط
      allow read, update: if request.auth != null && request.auth.uid == userId;
      // المشرفون يمكنهم إرسال إشعارات
      allow create: if request.auth != null && 
                       exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    // ==================== قواعد التقارير ====================
    match /reports/{reportId} {
      // المستخدمون يمكنهم إرسال تقارير
      allow create: if request.auth != null && 
                       request.auth.uid == resource.data.reporterId;
      // المشرفون يمكنهم قراءة وإدارة التقارير
      allow read, update, delete: if request.auth != null && 
                                     exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    // ==================== قواعد الإحصائيات ====================
    match /statistics/{statId} {
      // فقط المشرفون يمكنهم الوصول للإحصائيات
      allow read, write: if request.auth != null && 
                            exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    // ==================== قواعد الإعدادات ====================
    match /settings/{settingId} {
      // الجميع يمكنهم قراءة الإعدادات العامة
      allow read: if request.auth != null;
      // فقط المشرفون يمكنهم تعديل الإعدادات
      allow write: if request.auth != null && 
                      exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    // ==================== قواعد النسخ الاحتياطية ====================
    match /backups/{backupId} {
      // فقط المشرفون يمكنهم الوصول للنسخ الاحتياطية
      allow read, write: if request.auth != null && 
                            exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    // ==================== قواعد السجلات ====================
    match /logs/{logId} {
      // فقط المشرفون يمكنهم قراءة السجلات
      allow read: if request.auth != null && 
                     exists(/databases/$(database)/documents/admins/$(request.auth.uid));
      // النظام يمكنه كتابة السجلات (من خلال Cloud Functions)
      allow create: if request.auth != null;
    }
  }
}
```

---

### 🔥 **قواعد Firebase Storage**
انسخ هذا الكود إلى **Storage > Rules**:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // ==================== صور الأدوية ====================
    match /medications/{medicationId}/{allPaths=**} {
      // الجميع يمكنهم القراءة
      allow read: if request.auth != null;
      // فقط المشرفون يمكنهم الرفع والحذف
      allow write: if request.auth != null && 
                      exists(/databases/(default)/documents/admins/$(request.auth.uid));
    }
    
    // ==================== صور الأطعمة ====================
    match /foods/{foodId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
                      exists(/databases/(default)/documents/admins/$(request.auth.uid));
    }
    
    // ==================== صور المطاعم ====================
    match /restaurants/{restaurantId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
                      exists(/databases/(default)/documents/admins/$(request.auth.uid));
    }
    
    // ==================== صور المقالات ====================
    match /articles/{articleId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
                      exists(/databases/(default)/documents/admins/$(request.auth.uid));
    }
    
    // ==================== صور المستخدمين ====================
    match /users/{userId}/{allPaths=**} {
      // المستخدم يمكنه إدارة صوره فقط
      allow read, write: if request.auth != null && request.auth.uid == userId;
      // المشرفون يمكنهم قراءة جميع الصور
      allow read: if request.auth != null && 
                     exists(/databases/(default)/documents/admins/$(request.auth.uid));
    }
    
    // ==================== صور المنتدى ====================
    match /forum/{postId}/{allPaths=**} {
      allow read: if request.auth != null;
      // المستخدمون يمكنهم رفع صور لمنشوراتهم
      allow write: if request.auth != null;
    }
    
    // ==================== الملفات المؤقتة ====================
    match /temp/{userId}/{allPaths=**} {
      // المستخدم يمكنه رفع ملفات مؤقتة
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // ==================== النسخ الاحتياطية ====================
    match /backups/{allPaths=**} {
      // فقط المشرفون يمكنهم الوصول للنسخ الاحتياطية
      allow read, write: if request.auth != null && 
                            exists(/databases/(default)/documents/admins/$(request.auth.uid));
    }
  }
}
```

---

## 🛡️ **خطوات تطبيق القواعد**

### **الخطوة 1: Firestore Database**
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. اختر مشروع `yassincil`
3. **Firestore Database** > **Rules**
4. انسخ قواعد Firestore أعلاه
5. اضغط **Publish**

### **الخطوة 2: Storage**
1. في نفس المشروع، اذهب إلى **Storage**
2. **Rules**
3. انسخ قواعد Storage أعلاه
4. اضغط **Publish**

### **الخطوة 3: إضافة مشرف**
أضف نفسك كمشرف يدوياً:
1. **Firestore Database** > **Data**
2. أنشئ مجموعة جديدة: `admins`
3. أضف مستند بـ ID = `your-user-id`
4. أضف حقل: `isAdmin: true`

---

## 🔒 **ميزات الأمان المطبقة**

### ✅ **الحماية الأساسية:**
- كل العمليات تتطلب تسجيل دخول
- المستخدمون يصلون لبياناتهم فقط
- المشرفون لديهم صلاحيات إضافية

### ✅ **حماية البيانات الحساسة:**
- قائمة المشرفين محمية
- الإحصائيات للمشرفين فقط
- السجلات للمشرفين فقط

### ✅ **حماية المحتوى:**
- المشرفون فقط يضيفون/يعدلون المحتوى
- المستخدمون يديرون تعليقاتهم فقط
- المفضلة خاصة بكل مستخدم

### ✅ **حماية الملفات:**
- الصور محمية حسب النوع
- المستخدمون يرفعون لمجلداتهم فقط
- النسخ الاحتياطية للمشرفين فقط

---

## 🚀 **جاهز للتطوير!**

بعد تطبيق هذه القواعد، ستتمكن من:
- ✅ تطوير جميع الميزات بأمان
- ✅ اختبار التطبيق دون قيود
- ✅ حماية البيانات الحساسة
- ✅ إدارة الصلاحيات بمرونة

**هذه القواعد متوازنة بين الأمان وسهولة التطوير!** 🎉
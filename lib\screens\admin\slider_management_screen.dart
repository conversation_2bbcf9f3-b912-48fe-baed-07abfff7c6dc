import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:yassincil/providers/slider_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/models/slider_item.dart';
import 'package:yassincil/screens/admin/add_edit_slider_item_screen.dart';

class SliderManagementScreen extends StatefulWidget {
  const SliderManagementScreen({super.key});

  @override
  State<SliderManagementScreen> createState() => _SliderManagementScreenState();
}

class _SliderManagementScreenState extends State<SliderManagementScreen>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late TabController _tabController;
  String _searchQuery = '';
  String _selectedFilter = 'الكل';

  final List<String> _filters = ['الكل', 'نشط', 'غير نشط'];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _tabController = TabController(length: _filters.length, vsync: this);

    // Fetch slider items when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<SliderProvider>(context, listen: false).fetchSliderItems();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _refresh() async {
    if (!mounted) return;

    try {
      final sliderProvider = Provider.of<SliderProvider>(
        context,
        listen: false,
      );
      await sliderProvider.fetchSliderItems();
    } catch (e) {
      debugPrint('Error during refresh: $e');
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء تحديث البيانات',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<SliderItem> _getFilteredSliderItems(List<SliderItem> sliderItems) {
    return sliderItems.where((item) {
      // فلترة البحث
      final matchesSearch =
          _searchQuery.isEmpty ||
          item.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (item.description?.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ??
              false) ||
          (item.linkUrl?.toLowerCase().contains(_searchQuery.toLowerCase()) ??
              false);

      // فلترة الحالة
      bool matchesFilter = true;
      switch (_selectedFilter) {
        case 'نشط':
          matchesFilter = item.isActive;
          break;
        case 'غير نشط':
          matchesFilter = !item.isActive;
          break;
        default:
          matchesFilter = true;
      }

      return matchesSearch && matchesFilter;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final sliderProvider = Provider.of<SliderProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final isAdmin = authProvider.isAdmin;

    final filteredSliderItems = _getFilteredSliderItems(
      sliderProvider.sliderItems,
    );

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildFilterTabs(),
          _buildStatisticsCard(sliderProvider.sliderItems),
          Expanded(
            child: _buildContent(filteredSliderItems, sliderProvider, isAdmin),
          ),
        ],
      ),
      floatingActionButton: isAdmin ? _buildAddFAB() : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'إدارة السيدلار',
        style: GoogleFonts.cairo(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.indigo.shade600,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh, color: Colors.white),
          onPressed: _refresh,
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.indigo.shade600,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TextField(
          controller: _searchController,
          style: GoogleFonts.cairo(),
          decoration: InputDecoration(
            hintText: 'ابحث في عناصر السيدلار...',
            hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500),
            prefixIcon: Icon(Icons.search, color: Colors.indigo.shade600),
            suffixIcon: _searchQuery.isNotEmpty
                ? IconButton(
                    icon: Icon(Icons.clear, color: Colors.grey.shade600),
                    onPressed: () {
                      _searchController.clear();
                      setState(() {
                        _searchQuery = '';
                      });
                    },
                  )
                : null,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 15,
            ),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
      ),
    );
  }

  Widget _buildFilterTabs() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: TabBar(
        controller: _tabController,
        indicatorColor: Colors.indigo.shade600,
        labelColor: Colors.indigo.shade600,
        unselectedLabelColor: Colors.grey.shade600,
        labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        unselectedLabelStyle: GoogleFonts.cairo(fontWeight: FontWeight.normal),
        onTap: (index) {
          setState(() {
            _selectedFilter = _filters[index];
          });
        },
        tabs: _filters.map((filter) => Tab(text: filter)).toList(),
      ),
    );
  }

  Widget _buildStatisticsCard(List<SliderItem> allSliderItems) {
    final totalItems = allSliderItems.length;
    final activeItems = allSliderItems.where((item) => item.isActive).length;
    final inactiveItems = allSliderItems.where((item) => !item.isActive).length;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.analytics,
                    color: Colors.indigo.shade600,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'إحصائيات السيدلار',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'إجمالي العناصر',
                      totalItems.toString(),
                      Colors.indigo.shade600,
                      Icons.view_carousel,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatItem(
                      'نشط',
                      activeItems.toString(),
                      Colors.green.shade600,
                      Icons.visibility,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatItem(
                      'غير نشط',
                      inactiveItems.toString(),
                      Colors.red.shade600,
                      Icons.visibility_off,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildContent(
    List<SliderItem> sliderItems,
    SliderProvider sliderProvider,
    bool isAdmin,
  ) {
    if (sliderProvider.isLoading && sliderItems.isEmpty) {
      return _buildLoadingState();
    }

    if (sliderProvider.errorMessage != null && sliderItems.isEmpty) {
      return _buildErrorState(sliderProvider);
    }

    if (sliderItems.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _refresh,
      color: Colors.indigo.shade600,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: sliderItems.length,
        itemBuilder: (context, index) {
          return _buildSliderItemCard(
            sliderItems[index],
            sliderProvider,
            isAdmin,
          );
        },
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Colors.indigo.shade600),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل عناصر السيدلار...',
            style: GoogleFonts.cairo(fontSize: 16, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(SliderProvider sliderProvider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 80, color: Colors.red.shade300),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ أثناء تحميل البيانات',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            sliderProvider.errorMessage ?? 'خطأ غير معروف',
            textAlign: TextAlign.center,
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: _refresh,
            icon: const Icon(Icons.refresh),
            label: Text('إعادة المحاولة', style: GoogleFonts.cairo()),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.indigo.shade600,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.view_carousel_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد عناصر سيدلار مطابقة',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير معايير البحث أو الفلترة',
            textAlign: TextAlign.center,
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildSliderItemCard(
    SliderItem sliderItem,
    SliderProvider sliderProvider,
    bool isAdmin,
  ) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () {
          _showSliderItemDetails(sliderItem);
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة السيدلار
            Container(
              height: 200,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(16),
                ),
                image: sliderItem.imageUrl.isNotEmpty
                    ? DecorationImage(
                        image: NetworkImage(sliderItem.imageUrl),
                        fit: BoxFit.cover,
                        onError: (exception, stackTrace) {
                          debugPrint('Failed to load slider image: $exception');
                        },
                      )
                    : null,
                gradient: sliderItem.imageUrl.isEmpty
                    ? LinearGradient(
                        colors: [Colors.indigo.shade100, Colors.indigo.shade50],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      )
                    : null,
              ),
              child: Stack(
                children: [
                  if (sliderItem.imageUrl.isEmpty)
                    Center(
                      child: Icon(
                        Icons.image,
                        size: 60,
                        color: Colors.indigo.shade300,
                      ),
                    ),
                  // حالة النشاط
                  Positioned(
                    top: 12,
                    right: 12,
                    child: _buildStatusBadge(sliderItem.isActive),
                  ),
                  // أزرار الإدارة
                  if (isAdmin)
                    Positioned(
                      top: 12,
                      left: 12,
                      child: _buildAdminActions(sliderItem, sliderProvider),
                    ),
                  // العنوان على الصورة
                  if (sliderItem.title.isNotEmpty)
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.bottomCenter,
                            end: Alignment.topCenter,
                            colors: [
                              Colors.black.withValues(alpha: 0.8),
                              Colors.transparent,
                            ],
                          ),
                        ),
                        child: Text(
                          sliderItem.title,
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            // معلومات السيدلار
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (sliderItem.description != null &&
                      sliderItem.description!.isNotEmpty) ...[
                    Row(
                      children: [
                        Icon(
                          Icons.description,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            sliderItem.description!,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.grey.shade700,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                  ],
                  if (sliderItem.linkUrl != null &&
                      sliderItem.linkUrl!.isNotEmpty) ...[
                    Row(
                      children: [
                        Icon(
                          sliderItem.linkUrl!.startsWith('http')
                              ? Icons.link
                              : Icons.apps,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            sliderItem.linkUrl!.startsWith('http')
                                ? 'رابط خارجي'
                                : 'شاشة: ${sliderItem.linkUrl}',
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                  ],
                  Row(
                    children: [
                      Icon(Icons.sort, size: 16, color: Colors.grey.shade600),
                      const SizedBox(width: 8),
                      Text(
                        'الترتيب: ${sliderItem.order}',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        'تم الإنشاء: ${_formatDate(sliderItem.createdAt)}',
                        style: GoogleFonts.cairo(
                          fontSize: 10,
                          color: Colors.grey.shade500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(bool isActive) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isActive ? Colors.green.shade50 : Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isActive ? Colors.green.shade300 : Colors.red.shade300,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isActive ? Icons.visibility : Icons.visibility_off,
            size: 14,
            color: isActive ? Colors.green.shade600 : Colors.red.shade600,
          ),
          const SizedBox(width: 4),
          Text(
            isActive ? 'نشط' : 'غير نشط',
            style: GoogleFonts.cairo(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: isActive ? Colors.green.shade600 : Colors.red.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdminActions(
    SliderItem sliderItem,
    SliderProvider sliderProvider,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(20),
      ),
      child: PopupMenuButton<String>(
        icon: const Icon(Icons.more_vert, color: Colors.white, size: 20),
        onSelected: (value) async {
          switch (value) {
            case 'edit':
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) =>
                      AddEditSliderItemScreen(sliderItem: sliderItem),
                ),
              );
              break;
            case 'toggle':
              await _toggleSliderItemStatus(sliderItem, sliderProvider);
              break;
            case 'delete':
              await _deleteSliderItem(sliderItem, sliderProvider);
              break;
          }
        },
        itemBuilder: (context) => [
          PopupMenuItem<String>(
            value: 'edit',
            child: Row(
              children: [
                Icon(Icons.edit, size: 16, color: Colors.blue.shade600),
                const SizedBox(width: 8),
                Text('تعديل', style: GoogleFonts.cairo(fontSize: 12)),
              ],
            ),
          ),
          PopupMenuItem<String>(
            value: 'toggle',
            child: Row(
              children: [
                Icon(
                  sliderItem.isActive ? Icons.visibility_off : Icons.visibility,
                  size: 16,
                  color: sliderItem.isActive
                      ? Colors.red.shade600
                      : Colors.green.shade600,
                ),
                const SizedBox(width: 8),
                Text(
                  sliderItem.isActive ? 'إخفاء' : 'إظهار',
                  style: GoogleFonts.cairo(fontSize: 12),
                ),
              ],
            ),
          ),
          PopupMenuItem<String>(
            value: 'delete',
            child: Row(
              children: [
                Icon(Icons.delete, size: 16, color: Colors.red.shade600),
                const SizedBox(width: 8),
                Text('حذف', style: GoogleFonts.cairo(fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddFAB() {
    return FloatingActionButton.extended(
      onPressed: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const AddEditSliderItemScreen(),
          ),
        );
      },
      backgroundColor: Colors.indigo.shade600,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.add),
      label: Text('إضافة عنصر', style: GoogleFonts.cairo()),
    );
  }

  String _formatDate(Timestamp timestamp) {
    final date = timestamp.toDate();
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showSliderItemDetails(SliderItem sliderItem) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.indigo.shade600,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  Icon(Icons.view_carousel, color: Colors.white),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      sliderItem.title,
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (sliderItem.imageUrl.isNotEmpty)
                      Center(
                        child: Container(
                          width: double.infinity,
                          height: 200,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(16),
                            child: Image.network(
                              sliderItem.imageUrl,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          Colors.indigo.shade100,
                                          Colors.indigo.shade50,
                                        ],
                                      ),
                                    ),
                                    child: Icon(
                                      Icons.image,
                                      size: 60,
                                      color: Colors.indigo.shade300,
                                    ),
                                  ),
                            ),
                          ),
                        ),
                      ),
                    const SizedBox(height: 20),
                    _buildDetailItem('العنوان', sliderItem.title, Icons.title),
                    if (sliderItem.description != null &&
                        sliderItem.description!.isNotEmpty)
                      _buildDetailItem(
                        'الوصف',
                        sliderItem.description!,
                        Icons.description,
                      ),
                    if (sliderItem.linkUrl != null &&
                        sliderItem.linkUrl!.isNotEmpty)
                      _buildDetailItem(
                        'الرابط',
                        sliderItem.linkUrl!,
                        sliderItem.linkUrl!.startsWith('http')
                            ? Icons.link
                            : Icons.apps,
                      ),
                    _buildDetailItem(
                      'الشاشة المستهدفة',
                      sliderItem.targetScreen,
                      Icons.screen_share,
                    ),
                    _buildDetailItem(
                      'الحالة',
                      sliderItem.isActive ? 'نشط' : 'غير نشط',
                      sliderItem.isActive
                          ? Icons.visibility
                          : Icons.visibility_off,
                      color: sliderItem.isActive ? Colors.green : Colors.red,
                    ),
                    _buildDetailItem(
                      'الترتيب',
                      sliderItem.order.toString(),
                      Icons.sort,
                    ),
                    _buildDetailItem(
                      'تاريخ الإنشاء',
                      _formatDate(sliderItem.createdAt),
                      Icons.calendar_today,
                    ),
                    if (sliderItem.startDate != null)
                      _buildDetailItem(
                        'تاريخ البداية',
                        _formatDate(sliderItem.startDate!),
                        Icons.play_arrow,
                      ),
                    if (sliderItem.endDate != null)
                      _buildDetailItem(
                        'تاريخ النهاية',
                        _formatDate(sliderItem.endDate!),
                        Icons.stop,
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(
    String label,
    String value,
    IconData icon, {
    Color? color,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 20, color: color ?? Colors.indigo.shade600),
              const SizedBox(width: 8),
              Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: color ?? Colors.grey.shade800,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _toggleSliderItemStatus(
    SliderItem sliderItem,
    SliderProvider sliderProvider,
  ) async {
    try {
      // Create updated slider item with toggled status
      final updatedItem = SliderItem(
        id: sliderItem.id,
        imageUrl: sliderItem.imageUrl,
        title: sliderItem.title,
        description: sliderItem.description,
        linkUrl: sliderItem.linkUrl,
        targetScreen: sliderItem.targetScreen,
        createdAt: sliderItem.createdAt,
        order: sliderItem.order,
        isActive: !sliderItem.isActive,
        startDate: sliderItem.startDate,
        endDate: sliderItem.endDate,
        likes: sliderItem.likes,
        likesCount: sliderItem.likesCount,
        commentsCount: sliderItem.commentsCount,
      );

      await sliderProvider.updateSliderItem(updatedItem);

      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تحديث حالة العنصر بنجاح',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في تحديث حالة العنصر',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteSliderItem(
    SliderItem sliderItem,
    SliderProvider sliderProvider,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(
          'تأكيد الحذف',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل أنت متأكد أنك تريد حذف "${sliderItem.title}"؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(false),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(ctx).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await sliderProvider.deleteSliderItem(sliderItem.id!);
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف العنصر بنجاح', style: GoogleFonts.cairo()),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في حذف العنصر', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}

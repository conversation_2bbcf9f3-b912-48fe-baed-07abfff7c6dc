import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/models/product.dart';
import 'package:yassincil/providers/product_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/screens/barcode/edit_product_screen.dart';
import 'package:yassincil/services/barcode_service.dart';
import 'package:yassincil/utils/dialog_utils.dart';

class ProductDetailScreen extends StatelessWidget {
  final Product product;

  const ProductDetailScreen({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final isAdmin = authProvider.userProfile?.role == 'admin';

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'تفاصيل المنتج',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          if (isAdmin) ...[
            IconButton(
              icon: const Icon(Icons.edit),
              tooltip: 'تعديل المنتج',
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => EditProductScreen(product: product),
                  ),
                );
              },
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              tooltip: 'حذف المنتج',
              onPressed: () => _deleteProduct(context),
            ),
          ],
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // معلومات أساسية
            _buildBasicInfo(theme),

            const SizedBox(height: 20),

            // حالة الأمان
            _buildSafetyStatus(theme),

            const SizedBox(height: 20),

            // المكونات
            if (product.ingredients.isNotEmpty) ...[
              _buildIngredientsSection(theme),
              const SizedBox(height: 20),
            ],

            // مسببات الحساسية
            if (product.allergens.isNotEmpty) ...[
              _buildAllergensSection(theme),
              const SizedBox(height: 20),
            ],

            // الشهادات
            if (product.certifications.isNotEmpty) ...[
              _buildCertificationsSection(theme),
              const SizedBox(height: 20),
            ],

            // معلومات إضافية
            _buildAdditionalInfo(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfo(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.deepPurple.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المنتج (إذا توفرت)
          if (product.imageUrl != null) ...[
            Container(
              height: 150,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                image: DecorationImage(
                  image: NetworkImage(product.imageUrl!),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // اسم المنتج
          Text(
            product.name,
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),

          const SizedBox(height: 8),

          // العلامة التجارية
          Row(
            children: [
              Icon(Icons.business, color: Colors.grey[600], size: 16),
              const SizedBox(width: 8),
              Text(
                product.brand,
                style: GoogleFonts.cairo(fontSize: 16, color: Colors.grey[600]),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // الباركود
          Row(
            children: [
              Icon(Icons.qr_code, color: Colors.grey[600], size: 16),
              const SizedBox(width: 8),
              Text(
                product.barcode,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontFamily: 'monospace',
                ),
              ),
            ],
          ),

          if (product.description.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              product.description,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.5,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSafetyStatus(ThemeData theme) {
    final safety = BarcodeService.evaluateProductSafety(product);
    Color safetyColor;
    IconData safetyIcon;
    String safetyText;
    String safetyDescription;

    switch (safety) {
      case ProductSafety.safe:
        safetyColor = Colors.green;
        safetyIcon = Icons.check_circle;
        safetyText = 'آمن لمرضى السيلياك';
        safetyDescription = 'هذا المنتج آمن للاستهلاك ولا يحتوي على غلوتين';
        break;
      case ProductSafety.unsafe:
        safetyColor = Colors.red;
        safetyIcon = Icons.warning;
        safetyText = 'غير آمن لمرضى السيلياك';
        safetyDescription = 'هذا المنتج يحتوي على غلوتين وغير آمن للاستهلاك';
        break;
      case ProductSafety.unknown:
        safetyColor = Colors.orange;
        safetyIcon = Icons.help;
        safetyText = 'حالة الأمان غير محددة';
        safetyDescription = 'لا توجد معلومات كافية لتحديد مدى أمان هذا المنتج';
        break;
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: safetyColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: safetyColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(safetyIcon, color: safetyColor, size: 48),
          const SizedBox(height: 12),
          Text(
            safetyText,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: safetyColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            safetyDescription,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: safetyColor.withValues(alpha: 0.8),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildIngredientsSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.deepPurple.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.list_alt, color: theme.primaryColor, size: 20),
              const SizedBox(width: 8),
              Text(
                'المكونات',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: product.ingredients.map((ingredient) {
              final containsGluten = BarcodeService.containsGluten([
                ingredient,
              ]);
              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: containsGluten ? Colors.red[50] : Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: containsGluten
                        ? Colors.red[300]!
                        : Colors.grey[300]!,
                  ),
                ),
                child: Text(
                  ingredient,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: containsGluten ? Colors.red[700] : Colors.grey[700],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAllergensSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.deepPurple.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.warning_amber, color: Colors.orange[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'مسببات الحساسية',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: product.allergens.map((allergen) {
              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.orange[300]!),
                ),
                child: Text(
                  allergen,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.orange[700],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildCertificationsSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.deepPurple.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.verified, color: Colors.green[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'الشهادات',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Column(
            children: product.certifications.map((certification) {
              return Container(
                width: double.infinity,
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[300]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: Colors.green[600],
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        certification,
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.green[700],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.deepPurple.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات إضافية',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 16),

          if (product.category.isNotEmpty)
            _buildInfoRow('الفئة', product.category, Icons.category),

          if (product.rating != null)
            _buildInfoRow('التقييم', product.ratingText, Icons.star),

          _buildInfoRow(
            'معتمد',
            product.isVerified ? 'نعم' : 'لا',
            product.isVerified ? Icons.verified : Icons.pending,
          ),

          if (product.addedAt != null)
            _buildInfoRow(
              'تاريخ الإضافة',
              '${product.addedAt!.day}/${product.addedAt!.month}/${product.addedAt!.year}',
              Icons.calendar_today,
            ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, color: Colors.grey[600], size: 16),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteProduct(BuildContext context) async {
    final confirmed = await DialogUtils.showConfirmationDialog(
      context,
      title: 'حذف المنتج',
      message:
          'هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.',
      confirmText: 'حذف',
      cancelText: 'إلغاء',
    );

    if (confirmed && context.mounted) {
      try {
        await Provider.of<ProductProvider>(
          context,
          listen: false,
        ).deleteProduct(product.id!);

        if (context.mounted) {
          Navigator.of(context).pop(); // العودة للشاشة السابقة
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('تم حذف المنتج بنجاح')));
        }
      } catch (e) {
        if (context.mounted) {
          DialogUtils.showErrorDialog(
            context,
            title: 'خطأ',
            message: 'فشل في حذف المنتج: ${e.toString()}',
          );
        }
      }
    }
  }
}

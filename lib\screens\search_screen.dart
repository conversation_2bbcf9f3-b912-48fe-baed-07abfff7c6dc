import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:yassincil/providers/medication_provider.dart'; // مثال للبحث في الأدوية
import 'package:yassincil/models/medication.dart';
import 'package:yassincil/screens/medications/medication_detail_screen.dart'; // لتفاصيل الدواء

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Medication> _searchResults =
      []; // يمكن أن تكون قائمة من نوع ديناميكي أو كائن base class
  bool _isSearching = false;

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      // مثال: البحث عن الأدوية
      // يمكنك توسيع هذا ليشمل جلب البيانات من FoodProvider, RecipeProvider, etc.
      // ثم دمج النتائج وتصنيفها.
      final allMedications = Provider.of<MedicationProvider>(
        context,
        listen: false,
      ).medications;
      _searchResults = allMedications.where((medication) {
        return medication.name.toLowerCase().contains(query.toLowerCase()) ||
            medication.company.toLowerCase().contains(query.toLowerCase()) ||
            medication.ingredients.toLowerCase().contains(query.toLowerCase());
      }).toList();

      // يمكن إضافة البحث في الأطعمة والوصفات هنا لاحقاً
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في البحث: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSearching = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'ابحث عن أدوية، أطعمة، وصفات...',
            border: InputBorder.none,
            hintStyle: TextStyle(
              color: Theme.of(
                context,
              ).appBarTheme.foregroundColor?.withValues(alpha: 0.7),
            ),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear, color: Colors.white),
                    onPressed: () {
                      _searchController.clear();
                      _performSearch('');
                    },
                  )
                : null,
          ),
          style: const TextStyle(color: Colors.white, fontSize: 18),
          onSubmitted: _performSearch, // Search when Enter is pressed
          onChanged: (value) {
            // Optional: Live search or clear results if empty
            if (value.isEmpty) {
              _performSearch('');
            }
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _performSearch(_searchController.text),
          ),
        ],
      ),
      body: _isSearching
          ? const Center(child: CircularProgressIndicator())
          : _searchResults.isEmpty && _searchController.text.isNotEmpty
          ? const Center(child: Text('لا توجد نتائج مطابقة.'))
          : _searchController.text.isEmpty
          ? const Center(
              child: Padding(
                padding: EdgeInsets.all(20.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.search, size: 80, color: Colors.grey),
                    SizedBox(height: 16),
                    Text(
                      'ابدأ البحث للعثور على ما تحتاجه.',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 18, color: Colors.grey),
                    ),
                  ],
                ),
              ),
            )
          : ListView.builder(
              itemCount: _searchResults.length,
              itemBuilder: (context, index) {
                final medication =
                    _searchResults[index]; // Assuming all results are medications for simplicity
                return Card(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: ListTile(
                    title: Text(medication.name),
                    subtitle: Text(medication.company),
                    onTap: () {
                      // Navigate to the detail screen of the found item
                      // This will require checking the type of the item if you search across multiple models
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) =>
                              MedicationDetailScreen(medication: medication),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
    );
  }
}

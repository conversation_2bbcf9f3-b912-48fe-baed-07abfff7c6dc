# 🛡️ دليل قواعد Firebase Security Rules المتكاملة

## 📋 نظرة عامة

هذا الملف يحتوي على قواعد Firebase Security Rules شاملة ومتكاملة لجميع أقسام تطبيق "رفيق السيلياك".

## 🔧 كيفية تطبيق القواعد

### 1. عبر Firebase Console:
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروعك
3. اذهب إلى **Firestore Database**
4. اختر تبويب **Rules**
5. انسخ محتوى ملف `firestore.rules` والصقه
6. اضغط **Publish**

### 2. عبر Firebase CLI:
```bash
# تأكد من تسجيل الدخول
firebase login

# نشر القواعد
firebase deploy --only firestore:rules

# أو استخدم السكريبت المرفق
deploy-rules.bat
```

## 🏗️ هيكل القواعد

### 🔧 الدوال المساعدة:
- `isAuthenticated()` - التحقق من تسجيل الدخول
- `isAdmin()` - التحقق من صلاحيات المشرف
- `isOwner(userId)` - التحقق من ملكية البيانات
- `isOwnerOrAdmin(userId)` - التحقق من الملكية أو الإدارة

### 📊 الأقسام المشمولة:

#### 👥 إدارة المستخدمين:
- `/users/{userId}` - بيانات المستخدمين
- `/users/{userId}/preferences/{document}` - الإعدادات الشخصية
- `/users/{userId}/notifications/{document}` - الإشعارات

#### 🍽️ المحتوى الغذائي:
- `/foodItems/{foodId}` - الأطعمة والمكونات
- `/recipes/{recipeId}` - الوصفات
- `/restaurants/{restaurantId}` - المطاعم

#### 💊 المحتوى الطبي:
- `/medications/{medicationId}` - الأدوية
- `/pharmacies/{pharmacyId}` - الصيدليات
- `/doctors/{doctorId}` - الأطباء
- `/hospitals/{hospitalId}` - المستشفيات

#### 🏪 المحتوى التجاري:
- `/stores/{storeId}` - المتاجر

#### 📚 المحتوى التعليمي:
- `/articles/{articleId}` - المقالات

#### 💬 التفاعل الاجتماعي:
- `/posts/{postId}` - منشورات المنتدى
- `/reviews/{reviewId}` - التقييمات العامة
- `/messages/{messageId}` - الرسائل
- `/conversations/{conversationId}` - المحادثات

#### 📊 البيانات الشخصية:
- `/symptom_entries/{entryId}` - تسجيل الأعراض
- `/medication_reminders/{reminderId}` - تذكيرات الأدوية
- `/nutrition_entries/{entryId}` - سجل التغذية
- `/nutrition_goals/{goalId}` - أهداف التغذية

#### ⚙️ الإعدادات والإدارة:
- `/app_settings/{settingId}` - إعدادات التطبيق
- `/sliderItems/{sliderId}` - عناصر السلايدر
- `/admin/{document}` - بيانات الإدارة
- `/analytics/{document}` - الإحصائيات

## 🔐 مستويات الصلاحيات

### 👤 المستخدم العادي:
- **قراءة:** جميع المحتوى العام
- **كتابة:** بياناته الشخصية فقط
- **تعليق:** على جميع المحتوى
- **تقييم:** جميع الخدمات

### 👨‍💼 المشرف:
- **قراءة:** جميع البيانات
- **كتابة:** جميع المحتوى العام
- **إدارة:** المستخدمين والمحتوى
- **تحليل:** الإحصائيات والتقارير

### 🚫 غير المسجل:
- **منع الوصول** لجميع البيانات

## 🛡️ ميزات الأمان

### ✅ الحماية المطبقة:
- **تشفير البيانات** أثناء النقل والتخزين
- **فحص الهوية** لكل عملية
- **تحديد الصلاحيات** حسب نوع المستخدم
- **منع الوصول غير المصرح** به
- **حماية البيانات الشخصية**

### 🔒 القيود المطبقة:
- **منع الوصول المجهول** لأي بيانات
- **حماية بيانات المستخدمين** الشخصية
- **تقييد عمليات الإدارة** للمشرفين فقط
- **منع تعديل بيانات الآخرين**

## 🚨 تحذيرات مهمة

### ⚠️ قبل النشر:
1. **اختبر القواعد** في بيئة التطوير أولاً
2. **تأكد من صحة معرفات المجموعات**
3. **احذف قواعد الاختبار** من الإنتاج
4. **راجع صلاحيات المشرفين**

### 🔧 للصيانة:
1. **راقب سجلات الأخطاء** بانتظام
2. **حدث القواعد** عند إضافة ميزات جديدة
3. **اختبر التغييرات** قبل النشر
4. **احتفظ بنسخة احتياطية** من القواعد

## 📞 الدعم

في حالة وجود مشاكل:
1. تحقق من سجلات Firebase Console
2. تأكد من تسجيل الدخول في التطبيق
3. راجع صلاحيات المستخدم
4. تحقق من اتصال الإنترنت

---

**تم إنشاء هذه القواعد لضمان أقصى درجات الأمان والحماية لبيانات المستخدمين.**

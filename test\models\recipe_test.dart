import 'package:flutter_test/flutter_test.dart';
import 'package:yassincil/models/recipe.dart';

void main() {
  group('Recipe Model Tests', () {
    test('should create a recipe with required fields', () {
      // Arrange
      final recipe = Recipe(
        title: 'وصفة تجريبية',
        description: 'وصف تجريبي',
        ingredients: ['مكون 1', 'مكون 2'],
        instructions: ['خطوة 1', 'خطوة 2'],
        category: 'مخبوزات',
        userId: 'user-1',
        username: 'مستخدم تجريبي',
        createdAt: DateTime.now(),
      );

      // Assert
      expect(recipe.title, 'وصفة تجريبية');
      expect(recipe.description, 'وصف تجريبي');
      expect(recipe.ingredients.length, 2);
      expect(recipe.instructions.length, 2);
      expect(recipe.category, 'مخبوزات');
      expect(recipe.userId, 'user-1');
      expect(recipe.username, 'مستخدم تجريبي');
    });

    test('should create recipe with default values', () {
      // Arrange
      final recipe = Recipe(
        title: 'وصفة تجريبية',
        description: 'وصف تجريبي',
        ingredients: ['مكون 1'],
        instructions: ['خطوة 1'],
        category: 'مخبوزات',
        userId: 'user-1',
        username: 'مستخدم تجريبي',
        createdAt: DateTime.now(),
      );

      // Assert
      expect(recipe.imageUrls, isEmpty);
      expect(recipe.likesCount, 0);
      expect(recipe.commentsCount, 0);
      expect(recipe.ratingsCount, 0);
      expect(recipe.averageRating, 0.0);
      expect(recipe.isGlutenFree, false);
      expect(recipe.isApproved, true);
      expect(recipe.isFeatured, false);
      expect(recipe.prepTime, 30);
      expect(recipe.cookTime, 30);
      expect(recipe.servings, 4);
      expect(recipe.difficulty, 'متوسط');
    });

    test('should create recipe with nutrition information', () {
      // Arrange
      final recipe = Recipe(
        title: 'وصفة تجريبية',
        description: 'وصف تجريبي',
        ingredients: ['مكون 1'],
        instructions: ['خطوة 1'],
        category: 'مخبوزات',
        userId: 'user-1',
        username: 'مستخدم تجريبي',
        createdAt: DateTime.now(),
        calories: 250.0,
        protein: 15.0,
        carbs: 30.0,
        fat: 8.0,
        isGlutenFree: true,
      );

      // Assert
      expect(recipe.calories, 250.0);
      expect(recipe.protein, 15.0);
      expect(recipe.carbs, 30.0);
      expect(recipe.fat, 8.0);
      expect(recipe.isGlutenFree, true);
    });

    test('should have valid properties', () {
      // Arrange
      final recipe = Recipe(
        title: 'وصفة تجريبية',
        description: 'وصف تجريبي',
        ingredients: ['مكون 1'],
        instructions: ['خطوة 1'],
        category: 'مخبوزات',
        userId: 'user-1',
        username: 'مستخدم تجريبي',
        createdAt: DateTime.now(),
      );

      // Assert
      expect(recipe.title, isNotEmpty);
      expect(recipe.description, isNotEmpty);
      expect(recipe.ingredients, isNotEmpty);
      expect(recipe.instructions, isNotEmpty);
      expect(recipe.category, isNotEmpty);
      expect(recipe.userId, isNotEmpty);
      expect(recipe.username, isNotEmpty);
    });
  });
}

import 'package:flutter_test/flutter_test.dart';
import 'package:yassincil/models/pharmacy.dart';

void main() {
  group('Pharmacy Model Tests', () {
    test('should create a pharmacy with required fields', () {
      // Arrange
      final pharmacy = Pharmacy(
        name: 'صيدلية النهدي',
        address: 'الرياض - شارع العليا',
        phone: '+966112345678',
        latitude: 24.7136,
        longitude: 46.6753,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Assert
      expect(pharmacy.name, 'صيدلية النهدي');
      expect(pharmacy.address, 'الرياض - شارع العليا');
      expect(pharmacy.phone, '+966112345678');
      expect(pharmacy.latitude, 24.7136);
      expect(pharmacy.longitude, 46.6753);
      expect(pharmacy.createdAt, isA<DateTime>());
      expect(pharmacy.updatedAt, isA<DateTime>());
    });

    test('should create pharmacy with default values', () {
      // Arrange
      final pharmacy = Pharmacy(
        name: 'صيدلية الدواء',
        address: 'جدة - شارع التحلية',
        phone: '+966112345679',
        latitude: 21.4858,
        longitude: 39.1925,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Assert
      expect(pharmacy.rating, 4.0);
      expect(pharmacy.distance, '0 كم');
      expect(pharmacy.isOpen, true);
      expect(pharmacy.openingHours, '24 ساعة');
      expect(pharmacy.hasGlutenFreeProducts, false);
      expect(pharmacy.specialServices, isEmpty);
      expect(pharmacy.availableMedicines, isEmpty);
      expect(pharmacy.hasDelivery, false);
      expect(pharmacy.acceptsInsurance, true);
    });

    test('should create pharmacy with optional fields', () {
      // Arrange
      final pharmacy = Pharmacy(
        name: 'صيدلية صحتك',
        address: 'الدمام - الكورنيش',
        phone: '+966112345680',
        latitude: 26.4207,
        longitude: 50.0888,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        rating: 4.8,
        distance: '2.5 كم',
        isOpen: true,
        openingHours: '8:00 ص - 12:00 ص',
        hasGlutenFreeProducts: true,
        specialServices: ['استشارات دوائية', 'قياس الضغط', 'قياس السكر'],
        website: 'https://sahtak-pharmacy.com',
        email: '<EMAIL>',
        description: 'صيدلية متخصصة في الأدوية والمنتجات الصحية',
        availableMedicines: ['أدوية الضغط', 'أدوية السكري', 'فيتامينات'],
        hasDelivery: true,
        acceptsInsurance: true,
        imageUrl: 'https://example.com/pharmacy.jpg',
      );

      // Assert
      expect(pharmacy.rating, 4.8);
      expect(pharmacy.distance, '2.5 كم');
      expect(pharmacy.openingHours, '8:00 ص - 12:00 ص');
      expect(pharmacy.hasGlutenFreeProducts, true);
      expect(pharmacy.specialServices.length, 3);
      expect(pharmacy.website, 'https://sahtak-pharmacy.com');
      expect(pharmacy.email, '<EMAIL>');
      expect(pharmacy.description, 'صيدلية متخصصة في الأدوية والمنتجات الصحية');
      expect(pharmacy.availableMedicines.length, 3);
      expect(pharmacy.hasDelivery, true);
      expect(pharmacy.imageUrl, 'https://example.com/pharmacy.jpg');
    });

    test('should convert to map correctly', () {
      // Arrange
      final pharmacy = Pharmacy(
        name: 'صيدلية الشفاء',
        address: 'مكة - العزيزية',
        phone: '+966112345681',
        latitude: 21.3891,
        longitude: 39.8579,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        rating: 4.5,
        hasGlutenFreeProducts: true,
        hasDelivery: true,
        specialServices: ['توصيل مجاني', 'استشارات'],
      );

      // Act
      final map = pharmacy.toMap();

      // Assert
      expect(map['name'], 'صيدلية الشفاء');
      expect(map['address'], 'مكة - العزيزية');
      expect(map['phone'], '+966112345681');
      expect(map['latitude'], 21.3891);
      expect(map['longitude'], 39.8579);
      expect(map['rating'], 4.5);
      expect(map['hasGlutenFreeProducts'], true);
      expect(map['hasDelivery'], true);
      expect(map['specialServices'], ['توصيل مجاني', 'استشارات']);
      expect(map['createdAt'], isA<dynamic>());
      expect(map['updatedAt'], isA<dynamic>());
    });

    test('should have valid properties', () {
      // Arrange
      final pharmacy = Pharmacy(
        name: 'صيدلية الحياة',
        address: 'الطائف - شارع الملك فهد',
        phone: '+966112345682',
        latitude: 21.2703,
        longitude: 40.4158,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Assert
      expect(pharmacy.name, isNotEmpty);
      expect(pharmacy.address, isNotEmpty);
      expect(pharmacy.phone, isNotEmpty);
      expect(pharmacy.latitude, isA<double>());
      expect(pharmacy.longitude, isA<double>());
      expect(pharmacy.rating, greaterThanOrEqualTo(0.0));
      expect(pharmacy.rating, lessThanOrEqualTo(5.0));
      expect(pharmacy.createdAt, isA<DateTime>());
      expect(pharmacy.updatedAt, isA<DateTime>());
    });

    test('should handle all services correctly', () {
      // Arrange
      final pharmacy = Pharmacy(
        name: 'صيدلية المستقبل',
        address: 'الرياض - حي النرجس',
        phone: '+966112345683',
        latitude: 24.7136,
        longitude: 46.6753,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        specialServices: ['استشارات دوائية', 'قياس الضغط'],
        hasGlutenFreeProducts: true,
        hasDelivery: true,
        acceptsInsurance: true,
      );

      // Act
      final allServices = pharmacy.allServices;

      // Assert
      expect(allServices, contains('استشارات دوائية'));
      expect(allServices, contains('قياس الضغط'));
      expect(allServices, contains('منتجات خالية من الجلوتين'));
      expect(allServices, contains('خدمة التوصيل'));
      expect(allServices, contains('يقبل التأمين'));
    });

    test('should copy with new values', () {
      // Arrange
      final originalPharmacy = Pharmacy(
        name: 'صيدلية الأصل',
        address: 'الرياض - العليا',
        phone: '+966112345684',
        latitude: 24.7136,
        longitude: 46.6753,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        rating: 4.0,
        hasDelivery: false,
      );

      // Act
      final copiedPharmacy = originalPharmacy.copyWith(
        name: 'صيدلية الجديدة',
        rating: 4.8,
        hasDelivery: true,
      );

      // Assert
      expect(copiedPharmacy.name, 'صيدلية الجديدة');
      expect(copiedPharmacy.rating, 4.8);
      expect(copiedPharmacy.hasDelivery, true);
      expect(copiedPharmacy.address, originalPharmacy.address); // unchanged
      expect(copiedPharmacy.phone, originalPharmacy.phone); // unchanged
    });
  });
}

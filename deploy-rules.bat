@echo off
echo Deploying Firebase Firestore Rules...
echo.

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Firebase CLI is not installed. Please install it first:
    echo npm install -g firebase-tools
    echo.
    pause
    exit /b 1
)

REM Login to Firebase (if not already logged in)
echo Checking Firebase login status...
firebase projects:list >nul 2>&1
if %errorlevel% neq 0 (
    echo Please login to Firebase first:
    firebase login
    pause
)

REM Deploy Firestore rules
echo Deploying Firestore security rules...
firebase deploy --only firestore:rules

if %errorlevel% equ 0 (
    echo.
    echo ✅ Firebase rules deployed successfully!
    echo.
    echo The new security rules are now active and should fix the permission issues.
    echo You can now try adding comments and likes in the app.
) else (
    echo.
    echo ❌ Failed to deploy Firebase rules.
    echo Please check your Firebase configuration and try again.
)

echo.
pause

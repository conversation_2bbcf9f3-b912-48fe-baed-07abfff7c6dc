// lib/utils/modern_ui_kit.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/utils/animations.dart';
import 'package:yassincil/widgets/modern_widgets.dart';
import 'package:yassincil/utils/responsive_layout.dart';

/// مجموعة شاملة من العناصر العصرية للتطبيق
class ModernUIKit {
  /// إنشاء AppBar عصري مع تدرجات وتأثيرات
  static PreferredSizeWidget modernAppBar({
    required String title,
    List<Color>? gradientColors,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = true,
    double elevation = 0,
  }) {
    final colors =
        gradientColors ?? [const Color(0xFF6366F1), const Color(0xFF8B5CF6)];

    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: colors,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: AppBar(
          title: Text(
            title,
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              color: Colors.white,
            ),
          ),
          backgroundColor: Colors.transparent,
          elevation: elevation,
          centerTitle: centerTitle,
          leading: leading,
          actions: actions,
          iconTheme: const IconThemeData(color: Colors.white),
        ),
      ),
    );
  }

  /// إنشاء بطاقة عصرية مع انيميشن
  static Widget modernCard({
    required Widget child,
    VoidCallback? onTap,
    List<Color>? gradientColors,
    double borderRadius = 16.0,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Duration? animationDelay,
    bool enableGlow = false,
    bool enableGlass = false,
  }) {
    Widget card = InteractiveCard(
      onTap: onTap,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      gradientColors: gradientColors,
      child: child,
    );

    if (enableGlass) {
      card = GlassCard(
        borderRadius: borderRadius,
        margin: margin,
        padding: padding,
        child: child,
      );
    }

    if (enableGlow) {
      card = GlowContainer(borderRadius: borderRadius, child: card);
    }

    if (animationDelay != null) {
      card = AnimatedCard(delay: animationDelay, child: card);
    }

    return card;
  }

  /// إنشاء زر عصري متقدم
  static Widget modernButton({
    required Widget child,
    required VoidCallback? onPressed,
    List<Color>? gradientColors,
    double borderRadius = 12.0,
    EdgeInsetsGeometry? padding,
    bool enableGlow = false,
    bool enableNeumorphism = false,
    double elevation = 4.0,
  }) {
    Widget button = ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: gradientColors?.first ?? const Color(0xFF6366F1),
        foregroundColor: Colors.white,
        elevation: elevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        padding:
            padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
      child: child,
    );

    if (enableGlow) {
      button = GlowContainer(borderRadius: borderRadius, child: button);
    }

    return button;
  }

  /// إنشاء FAB عصري
  static Widget modernFAB({
    required VoidCallback? onPressed,
    required Widget child,
    String? tooltip,
    List<Color>? gradientColors,
    double elevation = 8.0,
    double size = 56.0,
  }) {
    return ModernFloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      gradientColors: gradientColors,
      elevation: elevation,
      size: size,
      child: child,
    );
  }

  /// إنشاء شريط بحث عصري
  static Widget modernSearchBar({
    String hintText = 'البحث...',
    ValueChanged<String>? onChanged,
    VoidCallback? onTap,
    TextEditingController? controller,
    List<Color>? gradientColors,
    double borderRadius = 25.0,
  }) {
    return ModernSearchBar(
      hintText: hintText,
      onChanged: onChanged,
      onTap: onTap,
      controller: controller,
      gradientColors: gradientColors,
      borderRadius: borderRadius,
    );
  }

  /// إنشاء شبكة متجاوبة
  static Widget responsiveGrid({
    required List<Widget> children,
    double? childAspectRatio,
    double mainAxisSpacing = 16.0,
    double crossAxisSpacing = 16.0,
    EdgeInsetsGeometry? padding,
  }) {
    return ResponsiveGrid(
      childAspectRatio: childAspectRatio,
      mainAxisSpacing: mainAxisSpacing,
      crossAxisSpacing: crossAxisSpacing,
      padding: padding,
      children: children,
    );
  }

  /// إنشاء نص متجاوب
  static Widget responsiveText(
    String text, {
    FontSizeType sizeType = FontSizeType.medium,
    FontWeight? fontWeight,
    Color? color,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) {
    return ResponsiveText(
      text,
      sizeType: sizeType,
      fontWeight: fontWeight,
      color: color,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }

  /// إنشاء أيقونة متجاوبة
  static Widget responsiveIcon(
    IconData icon, {
    IconSizeType sizeType = IconSizeType.medium,
    Color? color,
  }) {
    return ResponsiveIcon(icon, sizeType: sizeType, color: color);
  }

  /// إنشاء حاوية بعرض محدود
  static Widget maxWidthContainer({
    required Widget child,
    double? maxWidth,
    EdgeInsetsGeometry? padding,
  }) {
    return MaxWidthContainer(
      maxWidth: maxWidth,
      padding: padding,
      child: child,
    );
  }

  /// إنشاء تدرج لوني عصري
  static Widget gradientContainer({
    required Widget child,
    required List<Color> colors,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    double borderRadius = 16.0,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    List<double>? stops,
  }) {
    return GradientContainer(
      colors: colors,
      begin: begin,
      end: end,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      stops: stops,
      child: child,
    );
  }

  /// إنشاء تأثير ضوئي
  static Widget glowContainer({
    required Widget child,
    Color glowColor = const Color(0xFF6366F1),
    double glowRadius = 20.0,
    double borderRadius = 16.0,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    bool animate = false,
  }) {
    return GlowContainer(
      glowColor: glowColor,
      glowRadius: glowRadius,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      animate: animate,
      child: child,
    );
  }

  /// إنشاء انتقال صفحة عصري
  static PageRouteBuilder<T> modernPageRoute<T>(
    Widget page, {
    PageTransitionType type = PageTransitionType.slideFromRight,
  }) {
    switch (type) {
      case PageTransitionType.slideFromRight:
        return AppAnimations.slideFromRight<T>(page);
      case PageTransitionType.fadeScale:
        return AppAnimations.fadeScale<T>(page);
      case PageTransitionType.slideFromBottom:
        return AppAnimations.slideFromBottom<T>(page);
    }
  }

  /// ألوان التدرجات الجاهزة
  static const List<Color> primaryGradient = [
    Color(0xFF6366F1),
    Color(0xFF8B5CF6),
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFF10B981),
    Color(0xFF34D399),
  ];

  static const List<Color> warningGradient = [
    Color(0xFFF59E0B),
    Color(0xFFFBBF24),
  ];

  static const List<Color> errorGradient = [
    Color(0xFFEF4444),
    Color(0xFFF87171),
  ];

  static const List<Color> successGradient = [
    Color(0xFF10B981),
    Color(0xFF059669),
  ];

  static const List<Color> infoGradient = [
    Color(0xFF3B82F6),
    Color(0xFF1D4ED8),
  ];

  static const List<Color> sunsetGradient = [
    Color(0xFFFF7B7B),
    Color(0xFFFF8E53),
    Color(0xFFFF6B6B),
  ];

  static const List<Color> oceanGradient = [
    Color(0xFF667eea),
    Color(0xFF764ba2),
  ];

  static const List<Color> forestGradient = [
    Color(0xFF134E5E),
    Color(0xFF71B280),
  ];
}

/// أنواع انتقالات الصفحات
enum PageTransitionType { slideFromRight, fadeScale, slideFromBottom }

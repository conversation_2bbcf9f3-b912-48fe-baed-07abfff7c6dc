import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:yassincil/models/review.dart';
import 'package:yassincil/services/firestore_service.dart';
import 'package:yassincil/utils/app_constants.dart';

class ReviewProvider extends ChangeNotifier {
  final FirestoreService _firestoreService;
  
  List<Review> _reviews = [];
  bool _isLoading = false;
  String? _errorMessage;
  ReviewSummary? _currentSummary;

  // Getters
  List<Review> get reviews => _reviews;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  ReviewSummary? get currentSummary => _currentSummary;

  ReviewProvider(this._firestoreService);

  // Fetch reviews for a specific target (restaurant, store, etc.)
  Future<void> fetchReviews(String targetId, String targetType) async {
    _setLoading(true);
    try {
      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.reviewsCollection)
          .where('targetId', isEqualTo: targetId)
          .where('targetType', isEqualTo: targetType)
          .where('isReported', isEqualTo: false)
          .orderBy('createdAt', descending: true)
          .limit(100)
          .get();

      _reviews = querySnapshot.docs
          .map((doc) => Review.fromFirestore(doc))
          .toList();

      _currentSummary = ReviewSummary.fromReviews(_reviews);
      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'فشل في تحميل التقييمات: $e';
      debugPrint('Error fetching reviews: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Add new review
  Future<void> addReview(Review review) async {
    _setLoading(true);
    try {
      final docRef = await _firestoreService.db
          .collection(AppConstants.reviewsCollection)
          .add(review.toMap());

      final newReview = review.copyWith(id: docRef.id);
      _reviews.insert(0, newReview);
      
      // Update summary
      _currentSummary = ReviewSummary.fromReviews(_reviews);
      
      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'فشل في إضافة التقييم: $e';
      debugPrint('Error adding review: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Update existing review
  Future<void> updateReview(Review review) async {
    _setLoading(true);
    try {
      await _firestoreService.db
          .collection(AppConstants.reviewsCollection)
          .doc(review.id)
          .update(review.copyWith(updatedAt: DateTime.now()).toMap());

      final index = _reviews.indexWhere((r) => r.id == review.id);
      if (index != -1) {
        _reviews[index] = review.copyWith(updatedAt: DateTime.now());
        _currentSummary = ReviewSummary.fromReviews(_reviews);
      }

      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'فشل في تحديث التقييم: $e';
      debugPrint('Error updating review: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Delete review
  Future<void> deleteReview(String reviewId) async {
    _setLoading(true);
    try {
      await _firestoreService.db
          .collection(AppConstants.reviewsCollection)
          .doc(reviewId)
          .delete();

      _reviews.removeWhere((review) => review.id == reviewId);
      _currentSummary = ReviewSummary.fromReviews(_reviews);

      _errorMessage = null;
    } catch (e) {
      _errorMessage = 'فشل في حذف التقييم: $e';
      debugPrint('Error deleting review: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Mark review as helpful
  Future<void> markReviewAsHelpful(String reviewId, String userId) async {
    try {
      final reviewIndex = _reviews.indexWhere((r) => r.id == reviewId);
      if (reviewIndex == -1) return;

      final review = _reviews[reviewIndex];
      final isAlreadyHelpful = review.helpfulUsers.contains(userId);

      List<String> updatedHelpfulUsers;
      int updatedHelpfulCount;

      if (isAlreadyHelpful) {
        // Remove from helpful
        updatedHelpfulUsers = review.helpfulUsers.where((id) => id != userId).toList();
        updatedHelpfulCount = review.helpfulCount - 1;
      } else {
        // Add to helpful
        updatedHelpfulUsers = [...review.helpfulUsers, userId];
        updatedHelpfulCount = review.helpfulCount + 1;
      }

      await _firestoreService.db
          .collection(AppConstants.reviewsCollection)
          .doc(reviewId)
          .update({
        'helpfulUsers': updatedHelpfulUsers,
        'helpfulCount': updatedHelpfulCount,
      });

      // Update local state
      _reviews[reviewIndex] = review.copyWith(
        helpfulUsers: updatedHelpfulUsers,
        helpfulCount: updatedHelpfulCount,
      );

      notifyListeners();
    } catch (e) {
      debugPrint('Error marking review as helpful: $e');
    }
  }

  // Report review
  Future<void> reportReview(String reviewId, String reason) async {
    try {
      await _firestoreService.db
          .collection(AppConstants.reviewsCollection)
          .doc(reviewId)
          .update({
        'isReported': true,
        'reportReason': reason,
      });

      // Remove from local list
      _reviews.removeWhere((review) => review.id == reviewId);
      _currentSummary = ReviewSummary.fromReviews(_reviews);

      notifyListeners();
    } catch (e) {
      debugPrint('Error reporting review: $e');
    }
  }

  // Get user's reviews
  Future<List<Review>> getUserReviews(String userId) async {
    try {
      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.reviewsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Review.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error fetching user reviews: $e');
      return [];
    }
  }

  // Check if user has reviewed this target
  Future<Review?> getUserReviewForTarget(String userId, String targetId, String targetType) async {
    try {
      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.reviewsCollection)
          .where('userId', isEqualTo: userId)
          .where('targetId', isEqualTo: targetId)
          .where('targetType', isEqualTo: targetType)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return Review.fromFirestore(querySnapshot.docs.first);
      }
      return null;
    } catch (e) {
      debugPrint('Error checking user review: $e');
      return null;
    }
  }

  // Get reviews by rating
  List<Review> getReviewsByRating(int rating) {
    return _reviews.where((review) => review.rating.round() == rating).toList();
  }

  // Get verified reviews only
  List<Review> getVerifiedReviews() {
    return _reviews.where((review) => review.isVerified).toList();
  }

  // Get reviews with images
  List<Review> getReviewsWithImages() {
    return _reviews.where((review) => review.hasImages).toList();
  }

  // Get recent reviews (last 30 days)
  List<Review> getRecentReviews() {
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    return _reviews.where((review) => review.createdAt.isAfter(thirtyDaysAgo)).toList();
  }

  // Search reviews
  List<Review> searchReviews(String query) {
    if (query.isEmpty) return _reviews;
    
    final lowerQuery = query.toLowerCase();
    return _reviews.where((review) {
      return review.title.toLowerCase().contains(lowerQuery) ||
             review.comment.toLowerCase().contains(lowerQuery) ||
             review.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  // Get reviews by tag
  List<Review> getReviewsByTag(String tag) {
    return _reviews.where((review) => review.tags.contains(tag)).toList();
  }

  // Get top reviews (high rating + helpful)
  List<Review> getTopReviews({int limit = 5}) {
    final sortedReviews = List<Review>.from(_reviews);
    sortedReviews.sort((a, b) {
      // Sort by rating first, then by helpful count
      final ratingComparison = b.rating.compareTo(a.rating);
      if (ratingComparison != 0) return ratingComparison;
      return b.helpfulCount.compareTo(a.helpfulCount);
    });
    
    return sortedReviews.take(limit).toList();
  }

  // Get review statistics for admin
  Map<String, dynamic> getReviewStatistics() {
    if (_reviews.isEmpty) {
      return {
        'totalReviews': 0,
        'averageRating': 0.0,
        'verifiedPercentage': 0.0,
        'recentReviewsCount': 0,
        'topTags': <String>[],
      };
    }

    final recentCount = getRecentReviews().length;
    final verifiedCount = getVerifiedReviews().length;
    final verifiedPercentage = (verifiedCount / _reviews.length) * 100;

    // Get top tags
    final Map<String, int> tagCount = {};
    for (final review in _reviews) {
      for (final tag in review.tags) {
        tagCount[tag] = (tagCount[tag] ?? 0) + 1;
      }
    }
    final topTags = tagCount.entries
        .toList()
        ..sort((a, b) => b.value.compareTo(a.value));

    return {
      'totalReviews': _reviews.length,
      'averageRating': _currentSummary?.averageRating ?? 0.0,
      'verifiedPercentage': verifiedPercentage,
      'recentReviewsCount': recentCount,
      'topTags': topTags.take(10).map((e) => e.key).toList(),
    };
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearReviews() {
    _reviews.clear();
    _currentSummary = null;
    notifyListeners();
  }
}

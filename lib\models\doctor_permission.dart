import 'package:cloud_firestore/cloud_firestore.dart';

enum DoctorPermissionType {
  view,
  add,
  edit,
  delete,
  approve,
  feature,
  verify,
  manageComments,
  manageAppointments,
  viewStatistics,
  exportData,
  bulkActions,
}

class DoctorPermission {
  final String? id;
  final String userId;
  final String userEmail;
  final List<DoctorPermissionType> permissions;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? createdBy;
  final String? notes;

  DoctorPermission({
    this.id,
    required this.userId,
    required this.userEmail,
    required this.permissions,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.notes,
  });

  factory DoctorPermission.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DoctorPermission(
      id: doc.id,
      userId: data['userId'] ?? '',
      userEmail: data['userEmail'] ?? '',
      permissions: (data['permissions'] as List<dynamic>?)
              ?.map((p) => DoctorPermissionType.values
                  .firstWhere((type) => type.name == p))
              .toList() ??
          [],
      isActive: data['isActive'] ?? true,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
      createdBy: data['createdBy'],
      notes: data['notes'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userEmail': userEmail,
      'permissions': permissions.map((p) => p.name).toList(),
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'createdBy': createdBy,
      'notes': notes,
    };
  }

  DoctorPermission copyWith({
    String? id,
    String? userId,
    String? userEmail,
    List<DoctorPermissionType>? permissions,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? notes,
  }) {
    return DoctorPermission(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userEmail: userEmail ?? this.userEmail,
      permissions: permissions ?? this.permissions,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      notes: notes ?? this.notes,
    );
  }

  bool hasPermission(DoctorPermissionType permission) {
    return isActive && permissions.contains(permission);
  }

  bool hasAnyPermission(List<DoctorPermissionType> permissionList) {
    return isActive && permissionList.any((p) => permissions.contains(p));
  }

  bool hasAllPermissions(List<DoctorPermissionType> permissionList) {
    return isActive && permissionList.every((p) => permissions.contains(p));
  }

  static List<DoctorPermissionType> getAdminPermissions() {
    return DoctorPermissionType.values;
  }

  static List<DoctorPermissionType> getModeratorPermissions() {
    return [
      DoctorPermissionType.view,
      DoctorPermissionType.add,
      DoctorPermissionType.edit,
      DoctorPermissionType.manageComments,
      DoctorPermissionType.manageAppointments,
      DoctorPermissionType.viewStatistics,
    ];
  }

  static List<DoctorPermissionType> getEditorPermissions() {
    return [
      DoctorPermissionType.view,
      DoctorPermissionType.add,
      DoctorPermissionType.edit,
    ];
  }

  static List<DoctorPermissionType> getViewerPermissions() {
    return [
      DoctorPermissionType.view,
      DoctorPermissionType.viewStatistics,
    ];
  }

  String getPermissionDisplayName(DoctorPermissionType permission) {
    switch (permission) {
      case DoctorPermissionType.view:
        return 'عرض الأطباء';
      case DoctorPermissionType.add:
        return 'إضافة أطباء';
      case DoctorPermissionType.edit:
        return 'تعديل الأطباء';
      case DoctorPermissionType.delete:
        return 'حذف الأطباء';
      case DoctorPermissionType.approve:
        return 'الموافقة على الأطباء';
      case DoctorPermissionType.feature:
        return 'تمييز الأطباء';
      case DoctorPermissionType.verify:
        return 'التحقق من الأطباء';
      case DoctorPermissionType.manageComments:
        return 'إدارة التعليقات';
      case DoctorPermissionType.manageAppointments:
        return 'إدارة المواعيد';
      case DoctorPermissionType.viewStatistics:
        return 'عرض الإحصائيات';
      case DoctorPermissionType.exportData:
        return 'تصدير البيانات';
      case DoctorPermissionType.bulkActions:
        return 'العمليات المجمعة';
    }
  }
}

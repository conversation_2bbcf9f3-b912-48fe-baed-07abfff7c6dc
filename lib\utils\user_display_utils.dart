/// مساعدات عرض معلومات المستخدم
/// يحتوي على دوال مساعدة لعرض أسماء المستخدمين والأحرف الأولى
/// مع الحفاظ على خصوصية المستخدم
library;

class UserDisplayUtils {
  /// الحصول على الاسم الكامل للعرض من UserProfile
  static String getDisplayName({
    String? firstName,
    String? lastName,
    String? username,
  }) {
    if (firstName != null && lastName != null && 
        firstName.isNotEmpty && lastName.isNotEmpty) {
      return '$firstName $lastName';
    } else if (firstName != null && firstName.isNotEmpty) {
      return firstName;
    } else if (lastName != null && lastName.isNotEmpty) {
      return lastName;
    } else if (username != null && username.isNotEmpty) {
      return username;
    } else {
      return 'مستخدم';
    }
  }

  /// الحصول على الأحرف الأولى للاسم للأفاتار
  static String getInitials({
    String? firstName,
    String? lastName,
    String? username,
  }) {
    if (firstName != null && lastName != null && 
        firstName.isNotEmpty && lastName.isNotEmpty) {
      return '${firstName[0]}${lastName[0]}';
    } else if (firstName != null && firstName.isNotEmpty) {
      return firstName[0];
    } else if (lastName != null && lastName.isNotEmpty) {
      return lastName[0];
    } else if (username != null && username.isNotEmpty) {
      return username[0];
    } else {
      return 'م';
    }
  }

  /// الحصول على الأحرف الأولى من اسم مستخدم (للتوافق مع النظام القديم)
  static String getUserInitials(String username) {
    if (username.isEmpty) return 'م';
    
    final nameParts = username.split(' ');
    if (nameParts.length > 1) {
      // إذا كان الاسم يحتوي على مسافة، نأخذ الحرف الأول من كل جزء
      return '${nameParts[0][0]}${nameParts[1][0]}';
    } else {
      // إذا كان الاسم مفرداً، نأخذ الحرف الأول فقط
      return username[0];
    }
  }

  /// الحصول على اسم المستخدم للعرض مع @ 
  static String getUsernameDisplay(String username) {
    return '@$username';
  }

  /// التحقق من وجود صورة ملف شخصي صالحة
  static bool hasValidProfileImage(String? imageUrl) {
    return imageUrl != null && imageUrl.isNotEmpty;
  }
}

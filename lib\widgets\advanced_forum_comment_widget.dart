// lib/widgets/advanced_forum_comment_widget.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../models/comment.dart';
import '../providers/forum_provider.dart';
import '../providers/auth_provider.dart' as app_auth;
import '../utils/app_colors.dart';
import '../utils/error_handler.dart';
import 'report_dialog.dart';

class AdvancedForumCommentWidget extends StatefulWidget {
  final Comment comment;
  final String postId;
  final VoidCallback? onReply;
  final bool showReplies;

  const AdvancedForumCommentWidget({
    super.key,
    required this.comment,
    required this.postId,
    this.onReply,
    this.showReplies = true,
  });

  @override
  State<AdvancedForumCommentWidget> createState() =>
      _AdvancedForumCommentWidgetState();
}

class _AdvancedForumCommentWidgetState
    extends State<AdvancedForumCommentWidget> {
  bool _isLiked = false;
  bool _showReplies = false;
  bool _isEditing = false;
  final TextEditingController _editController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _editController.text = widget.comment.content;
    // فحص ما إذا كان المستخدم معجب بالتعليق (مؤقتاً: افتراضي false)
  }

  @override
  void dispose() {
    _editController.dispose();
    super.dispose();
  }

  Future<void> _toggleLike() async {
    // تطبيق نظام الإعجابات للتعليقات (مؤقتاً: تحديث محلي)
    setState(() {
      _isLiked = !_isLiked;
    });
  }

  Future<void> _saveEdit() async {
    if (_editController.text.trim().isEmpty) return;

    try {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);

      await forumProvider.updateComment(
        postId: widget.postId,
        commentId: widget.comment.id!,
        newContent: _editController.text.trim(),
      );

      setState(() {
        _isEditing = false;
      });

      if (mounted) {
        ErrorHandler.showSuccessSnackBar(context, 'تم تعديل التعليق بنجاح');
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(context, e);
      }
    }
  }

  Future<void> _deleteComment(bool isAdmin) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف التعليق', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف هذا التعليق؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'حذف',
              style: GoogleFonts.cairo(color: AppColors.error),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        final forumProvider = Provider.of<ForumProvider>(
          context,
          listen: false,
        );

        await forumProvider.deleteComment(
          postId: widget.postId,
          commentId: widget.comment.id!,
        );

        if (mounted) {
          ErrorHandler.showSuccessSnackBar(context, 'تم حذف التعليق بنجاح');
        }
      } catch (e) {
        if (mounted) {
          ErrorHandler.showErrorSnackBar(context, e);
        }
      }
    }
  }

  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => ReportDialog(
        contentId: widget.comment.id!,
        contentType: 'comment',
        postId: widget.postId,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<app_auth.AuthProvider>(context);
    final currentUser = authProvider.currentUser;
    final isOwner = currentUser?.uid == widget.comment.userId;
    final isAdmin = authProvider.isAdmin;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس التعليق
              _buildCommentHeader(isOwner, isAdmin),

              const SizedBox(height: 8),

              // محتوى التعليق
              if (_isEditing) _buildEditingWidget() else _buildCommentContent(),

              // صور التعليق
              if (widget.comment.imageUrls.isNotEmpty) _buildCommentImages(),

              const SizedBox(height: 8),

              // أزرار التفاعل
              _buildActionButtons(isOwner, isAdmin),

              // الردود
              if (widget.showReplies && _showReplies) _buildRepliesSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCommentHeader(bool isOwner, bool isAdmin) {
    return Row(
      children: [
        // صورة المستخدم
        CircleAvatar(
          radius: 16,
          backgroundColor: AppColors.primary.withValues(alpha: 0.1),
          backgroundImage: widget.comment.userAvatar != null
              ? NetworkImage(widget.comment.userAvatar!)
              : null,
          child: widget.comment.userAvatar == null
              ? Icon(Icons.person, size: 16, color: AppColors.primary)
              : null,
        ),
        const SizedBox(width: 8),

        // اسم المستخدم والوقت
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    widget.comment.username,
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  if (widget.comment.isEdited) ...[
                    const SizedBox(width: 4),
                    Text(
                      '(محرر)',
                      style: GoogleFonts.cairo(
                        fontSize: 10,
                        color: AppColors.textSecondary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
              Text(
                _formatTime(widget.comment.createdAt),
                style: GoogleFonts.cairo(
                  fontSize: 10,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),

        // قائمة الخيارات
        PopupMenuButton<String>(
          icon: Icon(Icons.more_vert, size: 16, color: AppColors.textSecondary),
          onSelected: (value) {
            if (value == 'edit' && isOwner) {
              setState(() {
                _isEditing = true;
              });
            } else if (value == 'delete') {
              _deleteComment(isAdmin);
            } else if (value == 'report') {
              _showReportDialog();
            }
          },
          itemBuilder: (context) => [
            if (isOwner)
              PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 16, color: AppColors.primary),
                    const SizedBox(width: 8),
                    Text('تعديل', style: GoogleFonts.cairo()),
                  ],
                ),
              ),
            PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 16, color: AppColors.error),
                  const SizedBox(width: 8),
                  Text('حذف', style: GoogleFonts.cairo()),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCommentContent() {
    return Text(
      widget.comment.content,
      style: GoogleFonts.cairo(fontSize: 14, color: AppColors.textPrimary),
    );
  }

  Widget _buildEditingWidget() {
    return Column(
      children: [
        TextField(
          controller: _editController,
          maxLines: null,
          style: GoogleFonts.cairo(),
          decoration: InputDecoration(
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.primary),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () {
                setState(() {
                  _isEditing = false;
                  _editController.text = widget.comment.content;
                });
              },
              child: Text('إلغاء', style: GoogleFonts.cairo()),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _saveEdit,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
              ),
              child: Text(
                'حفظ',
                style: GoogleFonts.cairo(color: AppColors.textOnPrimary),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCommentImages() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.comment.imageUrls.length,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.only(left: 8),
            width: 100,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.border),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: CachedNetworkImage(
                imageUrl: widget.comment.imageUrls[index],
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppColors.background,
                  child: Center(child: CircularProgressIndicator()),
                ),
                errorWidget: (context, url, error) => Container(
                  color: AppColors.background,
                  child: Icon(
                    Icons.broken_image,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildActionButtons(bool isOwner, bool isAdmin) {
    return Row(
      children: [
        // زر الإعجاب
        InkWell(
          onTap: _toggleLike,
          child: Row(
            children: [
              Icon(
                _isLiked ? Icons.favorite : Icons.favorite_border,
                size: 18,
                color: _isLiked ? AppColors.error : AppColors.textSecondary,
              ),
              const SizedBox(width: 4),
              Text(
                '${widget.comment.likesCount}',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: _isLiked ? AppColors.error : AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(width: 16),

        // زر الرد
        if (widget.comment.parentCommentId == null)
          InkWell(
            onTap: widget.onReply,
            child: Row(
              children: [
                Icon(Icons.reply, size: 18, color: AppColors.textSecondary),
                const SizedBox(width: 4),
                Text(
                  'رد',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

        const Spacer(),

        // زر عرض الردود
        if (widget.comment.parentCommentId == null &&
            widget.comment.repliesCount > 0)
          InkWell(
            onTap: () {
              setState(() {
                _showReplies = !_showReplies;
              });
            },
            child: Row(
              children: [
                Icon(
                  _showReplies ? Icons.expand_less : Icons.expand_more,
                  size: 18,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 4),
                Text(
                  '${widget.comment.repliesCount} ${widget.comment.repliesCount == 1 ? 'رد' : 'ردود'}',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildRepliesSection() {
    // سيتم تنفيذ نظام الردود في تحديث لاحق
    // حالياً نعرض فقط إشارة لوجود ردود
    if (widget.comment.parentCommentId != null) {
      // هذا رد، لا نعرض ردود إضافية
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: TextButton.icon(
        onPressed: () {
          // فتح نافذة الردود (مؤقتاً: رسالة تأكيد)
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'ميزة الردود ستكون متاحة قريباً',
                style: GoogleFonts.cairo(),
              ),
            ),
          );
        },
        icon: const Icon(Icons.reply, size: 16),
        label: Text('رد', style: GoogleFonts.cairo(fontSize: 12)),
        style: TextButton.styleFrom(
          foregroundColor: Colors.grey.shade600,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        ),
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }
}

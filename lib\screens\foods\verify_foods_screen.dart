import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/providers/food_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/models/food_item.dart';
import 'package:yassincil/screens/foods/food_detail_screen.dart';
import 'package:yassincil/utils/app_colors.dart';

class VerifyFoodsScreen extends StatefulWidget {
  const VerifyFoodsScreen({super.key});

  @override
  State<VerifyFoodsScreen> createState() =>
      _VerifyFoodsScreenState();
}

class _VerifyFoodsScreenState extends State<VerifyFoodsScreen> {
  @override
  Widget build(BuildContext context) {
    final isAdmin = Provider.of<AuthProvider>(context).isAdmin;
    if (!isAdmin) {
      return Scaffold(
        appBar: AppBar(title: Text('التحقق من الأطعمة')),
        body: Center(
          child: Text(
            'ليست لديك صلاحية للوصول إلى هذه الصفحة',
            style: GoogleFonts.cairo(fontSize: 16),
          ),
        ),
      );
    }

    final foodProvider = Provider.of<FoodProvider>(context);
    final unapprovedFoods = foodProvider.foodItems
        .where((f) => !f.isApproved)
        .toList();

    return Scaffold(
      appBar: AppBar(title: Text('التحقق من الأطعمة')),
      body: unapprovedFoods.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    size: 80,
                    color: Colors.green,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد أطعمة تحتاج إلى تحقق حالياً',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(8.0),
              itemCount: unapprovedFoods.length,
              itemBuilder: (context, index) {
                final food = unapprovedFoods[index];
                return Card(
                  margin: const EdgeInsets.symmetric(
                    vertical: 8.0,
                    horizontal: 4.0,
                  ),
                  elevation: 3,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.all(12.0),
                    leading: CircleAvatar(
                      radius: 30,
                      backgroundImage: food.imageUrl != null
                          ? NetworkImage(food.imageUrl!)
                          : null,
                      child: food.imageUrl == null
                          ? const Icon(Icons.restaurant_menu, size: 30)
                          : null,
                    ),
                    title: Text(
                      food.name,
                      style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Text(
                      food.category,
                      style: GoogleFonts.cairo(),
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => FoodDetailScreen(
                            foodItem: food,
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
    );
  }
}

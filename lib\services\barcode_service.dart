import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:yassincil/services/firestore_service.dart';
import 'package:yassincil/models/product.dart';
import 'package:yassincil/utils/app_constants.dart';

class BarcodeService {
  static final FirestoreService _firestoreService = FirestoreService();

  /// مسح الباركود باستخدام mobile_scanner
  static Future<String?> scanBarcode(BuildContext context) async {
    try {
      final result = await Navigator.of(context).push<String>(
        MaterialPageRoute(builder: (context) => const _BarcodeScannerScreen()),
      );

      if (result == null) {
        // المستخدم ألغى المسح
        return null;
      }

      debugPrint('تم مسح الباركود: $result');
      return result;
    } catch (e) {
      debugPrint('خطأ في مسح الباركود: $e');
      throw Exception('فشل في مسح الباركود: $e');
    }
  }

  /// البحث عن منتج بالباركود في قاعدة البيانات المحلية
  static Future<Product?> findProductByBarcode(String barcode) async {
    try {
      final snapshot = await _firestoreService.getCollection(
        AppConstants.productsCollection,
        whereField: 'barcode',
        isEqualTo: barcode,
      );

      if (snapshot.docs.isNotEmpty) {
        return Product.fromFirestore(snapshot.docs.first);
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في البحث عن المنتج: $e');
      throw Exception('فشل في البحث عن المنتج: $e');
    }
  }

  /// إضافة منتج جديد إلى قاعدة البيانات
  static Future<void> addProduct(Product product) async {
    try {
      await _firestoreService.addDocument(
        AppConstants.productsCollection,
        product.toMap(),
      );
      debugPrint('تم إضافة المنتج: ${product.name}');
    } catch (e) {
      debugPrint('خطأ في إضافة المنتج: $e');
      throw Exception('فشل في إضافة المنتج: $e');
    }
  }

  /// تحديث معلومات منتج
  static Future<void> updateProduct(Product product) async {
    try {
      if (product.id == null) {
        throw Exception('معرف المنتج مطلوب للتحديث');
      }

      await _firestoreService.updateDocument(
        AppConstants.productsCollection,
        product.id!,
        product.toMap(),
      );
      debugPrint('تم تحديث المنتج: ${product.name}');
    } catch (e) {
      debugPrint('خطأ في تحديث المنتج: $e');
      throw Exception('فشل في تحديث المنتج: $e');
    }
  }

  /// حذف منتج
  static Future<void> deleteProduct(String productId) async {
    try {
      await _firestoreService.deleteDocument(
        AppConstants.productsCollection,
        productId,
      );
      debugPrint('تم حذف المنتج: $productId');
    } catch (e) {
      debugPrint('خطأ في حذف المنتج: $e');
      throw Exception('فشل في حذف المنتج: $e');
    }
  }

  /// البحث عن منتج في قواعد البيانات الخارجية (مثل Open Food Facts)
  static Future<Product?> searchProductOnline(String barcode) async {
    try {
      // يمكن هنا استخدام API خارجي مثل Open Food Facts
      // للبحث عن معلومات المنتج
      debugPrint('البحث عن المنتج أونلاين: $barcode');

      // مثال على استخدام Open Food Facts API
      // final response = await http.get(
      //   Uri.parse('https://world.openfoodfacts.org/api/v0/product/$barcode.json'),
      // );

      // if (response.statusCode == 200) {
      //   final data = json.decode(response.body);
      //   if (data['status'] == 1) {
      //     return Product.fromOpenFoodFacts(data['product']);
      //   }
      // }

      return null;
    } catch (e) {
      debugPrint('خطأ في البحث الأونلاين: $e');
      return null;
    }
  }

  /// تحليل مكونات المنتج للتحقق من وجود الغلوتين
  static bool containsGluten(List<String> ingredients) {
    final glutenKeywords = [
      'قمح',
      'شعير',
      'شوفان',
      'جاودار',
      'حنطة',
      'wheat',
      'barley',
      'oats',
      'rye',
      'spelt',
      'gluten',
      'غلوتين',
      'دقيق القمح',
      'نشا القمح',
      'مالت',
      'malt',
      'خميرة البيرة',
      'brewer\'s yeast',
    ];

    for (String ingredient in ingredients) {
      for (String keyword in glutenKeywords) {
        if (ingredient.toLowerCase().contains(keyword.toLowerCase())) {
          return true;
        }
      }
    }

    return false;
  }

  /// تقييم مدى أمان المنتج لمرضى السيلياك
  static ProductSafety evaluateProductSafety(Product product) {
    if (product.isGlutenFree == true) {
      return ProductSafety.safe;
    }

    if (product.ingredients.isNotEmpty) {
      if (containsGluten(product.ingredients)) {
        return ProductSafety.unsafe;
      }
    }

    // إذا لم تكن هناك معلومات كافية
    return ProductSafety.unknown;
  }

  /// الحصول على جميع المنتجات
  static Future<List<Product>> getAllProducts() async {
    try {
      final snapshot = await _firestoreService.getCollection(
        AppConstants.productsCollection,
        orderBy: 'name',
      );

      return snapshot.docs.map((doc) => Product.fromFirestore(doc)).toList();
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات: $e');
      throw Exception('فشل في جلب المنتجات: $e');
    }
  }

  /// البحث في المنتجات بالاسم
  static Future<List<Product>> searchProductsByName(String query) async {
    try {
      final snapshot = await _firestoreService.getCollection(
        AppConstants.productsCollection,
        orderBy: 'name',
      );

      final allProducts = snapshot.docs
          .map((doc) => Product.fromFirestore(doc))
          .toList();

      return allProducts
          .where(
            (product) =>
                product.name.toLowerCase().contains(query.toLowerCase()) ||
                product.brand.toLowerCase().contains(query.toLowerCase()),
          )
          .toList();
    } catch (e) {
      debugPrint('خطأ في البحث في المنتجات: $e');
      throw Exception('فشل في البحث في المنتجات: $e');
    }
  }

  /// فلترة المنتجات الآمنة فقط
  static List<Product> filterSafeProducts(List<Product> products) {
    return products
        .where(
          (product) => evaluateProductSafety(product) == ProductSafety.safe,
        )
        .toList();
  }

  /// فلترة المنتجات غير الآمنة
  static List<Product> filterUnsafeProducts(List<Product> products) {
    return products
        .where(
          (product) => evaluateProductSafety(product) == ProductSafety.unsafe,
        )
        .toList();
  }

  /// فلترة المنتجات غير المعروفة
  static List<Product> filterUnknownProducts(List<Product> products) {
    return products
        .where(
          (product) => evaluateProductSafety(product) == ProductSafety.unknown,
        )
        .toList();
  }
}

/// تقييم أمان المنتج
enum ProductSafety {
  safe, // آمن
  unsafe, // غير آمن
  unknown, // غير معروف
}

// شاشة مسح الباركود
class _BarcodeScannerScreen extends StatefulWidget {
  const _BarcodeScannerScreen();

  @override
  State<_BarcodeScannerScreen> createState() => _BarcodeScannerScreenState();
}

class _BarcodeScannerScreenState extends State<_BarcodeScannerScreen> {
  MobileScannerController cameraController = MobileScannerController();
  bool _isFlashOn = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مسح الباركود'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            color: Colors.white,
            icon: Icon(
              _isFlashOn ? Icons.flash_on : Icons.flash_off,
              color: _isFlashOn ? Colors.yellow : Colors.grey,
            ),
            iconSize: 32.0,
            onPressed: () {
              setState(() {
                _isFlashOn = !_isFlashOn;
              });
              cameraController.toggleTorch();
            },
          ),
          IconButton(
            color: Colors.white,
            icon: const Icon(Icons.flip_camera_ios),
            iconSize: 32.0,
            onPressed: () => cameraController.switchCamera(),
          ),
        ],
      ),
      body: MobileScanner(
        controller: cameraController,
        onDetect: (capture) {
          final List<Barcode> barcodes = capture.barcodes;
          for (final barcode in barcodes) {
            if (barcode.rawValue != null) {
              Navigator.of(context).pop(barcode.rawValue);
              return;
            }
          }
        },
      ),
    );
  }

  @override
  void dispose() {
    cameraController.dispose();
    super.dispose();
  }
}

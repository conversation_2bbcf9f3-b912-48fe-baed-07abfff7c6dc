import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../providers/article_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/article.dart';
import '../../utils/app_colors.dart';
import '../../utils/error_handler.dart';
import '../../widgets/loading_widget.dart' hide EmptyStateWidget;
import '../../widgets/empty_state_widget.dart';
import '../articles/add_edit_article_screen.dart';
import '../articles/article_detail_screen.dart';

class ArticlesManagementScreen extends StatefulWidget {
  const ArticlesManagementScreen({super.key});

  @override
  State<ArticlesManagementScreen> createState() =>
      _ArticlesManagementScreenState();
}

class _ArticlesManagementScreenState extends State<ArticlesManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  String _selectedFilter = 'الكل';
  String _selectedCategory = 'الكل';
  bool _isLoading = false;

  final List<String> _filters = [
    'الكل',
    'الأحدث',
    'الأقدم',
    'الأعلى تقييماً',
    'الأقل تقييماً',
    'الأكثر مشاهدة',
    'الأكثر تفاعلاً',
    'الأكثر مشاركة',
  ];

  final List<String> _categoryFilters = [
    'الكل',
    'التغذية',
    'الصحة العامة',
    'الوصفات',
    'نصائح طبية',
    'أبحاث علمية',
    'تجارب شخصية',
    'أخبار طبية',
    'دليل المريض',
    'الحياة اليومية',
    'التمارين الرياضية',
    'الصحة النفسية',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadArticles();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadArticles() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final articleProvider = Provider.of<ArticleProvider>(
        context,
        listen: false,
      );
      await articleProvider.fetchArticles();
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(context, e);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildTabBar(),
          Expanded(
            child: _isLoading
                ? const LoadingWidget(message: 'جاري تحميل المقالات...')
                : _buildTabBarView(),
          ),
        ],
      ),
      floatingActionButton: _buildAddArticleFAB(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'إدارة المقالات',
        style: GoogleFonts.cairo(fontWeight: FontWeight.bold, fontSize: 20),
      ),
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadArticles,
          tooltip: 'تحديث',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'statistics':
                _showStatistics();
                break;
              case 'export':
                _exportArticles();
                break;
              case 'bulk_actions':
                _showBulkActionsDialog();
                break;
              case 'categories':
                _manageCategoriesDialog();
                break;
              case 'contributions':
                _showContributionsScreen();
                break;
              case 'analytics':
                _showAnalyticsScreen();
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'statistics',
              child: Row(
                children: [
                  const Icon(Icons.analytics, size: 20),
                  const SizedBox(width: 8),
                  Text('الإحصائيات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'contributions',
              child: Row(
                children: [
                  const Icon(Icons.people, size: 20),
                  const SizedBox(width: 8),
                  Text('مساهمات المستخدمين', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'analytics',
              child: Row(
                children: [
                  const Icon(Icons.insights, size: 20),
                  const SizedBox(width: 8),
                  Text('تحليلات المحتوى', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  const Icon(Icons.download, size: 20),
                  const SizedBox(width: 8),
                  Text('تصدير البيانات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'bulk_actions',
              child: Row(
                children: [
                  const Icon(Icons.checklist, size: 20),
                  const SizedBox(width: 8),
                  Text('إجراءات جماعية', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'categories',
              child: Row(
                children: [
                  const Icon(Icons.category, size: 20),
                  const SizedBox(width: 8),
                  Text('إدارة الفئات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          Container(
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث في المقالات...',
                hintStyle: GoogleFonts.cairo(color: Colors.grey.shade600),
                prefixIcon: Icon(Icons.search, color: Colors.grey.shade600),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(Icons.clear, color: Colors.grey.shade600),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {});
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              style: GoogleFonts.cairo(),
              onChanged: (value) {
                setState(() {});
              },
            ),
          ),
          const SizedBox(height: 12),
          // فلاتر
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: _selectedFilter,
                      isExpanded: true,
                      icon: Icon(
                        Icons.arrow_drop_down,
                        color: AppColors.primary,
                      ),
                      style: GoogleFonts.cairo(color: AppColors.textPrimary),
                      onChanged: (value) {
                        setState(() {
                          _selectedFilter = value!;
                        });
                      },
                      items: _filters.map((filter) {
                        return DropdownMenuItem(
                          value: filter,
                          child: Text(filter),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: _selectedCategory,
                      isExpanded: true,
                      icon: Icon(
                        Icons.arrow_drop_down,
                        color: AppColors.primary,
                      ),
                      style: GoogleFonts.cairo(color: AppColors.textPrimary),
                      onChanged: (value) {
                        setState(() {
                          _selectedCategory = value!;
                        });
                      },
                      items: _categoryFilters.map((category) {
                        return DropdownMenuItem(
                          value: category,
                          child: Text(category),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: Colors.grey.shade600,
        labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        unselectedLabelStyle: GoogleFonts.cairo(fontWeight: FontWeight.normal),
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        isScrollable: true,
        tabs: const [
          Tab(text: 'جميع المقالات'),
          Tab(text: 'في انتظار المراجعة'),
          Tab(text: 'المميزة'),
          Tab(text: 'المثبتة'),
          Tab(text: 'المرفوضة'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildArticlesList('all'),
        _buildArticlesList('pending'),
        _buildArticlesList('featured'),
        _buildArticlesList('pinned'),
        _buildArticlesList('rejected'),
      ],
    );
  }

  Widget _buildArticlesList(String type) {
    return Consumer<ArticleProvider>(
      builder: (context, articleProvider, child) {
        final articles = _getFilteredArticles(articleProvider.articles, type);

        if (articles.isEmpty) {
          return EmptyStateWidget(
            icon: Icons.article,
            title: 'لا توجد مقالات',
            subtitle: _getEmptyMessage(type),
            action: ElevatedButton.icon(
              onPressed: _loadArticles,
              icon: const Icon(Icons.refresh),
              label: Text('إعادة التحميل', style: GoogleFonts.cairo()),
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _loadArticles,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: articles.length,
            itemBuilder: (context, index) {
              final article = articles[index];
              return _buildArticleManagementCard(article);
            },
          ),
        );
      },
    );
  }

  List<Article> _getFilteredArticles(List<Article> articles, String type) {
    var filtered = articles.where((article) {
      // فلترة حسب البحث
      if (_searchController.text.isNotEmpty) {
        final query = _searchController.text.toLowerCase();
        if (!article.title.toLowerCase().contains(query) &&
            !article.content.toLowerCase().contains(query) &&
            !article.author.toLowerCase().contains(query) &&
            !article.tags.any((tag) => tag.toLowerCase().contains(query))) {
          return false;
        }
      }

      // فلترة حسب الفئة
      if (_selectedCategory != 'الكل') {
        if (article.category != _selectedCategory) {
          return false;
        }
      }

      // فلترة حسب النوع
      switch (type) {
        case 'pending':
          return !article.isApproved;
        case 'featured':
          return article.isFeatured;
        case 'pinned':
          return article.isPinned;
        case 'rejected':
          return !article.isApproved; // المقالات غير المعتمدة (مؤقتاً)
        default:
          return article.isApproved;
      }
    }).toList();

    // ترتيب حسب الفلتر المختار
    switch (_selectedFilter) {
      case 'الأحدث':
        filtered.sort((a, b) => b.publishDate.compareTo(a.publishDate));
        break;
      case 'الأقدم':
        filtered.sort((a, b) => a.publishDate.compareTo(b.publishDate));
        break;
      case 'الأعلى تقييماً':
        filtered.sort((a, b) => b.averageRating.compareTo(a.averageRating));
        break;
      case 'الأقل تقييماً':
        filtered.sort((a, b) => a.averageRating.compareTo(b.averageRating));
        break;
      case 'الأكثر مشاهدة':
        filtered.sort((a, b) => b.viewsCount.compareTo(a.viewsCount));
        break;
      case 'الأكثر تفاعلاً':
        filtered.sort(
          (a, b) => (b.likesCount + b.commentsCount).compareTo(
            a.likesCount + a.commentsCount,
          ),
        );
        break;
      case 'الأكثر مشاركة':
        filtered.sort((a, b) => b.sharesCount.compareTo(a.sharesCount));
        break;
    }

    return filtered;
  }

  String _getEmptyMessage(String type) {
    switch (type) {
      case 'pending':
        return 'لا توجد مقالات في انتظار المراجعة';
      case 'featured':
        return 'لا توجد مقالات مميزة';
      case 'pinned':
        return 'لا توجد مقالات مثبتة';
      case 'rejected':
        return 'لا توجد مقالات مرفوضة';
      default:
        return 'لم يتم العثور على مقالات تطابق البحث';
    }
  }

  Widget _buildArticleManagementCard(Article article) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المقال مع شارة الحالة
          Stack(
            children: [
              _buildArticleImage(article),
              Positioned(top: 12, right: 12, child: _buildStatusBadge(article)),
              if (article.isFeatured)
                Positioned(
                  top: 12,
                  left: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.star, size: 16, color: Colors.white),
                        const SizedBox(width: 4),
                        Text(
                          'مميز',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              if (article.isPinned)
                Positioned(
                  bottom: 12,
                  left: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.push_pin,
                          size: 16,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'مثبت',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
          // معلومات المقال
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان المقال والتقييم
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        article.title,
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    _buildRatingWidget(article.averageRating),
                  ],
                ),
                const SizedBox(height: 8),
                // المؤلف والتاريخ
                Row(
                  children: [
                    CircleAvatar(
                      radius: 12,
                      backgroundImage: article.authorAvatar != null
                          ? NetworkImage(article.authorAvatar!)
                          : null,
                      child: article.authorAvatar == null
                          ? Text(
                              article.author.isNotEmpty
                                  ? article.author[0].toUpperCase()
                                  : 'م',
                              style: GoogleFonts.cairo(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        article.author,
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                    Text(
                      _formatDate(article.publishDate),
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // الملخص أو بداية المحتوى
                Text(
                  article.summary ?? article.content,
                  style: GoogleFonts.cairo(fontSize: 14),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),
                // إحصائيات سريعة
                Row(
                  children: [
                    _buildStatItem(
                      Icons.favorite,
                      '${article.likesCount}',
                      'إعجاب',
                    ),
                    const SizedBox(width: 16),
                    _buildStatItem(
                      Icons.comment,
                      '${article.commentsCount}',
                      'تعليق',
                    ),
                    const SizedBox(width: 16),
                    _buildStatItem(
                      Icons.visibility,
                      '${article.viewsCount}',
                      'مشاهدة',
                    ),
                    const Spacer(),
                    _buildCategoryChip(article.category),
                  ],
                ),
                const SizedBox(height: 12),
                // أزرار الإدارة
                Row(
                  children: [
                    Expanded(
                      child: _buildActionButton(
                        icon: Icons.visibility,
                        label: 'عرض',
                        color: Colors.blue,
                        onTap: () => _viewArticle(article),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildActionButton(
                        icon: Icons.edit,
                        label: 'تعديل',
                        color: Colors.orange,
                        onTap: () => _editArticle(article),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildActionButton(
                        icon: Icons.delete,
                        label: 'حذف',
                        color: Colors.red,
                        onTap: () => _deleteArticle(article),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // الدوال المساعدة
  Widget _buildArticleImage(Article article) {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        color: Colors.grey.shade200,
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        child: article.imageUrls.isNotEmpty
            ? CachedNetworkImage(
                imageUrl: article.imageUrls.first,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey.shade200,
                  child: const Center(child: CircularProgressIndicator()),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey.shade200,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.article,
                        size: 48,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'لا توجد صورة',
                        style: GoogleFonts.cairo(color: Colors.grey.shade600),
                      ),
                    ],
                  ),
                ),
              )
            : Container(
                color: Colors.grey.shade200,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.article, size: 48, color: Colors.grey.shade400),
                    const SizedBox(height: 8),
                    Text(
                      'لا توجد صورة',
                      style: GoogleFonts.cairo(color: Colors.grey.shade600),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildStatusBadge(Article article) {
    Color color;
    String text;

    if (!article.isApproved) {
      color = Colors.orange;
      text = 'في انتظار المراجعة';
    } else {
      color = Colors.green;
      text = 'معتمد';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: GoogleFonts.cairo(
          fontSize: 10,
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildRatingWidget(double rating) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.amber.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.amber.shade200),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.star, size: 16, color: Colors.amber.shade600),
          const SizedBox(width: 4),
          Text(
            rating.toStringAsFixed(1),
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.amber.shade700,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  Widget _buildStatItem(IconData icon, String value, String label) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: Colors.grey.shade600),
        const SizedBox(width: 4),
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(width: 2),
        Text(
          label,
          style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  Widget _buildCategoryChip(String category) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Text(
        category,
        style: GoogleFonts.cairo(
          fontSize: 10,
          color: Colors.blue.shade700,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddArticleFAB() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: FloatingActionButton.extended(
        onPressed: _showAddArticleDialog,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        icon: const Icon(Icons.add),
        label: Text(
          'إضافة مقال',
          style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  // دوال الإجراءات
  void _viewArticle(Article article) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArticleDetailScreen(article: article),
      ),
    );
  }

  void _editArticle(Article article) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditArticleScreen(article: article),
      ),
    );
  }

  void _deleteArticle(Article article) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف المقال', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف مقال "${article.title}"؟ لا يمكن التراجع عن هذا الإجراء.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _performDeleteArticle(article);
            },
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _performDeleteArticle(Article article) async {
    try {
      final articleProvider = Provider.of<ArticleProvider>(
        context,
        listen: false,
      );
      await articleProvider.deleteArticle(article.id!);
      if (mounted) {
        ErrorHandler.showSuccessSnackBar(context, 'تم حذف المقال بنجاح');
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(context, 'فشل في حذف المقال: $e');
      }
    }
  }

  void _showAddArticleDialog() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddEditArticleScreen()),
    );
  }

  void _showStatistics() {
    // عرض إحصائيات المقالات
    final articleProvider = Provider.of<ArticleProvider>(
      context,
      listen: false,
    );
    final articles = articleProvider.articles;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إحصائيات المقالات',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatisticRow('إجمالي المقالات', '${articles.length}'),
            _buildStatisticRow(
              'المقالات المعتمدة',
              '${articles.where((a) => a.isApproved).length}',
            ),
            _buildStatisticRow(
              'في انتظار المراجعة',
              '${articles.where((a) => !a.isApproved).length}',
            ),
            _buildStatisticRow(
              'المقالات المميزة',
              '${articles.where((a) => a.isFeatured).length}',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: GoogleFonts.cairo()),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  void _exportArticles() {
    // تصدير المقالات
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تصدير المقالات بنجاح', style: GoogleFonts.cairo()),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showBulkActionsDialog() {
    // إجراءات جماعية
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم تطبيق الإجراءات الجماعية',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _manageCategoriesDialog() {
    // إدارة الفئات
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم فتح إدارة الفئات', style: GoogleFonts.cairo()),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showContributionsScreen() {
    // عرض مساهمات المستخدمين
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم فتح مساهمات المستخدمين', style: GoogleFonts.cairo()),
        backgroundColor: Colors.purple,
      ),
    );
  }

  void _showAnalyticsScreen() {
    // عرض تحليلات المحتوى
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم فتح تحليلات المحتوى', style: GoogleFonts.cairo()),
        backgroundColor: Colors.teal,
      ),
    );
  }
}

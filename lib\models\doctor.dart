import 'package:cloud_firestore/cloud_firestore.dart';

class Doctor {
  final String? id;
  final String name;
  final String specialty;
  final String clinic;
  final String address;
  final String phoneNumber;
  final String workingHours;
  final double rating;
  final List<String> imageUrls; // قائمة الصور
  final DateTime createdAt;
  final DateTime? updatedAt; // تاريخ التحديث
  final String? bio; // السيرة الذاتية
  final String? education; // التعليم والشهادات
  final String? experience; // الخبرات
  final List<String> languages; // اللغات المتحدث بها
  final String? email; // البريد الإلكتروني
  final String? website; // الموقع الإلكتروني
  final Map<String, dynamic>? location; // الموقع الجغرافي
  final List<String> services; // الخدمات المقدمة
  final Map<String, String>? socialMedia; // وسائل التواصل الاجتماعي
  final double? consultationFee; // رسوم الاستشارة
  final bool isVerified; // هل الطبيب موثق
  final bool isAvailable; // هل الطبيب متاح
  final bool isApproved; // هل الطبيب معتمد
  final bool isFeatured; // هل الطبيب مميز
  final int likesCount; // عدد الإعجابات
  final int reviewsCount; // عدد التقييمات
  final int appointmentsCount; // عدد المواعيد
  final String? userId; // معرف المستخدم الذي أضاف الطبيب
  final String? username; // اسم المستخدم الذي أضاف الطبيب
  final List<String> specializations; // التخصصات الفرعية
  final String? licenseNumber; // رقم الترخيص
  final DateTime? licenseExpiry; // تاريخ انتهاء الترخيص

  // للتوافق مع الكود القديم
  String? get imageUrl => imageUrls.isNotEmpty ? imageUrls.first : null;

  Doctor({
    this.id,
    required this.name,
    required this.specialty,
    required this.clinic,
    required this.address,
    required this.phoneNumber,
    this.workingHours = '',
    this.rating = 0.0,
    this.imageUrls = const [],
    required this.createdAt,
    this.updatedAt,
    this.bio,
    this.education,
    this.experience,
    this.languages = const [],
    this.email,
    this.website,
    this.location,
    this.services = const [],
    this.socialMedia,
    this.consultationFee,
    this.isVerified = false,
    this.isAvailable = true,
    this.isApproved = true,
    this.isFeatured = false,
    this.likesCount = 0,
    this.reviewsCount = 0,
    this.appointmentsCount = 0,
    this.userId,
    this.username,
    this.specializations = const [],
    this.licenseNumber,
    this.licenseExpiry,
  });

  factory Doctor.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // تحويل التاريخ بأمان
    DateTime createdAt;
    try {
      createdAt = (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now();
    } catch (e) {
      createdAt = DateTime.now();
    }

    DateTime? updatedAt;
    try {
      updatedAt = data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : null;
    } catch (e) {
      updatedAt = null;
    }

    DateTime? licenseExpiry;
    try {
      licenseExpiry = data['licenseExpiry'] != null
          ? (data['licenseExpiry'] as Timestamp).toDate()
          : null;
    } catch (e) {
      licenseExpiry = null;
    }

    return Doctor(
      id: doc.id,
      name: data['name'] ?? '',
      specialty: data['specialty'] ?? '',
      clinic: data['clinic'] ?? '',
      address: data['address'] ?? '',
      phoneNumber: data['phoneNumber'] ?? '',
      workingHours: data['workingHours'] ?? '',
      rating: (data['rating'] ?? 0.0).toDouble(),
      imageUrls: data['imageUrls'] != null
          ? List<String>.from(data['imageUrls'])
          : (data['imageUrl'] != null
                ? [data['imageUrl']]
                : []), // للتوافق مع البيانات القديمة
      createdAt: createdAt,
      updatedAt: updatedAt,
      bio: data['bio'],
      education: data['education'],
      experience: data['experience'],
      languages: List<String>.from(data['languages'] ?? []),
      email: data['email'],
      website: data['website'],
      location: data['location'],
      services: List<String>.from(data['services'] ?? []),
      socialMedia: data['socialMedia'] != null
          ? Map<String, String>.from(data['socialMedia'])
          : null,
      consultationFee: data['consultationFee']?.toDouble(),
      isVerified: data['isVerified'] ?? false,
      isAvailable: data['isAvailable'] ?? true,
      isApproved: data['isApproved'] ?? true,
      isFeatured: data['isFeatured'] ?? false,
      likesCount: data['likesCount'] ?? 0,
      reviewsCount: data['reviewsCount'] ?? 0,
      appointmentsCount: data['appointmentsCount'] ?? 0,
      userId: data['userId'],
      username: data['username'],
      specializations: List<String>.from(data['specializations'] ?? []),
      licenseNumber: data['licenseNumber'],
      licenseExpiry: licenseExpiry,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'specialty': specialty,
      'clinic': clinic,
      'address': address,
      'phoneNumber': phoneNumber,
      'workingHours': workingHours,
      'rating': rating,
      'imageUrls': imageUrls,
      'bio': bio,
      'education': education,
      'experience': experience,
      'languages': languages,
      'email': email,
      'website': website,
      'location': location,
      'services': services,
      'socialMedia': socialMedia,
      'consultationFee': consultationFee,
      'isVerified': isVerified,
      'isAvailable': isAvailable,
      'isApproved': isApproved,
      'isFeatured': isFeatured,
      'likesCount': likesCount,
      'reviewsCount': reviewsCount,
      'appointmentsCount': appointmentsCount,
      'userId': userId,
      'username': username,
      'specializations': specializations,
      'licenseNumber': licenseNumber,
      'licenseExpiry': licenseExpiry != null
          ? Timestamp.fromDate(licenseExpiry!)
          : null,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'createdAt': FieldValue.serverTimestamp(),
    };
  }

  Doctor copyWith({
    String? id,
    String? name,
    String? specialty,
    String? clinic,
    String? address,
    String? phoneNumber,
    String? workingHours,
    double? rating,
    List<String>? imageUrls,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? bio,
    String? education,
    String? experience,
    List<String>? languages,
    String? email,
    String? website,
    Map<String, dynamic>? location,
    List<String>? services,
    Map<String, String>? socialMedia,
    double? consultationFee,
    bool? isVerified,
    bool? isAvailable,
    bool? isApproved,
    bool? isFeatured,
    int? likesCount,
    int? reviewsCount,
    int? appointmentsCount,
    String? userId,
    String? username,
    List<String>? specializations,
    String? licenseNumber,
    DateTime? licenseExpiry,
  }) {
    return Doctor(
      id: id ?? this.id,
      name: name ?? this.name,
      specialty: specialty ?? this.specialty,
      clinic: clinic ?? this.clinic,
      address: address ?? this.address,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      workingHours: workingHours ?? this.workingHours,
      rating: rating ?? this.rating,
      imageUrls: imageUrls ?? this.imageUrls,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      bio: bio ?? this.bio,
      education: education ?? this.education,
      experience: experience ?? this.experience,
      languages: languages ?? this.languages,
      email: email ?? this.email,
      website: website ?? this.website,
      location: location ?? this.location,
      services: services ?? this.services,
      socialMedia: socialMedia ?? this.socialMedia,
      consultationFee: consultationFee ?? this.consultationFee,
      isVerified: isVerified ?? this.isVerified,
      isAvailable: isAvailable ?? this.isAvailable,
      isApproved: isApproved ?? this.isApproved,
      isFeatured: isFeatured ?? this.isFeatured,
      likesCount: likesCount ?? this.likesCount,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      appointmentsCount: appointmentsCount ?? this.appointmentsCount,
      userId: userId ?? this.userId,
      username: username ?? this.username,
      specializations: specializations ?? this.specializations,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      licenseExpiry: licenseExpiry ?? this.licenseExpiry,
    );
  }

  @override
  String toString() {
    return 'Doctor(id: $id, name: $name, specialty: $specialty, clinic: $clinic, address: $address, phoneNumber: $phoneNumber, workingHours: $workingHours, rating: $rating, imageUrl: $imageUrl, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Doctor &&
        other.id == id &&
        other.name == name &&
        other.specialty == specialty &&
        other.clinic == clinic &&
        other.address == address &&
        other.phoneNumber == phoneNumber &&
        other.workingHours == workingHours &&
        other.rating == rating &&
        other.imageUrl == imageUrl &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        specialty.hashCode ^
        clinic.hashCode ^
        address.hashCode ^
        phoneNumber.hashCode ^
        workingHours.hashCode ^
        rating.hashCode ^
        imageUrl.hashCode ^
        createdAt.hashCode;
  }
}

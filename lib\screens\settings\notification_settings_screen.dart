import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/providers/notification_provider.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  @override
  void initState() {
    super.initState();
    // تهيئة إعدادات الإشعارات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<NotificationProvider>(context, listen: false).initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'إعدادات الإشعارات',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Consumer<NotificationProvider>(
        builder: (context, notificationProvider, child) {
          if (notificationProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الإشعارات العامة
                _buildSectionHeader('الإعدادات العامة'),
                _buildNotificationCard(
                  icon: Icons.notifications,
                  title: 'تفعيل الإشعارات',
                  subtitle: 'تفعيل أو إلغاء تفعيل جميع الإشعارات',
                  value: notificationProvider.notificationsEnabled,
                  onChanged: (value) {
                    notificationProvider.setNotificationsEnabled(value);
                  },
                  iconColor: theme.primaryColor,
                ),
                
                const SizedBox(height: 24),
                
                // إشعارات محددة
                _buildSectionHeader('أنواع الإشعارات'),
                
                _buildNotificationCard(
                  icon: Icons.medical_services,
                  title: 'تذكيرات الأدوية',
                  subtitle: 'تذكيرك بمواعيد تناول الأدوية',
                  value: notificationProvider.medicationReminders,
                  onChanged: notificationProvider.notificationsEnabled
                      ? (value) {
                          notificationProvider.setMedicationReminders(value);
                        }
                      : null,
                  iconColor: Colors.red[400]!,
                ),
                
                const SizedBox(height: 12),
                
                _buildNotificationCard(
                  icon: Icons.fastfood,
                  title: 'الأطعمة الجديدة',
                  subtitle: 'إشعارات عند إضافة أطعمة جديدة آمنة',
                  value: notificationProvider.newFoodsNotifications,
                  onChanged: notificationProvider.notificationsEnabled
                      ? (value) {
                          notificationProvider.setNewFoodsNotifications(value);
                        }
                      : null,
                  iconColor: Colors.green[400]!,
                ),
                
                const SizedBox(height: 12),
                
                _buildNotificationCard(
                  icon: Icons.forum,
                  title: 'منشورات المنتدى',
                  subtitle: 'إشعارات عند إضافة منشورات جديدة',
                  value: notificationProvider.forumNotifications,
                  onChanged: notificationProvider.notificationsEnabled
                      ? (value) {
                          notificationProvider.setForumNotifications(value);
                        }
                      : null,
                  iconColor: Colors.blue[400]!,
                ),
                
                const SizedBox(height: 12),
                
                _buildNotificationCard(
                  icon: Icons.article,
                  title: 'المقالات الجديدة',
                  subtitle: 'إشعارات عند نشر مقالات جديدة',
                  value: notificationProvider.articlesNotifications,
                  onChanged: notificationProvider.notificationsEnabled
                      ? (value) {
                          notificationProvider.setArticlesNotifications(value);
                        }
                      : null,
                  iconColor: Colors.orange[400]!,
                ),
                
                const SizedBox(height: 32),
                
                // معلومات إضافية
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                          const SizedBox(width: 8),
                          Text(
                            'معلومات مهمة:',
                            style: GoogleFonts.cairo(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue[800],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '• يمكنك تخصيص أنواع الإشعارات حسب احتياجاتك\n'
                        '• تذكيرات الأدوية تساعدك في الالتزام بالعلاج\n'
                        '• إشعارات الأطعمة تبقيك على اطلاع بالخيارات الآمنة\n'
                        '• يمكنك إلغاء تفعيل الإشعارات في أي وقت',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.blue[700],
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.grey[800],
        ),
      ),
    );
  }

  Widget _buildNotificationCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool>? onChanged,
    required Color iconColor,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.deepPurple.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: iconColor,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey[800],
          ),
        ),
        subtitle: Text(
          subtitle,
          style: GoogleFonts.cairo(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        trailing: Switch(
          value: value,
          onChanged: onChanged,
          activeColor: iconColor,
          inactiveThumbColor: Colors.grey[400],
          inactiveTrackColor: Colors.grey[300],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class BristolStoolVisualSelector extends StatelessWidget {
  final int? selectedType;
  final ValueChanged<int?> onSelected;

  const BristolStoolVisualSelector({
    super.key,
    this.selectedType,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'مقياس بريستول للبراز (اختياري)',
          style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 0.8,
          ),
          itemCount: 7,
          itemBuilder: (context, index) {
            final type = index + 1;
            final isSelected = selectedType == type;
            return GestureDetector(
              onTap: () {
                // Allow deselecting by tapping again
                onSelected(isSelected ? null : type);
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade300,
                    width: isSelected ? 2.0 : 1.0,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Placeholder for the image
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Image.asset(
                          'assets/images/bristol/type-$type.png', // Assuming images are named type-1.png, type-2.png, etc.
                          fit: BoxFit.contain,
                           errorBuilder: (context, error, stackTrace) {
                            // Show a placeholder if the image fails to load
                            return Center(child: Icon(Icons.image_not_supported, color: Colors.grey.shade400, size: 30));
                          },
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Text(
                        'النوع $type',
                        style: GoogleFonts.cairo(
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          color: isSelected ? Theme.of(context).primaryColor : Colors.black87,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}

import 'package:flutter_test/flutter_test.dart';
import 'package:yassincil/models/safe_store.dart';

void main() {
  group('SafeStore Model Tests', () {
    test('should create a safe store with required fields', () {
      // Arrange
      final store = SafeStore(
        name: 'متجر الصحة الطبيعية',
        category: 'متاجر صحية',
        description: 'متجر متخصص في المنتجات الصحية والخالية من الجلوتين',
        location: 'الرياض - حي العليا',
        phone: '+966112345678',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Assert
      expect(store.name, 'متجر الصحة الطبيعية');
      expect(store.category, 'متاجر صحية');
      expect(store.description, 'متجر متخصص في المنتجات الصحية والخالية من الجلوتين');
      expect(store.location, 'الرياض - حي العليا');
      expect(store.phone, '+966112345678');
      expect(store.createdAt, isA<DateTime>());
      expect(store.updatedAt, isA<DateTime>());
    });

    test('should create safe store with default values', () {
      // Arrange
      final store = SafeStore(
        name: 'كارفور',
        category: 'سوبر ماركت',
        description: 'قسم خاص للمنتجات الخالية من الجلوتين',
        location: 'عدة فروع في المملكة',
        phone: '+966112345679',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Assert
      expect(store.hasGlutenFreeSection, true);
      expect(store.rating, 4.0);
      expect(store.products, isEmpty);
      expect(store.isOpen, true);
      expect(store.openingHours, '9:00 ص - 10:00 م');
      expect(store.hasDelivery, false);
      expect(store.acceptsInsurance, false);
      expect(store.specialServices, isEmpty);
      expect(store.isFeatured, false);
      expect(store.isVerified, false);
      expect(store.likesCount, 0);
      expect(store.reviewsCount, 0);
      expect(store.visitsCount, 0);
    });

    test('should create safe store with optional fields', () {
      // Arrange
      final store = SafeStore(
        name: 'مخبز الحياة الصحية',
        category: 'مخابز خاصة',
        description: 'مخبز متخصص في الخبز الخالي من الجلوتين',
        location: 'جدة - حي الزهراء',
        phone: '+966112345680',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        rating: 4.9,
        products: ['خبز طازج يومياً', 'كعك', 'بيتزا', 'معجنات'],
        website: 'https://healthy-bakery.com',
        email: '<EMAIL>',
        address: 'شارع الأمير سلطان، جدة',
        latitude: 21.4858,
        longitude: 39.1925,
        distance: '1.2 كم',
        isOpen: true,
        openingHours: '6:00 ص - 11:00 م',
        hasDelivery: true,
        specialServices: ['خبز طازج يومياً', 'طلبات خاصة'],
        isFeatured: true,
        isVerified: true,
        likesCount: 250,
        reviewsCount: 85,
        visitsCount: 1500,
      );

      // Assert
      expect(store.rating, 4.9);
      expect(store.products.length, 4);
      expect(store.website, 'https://healthy-bakery.com');
      expect(store.email, '<EMAIL>');
      expect(store.address, 'شارع الأمير سلطان، جدة');
      expect(store.latitude, 21.4858);
      expect(store.longitude, 39.1925);
      expect(store.distance, '1.2 كم');
      expect(store.openingHours, '6:00 ص - 11:00 م');
      expect(store.hasDelivery, true);
      expect(store.specialServices.length, 2);
      expect(store.isFeatured, true);
      expect(store.isVerified, true);
      expect(store.likesCount, 250);
      expect(store.reviewsCount, 85);
      expect(store.visitsCount, 1500);
    });

    test('should convert to map correctly', () {
      // Arrange
      final store = SafeStore(
        name: 'متجر سيلياك أونلاين',
        category: 'متاجر أونلاين',
        description: 'متجر إلكتروني متخصص في منتجات السيلياك',
        location: 'توصيل لجميع أنحاء المملكة',
        phone: '+966112345681',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        rating: 4.6,
        products: ['منتجات مستوردة', 'خبز مجمد', 'حلويات خاصة'],
        hasDelivery: true,
        isVerified: true,
        likesCount: 180,
        reviewsCount: 60,
      );

      // Act
      final map = store.toMap();

      // Assert
      expect(map['name'], 'متجر سيلياك أونلاين');
      expect(map['category'], 'متاجر أونلاين');
      expect(map['description'], 'متجر إلكتروني متخصص في منتجات السيلياك');
      expect(map['location'], 'توصيل لجميع أنحاء المملكة');
      expect(map['phone'], '+966112345681');
      expect(map['rating'], 4.6);
      expect(map['products'], ['منتجات مستوردة', 'خبز مجمد', 'حلويات خاصة']);
      expect(map['hasDelivery'], true);
      expect(map['isVerified'], true);
      expect(map['likesCount'], 180);
      expect(map['reviewsCount'], 60);
      expect(map['createdAt'], isA<dynamic>());
      expect(map['updatedAt'], isA<dynamic>());
    });

    test('should have valid properties', () {
      // Arrange
      final store = SafeStore(
        name: 'لولو هايبر ماركت',
        category: 'سوبر ماركت',
        description: 'مجموعة واسعة من المنتجات الخالية من الجلوتين',
        location: 'الرياض، جدة، الدمام',
        phone: '+966112345682',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Assert
      expect(store.name, isNotEmpty);
      expect(store.category, isNotEmpty);
      expect(store.description, isNotEmpty);
      expect(store.location, isNotEmpty);
      expect(store.phone, isNotEmpty);
      expect(store.rating, greaterThanOrEqualTo(0.0));
      expect(store.rating, lessThanOrEqualTo(5.0));
      expect(store.createdAt, isA<DateTime>());
      expect(store.updatedAt, isA<DateTime>());
    });

    test('should copy with new values', () {
      // Arrange
      final originalStore = SafeStore(
        name: 'المتجر الأصلي',
        category: 'متاجر صحية',
        description: 'وصف المتجر الأصلي',
        location: 'الرياض',
        phone: '+966112345683',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        rating: 4.0,
        isVerified: false,
        likesCount: 50,
      );

      // Act
      final copiedStore = originalStore.copyWith(
        name: 'المتجر الجديد',
        rating: 4.8,
        isVerified: true,
        likesCount: 150,
      );

      // Assert
      expect(copiedStore.name, 'المتجر الجديد');
      expect(copiedStore.rating, 4.8);
      expect(copiedStore.isVerified, true);
      expect(copiedStore.likesCount, 150);
      expect(copiedStore.category, originalStore.category); // unchanged
      expect(copiedStore.description, originalStore.description); // unchanged
      expect(copiedStore.location, originalStore.location); // unchanged
      expect(copiedStore.phone, originalStore.phone); // unchanged
    });

    test('should handle equality correctly', () {
      // Arrange
      final store1 = SafeStore(
        id: 'store123',
        name: 'متجر الاختبار',
        category: 'متاجر صحية',
        description: 'متجر للاختبار',
        location: 'الرياض',
        phone: '+966112345684',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final store2 = SafeStore(
        id: 'store123',
        name: 'متجر آخر',
        category: 'سوبر ماركت',
        description: 'متجر مختلف',
        location: 'جدة',
        phone: '+966112345685',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final store3 = SafeStore(
        id: 'store456',
        name: 'متجر الاختبار',
        category: 'متاجر صحية',
        description: 'متجر للاختبار',
        location: 'الرياض',
        phone: '+966112345684',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Assert
      expect(store1, equals(store2)); // same id
      expect(store1, isNot(equals(store3))); // different id
      expect(store1.hashCode, equals(store2.hashCode)); // same hash
      expect(store1.hashCode, isNot(equals(store3.hashCode))); // different hash
    });

    test('should convert toString correctly', () {
      // Arrange
      final store = SafeStore(
        id: 'store789',
        name: 'متجر النموذج',
        category: 'مخابز خاصة',
        description: 'متجر نموذجي للاختبار',
        location: 'الدمام',
        phone: '+966112345686',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        rating: 4.7,
      );

      // Act
      final stringRepresentation = store.toString();

      // Assert
      expect(stringRepresentation, contains('store789'));
      expect(stringRepresentation, contains('متجر النموذج'));
      expect(stringRepresentation, contains('مخابز خاصة'));
      expect(stringRepresentation, contains('4.7'));
    });
  });
}

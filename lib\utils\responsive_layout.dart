// lib/utils/responsive_layout.dart
import 'package:flutter/material.dart';

/// نظام التخطيط المتجاوب للتطبيق
class ResponsiveLayout {
  // نقاط الكسر للشاشات
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  /// تحديد نوع الجهاز بناءً على عرض الشاشة
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (width < tabletBreakpoint) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  /// الحصول على عدد الأعمدة المناسب للشبكة
  static int getGridColumns(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return 2;
      case DeviceType.tablet:
        return 3;
      case DeviceType.desktop:
        return 4;
    }
  }

  /// الحصول على نسبة العرض إلى الارتفاع للبطاقات
  static double getCardAspectRatio(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return 1.1;
      case DeviceType.tablet:
        return 1.2;
      case DeviceType.desktop:
        return 1.3;
    }
  }

  /// الحصول على المسافات المناسبة
  static EdgeInsets getScreenPadding(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case DeviceType.tablet:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
      case DeviceType.desktop:
        return const EdgeInsets.symmetric(horizontal: 32, vertical: 16);
    }
  }

  /// الحصول على حجم الخط المناسب
  static double getFontSize(BuildContext context, FontSizeType type) {
    final deviceType = getDeviceType(context);
    
    switch (type) {
      case FontSizeType.small:
        return deviceType == DeviceType.mobile ? 12 : 14;
      case FontSizeType.medium:
        return deviceType == DeviceType.mobile ? 14 : 16;
      case FontSizeType.large:
        return deviceType == DeviceType.mobile ? 16 : 18;
      case FontSizeType.extraLarge:
        return deviceType == DeviceType.mobile ? 20 : 24;
      case FontSizeType.title:
        return deviceType == DeviceType.mobile ? 24 : 28;
    }
  }

  /// الحصول على حجم الأيقونة المناسب
  static double getIconSize(BuildContext context, IconSizeType type) {
    final deviceType = getDeviceType(context);
    
    switch (type) {
      case IconSizeType.small:
        return deviceType == DeviceType.mobile ? 16 : 18;
      case IconSizeType.medium:
        return deviceType == DeviceType.mobile ? 20 : 24;
      case IconSizeType.large:
        return deviceType == DeviceType.mobile ? 24 : 28;
      case IconSizeType.extraLarge:
        return deviceType == DeviceType.mobile ? 32 : 36;
    }
  }

  /// تحديد ما إذا كان يجب استخدام تخطيط أفقي أم عمودي
  static bool shouldUseHorizontalLayout(BuildContext context) {
    return getDeviceType(context) != DeviceType.mobile;
  }

  /// الحصول على عرض المحتوى الأقصى
  static double getMaxContentWidth(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return double.infinity;
      case DeviceType.tablet:
        return 800;
      case DeviceType.desktop:
        return 1200;
    }
  }
}

/// أنواع الأجهزة
enum DeviceType {
  mobile,
  tablet,
  desktop,
}

/// أنواع أحجام الخطوط
enum FontSizeType {
  small,
  medium,
  large,
  extraLarge,
  title,
}

/// أنواع أحجام الأيقونات
enum IconSizeType {
  small,
  medium,
  large,
  extraLarge,
}

/// ويدجت للتخطيط المتجاوب
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, DeviceType deviceType) builder;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveLayout.getDeviceType(context);
    return builder(context, deviceType);
  }
}

/// ويدجت للشبكة المتجاوبة
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double? childAspectRatio;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final EdgeInsetsGeometry? padding;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.childAspectRatio,
    this.mainAxisSpacing = 16.0,
    this.crossAxisSpacing = 16.0,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveLayout.getGridColumns(context);
    final aspectRatio = childAspectRatio ?? ResponsiveLayout.getCardAspectRatio(context);
    final screenPadding = padding ?? ResponsiveLayout.getScreenPadding(context);

    return Padding(
      padding: screenPadding,
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: columns,
          childAspectRatio: aspectRatio,
          mainAxisSpacing: mainAxisSpacing,
          crossAxisSpacing: crossAxisSpacing,
        ),
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
      ),
    );
  }
}

/// ويدجت للمحتوى المحدود العرض
class MaxWidthContainer extends StatelessWidget {
  final Widget child;
  final double? maxWidth;
  final EdgeInsetsGeometry? padding;

  const MaxWidthContainer({
    super.key,
    required this.child,
    this.maxWidth,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final maxContentWidth = maxWidth ?? ResponsiveLayout.getMaxContentWidth(context);
    final screenPadding = padding ?? ResponsiveLayout.getScreenPadding(context);

    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: maxContentWidth),
        padding: screenPadding,
        child: child,
      ),
    );
  }
}

/// ويدجت للنص المتجاوب
class ResponsiveText extends StatelessWidget {
  final String text;
  final FontSizeType sizeType;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const ResponsiveText(
    this.text, {
    super.key,
    this.sizeType = FontSizeType.medium,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    final fontSize = ResponsiveLayout.getFontSize(context, sizeType);

    return Text(
      text,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// ويدجت للأيقونة المتجاوبة
class ResponsiveIcon extends StatelessWidget {
  final IconData icon;
  final IconSizeType sizeType;
  final Color? color;

  const ResponsiveIcon(
    this.icon, {
    super.key,
    this.sizeType = IconSizeType.medium,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final iconSize = ResponsiveLayout.getIconSize(context, sizeType);

    return Icon(
      icon,
      size: iconSize,
      color: color,
    );
  }
}

// lib/models/food_item.dart
import 'package:cloud_firestore/cloud_firestore.dart';

class FoodItem {
  final String? id;
  final String name;
  final String category; // e.g., 'حبوب', 'ألبان', 'خضروات'
  final bool isGlutenFree; // هل هذا المكون خالي من الغلوتين؟
  final String details; // وصف أو معلومات إضافية
  final List<String> imageUrls; // قائمة الصور
  final int likesCount; // عدد الإعجابات
  final int commentsCount; // عدد التعليقات
  final int ratingsCount; // عدد التقييمات
  final double averageRating; // متوسط التقييمات
  final String? ingredients; // مكونات العنصر
  final String? warnings; // تحذيرات غذائية (مثلاً: يحتوي على لاكتوز)
  final bool? available; // حالة التوفر
  final DateTime? updatedAt; // آخر تحديث
  final List<String>? likedUserIds; // قائمة معرفات المستخدمين الذين أعجبوا
  final double calories;
  final double protein;
  final double carbohydrates;
  final double fat;
  final double fiber; // الألياف
  final double sugar; // السكر
  final double sodium; // الصوديوم
  final int glycemicIndex;
  final String? brand; // العلامة التجارية
  final String? barcode; // الباركود
  final List<String> alternatives; // البدائل الآمنة
  final String? source; // مصدر المعلومات
  final bool isApproved; // هل الطعام معتمد
  final bool isFeatured; // هل الطعام مميز
  final String? userId; // معرف المستخدم الذي أضاف الطعام
  final String? username; // اسم المستخدم الذي أضاف الطعام
  final DateTime createdAt; // تاريخ الإنشاء
  final List<String> tags; // العلامات
  final Map<String, String>? translations; // ترجمات الاسم

  // للتوافق مع الكود القديم
  String? get imageUrl => imageUrls.isNotEmpty ? imageUrls.first : null;
  double? get rating => averageRating;

  FoodItem({
    this.id,
    required this.name,
    required this.category,
    required this.isGlutenFree,
    this.details = '',
    this.imageUrls = const [],
    this.likesCount = 0,
    this.commentsCount = 0,
    this.ratingsCount = 0,
    this.averageRating = 0.0,
    this.ingredients,
    this.warnings,
    this.available,
    this.updatedAt,
    this.likedUserIds,
    this.calories = 0.0,
    this.protein = 0.0,
    this.carbohydrates = 0.0,
    this.fat = 0.0,
    this.fiber = 0.0,
    this.sugar = 0.0,
    this.sodium = 0.0,
    this.glycemicIndex = 0,
    this.brand,
    this.barcode,
    this.alternatives = const [],
    this.source,
    this.isApproved = true,
    this.isFeatured = false,
    this.userId,
    this.username,
    required this.createdAt,
    this.tags = const [],
    this.translations,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'category': category,
      'isGlutenFree': isGlutenFree,
      'details': details,
      'imageUrls': imageUrls,
      'likesCount': likesCount,
      'commentsCount': commentsCount,
      'ratingsCount': ratingsCount,
      'averageRating': averageRating,
      'ingredients': ingredients,
      'warnings': warnings,
      'available': available,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'createdAt': Timestamp.fromDate(createdAt),
      'likedUserIds': likedUserIds,
      'calories': calories,
      'protein': protein,
      'carbohydrates': carbohydrates,
      'fat': fat,
      'fiber': fiber,
      'sugar': sugar,
      'sodium': sodium,
      'glycemicIndex': glycemicIndex,
      'brand': brand,
      'barcode': barcode,
      'alternatives': alternatives,
      'source': source,
      'isApproved': isApproved,
      'isFeatured': isFeatured,
      'userId': userId,
      'username': username,
      'tags': tags,
      'translations': translations,
    };
  }

  factory FoodItem.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // تحويل التاريخ بأمان
    DateTime createdAt;
    try {
      createdAt = (data['createdAt'] as Timestamp).toDate();
    } catch (e) {
      createdAt = DateTime.now();
    }

    DateTime? updatedAt;
    try {
      updatedAt = data['updatedAt'] is Timestamp
          ? (data['updatedAt'] as Timestamp).toDate()
          : null;
    } catch (e) {
      updatedAt = null;
    }

    return FoodItem(
      id: doc.id,
      name: data['name'] ?? '',
      category: data['category'] ?? '',
      isGlutenFree: data['isGlutenFree'] ?? false,
      details: data['details'] ?? '',
      imageUrls: data['imageUrls'] != null
          ? List<String>.from(data['imageUrls'])
          : (data['imageUrl'] != null
                ? [data['imageUrl']]
                : []), // للتوافق مع البيانات القديمة
      likesCount: data['likesCount'] ?? 0,
      commentsCount: data['commentsCount'] ?? 0,
      ratingsCount: data['ratingsCount'] ?? 0,
      averageRating: (data['averageRating'] ?? 0.0).toDouble(),
      ingredients: data['ingredients'],
      warnings: data['warnings'],
      available: data['available'],
      updatedAt: updatedAt,
      likedUserIds: data['likedUserIds'] != null
          ? List<String>.from(data['likedUserIds'] as List)
          : <String>[],
      calories: (data['calories'] ?? 0.0).toDouble(),
      protein: (data['protein'] ?? 0.0).toDouble(),
      carbohydrates: (data['carbohydrates'] ?? 0.0).toDouble(),
      fat: (data['fat'] ?? 0.0).toDouble(),
      fiber: (data['fiber'] ?? 0.0).toDouble(),
      sugar: (data['sugar'] ?? 0.0).toDouble(),
      sodium: (data['sodium'] ?? 0.0).toDouble(),
      glycemicIndex: data['glycemicIndex'] ?? 0,
      brand: data['brand'],
      barcode: data['barcode'],
      alternatives: List<String>.from(data['alternatives'] ?? []),
      source: data['source'],
      isApproved: data['isApproved'] ?? true,
      isFeatured: data['isFeatured'] ?? false,
      userId: data['userId'],
      username: data['username'],
      createdAt: createdAt,
      tags: List<String>.from(data['tags'] ?? []),
      translations: data['translations'] != null
          ? Map<String, String>.from(data['translations'])
          : null,
    );
  }

  FoodItem copyWith({
    String? id,
    String? name,
    String? category,
    bool? isGlutenFree,
    String? details,
    List<String>? imageUrls,
    int? likesCount,
    int? commentsCount,
    int? ratingsCount,
    double? averageRating,
    String? ingredients,
    String? warnings,
    bool? available,
    DateTime? updatedAt,
    List<String>? likedUserIds,
    double? calories,
    double? protein,
    double? carbohydrates,
    double? fat,
    double? fiber,
    double? sugar,
    double? sodium,
    int? glycemicIndex,
    String? brand,
    String? barcode,
    List<String>? alternatives,
    String? source,
    bool? isApproved,
    bool? isFeatured,
    String? userId,
    String? username,
    DateTime? createdAt,
    List<String>? tags,
    Map<String, String>? translations,
  }) {
    return FoodItem(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      isGlutenFree: isGlutenFree ?? this.isGlutenFree,
      details: details ?? this.details,
      imageUrls: imageUrls ?? this.imageUrls,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      ratingsCount: ratingsCount ?? this.ratingsCount,
      averageRating: averageRating ?? this.averageRating,
      ingredients: ingredients ?? this.ingredients,
      warnings: warnings ?? this.warnings,
      available: available ?? this.available,
      updatedAt: updatedAt ?? this.updatedAt,
      likedUserIds: likedUserIds ?? this.likedUserIds,
      calories: calories ?? this.calories,
      protein: protein ?? this.protein,
      carbohydrates: carbohydrates ?? this.carbohydrates,
      fat: fat ?? this.fat,
      fiber: fiber ?? this.fiber,
      sugar: sugar ?? this.sugar,
      sodium: sodium ?? this.sodium,
      glycemicIndex: glycemicIndex ?? this.glycemicIndex,
      brand: brand ?? this.brand,
      barcode: barcode ?? this.barcode,
      alternatives: alternatives ?? this.alternatives,
      source: source ?? this.source,
      isApproved: isApproved ?? this.isApproved,
      isFeatured: isFeatured ?? this.isFeatured,
      userId: userId ?? this.userId,
      username: username ?? this.username,
      createdAt: createdAt ?? this.createdAt,
      tags: tags ?? this.tags,
      translations: translations ?? this.translations,
    );
  }
}

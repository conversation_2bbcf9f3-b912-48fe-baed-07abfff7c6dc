// lib/widgets/add_recipe_comment_widget.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../models/comment.dart';
import '../providers/recipe_provider.dart';
import '../providers/auth_provider.dart' as app_auth;

class AddRecipeCommentWidget extends StatefulWidget {
  final String recipeId;
  final String? parentCommentId;
  final String? replyToUsername;
  final VoidCallback? onCommentAdded;

  const AddRecipeCommentWidget({
    super.key,
    required this.recipeId,
    this.parentCommentId,
    this.replyToUsername,
    this.onCommentAdded,
  });

  @override
  State<AddRecipeCommentWidget> createState() => _AddRecipeCommentWidgetState();
}

class _AddRecipeCommentWidgetState extends State<AddRecipeCommentWidget> {
  final TextEditingController _commentController = TextEditingController();
  final List<File> _selectedImages = [];
  final List<String> _uploadedImageUrls = [];
  bool _isSubmitting = false;
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  Future<void> _pickImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage();
      if (images.isNotEmpty) {
        setState(() {
          for (final image in images) {
            if (_selectedImages.length < 5) {
              _selectedImages.add(File(image.path));
            }
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء اختيار الصور'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  Future<void> _uploadImages() async {
    _uploadedImageUrls.clear();

    for (int i = 0; i < _selectedImages.length; i++) {
      try {
        // هنا يجب استخدام StorageService لرفع الصور
        // مؤقتاً سنضع رابط وهمي
        final imageUrl =
            'https://example.com/recipe_image_${DateTime.now().millisecondsSinceEpoch}_$i.jpg';
        _uploadedImageUrls.add(imageUrl);
      } catch (e) {
        debugPrint('خطأ في رفع الصورة: $e');
      }
    }
  }

  Future<void> _submitComment() async {
    if (_commentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى كتابة تعليق'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final authProvider = Provider.of<app_auth.AuthProvider>(
        context,
        listen: false,
      );

      // رفع الصور أولاً
      await _uploadImages();

      if (!mounted) return;

      final recipeProvider = Provider.of<RecipeProvider>(
        context,
        listen: false,
      );

      final comment = Comment(
        postId: widget.recipeId,
        content: _commentController.text.trim(),
        userId: authProvider.currentUser!.uid,
        username:
            authProvider.userProfile?.displayName ??
            authProvider.userProfile?.username ??
            'مستخدم',
        userAvatar: authProvider.userProfile?.profileImageUrl,
        createdAt: DateTime.now(),
        imageUrls: _uploadedImageUrls,
        parentCommentId: widget.parentCommentId,
      );

      if (widget.parentCommentId != null) {
        // إضافة رد
        await recipeProvider.addReplyToComment(
          recipeId: widget.recipeId,
          parentCommentId: widget.parentCommentId!,
          reply: comment,
        );
      } else {
        // إضافة تعليق جديد
        await recipeProvider.addComment(widget.recipeId, comment);
      }

      // مسح الحقول
      _commentController.clear();
      setState(() {
        _selectedImages.clear();
        _uploadedImageUrls.clear();
      });

      // إشعار بالنجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.parentCommentId != null
                  ? 'تم إضافة الرد بنجاح'
                  : 'تم إضافة التعليق بنجاح',
            ),
            backgroundColor: Colors.green,
          ),
        );

        // استدعاء callback إذا كان موجود
        widget.onCommentAdded?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إضافة التعليق'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<app_auth.AuthProvider>(context);

    if (authProvider.currentUser == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Text(
            'يجب تسجيل الدخول لإضافة تعليق',
            style: GoogleFonts.cairo(color: Colors.grey.shade600, fontSize: 16),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عرض اسم المستخدم المراد الرد عليه
          if (widget.replyToUsername != null) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Text(
                'رد على ${widget.replyToUsername}',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: const Color(0xFF4CAF50),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 12),
          ],

          // حقل النص والأزرار
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // حقل الإدخال
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(color: Colors.grey.shade300, width: 1),
                  ),
                  child: TextField(
                    controller: _commentController,
                    maxLines: null,
                    decoration: InputDecoration(
                      hintText: widget.parentCommentId != null
                          ? 'اكتب ردك...'
                          : 'اكتب تعليقك...',
                      hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    style: GoogleFonts.cairo(),
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // زر اختيار الصور
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.grey.shade300, width: 1),
                ),
                child: IconButton(
                  onPressed: _isSubmitting ? null : _pickImages,
                  icon: Icon(
                    Icons.image,
                    color: Colors.grey.shade600,
                    size: 20,
                  ),
                  tooltip: 'إضافة صور',
                ),
              ),

              const SizedBox(width: 8),

              // زر الإرسال
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF4CAF50), Color(0xFF388E3C)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: _isSubmitting ? null : _submitComment,
                  icon: _isSubmitting
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : const Icon(Icons.send, color: Colors.white, size: 20),
                  tooltip: 'إرسال',
                ),
              ),
            ],
          ),

          // عرض الصور المختارة
          if (_selectedImages.isNotEmpty) ...[
            const SizedBox(height: 12),
            SizedBox(
              height: 80,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    width: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300, width: 1),
                    ),
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(7),
                          child: Image.file(
                            _selectedImages[index],
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => _removeImage(index),
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.close,
                                size: 12,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }
}

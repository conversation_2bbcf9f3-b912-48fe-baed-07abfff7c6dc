// Firestore Security Rules for Forum Features
// Add these rules to your existing firestore.rules file

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Forum Posts Rules
    match /forum_posts/{postId} {
      // Allow read for approved posts
      allow read: if resource.data.isApproved == true && resource.data.isDeleted == false;
      
      // Allow create for authenticated users
      allow create: if request.auth != null 
        && request.auth.uid == request.resource.data.userId
        && request.resource.data.keys().hasAll(['userId', 'username', 'content', 'createdAt', 'type', 'category'])
        && request.resource.data.userId == request.auth.uid;
      
      // Allow update for post owner or admin
      allow update: if request.auth != null 
        && (request.auth.uid == resource.data.userId 
            || get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin')
        && request.resource.data.userId == resource.data.userId; // Prevent changing ownership
      
      // Allow delete for post owner or admin
      allow delete: if request.auth != null 
        && (request.auth.uid == resource.data.userId 
            || get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }
    
    // Comments Rules
    match /comments/{commentId} {
      // Allow read for approved comments
      allow read: if resource.data.isApproved == true && resource.data.isDeleted == false;
      
      // Allow create for authenticated users
      allow create: if request.auth != null 
        && request.auth.uid == request.resource.data.userId
        && request.resource.data.keys().hasAll(['postId', 'userId', 'username', 'content', 'createdAt'])
        && request.resource.data.userId == request.auth.uid;
      
      // Allow update for comment owner or admin
      allow update: if request.auth != null 
        && (request.auth.uid == resource.data.userId 
            || get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin')
        && request.resource.data.userId == resource.data.userId; // Prevent changing ownership
      
      // Allow delete for comment owner or admin
      allow delete: if request.auth != null 
        && (request.auth.uid == resource.data.userId 
            || get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }
    
    // Likes Rules
    match /likes/{likeId} {
      // Allow read for all authenticated users
      allow read: if request.auth != null;
      
      // Allow create/delete for the user who owns the like
      allow create, delete: if request.auth != null 
        && request.auth.uid == request.resource.data.userId;
      
      // Prevent updates to likes
      allow update: if false;
    }
    
    // Reports Rules
    match /reports/{reportId} {
      // Only allow read for admins
      allow read: if request.auth != null 
        && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
      
      // Allow create for authenticated users
      allow create: if request.auth != null 
        && request.auth.uid == request.resource.data.reporterId
        && request.resource.data.keys().hasAll(['reporterId', 'reporterName', 'targetId', 'targetType', 'reason', 'createdAt']);
      
      // Only allow update for admins (to change status)
      allow update: if request.auth != null 
        && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
      
      // Prevent deletion of reports
      allow delete: if false;
    }
    
    // Helper functions
    function isAdmin() {
      return request.auth != null 
        && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }
    
    function isAuthenticated() {
      return request.auth != null;
    }
  }
}
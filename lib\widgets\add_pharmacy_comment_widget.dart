import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/providers/pharmacy_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/utils/app_colors.dart';

class AddPharmacyCommentWidget extends StatefulWidget {
  final String pharmacyId;
  final String? parentCommentId;
  final String? replyToUsername;
  final VoidCallback onCommentAdded;

  const AddPharmacyCommentWidget({
    super.key,
    required this.pharmacyId,
    this.parentCommentId,
    this.replyToUsername,
    required this.onCommentAdded,
  });

  @override
  State<AddPharmacyCommentWidget> createState() =>
      _AddPharmacyCommentWidgetState();
}

class _AddPharmacyCommentWidgetState extends State<AddPharmacyCommentWidget> {
  final _commentController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  Future<void> _submitComment() async {
    if (_commentController.text.trim().isEmpty) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final pharmacyProvider =
          Provider.of<PharmacyProvider>(context, listen: false);

      await pharmacyProvider.addCommentToPharmacy(
        pharmacyId: widget.pharmacyId,
        userId: authProvider.currentUser!.uid,
        content: _commentController.text.trim(),
        parentCommentId: widget.parentCommentId,
      );

      _commentController.clear();
      widget.onCommentAdded();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إضافة التعليق', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.replyToUsername != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Text(
                'الرد على: ${widget.replyToUsername}',
                style: GoogleFonts.cairo(
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _commentController,
                  decoration: InputDecoration(
                    hintText: 'أضف تعليقك...',
                    hintStyle: GoogleFonts.cairo(),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  maxLines: 3,
                ),
              ),
              const SizedBox(width: 8),
              _isLoading
                  ? const CircularProgressIndicator()
                  : IconButton(
                      icon: const Icon(Icons.send, color: AppColors.primary),
                      onPressed: _submitComment,
                    ),
            ],
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:share_plus/share_plus.dart';

import 'package:yassincil/providers/favorites_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/models/food_item.dart';
import 'package:yassincil/screens/foods/food_detail_screen.dart';
import 'package:yassincil/utils/app_colors.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late TabController _tabController;
  String _searchQuery = '';
  String _selectedCategory = 'الكل';
  bool? _selectedStatus; // null = الكل، true = خالي من الجلوتين، false = يحتوي على جلوتين

  final List<String> _categories = [
    'الكل',
    'حبوب ومخبوزات',
    'ألبان ومنتجاتها',
    'خضروات',
    'فواكه',
    'لحوم وأسماك',
    'حلويات',
    'مشروبات',
    'توابل وبهارات',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _tabController = TabController(length: _categories.length, vsync: this);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        Provider.of<FavoritesProvider>(context, listen: false).fetchFavorites();
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  List<FoodItem> _getFilteredFavorites(List<FoodItem> favorites) {
    List<FoodItem> filtered = favorites;

    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((food) {
        return food.name.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            food.details.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            food.category.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            );
      }).toList();
    }

    if (_selectedCategory != 'الكل') {
      filtered = filtered.where((food) {
        return food.category == _selectedCategory;
      }).toList();
    }

    if (_selectedStatus != null) {
      filtered = filtered.where((food) {
        return food.isGlutenFree == _selectedStatus;
      }).toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;

    if (user == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('المفضلة')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.login, size: 80, color: Colors.grey.shade400),
              const SizedBox(height: 16),
              Text(
                'يجب تسجيل الدخول أولاً',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'سجل دخولك لحفظ الأطعمة المفضلة',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Consumer<FavoritesProvider>(
      builder: (context, favoritesProvider, child) {
        final filteredFavorites = _getFilteredFavorites(
          favoritesProvider.favoriteFoods,
        );

        return Scaffold(
          backgroundColor: const Color(0xFFF8FAFC),
          body: CustomScrollView(
            slivers: [
              _buildSliverAppBar(favoritesProvider),
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    _buildSearchAndFilters(),
                    _buildCategoryTabs(),
                    _buildStatistics(favoritesProvider),
                  ],
                ),
              ),
              if (favoritesProvider.isLoading)
                const SliverToBoxAdapter(
                  child: Center(
                    child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: CircularProgressIndicator(),
                    ),
                  ),
                )
              else if (favoritesProvider.errorMessage != null)
                SliverToBoxAdapter(
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(32.0),
                      child: Column(
                        children: [
                          const Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 60,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'حدث خطأ',
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            favoritesProvider.errorMessage!,
                            style: GoogleFonts.cairo(fontSize: 14),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton.icon(
                            onPressed: () => favoritesProvider.fetchFavorites(),
                            icon: const Icon(Icons.refresh),
                            label: Text(
                              'إعادة المحاولة',
                              style: GoogleFonts.cairo(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              else if (filteredFavorites.isEmpty)
                SliverToBoxAdapter(
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(32.0),
                      child: Column(
                        children: [
                          Icon(
                            _searchQuery.isNotEmpty ||
                                    _selectedCategory != 'الكل' ||
                                    _selectedStatus != null
                                ? Icons.search_off
                                : Icons.favorite_border,
                            size: 80,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _searchQuery.isNotEmpty ||
                                    _selectedCategory != 'الكل' ||
                                    _selectedStatus != null
                                ? 'لا توجد نتائج'
                                : 'لا توجد أطعمة مفضلة',
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _searchQuery.isNotEmpty ||
                                    _selectedCategory != 'الكل' ||
                                    _selectedStatus != null
                                ? 'جرب تغيير معايير البحث'
                                : 'ابدأ بإضافة أطعمة لقائمة المفضلة',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.grey.shade500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              else
                SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final food = filteredFavorites[index];
                    return Container(
                      margin: const EdgeInsets.only(
                        bottom: 16,
                        left: 16,
                        right: 16,
                      ),
                      child: _buildFavoriteFoodCard(
                        food,
                        favoritesProvider,
                      ),
                    );
                  }, childCount: filteredFavorites.length),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSliverAppBar(FavoritesProvider favoritesProvider) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: AppColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'الأطعمة المفضلة',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontSize: 18,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primary,
                AppColors.primary.withOpacity(0.85),
              ],
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                right: -50,
                top: -50,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.1),
                  ),
                ),
              ),
              Positioned(
                left: -30,
                bottom: -30,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.05),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh_rounded, color: Colors.white),
          onPressed: () {
            Provider.of<FavoritesProvider>(
              context,
              listen: false,
            ).fetchFavorites();
          },
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              style: GoogleFonts.cairo(fontSize: 16),
              decoration: InputDecoration(
                hintText: 'ابحث في المفضلة...',
                hintStyle: GoogleFonts.cairo(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                ),
                prefixIcon: Container(
                  padding: const EdgeInsets.all(12),
                  child: Icon(
                    Icons.search_rounded,
                    color: AppColors.primary,
                    size: 24,
                  ),
                ),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: Icon(
                          Icons.clear_rounded,
                          color: Colors.grey.shade600,
                        ),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryTabs() {
    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(vertical: 12),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        indicatorSize: TabBarIndicatorSize.label,
        labelColor: AppColors.primary,
        unselectedLabelColor: Colors.grey.shade600,
        labelStyle: GoogleFonts.cairo(
          fontWeight: FontWeight.bold,
          fontSize: 15,
        ),
        unselectedLabelStyle: GoogleFonts.cairo(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
        labelPadding: const EdgeInsets.symmetric(horizontal: 20),
        onTap: (index) {
          setState(() {
            _selectedCategory = _categories[index];
          });
        },
        tabs: _categories
            .map(
              (category) => Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: Text(category),
              ),
            )
            .toList(),
      ),
    );
  }

  Widget _buildStatistics(FavoritesProvider favoritesProvider) {
    final stats = favoritesProvider.getFavoritesStatistics(itemType: 'food');

    if (stats['total'] == 0) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildStatCard(
            'المجموع',
            stats['total'].toString(),
            Icons.favorite,
            AppColors.primary,
          ),
          const SizedBox(width: 16),
          _buildStatCard(
            'خالي من الجلوتين',
            stats['glutenFree'].toString(),
            Icons.check_circle,
            Colors.green,
          ),
          const SizedBox(width: 16),
          _buildStatCard(
            'يحتوي على جلوتين',
            stats['gluten'].toString(),
            Icons.warning,
            Colors.orange,
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.2), width: 1),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoriteFoodCard(
    FoodItem food,
    FavoritesProvider favoritesProvider,
  ) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) =>
                FoodDetailScreen(foodItem: food),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: AppColors.primary.withOpacity(0.1),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withOpacity(0.1),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary.withOpacity(0.1),
                    AppColors.primary.withOpacity(0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      gradient: LinearGradient(
                        colors: [
                          Colors.white.withOpacity(0.9),
                          Colors.white.withOpacity(0.7),
                        ],
                      ),
                      border: Border.all(
                        color: AppColors.primary.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(13),
                      child:
                          food.imageUrl != null &&
                              food.imageUrl!.isNotEmpty
                          ? Image.network(
                              food.imageUrl!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.restaurant_menu_rounded,
                                  color: AppColors.primary,
                                  size: 30,
                                );
                              },
                            )
                          : Icon(
                              Icons.restaurant_menu_rounded,
                              color: AppColors.primary,
                              size: 30,
                            ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          food.name,
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF1F2937),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          food.category,
                          style: GoogleFonts.cairo(
                            fontSize: 13,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: food.isGlutenFree
                                  ? [
                                      Colors.green.withOpacity(0.2),
                                      Colors.green.withOpacity(0.1),
                                    ]
                                  : [
                                      Colors.orange.withOpacity(0.2),
                                      Colors.orange.withOpacity(0.1),
                                    ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                food.isGlutenFree
                                    ? Icons.check_circle_rounded
                                    : Icons.warning_rounded,
                                size: 14,
                                color: food.isGlutenFree
                                    ? Colors.green
                                    : Colors.orange,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                food.isGlutenFree ? 'خالي من الجلوتين' : 'يحتوي على جلوتين',
                                style: GoogleFonts.cairo(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w600,
                                  color: food.isGlutenFree
                                      ? Colors.green
                                      : Colors.orange,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(
                        Icons.favorite,
                        color: AppColors.primary,
                        size: 22,
                      ),
                      onPressed: () async {
                        final wasFavorite = favoritesProvider.isFavorite(
                          food.id!,
                        );
                        await favoritesProvider.toggleFavorite(food);
                        if (!mounted) return;
                        ScaffoldMessenger.of(context).hideCurrentSnackBar();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              wasFavorite
                                  ? 'تمت الإزالة من المفضلة'
                                  : 'تمت الإضافة إلى المفضلة',
                              style: GoogleFonts.cairo(color: Colors.white),
                            ),
                            action: SnackBarAction(
                              label: 'تراجع',
                              textColor: Colors.white,
                              onPressed: () {
                                favoritesProvider.toggleFavorite(food);
                              },
                            ),
                            backgroundColor: wasFavorite
                                ? Colors.red
                                : Colors.green,
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    food.category,
                    style: GoogleFonts.cairo(
                      fontSize: 13,
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (food.details.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Text(
                      food.details,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
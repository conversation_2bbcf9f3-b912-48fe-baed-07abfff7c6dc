import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:yassincil/providers/auth_provider.dart';

class SpecialtyStoresScreen extends StatefulWidget {
  const SpecialtyStoresScreen({super.key});

  @override
  State<SpecialtyStoresScreen> createState() => _SpecialtyStoresScreenState();
}

class _SpecialtyStoresScreenState extends State<SpecialtyStoresScreen> {
  final List<SpecialtyStore> _stores = [
    SpecialtyStore(
      name: 'متجر الصحة الطبيعية',
      category: 'منتجات عضوية',
      address: 'شارع التحلية، الرياض',
      phone: '+966501234567',
      rating: 4.6,
      distance: '2.1 كم',
      isOpen: true,
      hasDelivery: true,
      specialties: ['خبز خالي من الجلوتين', 'معكرونة خاصة', 'حلويات صحية'],
      imageUrl: 'https://via.placeholder.com/150',
    ),
    SpecialtyStore(
      name: 'سوبر ماركت الحياة الصحية',
      category: 'سوبر ماركت متخصص',
      address: 'طريق الملك فهد، جدة',
      phone: '+966507654321',
      rating: 4.4,
      distance: '1.8 كم',
      isOpen: true,
      hasDelivery: true,
      specialties: ['منتجات خالية من الجلوتين', 'أطعمة عضوية', 'مكملات غذائية'],
      imageUrl: 'https://via.placeholder.com/150',
    ),
    SpecialtyStore(
      name: 'متجر الغذاء الآمن',
      category: 'متجر متخصص',
      address: 'حي الملز، الرياض',
      phone: '+966503456789',
      rating: 4.8,
      distance: '3.5 كم',
      isOpen: false,
      hasDelivery: true,
      specialties: ['منتجات السيلياك', 'أطعمة خاصة', 'استشارات غذائية'],
      imageUrl: 'https://via.placeholder.com/150',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final isAdmin = authProvider.userProfile?.role == 'admin';

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'المتاجر المتخصصة',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.purple.shade600,
        elevation: 0,
        actions: [
          if (isAdmin)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () {
                _showAddStoreDialog();
              },
            ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterOptions();
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.grey.shade50, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _stores.length,
          itemBuilder: (context, index) {
            final store = _stores[index];
            return _buildStoreCard(store);
          },
        ),
      ),
    );
  }

  Widget _buildStoreCard(SpecialtyStore store) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Store Header
          Container(
            height: 120,
            decoration: BoxDecoration(
              color: Colors.purple.shade100,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Center(
              child: Icon(Icons.store, size: 48, color: Colors.purple.shade600),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            store.name,
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            store.category,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.purple.shade600,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: store.isOpen
                                ? Colors.green.shade100
                                : Colors.red.shade100,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            store.isOpen ? 'مفتوح' : 'مغلق',
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: store.isOpen
                                  ? Colors.green.shade700
                                  : Colors.red.shade700,
                            ),
                          ),
                        ),
                        if (store.hasDelivery) ...[
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade100,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'توصيل',
                              style: GoogleFonts.cairo(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: Colors.blue.shade700,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(Icons.star, size: 16, color: Colors.amber),
                    const SizedBox(width: 4),
                    Text(
                      store.rating.toString(),
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '• ${store.distance}',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                _buildInfoRow(Icons.location_on, store.address),
                const SizedBox(height: 8),
                _buildInfoRow(Icons.phone, store.phone),
                const SizedBox(height: 12),
                Text(
                  'التخصصات:',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: [
                    ...store.specialties.map((specialty) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.purple.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.purple.shade200),
                        ),
                        child: Text(
                          specialty,
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Colors.purple.shade700,
                          ),
                        ),
                      );
                    }),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('الاتصال بـ ${store.phone}'),
                            ),
                          );
                        },
                        icon: const Icon(Icons.phone, size: 18),
                        label: Text(
                          'اتصال',
                          style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple.shade600,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          _showStoreDetails(store);
                        },
                        icon: const Icon(Icons.info, size: 18),
                        label: Text(
                          'التفاصيل',
                          style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                        ),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.purple.shade600,
                          side: BorderSide(color: Colors.purple.shade600),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () {
                        _showRatingDialog(store);
                      },
                      icon: Icon(Icons.star_border, color: Colors.amber),
                      tooltip: 'تقييم المتجر',
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey.shade600),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade700),
          ),
        ),
      ],
    );
  }

  void _showAddStoreDialog() {
    final nameController = TextEditingController();
    final categoryController = TextEditingController();
    final addressController = TextEditingController();
    final phoneController = TextEditingController();
    bool hasDelivery = false;
    List<String> selectedSpecialties = [];

    final availableSpecialties = [
      'خبز خالي من الجلوتين',
      'معكرونة خاصة',
      'حلويات صحية',
      'منتجات عضوية',
      'مكملات غذائية',
      'أطعمة خاصة',
      'استشارات غذائية',
    ];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            'إضافة متجر جديد',
            style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: InputDecoration(
                    labelText: 'اسم المتجر',
                    labelStyle: GoogleFonts.cairo(),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: categoryController,
                  decoration: InputDecoration(
                    labelText: 'فئة المتجر',
                    labelStyle: GoogleFonts.cairo(),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: addressController,
                  decoration: InputDecoration(
                    labelText: 'العنوان',
                    labelStyle: GoogleFonts.cairo(),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: phoneController,
                  decoration: InputDecoration(
                    labelText: 'رقم الهاتف',
                    labelStyle: GoogleFonts.cairo(),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                CheckboxListTile(
                  title: Text('خدمة التوصيل', style: GoogleFonts.cairo()),
                  value: hasDelivery,
                  onChanged: (value) {
                    setState(() {
                      hasDelivery = value!;
                    });
                  },
                ),
                const SizedBox(height: 12),
                Text(
                  'التخصصات:',
                  style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                ),
                ...availableSpecialties.map((specialty) {
                  return CheckboxListTile(
                    title: Text(
                      specialty,
                      style: GoogleFonts.cairo(fontSize: 14),
                    ),
                    value: selectedSpecialties.contains(specialty),
                    onChanged: (value) {
                      setState(() {
                        if (value!) {
                          selectedSpecialties.add(specialty);
                        } else {
                          selectedSpecialties.remove(specialty);
                        }
                      });
                    },
                  );
                }),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('إلغاء', style: GoogleFonts.cairo()),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.isNotEmpty &&
                    addressController.text.isNotEmpty &&
                    phoneController.text.isNotEmpty) {
                  setState(() {
                    _stores.add(
                      SpecialtyStore(
                        name: nameController.text,
                        category: categoryController.text.isNotEmpty
                            ? categoryController.text
                            : 'متجر متخصص',
                        address: addressController.text,
                        phone: phoneController.text,
                        rating: 4.0,
                        distance: '0.5 كم',
                        isOpen: true,
                        hasDelivery: hasDelivery,
                        specialties: selectedSpecialties.isNotEmpty
                            ? selectedSpecialties
                            : ['منتجات خالية من الجلوتين'],
                        imageUrl: 'https://via.placeholder.com/150',
                      ),
                    );
                  });
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم إضافة المتجر بنجاح')),
                  );
                }
              },
              child: Text('إضافة', style: GoogleFonts.cairo()),
            ),
          ],
        ),
      ),
    );
  }

  void _showFilterOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'خيارات التصفية',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.store),
              title: Text('جميع المتاجر', style: GoogleFonts.cairo()),
              onTap: () {
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.access_time),
              title: Text('مفتوح الآن', style: GoogleFonts.cairo()),
              onTap: () {
                Navigator.pop(context);
                // تصفية حسب المتاجر المفتوحة (مؤقتاً: رسالة تأكيد)
              },
            ),
            ListTile(
              leading: const Icon(Icons.delivery_dining),
              title: Text('يوفر التوصيل', style: GoogleFonts.cairo()),
              onTap: () {
                Navigator.pop(context);
                // تصفية حسب التوصيل (مؤقتاً: رسالة تأكيد)
              },
            ),
            ListTile(
              leading: const Icon(Icons.location_on),
              title: Text('الأقرب', style: GoogleFonts.cairo()),
              onTap: () {
                Navigator.pop(context);
                // ترتيب حسب المسافة (مؤقتاً: رسالة تأكيد)
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showStoreDetails(SpecialtyStore store) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          store.name,
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('الفئة', store.category),
              _buildDetailRow('العنوان', store.address),
              _buildDetailRow('الهاتف', store.phone),
              _buildDetailRow('التقييم', '${store.rating} ⭐'),
              _buildDetailRow('المسافة', store.distance),
              _buildDetailRow('الحالة', store.isOpen ? 'مفتوح' : 'مغلق'),
              _buildDetailRow(
                'التوصيل',
                store.hasDelivery ? 'متوفر' : 'غير متوفر',
              ),
              const SizedBox(height: 16),
              Text(
                'التخصصات:',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              ...store.specialties.map((specialty) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    children: [
                      Icon(Icons.check, size: 16, color: Colors.green),
                      const SizedBox(width: 8),
                      Text(specialty, style: GoogleFonts.cairo()),
                    ],
                  ),
                );
              }),
            ],
          ),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.cairo(color: Colors.grey.shade800),
            ),
          ),
        ],
      ),
    );
  }

  void _showRatingDialog(SpecialtyStore store) {
    int selectedRating = 5;
    final commentController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            'تقييم ${store.name}',
            style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('كيف كانت تجربتك؟', style: GoogleFonts.cairo(fontSize: 16)),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(5, (index) {
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        selectedRating = index + 1;
                      });
                    },
                    child: Icon(
                      index < selectedRating ? Icons.star : Icons.star_border,
                      color: Colors.amber,
                      size: 32,
                    ),
                  );
                }),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: commentController,
                decoration: InputDecoration(
                  labelText: 'تعليق (اختياري)',
                  labelStyle: GoogleFonts.cairo(),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                maxLines: 3,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('إلغاء', style: GoogleFonts.cairo()),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('شكراً لتقييمك! ($selectedRating نجوم)'),
                  ),
                );
              },
              child: Text('إرسال التقييم', style: GoogleFonts.cairo()),
            ),
          ],
        ),
      ),
    );
  }
}

class SpecialtyStore {
  final String name;
  final String category;
  final String address;
  final String phone;
  final double rating;
  final String distance;
  final bool isOpen;
  final bool hasDelivery;
  final List<String> specialties;
  final String imageUrl;

  SpecialtyStore({
    required this.name,
    required this.category,
    required this.address,
    required this.phone,
    required this.rating,
    required this.distance,
    required this.isOpen,
    required this.hasDelivery,
    required this.specialties,
    required this.imageUrl,
  });
}

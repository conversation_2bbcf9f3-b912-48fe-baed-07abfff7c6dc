import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../providers/recipe_provider.dart';
import '../../models/recipe.dart';
import '../../utils/app_colors.dart';
import '../../utils/error_handler.dart';
import '../../widgets/loading_widget.dart' hide EmptyStateWidget;
import '../../widgets/empty_state_widget.dart';
import '../recipes/add_edit_recipe_screen.dart';
import '../recipes/recipe_detail_screen.dart';

class RecipesManagementScreen extends StatefulWidget {
  const RecipesManagementScreen({super.key});

  @override
  State<RecipesManagementScreen> createState() =>
      _RecipesManagementScreenState();
}

class _RecipesManagementScreenState extends State<RecipesManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadRecipes();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadRecipes() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final recipeProvider = Provider.of<RecipeProvider>(
        context,
        listen: false,
      );
      await recipeProvider.fetchInitialRecipes();
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(context, e);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildTabBar(),
          Expanded(
            child: _isLoading
                ? const LoadingWidget(message: 'جاري تحميل الوصفات...')
                : _buildTabBarView(),
          ),
        ],
      ),
      floatingActionButton: _buildAddRecipeFAB(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'إدارة الوصفات',
        style: GoogleFonts.cairo(fontWeight: FontWeight.bold, fontSize: 20),
      ),
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadRecipes,
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث في الوصفات...',
                hintStyle: GoogleFonts.cairo(color: Colors.grey.shade600),
                prefixIcon: Icon(Icons.search, color: Colors.grey.shade600),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey.shade100,
              ),
              style: GoogleFonts.cairo(),
              onChanged: (value) {
                setState(() {});
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: Colors.grey.shade600,
        labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        indicatorColor: AppColors.primary,
        tabs: const [
          Tab(text: 'الكل'),
          Tab(text: 'قيد المراجعة'),
          Tab(text: 'المميزة'),
          Tab(text: 'المرفوضة'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildRecipesList('all'),
        _buildRecipesList('pending'),
        _buildRecipesList('featured'),
        _buildRecipesList('rejected'),
      ],
    );
  }

  Widget _buildRecipesList(String type) {
    return Consumer<RecipeProvider>(
      builder: (context, recipeProvider, child) {
        final recipes = _getFilteredRecipes(recipeProvider.recipes, type);

        if (recipes.isEmpty) {
          return EmptyStateWidget(
            icon: Icons.menu_book,
            title: 'لا توجد وصفات',
            subtitle: _getEmptyMessage(type),
          );
        }

        return RefreshIndicator(
          onRefresh: _loadRecipes,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: recipes.length,
            itemBuilder: (context, index) {
              final recipe = recipes[index];
              return _buildRecipeManagementCard(recipe);
            },
          ),
        );
      },
    );
  }

  List<Recipe> _getFilteredRecipes(List<Recipe> recipes, String type) {
    var filtered = recipes.where((recipe) {
      if (_searchController.text.isNotEmpty) {
        final query = _searchController.text.toLowerCase();
        if (!recipe.title.toLowerCase().contains(query)) {
          return false;
        }
      }

      switch (type) {
        case 'pending':
          return !recipe.isApproved;
        case 'featured':
          return recipe.isFeatured;
        case 'rejected':
          return false; // Add logic for rejected recipes if available
        default:
          return true;
      }
    }).toList();

    return filtered;
  }

  String _getEmptyMessage(String type) {
    switch (type) {
      case 'pending':
        return 'لا توجد وصفات في انتظار المراجعة';
      case 'featured':
        return 'لا توجد وصفات مميزة';
      case 'rejected':
        return 'لا توجد وصفات مرفوضة';
      default:
        return 'لم يتم العثور على وصفات';
    }
  }

  Widget _buildRecipeManagementCard(Recipe recipe) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: () => _viewRecipe(recipe),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              CachedNetworkImage(
                imageUrl: recipe.imageUrls.isNotEmpty
                    ? recipe.imageUrls.first
                    : '',
                imageBuilder: (context, imageProvider) => Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    image: DecorationImage(
                      image: imageProvider,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                placeholder: (context, url) => Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Center(child: CircularProgressIndicator()),
                ),
                errorWidget: (context, url, error) => Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.restaurant_menu, color: Colors.grey),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      recipe.title,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      recipe.category,
                      style: GoogleFonts.cairo(color: Colors.grey.shade600),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        _buildStatusBadge(recipe),
                        if (recipe.isFeatured)
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 16,
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) {
                  if (value == 'edit') {
                    _editRecipe(recipe);
                  } else if (value == 'delete') {
                    _deleteRecipe(recipe);
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'edit', child: Text('تعديل')),
                  const PopupMenuItem(value: 'delete', child: Text('حذف')),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(Recipe recipe) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: recipe.isApproved ? Colors.green : Colors.orange,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        recipe.isApproved ? 'معتمد' : 'قيد المراجعة',
        style: GoogleFonts.cairo(color: Colors.white, fontSize: 10),
      ),
    );
  }

  Widget _buildAddRecipeFAB() {
    return FloatingActionButton.extended(
      onPressed: _showAddRecipeDialog,
      label: Text('إضافة وصفة', style: GoogleFonts.cairo()),
      icon: const Icon(Icons.add),
      backgroundColor: AppColors.primary,
    );
  }

  void _viewRecipe(Recipe recipe) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RecipeDetailScreen(recipe: recipe),
      ),
    );
  }

  void _editRecipe(Recipe recipe) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditRecipeScreen(recipe: recipe),
      ),
    );
  }

  void _deleteRecipe(Recipe recipe) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف الوصفة', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف هذه الوصفة؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () async {
              try {
                await Provider.of<RecipeProvider>(
                  context,
                  listen: false,
                ).deleteRecipe(recipe.id!);
                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              } catch (e) {
                if (context.mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ أثناء حذف الوصفة'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showAddRecipeDialog() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddEditRecipeScreen()),
    );
  }
}

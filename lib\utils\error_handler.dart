import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'app_colors.dart';

class ErrorHandler {
  /// معالجة أخطاء Firebase Auth
  static String getAuthErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'لا يوجد مستخدم مسجل بهذا البريد الإلكتروني';
      case 'wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'email-already-in-use':
        return 'هذا البريد الإلكتروني مستخدم بالفعل';
      case 'weak-password':
        return 'كلمة المرور ضعيفة جداً';
      case 'invalid-email':
        return 'البريد الإلكتروني غير صالح';
      case 'user-disabled':
        return 'تم تعطيل هذا الحساب';
      case 'too-many-requests':
        return 'تم تجاوز عدد المحاولات المسموح. حاول مرة أخرى لاحقاً';
      case 'operation-not-allowed':
        return 'هذه العملية غير مسموحة';
      case 'invalid-credential':
        return 'بيانات الاعتماد غير صالحة';
      case 'account-exists-with-different-credential':
        return 'يوجد حساب بنفس البريد الإلكتروني بطريقة تسجيل دخول مختلفة';
      case 'requires-recent-login':
        return 'يتطلب تسجيل دخول حديث لإجراء هذه العملية';
      case 'network-request-failed':
        return 'فشل في الاتصال بالشبكة. تحقق من اتصالك بالإنترنت';
      default:
        return 'حدث خطأ غير متوقع: ${e.message}';
    }
  }

  /// معالجة أخطاء Firestore
  static String getFirestoreErrorMessage(FirebaseException e) {
    switch (e.code) {
      case 'permission-denied':
        return 'ليس لديك صلاحية للوصول إلى هذه البيانات';
      case 'not-found':
        return 'البيانات المطلوبة غير موجودة';
      case 'already-exists':
        return 'البيانات موجودة بالفعل';
      case 'resource-exhausted':
        return 'تم تجاوز الحد المسموح للعمليات';
      case 'failed-precondition':
        return 'لا يمكن تنفيذ العملية في الوقت الحالي';
      case 'aborted':
        return 'تم إلغاء العملية بسبب تضارب';
      case 'out-of-range':
        return 'البيانات خارج النطاق المسموح';
      case 'unimplemented':
        return 'هذه الميزة غير متاحة حالياً';
      case 'internal':
        return 'خطأ داخلي في الخادم';
      case 'unavailable':
        return 'الخدمة غير متاحة حالياً. حاول مرة أخرى لاحقاً';
      case 'data-loss':
        return 'فقدان في البيانات';
      case 'unauthenticated':
        return 'يجب تسجيل الدخول أولاً';
      case 'deadline-exceeded':
        return 'انتهت مهلة العملية. حاول مرة أخرى';
      case 'cancelled':
        return 'تم إلغاء العملية';
      default:
        return 'حدث خطأ في قاعدة البيانات: ${e.message}';
    }
  }

  /// معالجة الأخطاء العامة
  static String getGeneralErrorMessage(dynamic error) {
    if (error is FirebaseAuthException) {
      return getAuthErrorMessage(error);
    } else if (error is FirebaseException) {
      return getFirestoreErrorMessage(error);
    } else if (error is Exception) {
      return error.toString().replaceFirst('Exception: ', '');
    } else {
      return 'حدث خطأ غير متوقع';
    }
  }

  /// عرض رسالة خطأ في SnackBar
  static void showErrorSnackBar(BuildContext context, dynamic error) {
    final message = getGeneralErrorMessage(error);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.cairo(color: Colors.white, fontSize: 14),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        action: SnackBarAction(
          label: 'إغلاق',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// عرض رسالة نجاح في SnackBar
  static void showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle_outline, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.cairo(color: Colors.white, fontSize: 14),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// عرض رسالة تحذير في SnackBar
  static void showWarningSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.warning_outlined, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.cairo(color: Colors.white, fontSize: 14),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange.shade600,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// عرض نافذة خطأ
  static void showErrorDialog(
    BuildContext context,
    dynamic error, {
    String? title,
  }) {
    final message = getGeneralErrorMessage(error);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.error_outline, color: AppColors.error, size: 24),
            const SizedBox(width: 8),
            Text(
              title ?? 'خطأ',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: GoogleFonts.cairo(
            fontSize: 14,
            color: AppColors.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'حسناً',
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// معالجة الأخطاء مع إعادة المحاولة
  static Future<T?> handleWithRetry<T>(
    BuildContext context,
    Future<T> Function() operation, {
    int maxRetries = 3,
    String? errorTitle,
  }) async {
    int attempts = 0;

    while (attempts < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempts++;

        if (attempts >= maxRetries) {
          if (context.mounted) {
            showErrorDialog(context, e, title: errorTitle);
          }
          return null;
        }

        // انتظار قبل إعادة المحاولة
        await Future.delayed(Duration(seconds: attempts));
      }
    }

    return null;
  }

  /// تسجيل الأخطاء للمطورين
  static void logError(
    dynamic error, {
    String? context,
    StackTrace? stackTrace,
  }) {
    debugPrint('=== خطأ في التطبيق ===');
    debugPrint('السياق: ${context ?? 'غير محدد'}');
    debugPrint('الخطأ: $error');
    if (stackTrace != null) {
      debugPrint('تتبع المكدس: $stackTrace');
    }
    debugPrint('========================');
  }
}

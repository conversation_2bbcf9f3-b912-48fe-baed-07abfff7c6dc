import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:yassincil/models/notification.dart' as app_notification;
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/services/notification_service.dart';
import 'package:yassincil/utils/app_colors.dart';

class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final user = authProvider.currentUser;

    return Scaffold(
      appBar: AppBar(
        title: Text('الإشعارات', style: GoogleFonts.cairo()),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.done_all),
            onPressed: () {
              if (user != null) {
                NotificationService.markAllAsRead(user.uid);
              }
            },
            tooltip: 'وضع علامة على الكل كمقروء',
          ),
        ],
      ),
      body: user == null
          ? const Center(child: Text('يرجى تسجيل الدخول لعرض الإشعارات'))
          : StreamBuilder<List<app_notification.Notification>>(
              stream: NotificationService.getUserNotifications(user.uid),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.notifications_none,
                          size: 80,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 20),
                        Text(
                          'لا توجد إشعارات حالياً.',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                final notifications = snapshot.data!;
                return ListView.builder(
                  itemCount: notifications.length,
                  itemBuilder: (context, index) {
                    final notification = notifications[index];
                    return InkWell(
                      onTap: () {
                        if (notification.id != null) {
                          NotificationService.markAsRead(
                            user.uid,
                            notification.id!,
                          );
                        }
                        // TODO: Navigate to the target screen
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16.0,
                          vertical: 12.0,
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CircleAvatar(
                              backgroundImage: notification.senderAvatar != null
                                  ? NetworkImage(notification.senderAvatar!)
                                  : null,
                              child: notification.senderAvatar == null
                                  ? const Icon(Icons.person)
                                  : null,
                            ),
                            const SizedBox(width: 12.0),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _buildNotificationTitle(notification),
                                    style: GoogleFonts.cairo(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4.0),
                                  Text(
                                    timeago.format(
                                      notification.createdAt,
                                      locale: 'ar',
                                    ),
                                    style: GoogleFonts.cairo(
                                      color: Colors.grey[600],
                                      fontSize: 12.0,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (!notification.isRead)
                              const Padding(
                                padding: EdgeInsets.only(left: 8.0),
                                child: Icon(
                                  Icons.circle,
                                  color: Colors.blue,
                                  size: 12,
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
    );
  }

  String _buildNotificationTitle(app_notification.Notification notification) {
    switch (notification.type) {
      case app_notification.NotificationType.like:
        return '${notification.senderName} أعجب بمنشورك.';
      case app_notification.NotificationType.comment:
        return '${notification.senderName} علق على منشورك.';
      case app_notification.NotificationType.reply:
        return '${notification.senderName} رد على تعليقك.';
      case app_notification.NotificationType.follow:
        return '${notification.senderName} بدأ بمتابعتك.';
      case app_notification.NotificationType.mention:
        return '${notification.senderName} أشار إليك في تعليق.';
      default:
        return 'إشعار جديد';
    }
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';

class Product {
  final String? id;
  final String barcode;
  final String name;
  final String brand;
  final String description;
  final List<String> ingredients;
  final bool? isGlutenFree;
  final String? imageUrl;
  final String category;
  final double? rating;
  final int reviewCount;
  final DateTime? addedAt;
  final String? addedBy; // معرف المستخدم الذي أضاف المنتج
  final List<String> allergens; // مسببات الحساسية
  final Map<String, dynamic>? nutritionFacts; // القيم الغذائية
  final bool isVerified; // هل تم التحقق من المنتج من قبل المشرفين
  final List<String> certifications; // الشهادات (مثل شهادة خالي من الغلوتين)

  Product({
    this.id,
    required this.barcode,
    required this.name,
    required this.brand,
    this.description = '',
    this.ingredients = const [],
    this.isGlutenFree,
    this.imageUrl,
    this.category = '',
    this.rating,
    this.reviewCount = 0,
    this.addedAt,
    this.addedBy,
    this.allergens = const [],
    this.nutritionFacts,
    this.isVerified = false,
    this.certifications = const [],
  });

  Map<String, dynamic> toMap() {
    return {
      'barcode': barcode,
      'name': name,
      'brand': brand,
      'description': description,
      'ingredients': ingredients,
      'isGlutenFree': isGlutenFree,
      'imageUrl': imageUrl,
      'category': category,
      'rating': rating,
      'reviewCount': reviewCount,
      'addedAt': addedAt != null ? Timestamp.fromDate(addedAt!) : FieldValue.serverTimestamp(),
      'addedBy': addedBy,
      'allergens': allergens,
      'nutritionFacts': nutritionFacts,
      'isVerified': isVerified,
      'certifications': certifications,
    };
  }

  factory Product.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Product(
      id: doc.id,
      barcode: data['barcode'] ?? '',
      name: data['name'] ?? '',
      brand: data['brand'] ?? '',
      description: data['description'] ?? '',
      ingredients: List<String>.from(data['ingredients'] ?? []),
      isGlutenFree: data['isGlutenFree'],
      imageUrl: data['imageUrl'],
      category: data['category'] ?? '',
      rating: (data['rating'] is int) 
          ? (data['rating'] as int).toDouble() 
          : (data['rating'] is double) 
              ? data['rating'] 
              : null,
      reviewCount: data['reviewCount'] ?? 0,
      addedAt: data['addedAt'] is Timestamp 
          ? (data['addedAt'] as Timestamp).toDate() 
          : null,
      addedBy: data['addedBy'],
      allergens: List<String>.from(data['allergens'] ?? []),
      nutritionFacts: data['nutritionFacts'],
      isVerified: data['isVerified'] ?? false,
      certifications: List<String>.from(data['certifications'] ?? []),
    );
  }

  Product copyWith({
    String? id,
    String? barcode,
    String? name,
    String? brand,
    String? description,
    List<String>? ingredients,
    bool? isGlutenFree,
    String? imageUrl,
    String? category,
    double? rating,
    int? reviewCount,
    DateTime? addedAt,
    String? addedBy,
    List<String>? allergens,
    Map<String, dynamic>? nutritionFacts,
    bool? isVerified,
    List<String>? certifications,
  }) {
    return Product(
      id: id ?? this.id,
      barcode: barcode ?? this.barcode,
      name: name ?? this.name,
      brand: brand ?? this.brand,
      description: description ?? this.description,
      ingredients: ingredients ?? this.ingredients,
      isGlutenFree: isGlutenFree ?? this.isGlutenFree,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      addedAt: addedAt ?? this.addedAt,
      addedBy: addedBy ?? this.addedBy,
      allergens: allergens ?? this.allergens,
      nutritionFacts: nutritionFacts ?? this.nutritionFacts,
      isVerified: isVerified ?? this.isVerified,
      certifications: certifications ?? this.certifications,
    );
  }

  /// التحقق من وجود مسبب حساسية معين
  bool hasAllergen(String allergen) {
    return allergens.any((a) => a.toLowerCase().contains(allergen.toLowerCase()));
  }

  /// الحصول على نص حالة الغلوتين
  String get glutenStatus {
    if (isGlutenFree == true) {
      return 'خالي من الغلوتين';
    } else if (isGlutenFree == false) {
      return 'يحتوي على غلوتين';
    } else {
      return 'غير محدد';
    }
  }

  /// الحصول على لون حالة الغلوتين
  String get glutenStatusColor {
    if (isGlutenFree == true) {
      return 'green';
    } else if (isGlutenFree == false) {
      return 'red';
    } else {
      return 'orange';
    }
  }

  /// التحقق من وجود شهادة معينة
  bool hasCertification(String certification) {
    return certifications.any((c) => c.toLowerCase().contains(certification.toLowerCase()));
  }

  /// الحصول على نص التقييم
  String get ratingText {
    if (rating == null) return 'غير مقيم';
    return '${rating!.toStringAsFixed(1)} من 5';
  }

  /// التحقق من أن المنتج آمن لمرضى السيلياك
  bool get isSafeForCeliac {
    // إذا كان معتمد كخالي من الغلوتين
    if (isGlutenFree == true) return true;
    
    // إذا كان يحتوي على غلوتين بوضوح
    if (isGlutenFree == false) return false;
    
    // فحص المكونات للكلمات المفتاحية الخطيرة
    final dangerousIngredients = [
      'قمح', 'شعير', 'شوفان', 'جاودار', 'حنطة',
      'wheat', 'barley', 'oats', 'rye', 'spelt',
      'gluten', 'غلوتين'
    ];
    
    for (String ingredient in ingredients) {
      for (String dangerous in dangerousIngredients) {
        if (ingredient.toLowerCase().contains(dangerous.toLowerCase())) {
          return false;
        }
      }
    }
    
    return true; // افتراض الأمان إذا لم توجد مكونات خطيرة
  }

  @override
  String toString() {
    return 'Product{id: $id, name: $name, brand: $brand, barcode: $barcode, isGlutenFree: $isGlutenFree}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Product &&
          runtimeType == other.runtimeType &&
          barcode == other.barcode;

  @override
  int get hashCode => barcode.hashCode;
}

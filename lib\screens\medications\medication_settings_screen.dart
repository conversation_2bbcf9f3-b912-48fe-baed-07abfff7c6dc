import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/widgets/medications_app_bar.dart';
import 'package:yassincil/providers/auth_provider.dart';

class MedicationSettingsScreen extends StatefulWidget {
  const MedicationSettingsScreen({super.key});

  @override
  State<MedicationSettingsScreen> createState() =>
      _MedicationSettingsScreenState();
}

class _MedicationSettingsScreenState extends State<MedicationSettingsScreen> {
  bool _allowUserMedicationSubmission = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _allowUserMedicationSubmission =
          prefs.getBool('allowUserMedicationSubmission') ?? true;
    });
  }

  Future<void> _updateSetting(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('allowUserMedicationSubmission', value);
    setState(() {
      _allowUserMedicationSubmission = value;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isAdmin = Provider.of<AuthProvider>(context).isAdmin;

    if (!isAdmin) {
      return Scaffold(
        appBar: const MedicationsAppBar(title: 'إعدادات الأدوية'),
        body: Center(
          child: Text(
            'ليست لديك صلاحية للوصول إلى هذه الصفحة',
            style: GoogleFonts.cairo(fontSize: 16),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: const MedicationsAppBar(title: 'إعدادات الأدوية'),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [_buildSettingsCard()],
      ),
    );
  }

  Widget _buildSettingsCard() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات عامة',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: Text(
                'السماح للمستخدمين بإضافة أدوية',
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
              subtitle: Text(
                'في حالة التفعيل، يمكن للمستخدمين إضافة أدوية جديدة للمراجعة',
                style: GoogleFonts.cairo(),
              ),
              value: _allowUserMedicationSubmission,
              onChanged: _updateSetting,
              activeColor: AppColors.primary,
            ),
          ],
        ),
      ),
    );
  }
}

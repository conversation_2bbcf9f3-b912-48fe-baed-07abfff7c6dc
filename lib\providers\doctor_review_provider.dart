import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../models/doctor_review.dart';
import '../services/firestore_service.dart';
import '../utils/app_constants.dart';

class DoctorReviewProvider extends ChangeNotifier {
  final FirestoreService _firestoreService;

  List<DoctorReview> _reviews = [];
  Map<String, List<DoctorReview>> _doctorReviews = {};
  bool _isLoading = false;
  String? _errorMessage;

  DoctorReviewProvider(this._firestoreService);

  // Getters
  List<DoctorReview> get reviews => _reviews;
  Map<String, List<DoctorReview>> get doctorReviews => _doctorReviews;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Get reviews for specific doctor
  List<DoctorReview> getReviewsForDoctor(String doctorId) {
    return _doctorReviews[doctorId] ?? [];
  }

  // Get average rating for doctor
  double getAverageRating(String doctorId) {
    final doctorReviewsList = getReviewsForDoctor(doctorId);
    if (doctorReviewsList.isEmpty) return 0.0;

    final totalRating = doctorReviewsList.fold<double>(
      0,
      (total, review) => total + review.rating,
    );
    return totalRating / doctorReviewsList.length;
  }

  // Get rating distribution for doctor
  Map<int, int> getRatingDistribution(String doctorId) {
    final doctorReviewsList = getReviewsForDoctor(doctorId);
    final distribution = <int, int>{1: 0, 2: 0, 3: 0, 4: 0, 5: 0};

    for (final review in doctorReviewsList) {
      final rating = review.rating.round();
      distribution[rating] = (distribution[rating] ?? 0) + 1;
    }

    return distribution;
  }

  // Load reviews for specific doctor
  Future<void> loadDoctorReviews(String doctorId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.doctorReviewsCollection)
          .where('doctorId', isEqualTo: doctorId)
          .where('isApproved', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      final reviews = querySnapshot.docs
          .map((doc) => DoctorReview.fromFirestore(doc))
          .toList();

      _doctorReviews[doctorId] = reviews;
    } catch (e) {
      _errorMessage = 'خطأ في تحميل التقييمات: $e';
      debugPrint('Error loading doctor reviews: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load all reviews (admin only)
  Future<void> loadAllReviews() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.doctorReviewsCollection)
          .orderBy('createdAt', descending: true)
          .get();

      _reviews = querySnapshot.docs
          .map((doc) => DoctorReview.fromFirestore(doc))
          .toList();

      // Group reviews by doctor
      _doctorReviews.clear();
      for (final review in _reviews) {
        if (!_doctorReviews.containsKey(review.doctorId)) {
          _doctorReviews[review.doctorId] = [];
        }
        _doctorReviews[review.doctorId]!.add(review);
      }
    } catch (e) {
      _errorMessage = 'خطأ في تحميل التقييمات: $e';
      debugPrint('Error loading all reviews: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Add new review
  Future<void> addReview({
    required String doctorId,
    required String userId,
    required String userName,
    String? userAvatar,
    required double rating,
    required String comment,
    List<String> tags = const [],
    String? appointmentId,
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final review = DoctorReview(
        doctorId: doctorId,
        userId: userId,
        userName: userName,
        userAvatar: userAvatar,
        rating: rating,
        comment: comment,
        tags: tags,
        appointmentId: appointmentId,
        createdAt: DateTime.now(),
      );

      await _firestoreService.addDocument(
        AppConstants.doctorReviewsCollection,
        review.toMap(),
      );

      // Reload reviews for this doctor
      await loadDoctorReviews(doctorId);
    } catch (e) {
      _errorMessage = 'خطأ في إضافة التقييم: $e';
      debugPrint('Error adding review: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update review
  Future<void> updateReview(String reviewId, DoctorReview updatedReview) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await _firestoreService.updateDocument(
        AppConstants.doctorReviewsCollection,
        reviewId,
        updatedReview.copyWith(updatedAt: DateTime.now()).toMap(),
      );

      // Reload reviews for this doctor
      await loadDoctorReviews(updatedReview.doctorId);
    } catch (e) {
      _errorMessage = 'خطأ في تحديث التقييم: $e';
      debugPrint('Error updating review: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Delete review
  Future<void> deleteReview(String reviewId, String doctorId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await _firestoreService.deleteDocument(
        AppConstants.doctorReviewsCollection,
        reviewId,
      );

      // Reload reviews for this doctor
      await loadDoctorReviews(doctorId);
    } catch (e) {
      _errorMessage = 'خطأ في حذف التقييم: $e';
      debugPrint('Error deleting review: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Like/Unlike review
  Future<void> toggleReviewLike(String reviewId, String userId) async {
    try {
      final review = _reviews.firstWhere((r) => r.id == reviewId);
      final isLiked = review.isLikedBy(userId);
      final isDisliked = review.isDislikedBy(userId);

      List<String> newLikedBy = List.from(review.likedBy);
      List<String> newDislikedBy = List.from(review.dislikedBy);

      if (isLiked) {
        // Remove like
        newLikedBy.remove(userId);
      } else {
        // Add like and remove dislike if exists
        newLikedBy.add(userId);
        if (isDisliked) {
          newDislikedBy.remove(userId);
        }
      }

      final updatedReview = review.copyWith(
        likedBy: newLikedBy,
        dislikedBy: newDislikedBy,
        likesCount: newLikedBy.length,
        dislikesCount: newDislikedBy.length,
        updatedAt: DateTime.now(),
      );

      await updateReview(reviewId, updatedReview);
    } catch (e) {
      _errorMessage = 'خطأ في تحديث الإعجاب: $e';
      debugPrint('Error toggling review like: $e');
    }
  }

  // Dislike/Undislike review
  Future<void> toggleReviewDislike(String reviewId, String userId) async {
    try {
      final review = _reviews.firstWhere((r) => r.id == reviewId);
      final isLiked = review.isLikedBy(userId);
      final isDisliked = review.isDislikedBy(userId);

      List<String> newLikedBy = List.from(review.likedBy);
      List<String> newDislikedBy = List.from(review.dislikedBy);

      if (isDisliked) {
        // Remove dislike
        newDislikedBy.remove(userId);
      } else {
        // Add dislike and remove like if exists
        newDislikedBy.add(userId);
        if (isLiked) {
          newLikedBy.remove(userId);
        }
      }

      final updatedReview = review.copyWith(
        likedBy: newLikedBy,
        dislikedBy: newDislikedBy,
        likesCount: newLikedBy.length,
        dislikesCount: newDislikedBy.length,
        updatedAt: DateTime.now(),
      );

      await updateReview(reviewId, updatedReview);
    } catch (e) {
      _errorMessage = 'خطأ في تحديث عدم الإعجاب: $e';
      debugPrint('Error toggling review dislike: $e');
    }
  }

  // Approve/Disapprove review (admin only)
  Future<void> toggleReviewApproval(String reviewId) async {
    try {
      final review = _reviews.firstWhere((r) => r.id == reviewId);
      final updatedReview = review.copyWith(
        isApproved: !review.isApproved,
        updatedAt: DateTime.now(),
      );

      await updateReview(reviewId, updatedReview);
    } catch (e) {
      _errorMessage = 'خطأ في تحديث حالة الموافقة: $e';
      debugPrint('Error toggling review approval: $e');
    }
  }

  // Get reviews statistics
  Map<String, dynamic> getReviewsStatistics() {
    final totalReviews = _reviews.length;
    final approvedReviews = _reviews.where((r) => r.isApproved).length;
    final averageRating = _reviews.isEmpty
        ? 0.0
        : _reviews.fold<double>(0, (total, r) => total + r.rating) /
              totalReviews;

    final ratingDistribution = <int, int>{1: 0, 2: 0, 3: 0, 4: 0, 5: 0};
    for (final review in _reviews) {
      final rating = review.rating.round();
      ratingDistribution[rating] = (ratingDistribution[rating] ?? 0) + 1;
    }

    return {
      'totalReviews': totalReviews,
      'approvedReviews': approvedReviews,
      'averageRating': averageRating,
      'ratingDistribution': ratingDistribution,
    };
  }

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Reset provider
  void reset() {
    _reviews = [];
    _doctorReviews = {};
    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }
}

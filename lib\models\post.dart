import 'package:cloud_firestore/cloud_firestore.dart';

class Post {
  final String? id;
  final String title;
  final String content;
  final String userId;
  final String username;
  final DateTime createdAt;
  final String? imageUrl; // Optional image for the post
  final int commentCount; // Denormalized count for quick display
  final List<String> likes; // قائمة معرفات المستخدمين الذين أعجبوا
  final int likesCount;

  Post({
    this.id,
    required this.title,
    required this.content,
    required this.userId,
    required this.username,
    required this.createdAt,
    this.imageUrl,
    this.commentCount = 0,
    this.likes = const [],
    this.likesCount = 0,
  });

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'content': content,
      'userId': userId,
      'username': username,
      'createdAt': Timestamp.fromDate(createdAt),
      'imageUrl': imageUrl,
      'commentCount': commentCount,
      'likes': likes,
      'likesCount': likesCount,
    };
  }

  factory Post.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Post(
      id: doc.id,
      title: data['title'] ?? '',
      content: data['content'] ?? '',
      userId: data['userId'] ?? '',
      username: data['username'] ?? 'مستخدم',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      imageUrl: data['imageUrl'],
      commentCount: data['commentCount'] ?? 0,
      likes: List<String>.from(data['likes'] ?? []),
      likesCount: data['likesCount'] ?? 0,
    );
  }
}

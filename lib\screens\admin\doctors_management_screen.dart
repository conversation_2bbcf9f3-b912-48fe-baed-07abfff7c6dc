import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../providers/doctor_provider.dart';
import '../../providers/doctor_permission_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/doctor.dart';
import '../../models/doctor_permission.dart';
import '../../utils/app_colors.dart';
import '../../utils/error_handler.dart';
import '../../widgets/loading_widget.dart' hide EmptyStateWidget;
import '../../widgets/empty_state_widget.dart';
import '../doctors/add_edit_doctor_screen.dart';
import '../doctors/doctor_detail_screen.dart';

class DoctorsManagementScreen extends StatefulWidget {
  const DoctorsManagementScreen({super.key});

  @override
  State<DoctorsManagementScreen> createState() =>
      _DoctorsManagementScreenState();
}

class _DoctorsManagementScreenState extends State<DoctorsManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late TextEditingController _searchController;
  String _searchQuery = '';
  String _selectedSpecialty = 'الكل';
  String _selectedLocation = 'الكل';
  double _minRating = 0.0;
  double _maxFee = 1000.0;
  bool _showOnlyVerified = false;
  bool _showOnlyAvailable = false;

  final List<String> _specialties = [
    'الكل',
    'أمراض الجهاز الهضمي',
    'التغذية العلاجية',
    'الطب الباطني',
    'طب الأطفال',
    'الجراحة العامة',
    'أمراض النساء والولادة',
    'طب الأسرة',
    'الطب النفسي',
    'أمراض القلب',
    'الأمراض الجلدية',
    'طب العيون',
    'أنف وأذن وحنجرة',
    'العظام والمفاصل',
    'المسالك البولية',
    'الأشعة التشخيصية',
    'التخدير والعناية المركزة',
    'طب الطوارئ',
    'أخرى',
  ];

  final List<String> _locations = [
    'الكل',
    'الرياض',
    'جدة',
    'مكة المكرمة',
    'المدينة المنورة',
    'الدمام',
    'الخبر',
    'الظهران',
    'تبوك',
    'بريدة',
    'خميس مشيط',
    'حائل',
    'نجران',
    'جازان',
    'ينبع',
    'الطائف',
    'الأحساء',
    'القطيف',
    'أبها',
    'سكاكا',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _searchController = TextEditingController();
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _loadData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final doctorProvider = Provider.of<DoctorProvider>(
        context,
        listen: false,
      );
      final permissionProvider = Provider.of<DoctorPermissionProvider>(
        context,
        listen: false,
      );
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      doctorProvider.fetchDoctors();

      if (authProvider.currentUser != null) {
        permissionProvider.loadCurrentUserPermissions(
          authProvider.currentUser!.uid,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: Consumer3<DoctorProvider, DoctorPermissionProvider, AuthProvider>(
        builder:
            (context, doctorProvider, permissionProvider, authProvider, child) {
              if (!permissionProvider.hasPermission(
                DoctorPermissionType.view,
              )) {
                return const Center(
                  child: Text(
                    'ليس لديك صلاحية لعرض هذه الصفحة',
                    style: TextStyle(fontSize: 16),
                  ),
                );
              }

              if (doctorProvider.isLoading) {
                return const LoadingWidget();
              }

              if (doctorProvider.errorMessage != null) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        doctorProvider.errorMessage!,
                        style: GoogleFonts.cairo(fontSize: 16),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => doctorProvider.fetchDoctors(),
                        child: Text(
                          'إعادة المحاولة',
                          style: GoogleFonts.cairo(),
                        ),
                      ),
                    ],
                  ),
                );
              }

              return Column(
                children: [
                  _buildSearchAndFilters(),
                  _buildTabBar(),
                  Expanded(
                    child: _buildTabBarView(doctorProvider, permissionProvider),
                  ),
                ],
              );
            },
      ),
      floatingActionButton: Consumer<DoctorPermissionProvider>(
        builder: (context, permissionProvider, child) {
          if (!permissionProvider.hasPermission(DoctorPermissionType.add)) {
            return const SizedBox.shrink();
          }

          return FloatingActionButton.extended(
            onPressed: () => _navigateToAddDoctor(),
            backgroundColor: AppColors.primary,
            icon: const Icon(Icons.add, color: Colors.white),
            label: Text(
              'إضافة طبيب',
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'إدارة الأطباء',
        style: GoogleFonts.cairo(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: AppColors.primary,
      elevation: 0,
      actions: [
        Consumer<DoctorPermissionProvider>(
          builder: (context, permissionProvider, child) {
            if (!permissionProvider.hasPermission(
              DoctorPermissionType.viewStatistics,
            )) {
              return const SizedBox.shrink();
            }

            return IconButton(
              icon: const Icon(Icons.analytics, color: Colors.white),
              onPressed: () => _showStatistics(),
              tooltip: 'الإحصائيات',
            );
          },
        ),
        Consumer<DoctorPermissionProvider>(
          builder: (context, permissionProvider, child) {
            if (!permissionProvider.hasPermission(
              DoctorPermissionType.exportData,
            )) {
              return const SizedBox.shrink();
            }

            return IconButton(
              icon: const Icon(Icons.download, color: Colors.white),
              onPressed: () => _exportData(),
              tooltip: 'تصدير البيانات',
            );
          },
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onSelected: (value) => _handleMenuAction(value),
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'refresh',
              child: Row(
                children: [
                  const Icon(Icons.refresh, size: 20),
                  const SizedBox(width: 8),
                  Text('تحديث', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  const Icon(Icons.settings, size: 20),
                  const SizedBox(width: 8),
                  Text('الإعدادات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _navigateToAddDoctor() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AddEditDoctorScreen()),
    );
  }

  void _showStatistics() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('الإحصائيات قيد التطوير', style: GoogleFonts.cairo()),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _exportData() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تصدير البيانات قيد التطوير', style: GoogleFonts.cairo()),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'refresh':
        Provider.of<DoctorProvider>(context, listen: false).fetchDoctors();
        break;
      case 'settings':
        break;
    }
  }

  Widget _buildSearchAndFilters() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث عن طبيب...',
              hintStyle: GoogleFonts.cairo(color: Colors.grey.shade600),
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary),
              ),
              filled: true,
              fillColor: Colors.grey.shade50,
            ),
            style: GoogleFonts.cairo(),
            onChanged: (value) => setState(() => _searchQuery = value),
          ),
          const SizedBox(height: 12),
          // الفلاتر
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(
                  'التخصص',
                  _selectedSpecialty,
                  () => _showSpecialtyFilter(),
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  'المنطقة',
                  _selectedLocation,
                  () => _showLocationFilter(),
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  'التقييم',
                  _minRating > 0 ? '${_minRating.toStringAsFixed(1)}+' : 'الكل',
                  () => _showRatingFilter(),
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  'الرسوم',
                  _maxFee < 1000 ? 'حتى ${_maxFee.toInt()}' : 'الكل',
                  () => _showFeeFilter(),
                ),
                const SizedBox(width: 8),
                _buildToggleChip('موثق فقط', _showOnlyVerified, (value) {
                  setState(() => _showOnlyVerified = value);
                }),
                const SizedBox(width: 8),
                _buildToggleChip('متاح فقط', _showOnlyAvailable, (value) {
                  setState(() => _showOnlyAvailable = value);
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, VoidCallback onTap) {
    final isActive = value != 'الكل';
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive
              ? AppColors.primary.withValues(alpha: 0.1)
              : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isActive ? AppColors.primary : Colors.grey.shade300,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '$label: $value',
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isActive ? AppColors.primary : Colors.grey.shade700,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.keyboard_arrow_down,
              size: 16,
              color: isActive ? AppColors.primary : Colors.grey.shade600,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleChip(
    String label,
    bool isActive,
    Function(bool) onChanged,
  ) {
    return InkWell(
      onTap: () => onChanged(!isActive),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive
              ? AppColors.primary.withValues(alpha: 0.1)
              : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isActive ? AppColors.primary : Colors.grey.shade300,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isActive ? Icons.check_circle : Icons.radio_button_unchecked,
              size: 16,
              color: isActive ? AppColors.primary : Colors.grey.shade600,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isActive ? AppColors.primary : Colors.grey.shade700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: Colors.grey.shade600,
        labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        unselectedLabelStyle: GoogleFonts.cairo(fontWeight: FontWeight.normal),
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        isScrollable: true,
        tabs: const [
          Tab(text: 'جميع الأطباء'),
          Tab(text: 'في انتظار المراجعة'),
          Tab(text: 'المميزين'),
          Tab(text: 'المرفوضين'),
        ],
      ),
    );
  }

  Widget _buildTabBarView(
    DoctorProvider doctorProvider,
    DoctorPermissionProvider permissionProvider,
  ) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildDoctorsList('all', doctorProvider, permissionProvider),
        _buildDoctorsList('pending', doctorProvider, permissionProvider),
        _buildDoctorsList('featured', doctorProvider, permissionProvider),
        _buildDoctorsList('rejected', doctorProvider, permissionProvider),
      ],
    );
  }

  // دوال الفلاتر
  void _showSpecialtyFilter() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'اختر التخصص',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _specialties.length,
            itemBuilder: (context, index) {
              final specialty = _specialties[index];
              return RadioListTile<String>(
                title: Text(specialty, style: GoogleFonts.cairo()),
                value: specialty,
                groupValue: _selectedSpecialty,
                onChanged: (value) {
                  setState(() => _selectedSpecialty = value!);
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  void _showLocationFilter() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'اختر المنطقة',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _locations.length,
            itemBuilder: (context, index) {
              final location = _locations[index];
              return RadioListTile<String>(
                title: Text(location, style: GoogleFonts.cairo()),
                value: location,
                groupValue: _selectedLocation,
                onChanged: (value) {
                  setState(() => _selectedLocation = value!);
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  void _showRatingFilter() {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            'فلترة حسب التقييم',
            style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'الحد الأدنى للتقييم: ${_minRating.toStringAsFixed(1)}',
                style: GoogleFonts.cairo(),
              ),
              Slider(
                value: _minRating,
                min: 0.0,
                max: 5.0,
                divisions: 10,
                onChanged: (value) => setState(() => _minRating = value),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('إلغاء', style: GoogleFonts.cairo()),
            ),
            ElevatedButton(
              onPressed: () {
                this.setState(() {});
                Navigator.of(context).pop();
              },
              child: Text('تطبيق', style: GoogleFonts.cairo()),
            ),
          ],
        ),
      ),
    );
  }

  void _showFeeFilter() {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            'فلترة حسب الرسوم',
            style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'الحد الأقصى للرسوم: ${_maxFee.toInt()} ريال',
                style: GoogleFonts.cairo(),
              ),
              Slider(
                value: _maxFee,
                min: 0.0,
                max: 1000.0,
                divisions: 20,
                onChanged: (value) => setState(() => _maxFee = value),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('إلغاء', style: GoogleFonts.cairo()),
            ),
            ElevatedButton(
              onPressed: () {
                this.setState(() {});
                Navigator.of(context).pop();
              },
              child: Text('تطبيق', style: GoogleFonts.cairo()),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDoctorsList(
    String type,
    DoctorProvider doctorProvider,
    DoctorPermissionProvider permissionProvider,
  ) {
    List<Doctor> doctors = _getFilteredDoctors(doctorProvider.doctors, type);

    if (doctors.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.medical_services_outlined,
        title: 'لا توجد أطباء',
        subtitle: type == 'all'
            ? 'لم يتم إضافة أي أطباء بعد'
            : 'لا توجد أطباء في هذه الفئة',
        action: permissionProvider.hasPermission(DoctorPermissionType.add)
            ? ElevatedButton.icon(
                onPressed: () => _navigateToAddDoctor(),
                icon: const Icon(Icons.add),
                label: Text('إضافة طبيب جديد', style: GoogleFonts.cairo()),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              )
            : null,
      );
    }

    return RefreshIndicator(
      onRefresh: () async => doctorProvider.fetchDoctors(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: doctors.length,
        itemBuilder: (context, index) {
          final doctor = doctors[index];
          return _buildDoctorCard(doctor, permissionProvider);
        },
      ),
    );
  }

  List<Doctor> _getFilteredDoctors(List<Doctor> doctors, String type) {
    List<Doctor> filtered = doctors;

    // فلترة حسب النوع
    switch (type) {
      case 'pending':
        filtered = filtered.where((d) => !d.isApproved).toList();
        break;
      case 'featured':
        filtered = filtered.where((d) => d.isFeatured).toList();
        break;
      case 'rejected':
        // TODO: Add rejected status to Doctor model
        filtered = [];
        break;
      case 'all':
      default:
        break;
    }

    // فلترة حسب البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((doctor) {
        return doctor.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            doctor.specialty.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            (doctor.location?.toString().toLowerCase() ?? '').contains(
              _searchQuery.toLowerCase(),
            );
      }).toList();
    }

    // فلترة حسب التخصص
    if (_selectedSpecialty != 'الكل') {
      filtered = filtered
          .where((d) => d.specialty == _selectedSpecialty)
          .toList();
    }

    // فلترة حسب المنطقة
    if (_selectedLocation != 'الكل') {
      filtered = filtered
          .where((d) => d.location?.toString() == _selectedLocation)
          .toList();
    }

    // فلترة حسب التقييم
    if (_minRating > 0) {
      filtered = filtered.where((d) => d.rating >= _minRating).toList();
    }

    // فلترة حسب الرسوم
    if (_maxFee < 1000) {
      filtered = filtered
          .where((d) => (d.consultationFee ?? 0) <= _maxFee)
          .toList();
    }

    // فلترة حسب التحقق
    if (_showOnlyVerified) {
      filtered = filtered.where((d) => d.isVerified).toList();
    }

    // فلترة حسب التوفر
    if (_showOnlyAvailable) {
      filtered = filtered.where((d) => d.isAvailable).toList();
    }

    return filtered;
  }

  Widget _buildDoctorCard(
    Doctor doctor,
    DoctorPermissionProvider permissionProvider,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _navigateToDoctorDetail(doctor),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // صورة الطبيب
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: doctor.imageUrl != null && doctor.imageUrl!.isNotEmpty
                    ? CachedNetworkImage(
                        imageUrl: doctor.imageUrl!,
                        width: 60,
                        height: 60,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          width: 60,
                          height: 60,
                          color: Colors.grey.shade200,
                          child: const Icon(Icons.person, color: Colors.grey),
                        ),
                        errorWidget: (context, url, error) => Container(
                          width: 60,
                          height: 60,
                          color: Colors.grey.shade200,
                          child: const Icon(Icons.person, color: Colors.grey),
                        ),
                      )
                    : Container(
                        width: 60,
                        height: 60,
                        color: Colors.grey.shade200,
                        child: const Icon(Icons.person, color: Colors.grey),
                      ),
              ),
              const SizedBox(width: 12),
              // معلومات الطبيب
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            doctor.name,
                            style: GoogleFonts.cairo(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        if (doctor.isVerified)
                          Icon(
                            Icons.verified,
                            size: 20,
                            color: Colors.blue.shade600,
                          ),
                        if (doctor.isFeatured)
                          Icon(
                            Icons.star,
                            size: 20,
                            color: Colors.amber.shade600,
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      doctor.specialty,
                      style: GoogleFonts.cairo(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 14,
                          color: Colors.grey.shade500,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          doctor.location?.toString() ?? 'غير محدد',
                          style: GoogleFonts.cairo(
                            color: Colors.grey.shade500,
                            fontSize: 12,
                          ),
                        ),
                        const Spacer(),
                        Row(
                          children: [
                            Icon(
                              Icons.star,
                              size: 14,
                              color: Colors.amber.shade600,
                            ),
                            const SizedBox(width: 2),
                            Text(
                              doctor.rating.toStringAsFixed(1),
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    if (!doctor.isApproved) ...[
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade100,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'في انتظار المراجعة',
                          style: GoogleFonts.cairo(
                            fontSize: 10,
                            color: Colors.orange.shade700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // أزرار الإجراءات
              if (permissionProvider.hasPermission(DoctorPermissionType.edit) ||
                  permissionProvider.hasPermission(DoctorPermissionType.delete))
                PopupMenuButton<String>(
                  onSelected: (value) => _handleDoctorAction(value, doctor),
                  itemBuilder: (context) => [
                    if (permissionProvider.hasPermission(
                      DoctorPermissionType.edit,
                    ))
                      PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            const Icon(Icons.edit, size: 16),
                            const SizedBox(width: 8),
                            Text('تعديل', style: GoogleFonts.cairo()),
                          ],
                        ),
                      ),
                    if (permissionProvider.hasPermission(
                      DoctorPermissionType.approve,
                    ))
                      PopupMenuItem(
                        value: doctor.isApproved ? 'disapprove' : 'approve',
                        child: Row(
                          children: [
                            Icon(
                              doctor.isApproved
                                  ? Icons.cancel
                                  : Icons.check_circle,
                              size: 16,
                              color: doctor.isApproved
                                  ? Colors.orange
                                  : Colors.green,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              doctor.isApproved ? 'إلغاء الموافقة' : 'موافقة',
                              style: GoogleFonts.cairo(),
                            ),
                          ],
                        ),
                      ),
                    if (permissionProvider.hasPermission(
                      DoctorPermissionType.feature,
                    ))
                      PopupMenuItem(
                        value: doctor.isFeatured ? 'unfeature' : 'feature',
                        child: Row(
                          children: [
                            Icon(
                              doctor.isFeatured
                                  ? Icons.star_border
                                  : Icons.star,
                              size: 16,
                              color: Colors.amber,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              doctor.isFeatured ? 'إلغاء التمييز' : 'تمييز',
                              style: GoogleFonts.cairo(),
                            ),
                          ],
                        ),
                      ),
                    if (permissionProvider.hasPermission(
                      DoctorPermissionType.delete,
                    ))
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            const Icon(
                              Icons.delete,
                              size: 16,
                              color: Colors.red,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'حذف',
                              style: GoogleFonts.cairo(color: Colors.red),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToDoctorDetail(Doctor doctor) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DoctorDetailScreen(doctor: doctor),
      ),
    );
  }

  void _handleDoctorAction(String action, Doctor doctor) {
    switch (action) {
      case 'edit':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => AddEditDoctorScreen(doctor: doctor),
          ),
        );
        break;
      case 'approve':
        _toggleDoctorApproval(doctor);
        break;
      case 'disapprove':
        _toggleDoctorApproval(doctor);
        break;
      case 'feature':
        _toggleDoctorFeatured(doctor);
        break;
      case 'unfeature':
        _toggleDoctorFeatured(doctor);
        break;
      case 'delete':
        _showDeleteConfirmation(doctor);
        break;
    }
  }

  void _toggleDoctorApproval(Doctor doctor) async {
    try {
      final doctorProvider = Provider.of<DoctorProvider>(
        context,
        listen: false,
      );
      await doctorProvider.toggleDoctorApproval(doctor.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              doctor.isApproved
                  ? 'تم إلغاء موافقة الطبيب'
                  : 'تم الموافقة على الطبيب',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في تحديث حالة الموافقة',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleDoctorFeatured(Doctor doctor) async {
    try {
      final doctorProvider = Provider.of<DoctorProvider>(
        context,
        listen: false,
      );
      await doctorProvider.toggleDoctorFeatured(doctor.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              doctor.isFeatured ? 'تم إلغاء تمييز الطبيب' : 'تم تمييز الطبيب',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.amber,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في تحديث حالة التمييز',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDeleteConfirmation(Doctor doctor) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الحذف',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل أنت متأكد من حذف الطبيب "${doctor.name}"؟\nلا يمكن التراجع عن هذا الإجراء.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteDoctor(doctor);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text('حذف', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteDoctor(Doctor doctor) async {
    try {
      final doctorProvider = Provider.of<DoctorProvider>(
        context,
        listen: false,
      );
      await doctorProvider.deleteDoctor(doctor.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف الطبيب بنجاح', style: GoogleFonts.cairo()),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف الطبيب', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

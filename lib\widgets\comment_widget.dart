import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:timeago/timeago.dart' as timeago;

import 'package:yassincil/models/comment.dart';
import 'package:yassincil/providers/forum_provider.dart';
import 'package:yassincil/providers/auth_provider.dart' as app_auth;
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/screens/forum/report_screen.dart';

class CommentWidget extends StatefulWidget {
  final Comment comment;
  final Function(String) onDelete;
  final Function(String) onReport;
  final int depth;

  const CommentWidget({
    super.key,
    required this.comment,
    required this.onDelete,
    required this.onReport,
    this.depth = 0,
  });

  @override
  State<CommentWidget> createState() => _CommentWidgetState();
}

class _CommentWidgetState extends State<CommentWidget> {
  bool _showReplies = false;
  bool _isReplying = false;
  final TextEditingController _replyController = TextEditingController();

  @override
  void dispose() {
    _replyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<app_auth.AuthProvider>(context);
    final isOwner = authProvider.currentUser?.uid == widget.comment.userId;
    final isAdmin = authProvider.isAdmin;

    return Container(
      margin: EdgeInsets.only(left: widget.depth * 16.0, bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(isOwner, isAdmin),
                const SizedBox(height: 8),
                Text(
                  widget.comment.content,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey.shade800,
                  ),
                ),
                const SizedBox(height: 8),
                _buildFooter(),
              ],
            ),
          ),
          if (_isReplying) _buildReplyInput(),
          if (widget.comment.repliesCount > 0) _buildRepliesSection(),
        ],
      ),
    );
  }

  Widget _buildHeader(bool isOwner, bool isAdmin) {
    return Row(
      children: [
        CircleAvatar(
          radius: 18,
          backgroundImage: widget.comment.userAvatar != null && widget.comment.userAvatar!.isNotEmpty
              ? CachedNetworkImageProvider(widget.comment.userAvatar!)
              : null,
          child: widget.comment.userAvatar == null || widget.comment.userAvatar!.isEmpty
              ? const Icon(Icons.person, size: 18)
              : null,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.comment.username,
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              Text(
                timeago.format(widget.comment.createdAt, locale: 'ar'),
                style: GoogleFonts.cairo(
                  fontSize: 11,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
        if (isOwner || isAdmin) _buildPopupMenu(isOwner, isAdmin),
      ],
    );
  }

  Widget _buildFooter() {
    return Row(
      children: [
        Consumer<ForumProvider>(
          builder: (context, forumProvider, child) {
            final isLiked = widget.comment.id != null
                ? forumProvider.isCommentLiked(widget.comment.id!)
                : false;
            return _buildFooterButton(
              icon: isLiked ? Icons.favorite : Icons.favorite_border,
              label: widget.comment.likesCount.toString(),
              color: isLiked ? Colors.red : Colors.grey,
              onTap: _likeComment,
            );
          },
        ),
        const SizedBox(width: 16),
        _buildFooterButton(
          icon: Icons.reply,
          label: 'رد',
          onTap: () {
            setState(() {
              _isReplying = !_isReplying;
            });
          },
        ),
      ],
    );
  }

  Widget _buildFooterButton({
    required IconData icon,
    required String label,
    VoidCallback? onTap,
    Color color = Colors.grey,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPopupMenu(bool isOwner, bool isAdmin) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert, size: 20),
      onSelected: (value) {
        if (value == 'delete') {
          widget.onDelete(widget.comment.id!);
        } else if (value == 'report') {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ReportScreen(
                targetId: widget.comment.id!,
                targetType: 'comment',
              ),
            ),
          );
        }
      },
      itemBuilder: (context) => [
        if (isOwner || isAdmin)
          PopupMenuItem(
            value: 'delete',
            child: Text('حذف', style: GoogleFonts.cairo()),
          ),
        if (!isOwner)
          PopupMenuItem(
            value: 'report',
            child: Text('إبلاغ', style: GoogleFonts.cairo()),
          ),
      ],
    );
  }

  Widget _buildReplyInput() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _replyController,
              autofocus: true,
              decoration: InputDecoration(
                hintText: 'اكتب ردك...',
                hintStyle: GoogleFonts.cairo(),
                isDense: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.send, color: AppColors.primary),
            onPressed: _addReply,
          ),
        ],
      ),
    );
  }

  Widget _buildRepliesSection() {
    return Padding(
      padding: const EdgeInsets.only(left: 16, top: 8),
      child: Column(
        children: [
          if (!_showReplies)
            TextButton(
              onPressed: () => setState(() => _showReplies = true),
              child: Text(
                'عرض الردود (${widget.comment.repliesCount})',
                style: GoogleFonts.cairo(color: AppColors.primary),
              ),
            ),
          if (_showReplies)
            StreamBuilder<List<Comment>>(
              stream: Provider.of<ForumProvider>(context, listen: false)
                  .getCommentReplies(widget.comment.id!),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Center(child: CircularProgressIndicator()),
                  );
                }
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return const SizedBox.shrink();
                }
                final replies = snapshot.data!;
                return ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: replies.length,
                  itemBuilder: (context, index) {
                    return CommentWidget(
                      comment: replies[index],
                      onDelete: widget.onDelete,
                      onReport: widget.onReport,
                      depth: widget.depth + 1,
                    );
                  },
                );
              },
            ),
        ],
      ),
    );
  }

  Future<void> _likeComment() async {
    if (widget.comment.id == null) return;
    try {
      await Provider.of<ForumProvider>(context, listen: false)
          .toggleCommentLike(widget.comment.id!);
    } catch (e) {
      // Handle error
    }
  }

  Future<void> _addReply() async {
    if (_replyController.text.trim().isEmpty || widget.comment.id == null) {
      return;
    }
    try {
      await Provider.of<ForumProvider>(context, listen: false).addComment(
        postId: widget.comment.postId,
        content: _replyController.text.trim(),
        parentCommentId: widget.comment.id,
      );
      _replyController.clear();
      setState(() {
        _isReplying = false;
        _showReplies = true;
      });
    } catch (e) {
      // Handle error
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:share_plus/share_plus.dart';

import '../models/comment.dart';
import '../models/medication.dart';
import '../providers/medication_provider.dart';
import '../providers/auth_provider.dart';
import '../screens/medications/comment_replies_screen.dart';
import '../utils/app_colors.dart';
import '../config/interaction_config.dart';
import '../widgets/advanced_medication_comment_widget.dart';
import '../widgets/add_medication_comment_widget.dart';

class SocialInteractionsWidget extends StatefulWidget {
  final Medication medication;
  final String? currentUserId;
  final VoidCallback? onCommentButtonPressed;

  const SocialInteractionsWidget({
    super.key,
    required this.medication,
    this.currentUserId,
    this.onCommentButtonPressed,
  });

  @override
  State<SocialInteractionsWidget> createState() =>
      _SocialInteractionsWidgetState();
}

class _SocialInteractionsWidgetState extends State<SocialInteractionsWidget>
    with TickerProviderStateMixin {
  late AnimationController _likeAnimationController;
  late Animation<double> _likeScaleAnimation;
  late Animation<Color?> _likeColorAnimation;

  bool _isLiked = false;
  double _userRating = 0.0;
  double _averageRating = 0.0;
  int _ratingsCount = 0;
  int _likesCount = 0;
  int _commentsCount = 0;
  int _sharesCount = 0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadInteractionData();
  }

  void _initializeAnimations() {
    _likeAnimationController = AnimationController(
      duration: InteractionConfig.likeAnimationDuration,
      vsync: this,
    );

    _likeScaleAnimation = Tween<double>(begin: 1.0, end: 1.3).animate(
      CurvedAnimation(
        parent: _likeAnimationController,
        curve: InteractionCurves.elasticOut,
      ),
    );

    _likeColorAnimation = ColorTween(
      begin: Colors.grey.shade600,
      end: Color(InteractionConfig.likeColor),
    ).animate(_likeAnimationController);
  }

  Future<void> _loadInteractionData() async {
    final medicationProvider = Provider.of<MedicationProvider>(
      context,
      listen: false,
    );

    // جلب حالة الإعجاب الحالية للمستخدم
    bool liked = false;
    if (widget.currentUserId != null) {
      liked = await medicationProvider.hasLikedMedication(
        widget.medication.id!,
        widget.currentUserId!,
      );
    }

    if (mounted) {
      setState(() {
        _likesCount = widget.medication.likesCount;
        _commentsCount = widget.medication.commentsCount;
        _averageRating = widget.medication.averageRating;
        _ratingsCount = widget.medication.ratingsCount;
        _isLiked = liked;
        // _sharesCount can be handled locally or from a provider if needed
      });
    }
  }

  @override
  void dispose() {
    _likeAnimationController.dispose();
    super.dispose();
  }

  Future<void> _toggleLike() async {
    if (widget.currentUserId == null) {
      _showLoginRequired();
      return;
    }

    HapticFeedback.lightImpact();

    final medicationProvider = Provider.of<MedicationProvider>(
      context,
      listen: false,
    );
    final initialLikedState = _isLiked;

    setState(() {
      _isLiked = !_isLiked;
      _likesCount += _isLiked ? 1 : -1;
    });

    if (_isLiked) {
      _likeAnimationController.forward().then((_) {
        _likeAnimationController.reverse();
      });
    }

    try {
      await medicationProvider.toggleLikeOnMedication(
        widget.medication.id!,
        widget.currentUserId!,
      );
    } catch (e) {
      // Revert on error
      setState(() {
        _isLiked = initialLikedState;
        _likesCount += _isLiked ? 1 : -1;
      });
    }
  }

  Future<void> _shareMedication() async {
    HapticFeedback.mediumImpact();

    setState(() {
      _sharesCount++;
    });

    try {
      final shareText =
          '''
🏥 ${widget.medication.name}

🏢 الشركة: ${widget.medication.company}
🧪 المكونات: ${widget.medication.ingredients}
${widget.medication.isAllowed ? '✅ آمن لمرضى السيلياك' : '⚠️ غير آمن لمرضى السيلياك'}

📱 تطبيق ياسين سيل - دليلك للأدوية الآمنة
      ''';

      await Share.share(shareText);
    } catch (e) {
      setState(() {
        _sharesCount--;
      });
      debugPrint('خطأ في المشاركة: $e');
    }
  }

  void _showRatingDialog() {
    if (widget.currentUserId == null) {
      _showLoginRequired();
      return;
    }

    HapticFeedback.mediumImpact();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _buildRatingBottomSheet(),
    );
  }

  void _showLoginRequired() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.login, color: Colors.white),
            const SizedBox(width: 8),
            Text(
              'يجب تسجيل الدخول أولاً',
              style: GoogleFonts.cairo(color: Colors.white),
            ),
          ],
        ),
        backgroundColor: AppColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header with medication info
          _buildMedicationHeader(),

          // Interaction stats (likes, comments, shares)
          _buildInteractionStats(),

          // Action buttons (like, comment, share, rate)
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildMedicationHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Medication image
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                colors: widget.medication.isAllowed
                    ? [const Color(0xFF10B981), const Color(0xFF059669)]
                    : [const Color(0xFFEF4444), const Color(0xFFDC2626)],
              ),
            ),
            child:
                widget.medication.imageUrl != null &&
                    widget.medication.imageUrl!.isNotEmpty
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.network(
                      widget.medication.imageUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildMedicationIcon(),
                    ),
                  )
                : _buildMedicationIcon(),
          ),
          const SizedBox(width: 12),

          // Medication info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.medication.name,
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF1F2937),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.medication.company,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      widget.medication.isAllowed
                          ? Icons.verified_rounded
                          : Icons.dangerous_rounded,
                      size: 16,
                      color: widget.medication.isAllowed
                          ? const Color(0xFF10B981)
                          : const Color(0xFFEF4444),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      widget.medication.isAllowed
                          ? 'آمن لمرضى السيلياك'
                          : 'غير آمن لمرضى السيلياك',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: widget.medication.isAllowed
                            ? const Color(0xFF10B981)
                            : const Color(0xFFEF4444),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),

                // Rating display
                if (_averageRating > 0) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      ...List.generate(5, (index) {
                        return Icon(
                          index < _averageRating.floor()
                              ? Icons.star_rounded
                              : (index < _averageRating
                                    ? Icons.star_half_rounded
                                    : Icons.star_outline_rounded),
                          color: Color(InteractionConfig.ratingColor),
                          size: 16,
                        );
                      }),
                      const SizedBox(width: 8),
                      Text(
                        '${_averageRating.toStringAsFixed(1)} ($_ratingsCount تقييم)',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),

          // More options
          IconButton(
            onPressed: () => _showMoreOptions(),
            icon: const Icon(Icons.more_vert, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildMedicationIcon() {
    return Icon(Icons.medication_rounded, color: Colors.white, size: 30);
  }

  Widget _buildInteractionStats() {
    if (_likesCount == 0 && _commentsCount == 0 && _sharesCount == 0) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // Likes
          if (_likesCount > 0) ...[
            Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: Color(0xFF1877F2),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.thumb_up, color: Colors.white, size: 12),
            ),
            const SizedBox(width: 6),
            Text(
              _likesCount.toString(),
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ],

          const Spacer(),

          // Comments and shares
          Row(
            children: [
              if (_commentsCount > 0) ...[
                GestureDetector(
                  onTap: () {
                    // استدعاء دالة التمرير إلى قسم التعليقات
                    _scrollToCommentsSection();
                  },
                  child: Text(
                    _commentsCount == 1
                        ? 'تعليق واحد'
                        : '$_commentsCount تعليق',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
                if (_sharesCount > 0) ...[
                  const SizedBox(width: 16),
                  Text(
                    _sharesCount == 1 ? 'مشاركة واحدة' : '$_sharesCount مشاركة',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ] else if (_sharesCount > 0) ...[
                Text(
                  _sharesCount == 1 ? 'مشاركة واحدة' : '$_sharesCount مشاركة',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Colors.grey.shade200)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Like button
          Expanded(
            child: _buildActionButton(
              icon: AnimatedBuilder(
                animation: _likeAnimationController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _isLiked ? _likeScaleAnimation.value : 1.0,
                    child: Icon(
                      _isLiked ? Icons.thumb_up : Icons.thumb_up_outlined,
                      color: _isLiked
                          ? _likeColorAnimation.value
                          : Colors.grey.shade600,
                    ),
                  );
                },
              ),
              label: 'إعجاب',
              onTap: _toggleLike,
            ),
          ),

          // Comment button
          Expanded(
            child: _buildActionButton(
              icon: Icon(
                Icons.chat_bubble_outline,
                color: Colors.grey.shade600,
              ),
              label: 'تعليق',
              onTap: () {
                // توجيه المستخدم إلى قسم التعليقات في الصفحة الرئيسية
                _scrollToCommentsSection();
              },
            ),
          ),

          // Share button
          Expanded(
            child: _buildActionButton(
              icon: Icon(Icons.share_outlined, color: Colors.grey.shade600),
              label: 'مشاركة',
              onTap: _shareMedication,
            ),
          ),

          // Rate button
          Expanded(
            child: _buildActionButton(
              icon: Icon(Icons.star_outline, color: Colors.grey.shade600),
              label: 'تقييم',
              onTap: _showRatingDialog,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required Widget icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            icon,
            const SizedBox(height: 4),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingBottomSheet() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),

          // Title
          Text(
            'تقييم الدواء',
            style: GoogleFonts.cairo(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'ما هو تقييمك لهذا الدواء؟',
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 20),

          // Rating stars
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(5, (index) {
              return GestureDetector(
                onTap: () => _setRating(index + 1),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Icon(
                    index < _userRating
                        ? Icons.star_rounded
                        : Icons.star_outline_rounded,
                    size: 36,
                    color: Color(InteractionConfig.ratingColor),
                  ),
                ),
              );
            }),
          ),
          const SizedBox(height: 20),

          // Submit button
          ElevatedButton(
            onPressed: _userRating > 0 ? _submitRating : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'إرسال التقييم',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  void _setRating(double rating) {
    setState(() {
      _userRating = rating;
    });
  }

  Future<void> _submitRating() async {
    if (widget.currentUserId == null) {
      _showLoginRequired();
      return;
    }

    Navigator.pop(context);

    // final medicationProvider = Provider.of<MedicationProvider>(
    //   context,
    //   listen: false,
    // );

    try {
      // TODO: يجب إضافة دالة rateMedication في MedicationProvider
      // مؤقتاً سنعرض رسالة نجاح

      // await medicationProvider.rateMedication(
      //   widget.medication.id!,
      //   widget.currentUserId!,
      //   _userRating,
      // );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'شكراً لك! تم إرسال تقييمك',
              style: GoogleFonts.cairo(color: Colors.white),
            ),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء إرسال التقييم',
              style: GoogleFonts.cairo(color: Colors.white),
            ),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 50,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(3),
              ),
            ),

            // Options
            ListTile(
              leading: const Icon(Icons.bookmark_outline),
              title: Text('حفظ', style: GoogleFonts.cairo()),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement save functionality
              },
            ),
            ListTile(
              leading: const Icon(Icons.report_outlined),
              title: Text('إبلاغ', style: GoogleFonts.cairo()),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement report functionality
              },
            ),
            ListTile(
              leading: const Icon(Icons.info_outline),
              title: Text('معلومات إضافية', style: GoogleFonts.cairo()),
              onTap: () {
                Navigator.pop(context);
                // TODO: Show additional info
              },
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _scrollToCommentsSection() {
    // إرسال حدث للصفحة الرئيسية للتمرير إلى قسم التعليقات
    if (widget.onCommentButtonPressed != null) {
      widget.onCommentButtonPressed!();
    } else {
      // إذا لم يتم توفير دالة التمرير، نعرض رسالة للمستخدم
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'انتقل لأسفل لعرض التعليقات',
            style: GoogleFonts.cairo(),
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}

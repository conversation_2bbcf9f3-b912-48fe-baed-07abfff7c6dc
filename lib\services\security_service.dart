import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:crypto/crypto.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../services/firestore_service.dart';
import '../utils/app_constants.dart';

class SecurityService {
  static final FirestoreService _firestoreService = FirestoreService();

  /// تشفير النص
  static String encryptText(String text, String key) {
    final bytes = utf8.encode(text + key);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// التحقق من قوة كلمة المرور
  static Map<String, dynamic> checkPasswordStrength(String password) {
    int score = 0;
    List<String> feedback = [];

    // الطول
    if (password.length >= 8) {
      score += 2;
    } else {
      feedback.add('يجب أن تكون كلمة المرور 8 أحرف على الأقل');
    }

    // الأحرف الكبيرة
    if (password.contains(RegExp(r'[A-Z]'))) {
      score += 1;
    } else {
      feedback.add('يجب أن تحتوي على حرف كبير واحد على الأقل');
    }

    // الأحرف الصغيرة
    if (password.contains(RegExp(r'[a-z]'))) {
      score += 1;
    } else {
      feedback.add('يجب أن تحتوي على حرف صغير واحد على الأقل');
    }

    // الأرقام
    if (password.contains(RegExp(r'[0-9]'))) {
      score += 1;
    } else {
      feedback.add('يجب أن تحتوي على رقم واحد على الأقل');
    }

    // الرموز الخاصة
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      score += 1;
    } else {
      feedback.add('يجب أن تحتوي على رمز خاص واحد على الأقل');
    }

    String strength;
    Color color;
    if (score <= 2) {
      strength = 'ضعيفة';
      color = Colors.red;
    } else if (score <= 4) {
      strength = 'متوسطة';
      color = Colors.orange;
    } else {
      strength = 'قوية';
      color = Colors.green;
    }

    return {
      'score': score,
      'strength': strength,
      'color': color,
      'feedback': feedback,
    };
  }

  /// توليد كلمة مرور قوية
  static String generateStrongPassword({int length = 12}) {
    const String chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*()';
    final Random random = Random.secure();

    return List.generate(
      length,
      (index) => chars[random.nextInt(chars.length)],
    ).join();
  }

  /// تسجيل محاولة تسجيل دخول
  static Future<void> logLoginAttempt({
    required String email,
    required bool success,
    String? errorCode,
  }) async {
    try {
      await _firestoreService.addDocument('loginAttempts', {
        'email': email,
        'success': success,
        'errorCode': errorCode,
        'timestamp': FieldValue.serverTimestamp(),
        'ipAddress': 'unknown', // يمكن إضافة IP الحقيقي لاحقاً
      });
    } catch (e) {
      debugPrint('خطأ في تسجيل محاولة تسجيل الدخول: $e');
    }
  }

  /// التحقق من محاولات تسجيل الدخول المشبوهة
  static Future<bool> checkSuspiciousActivity(String email) async {
    try {
      final now = DateTime.now();
      final oneHourAgo = now.subtract(const Duration(hours: 1));

      final querySnapshot = await _firestoreService.db
          .collection('loginAttempts')
          .where('email', isEqualTo: email)
          .where('success', isEqualTo: false)
          .where('timestamp', isGreaterThan: Timestamp.fromDate(oneHourAgo))
          .get();

      // إذا كان هناك أكثر من 5 محاولات فاشلة في الساعة الواحدة
      return querySnapshot.docs.length > 5;
    } catch (e) {
      debugPrint('خطأ في فحص النشاط المشبوه: $e');
      return false;
    }
  }

  /// تفعيل المصادقة الثنائية
  static Future<void> enableTwoFactorAuth() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _firestoreService
          .updateDocument(AppConstants.usersCollection, user.uid, {
            'twoFactorEnabled': true,
            'twoFactorEnabledAt': FieldValue.serverTimestamp(),
          });

      debugPrint('تم تفعيل المصادقة الثنائية');
    } catch (e) {
      debugPrint('خطأ في تفعيل المصادقة الثنائية: $e');
      rethrow;
    }
  }

  /// إلغاء تفعيل المصادقة الثنائية
  static Future<void> disableTwoFactorAuth() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _firestoreService
          .updateDocument(AppConstants.usersCollection, user.uid, {
            'twoFactorEnabled': false,
            'twoFactorDisabledAt': FieldValue.serverTimestamp(),
          });

      debugPrint('تم إلغاء تفعيل المصادقة الثنائية');
    } catch (e) {
      debugPrint('خطأ في إلغاء تفعيل المصادقة الثنائية: $e');
      rethrow;
    }
  }

  /// حفظ إعدادات الخصوصية
  static Future<void> savePrivacySettings(Map<String, bool> settings) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _firestoreService
          .updateDocument(AppConstants.usersCollection, user.uid, {
            'privacySettings': settings,
            'privacyUpdatedAt': FieldValue.serverTimestamp(),
          });

      // حفظ محلياً أيضاً
      final prefs = await SharedPreferences.getInstance();
      for (final entry in settings.entries) {
        await prefs.setBool('privacy_${entry.key}', entry.value);
      }

      debugPrint('تم حفظ إعدادات الخصوصية');
    } catch (e) {
      debugPrint('خطأ في حفظ إعدادات الخصوصية: $e');
      rethrow;
    }
  }

  /// الحصول على إعدادات الخصوصية
  static Future<Map<String, bool>> getPrivacySettings() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return _getDefaultPrivacySettings();

      final doc = await _firestoreService.db
          .collection(AppConstants.usersCollection)
          .doc(user.uid)
          .get();

      if (doc.exists && doc.data()?['privacySettings'] != null) {
        return Map<String, bool>.from(doc.data()!['privacySettings']);
      }

      return _getDefaultPrivacySettings();
    } catch (e) {
      debugPrint('خطأ في جلب إعدادات الخصوصية: $e');
      return _getDefaultPrivacySettings();
    }
  }

  /// الإعدادات الافتراضية للخصوصية
  static Map<String, bool> _getDefaultPrivacySettings() {
    return {
      'shareSymptoms': false,
      'shareLocation': false,
      'allowAnalytics': true,
      'allowNotifications': true,
      'shareProfile': false,
      'allowDataExport': true,
    };
  }

  /// حذف جميع البيانات الشخصية
  static Future<void> deleteAllUserData() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('يجب تسجيل الدخول أولاً');

      final batch = _firestoreService.db.batch();

      // حذف الأعراض
      final symptomsQuery = await _firestoreService.db
          .collection('symptoms')
          .where('userId', isEqualTo: user.uid)
          .get();

      for (final doc in symptomsQuery.docs) {
        batch.delete(doc.reference);
      }

      // حذف المراجعات
      final reviewsQuery = await _firestoreService.db
          .collection('reviews')
          .where('userId', isEqualTo: user.uid)
          .get();

      for (final doc in reviewsQuery.docs) {
        batch.delete(doc.reference);
      }

      // حذف منشورات المنتدى
      final postsQuery = await _firestoreService.db
          .collection(AppConstants.forumPostsCollection)
          .where('userId', isEqualTo: user.uid)
          .get();

      for (final doc in postsQuery.docs) {
        batch.delete(doc.reference);
      }

      // حذف بيانات المستخدم
      batch.delete(
        _firestoreService.db
            .collection(AppConstants.usersCollection)
            .doc(user.uid),
      );

      await batch.commit();

      // حذف الحساب من Firebase Auth
      await user.delete();

      debugPrint('تم حذف جميع البيانات الشخصية');
    } catch (e) {
      debugPrint('خطأ في حذف البيانات الشخصية: $e');
      rethrow;
    }
  }

  /// تشفير البيانات الحساسة قبل الحفظ
  static String encryptSensitiveData(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return base64.encode(digest.bytes);
  }

  /// التحقق من صحة البيانات المدخلة
  static bool validateInput(String input, String type) {
    switch (type) {
      case 'email':
        return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(input);
      case 'phone':
        return RegExp(r'^\+?[1-9]\d{1,14}$').hasMatch(input);
      case 'name':
        return input.trim().length >= 2 &&
            RegExp(r'^[a-zA-Zأ-ي\s]+$').hasMatch(input);
      default:
        return input.trim().isNotEmpty;
    }
  }

  /// تنظيف النص من المحتوى الضار
  static String sanitizeInput(String input) {
    String cleaned = input;
    // إزالة HTML tags
    cleaned = cleaned.replaceAll(RegExp(r'<[^>]*>'), '');
    // إزالة الرموز الخطيرة
    cleaned = cleaned.replaceAll('<', '');
    cleaned = cleaned.replaceAll('>', '');
    cleaned = cleaned.replaceAll('&', '');
    cleaned = cleaned.replaceAll('"', '');
    cleaned = cleaned.replaceAll("'", '');
    return cleaned.trim();
  }

  /// تسجيل الأنشطة الأمنية
  static Future<void> logSecurityEvent({
    required String eventType,
    required String description,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;

      await _firestoreService.addDocument('securityLogs', {
        'userId': user?.uid,
        'eventType': eventType,
        'description': description,
        'metadata': metadata ?? {},
        'timestamp': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('خطأ في تسجيل الحدث الأمني: $e');
    }
  }
}

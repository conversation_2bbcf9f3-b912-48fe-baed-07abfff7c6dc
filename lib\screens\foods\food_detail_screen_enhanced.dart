import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:share_plus/share_plus.dart';
import 'package:cached_network_image/cached_network_image.dart';

import 'package:yassincil/providers/food_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/providers/favorites_provider.dart';
import 'package:yassincil/models/food_item.dart';
import 'package:yassincil/models/comment.dart';
import 'package:yassincil/screens/foods/add_edit_food_screen.dart';
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/widgets/advanced_food_comment_widget.dart';
import 'package:yassincil/widgets/add_food_comment_widget.dart';
import 'package:yassincil/widgets/social_interactions_widget.dart';
import 'package:yassincil/auth/screens/login_screen.dart';

class EnhancedFoodDetailScreen extends StatefulWidget {
  final FoodItem foodItem;
  final bool isForVerification;

  const EnhancedFoodDetailScreen({
    super.key,
    required this.foodItem,
    this.isForVerification = false,
  });

  @override
  State<EnhancedFoodDetailScreen> createState() =>
      _EnhancedFoodDetailScreenState();
}

class _EnhancedFoodDetailScreenState extends State<EnhancedFoodDetailScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _fabController;
  late Animation<double> _fadeAnimation;
  late PageController _imagePageController;

  int _selectedImageIndex = 0;
  int _selectedTabIndex = 0;

  final GlobalKey _commentsKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _imagePageController = PageController();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _fabController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController.forward();
      _fabController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _fabController.dispose();
    _imagePageController.dispose();
    super.dispose();
  }

  void _scrollToComments() {
    if (_commentsKey.currentContext != null) {
      Scrollable.ensureVisible(
        _commentsKey.currentContext!,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  void _shareFood() {
    final text =
        '''
🍽️ ${widget.foodItem.name}

📝 ${widget.foodItem.details}

🏷️ الفئة: ${widget.foodItem.category}

${widget.foodItem.isGlutenFree ? '✅ خالي من الجلوتين' : '⚠️ يحتوي على جلوتين'}

📱 تطبيق ياسين سيل - دليل الأطعمة الآمنة لمرضى السيلياك
''';
    Share.share(text);
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final foodProvider = Provider.of<FoodProvider>(context);
    final currentUserId = authProvider.currentUser?.uid;
    final isAdmin = authProvider.isAdmin;

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: CustomScrollView(
        slivers: [
          _buildModernSliverAppBar(isAdmin),
          SliverToBoxAdapter(
            child: Column(
              children: [
                _buildModernFoodInfo(),
                _buildModernInteractionSection(foodProvider, currentUserId),
                _buildModernCommentsSection(foodProvider, currentUserId),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButtons(isAdmin),
    );
  }

  Widget _buildModernSliverAppBar(bool isAdmin) {
    return SliverAppBar(
      expandedHeight: 400,
      pinned: true,
      floating: false,
      snap: false,
      elevation: 0,
      backgroundColor: Colors.transparent,
      stretch: true,
      stretchTriggerOffset: 80.0,
      onStretchTrigger: () async {
        HapticFeedback.lightImpact();
      },
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(Icons.share_rounded, color: Colors.white),
            onPressed: _shareFood,
          ),
        ),
        if (isAdmin)
          Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              borderRadius: BorderRadius.circular(12),
            ),
            child: PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert_rounded, color: Colors.white),
              onSelected: (value) => _handleAdminAction(value),
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit_rounded, color: AppColors.primary),
                      const SizedBox(width: 8),
                      Text('تعديل', style: GoogleFonts.cairo()),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      const Icon(Icons.delete_rounded, color: Colors.red),
                      const SizedBox(width: 8),
                      Text('حذف', style: GoogleFonts.cairo()),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'approve',
                  child: Row(
                    children: [
                      const Icon(
                        Icons.check_circle_rounded,
                        color: Colors.green,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        widget.foodItem.isApproved
                            ? 'إلغاء الموافقة'
                            : 'موافقة',
                        style: GoogleFonts.cairo(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
      ],
      flexibleSpace: FlexibleSpaceBar(background: _buildImageCarousel()),
    );
  }

  Widget _buildImageCarousel() {
    final images = widget.foodItem.imageUrls.isNotEmpty
        ? widget.foodItem.imageUrls
        : ['https://via.placeholder.com/400x300?text=لا+توجد+صورة'];

    return Stack(
      children: [
        PageView.builder(
          controller: _imagePageController,
          onPageChanged: (index) {
            setState(() {
              _selectedImageIndex = index;
            });
          },
          itemCount: images.length,
          itemBuilder: (context, index) {
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.transparent, Colors.black.withOpacity(0.7)],
                ),
              ),
              child: CachedNetworkImage(
                imageUrl: images[index],
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey.shade200,
                  child: const Center(child: CircularProgressIndicator()),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey.shade200,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.restaurant_rounded,
                        size: 80,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد صورة',
                        style: GoogleFonts.cairo(
                          color: Colors.grey.shade600,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
        // مؤشر الصور
        if (images.length > 1)
          Positioned(
            bottom: 20,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: images.asMap().entries.map((entry) {
                return Container(
                  width: 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _selectedImageIndex == entry.key
                        ? Colors.white
                        : Colors.white.withOpacity(0.4),
                  ),
                );
              }).toList(),
            ),
          ),
        // معلومات الطعام في الأسفل
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.transparent, Colors.black.withOpacity(0.8)],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.foodItem.name,
                        style: GoogleFonts.cairo(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    if (widget.foodItem.isGlutenFree)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.check_circle,
                              size: 16,
                              color: Colors.white,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'خالي من الجلوتين',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  widget.foodItem.category,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _handleAdminAction(String action) async {
    switch (action) {
      case 'edit':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => AddEditFoodScreen(foodItem: widget.foodItem),
          ),
        );
        break;
      case 'delete':
        _showDeleteConfirmation();
        break;
      case 'approve':
        _toggleApproval();
        break;
    }
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد الحذف', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف هذا الطعام؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                final foodProvider = Provider.of<FoodProvider>(
                  context,
                  listen: false,
                );
                await foodProvider.deleteFoodItem(widget.foodItem.id!);
                if (mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'تم حذف الطعام بنجاح',
                        style: GoogleFonts.cairo(),
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'فشل في حذف الطعام: $e',
                        style: GoogleFonts.cairo(),
                      ),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _toggleApproval() async {
    try {
      final foodProvider = Provider.of<FoodProvider>(context, listen: false);
      // تحديث حالة الموافقة - سيتم تنفيذها حسب API المتاح
      // await foodProvider.updateFoodApproval(widget.foodItem.id!, !widget.foodItem.isApproved);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.foodItem.isApproved
                  ? 'تم إلغاء موافقة الطعام'
                  : 'تم اعتماد الطعام',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في تحديث حالة الموافقة: $e',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildModernFoodInfo() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildModernInfoCard(
            Icons.restaurant_rounded,
            'معلومات الطعام',
            widget.foodItem.details,
            const [Color(0xFF3B82F6), Color(0xFF1D4ED8)],
          ),
          const SizedBox(height: 16),
          if (widget.foodItem.ingredients != null &&
              widget.foodItem.ingredients!.isNotEmpty) ...[
            _buildModernInfoCard(
              Icons.science_rounded,
              'المكونات',
              widget.foodItem.ingredients!,
              const [Color(0xFF10B981), Color(0xFF059669)],
            ),
            const SizedBox(height: 16),
          ],
          if (widget.foodItem.warnings != null &&
              widget.foodItem.warnings!.isNotEmpty) ...[
            _buildModernInfoCard(
              Icons.warning_rounded,
              'تحذيرات',
              widget.foodItem.warnings!,
              const [Color(0xFFF59E0B), Color(0xFFD97706)],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildModernInfoCard(
    IconData icon,
    String title,
    String content,
    List<Color> gradientColors,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: gradientColors,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: gradientColors.first.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: Colors.white, size: 24),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            content,
            style: GoogleFonts.cairo(
              fontSize: 16,
              color: Colors.white.withOpacity(0.95),
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernInteractionSection(FoodProvider provider, String? userId) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildInteractionButton(
                Icons.favorite_rounded,
                '${widget.foodItem.likesCount}',
                Colors.red,
                () => _toggleLike(provider, userId),
              ),
              _buildInteractionButton(
                Icons.comment_rounded,
                '${widget.foodItem.commentsCount}',
                Colors.blue,
                _scrollToComments,
              ),
              _buildInteractionButton(
                Icons.star_rounded,
                widget.foodItem.averageRating.toStringAsFixed(1),
                Colors.amber,
                () => _showRatingDialog(),
              ),
              _buildInteractionButton(
                Icons.share_rounded,
                'مشاركة',
                Colors.green,
                _shareFood,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInteractionButton(
    IconData icon,
    String label,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernCommentsSection(FoodProvider provider, String? userId) {
    return Container(
      key: _commentsKey,
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.comment_rounded, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                'التعليقات (${widget.foodItem.commentsCount})',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (userId != null) ...[
            // AddFoodCommentWidget(foodItemId: widget.foodItem.id!),
            const SizedBox(height: 16),
          ],
          Consumer<FoodProvider>(
            builder: (context, foodProvider, child) {
              // final comments = foodProvider.getCommentsForFood(widget.foodItem.id!);
              final comments = <dynamic>[]; // مؤقت حتى يتم تطبيق الدالة
              if (comments.isEmpty) {
                return Container(
                  padding: const EdgeInsets.all(40),
                  child: Column(
                    children: [
                      Icon(
                        Icons.comment_outlined,
                        size: 60,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد تعليقات بعد',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'كن أول من يعلق على هذا الطعام',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey.shade500,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: comments.length,
                itemBuilder: (context, index) {
                  final comment = comments[index];
                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: Container(
                      // AdvancedFoodCommentWidget(comment: comment, foodItemId: widget.foodItem.id!),
                      padding: const EdgeInsets.all(16),
                      child: Text('تعليق مؤقت'),
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButtons(bool isAdmin) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (isAdmin)
          FloatingActionButton.small(
            heroTag: "edit_food",
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) =>
                      AddEditFoodScreen(foodItem: widget.foodItem),
                ),
              );
            },
            backgroundColor: AppColors.primary,
            child: const Icon(Icons.edit_rounded, color: Colors.white),
          ),
        const SizedBox(height: 16),
        FloatingActionButton(
          heroTag: "favorite_food",
          onPressed: () => _toggleFavorite(),
          backgroundColor: Colors.red,
          child: const Icon(Icons.favorite_rounded, color: Colors.white),
        ),
      ],
    );
  }

  void _toggleLike(FoodProvider provider, String? userId) async {
    if (userId == null) {
      Navigator.of(
        context,
      ).push(MaterialPageRoute(builder: (context) => const LoginScreen()));
      return;
    }

    try {
      // await provider.toggleFoodLike(widget.foodItem.id!, userId);
      HapticFeedback.lightImpact();
      // مؤقت حتى يتم تطبيق الدالة
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في تحديث الإعجاب: $e',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleFavorite() async {
    final favoritesProvider = Provider.of<FavoritesProvider>(
      context,
      listen: false,
    );
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (authProvider.currentUser == null) {
      Navigator.of(
        context,
      ).push(MaterialPageRoute(builder: (context) => const LoginScreen()));
      return;
    }

    try {
      await favoritesProvider.toggleFoodFavorite(widget.foodItem);
      HapticFeedback.mediumImpact();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في تحديث المفضلة: $e',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showRatingDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تقييم الطعام', style: GoogleFonts.cairo()),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('كيف تقيم هذا الطعام؟', style: GoogleFonts.cairo()),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(5, (index) {
                return IconButton(
                  onPressed: () {
                    setState(() {
                      _userRating = index + 1.0;
                    });
                  },
                  icon: Icon(
                    index < _userRating ? Icons.star : Icons.star_border,
                    color: Colors.amber,
                    size: 32,
                  ),
                );
              }),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _submitRating();
            },
            child: Text('تقييم', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _submitRating() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (authProvider.currentUser == null) {
      Navigator.of(
        context,
      ).push(MaterialPageRoute(builder: (context) => const LoginScreen()));
      return;
    }

    try {
      final foodProvider = Provider.of<FoodProvider>(context, listen: false);
      // await foodProvider.rateFoodItem(widget.foodItem.id!, authProvider.currentUser!.uid, _userRating);
      // مؤقت حتى يتم تطبيق الدالة

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تقييم الطعام بنجاح', style: GoogleFonts.cairo()),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في تقييم الطعام: $e',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  double _userRating = 0.0;
}

import 'package:flutter/material.dart';

class MedicationReminder {
  final int id; // Unique ID for the notification
  final String medicationId;
  final String medicationName;
  final String? medicationDosage;
  final TimeOfDay time;
  final bool isActive;

  MedicationReminder({
    required this.id,
    required this.medicationId,
    required this.medicationName,
    this.medicationDosage,
    required this.time,
    this.isActive = true,
  });

  // Method to convert to a map for JSON serialization (e.g., for SharedPreferences)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'medicationId': medicationId,
      'medicationName': medicationName,
      'medicationDosage': medicationDosage,
      'hour': time.hour,
      'minute': time.minute,
      'isActive': isActive,
    };
  }

  // Factory constructor to create from a map (e.g., from SharedPreferences)
  factory MedicationReminder.fromJson(Map<String, dynamic> json) {
    return MedicationReminder(
      id: json['id'],
      medicationId: json['medicationId'],
      medicationName: json['medicationName'],
      medicationDosage: json['medicationDosage'],
      time: TimeOfDay(hour: json['hour'], minute: json['minute']),
      isActive: json['isActive'] ?? true,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../models/review.dart';
import '../services/firestore_service.dart';
import '../utils/app_constants.dart';

class ReviewService {
  static final FirestoreService _firestoreService = FirestoreService();

  /// إضافة مراجعة جديدة
  static Future<void> addReview(Review review) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('يجب تسجيل الدخول أولاً');

      // التحقق من عدم وجود مراجعة سابقة من نفس المستخدم
      final existingReview = await getUserReview(
        review.targetId,
        review.targetType,
      );
      if (existingReview != null) {
        throw Exception('لديك مراجعة سابقة لهذا العنصر');
      }

      // إضافة المراجعة
      final reviewData = review.copyWith(
        userId: user.uid,
        userName: user.displayName ?? user.email ?? 'مستخدم',
        userAvatar: user.photoURL,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestoreService.addDocument('reviews', reviewData.toMap());

      // تحديث إحصائيات التقييم
      await _updateTargetRating(review.targetId, review.targetType);

      debugPrint('تم إضافة المراجعة بنجاح');
    } catch (e) {
      debugPrint('خطأ في إضافة المراجعة: $e');
      rethrow;
    }
  }

  /// تحديث مراجعة موجودة
  static Future<void> updateReview(
    String reviewId,
    Review updatedReview,
  ) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('يجب تسجيل الدخول أولاً');

      final reviewData = updatedReview.copyWith(updatedAt: DateTime.now());

      await _firestoreService.updateDocument(
        'reviews',
        reviewId,
        reviewData.toMap(),
      );

      // تحديث إحصائيات التقييم
      await _updateTargetRating(
        updatedReview.targetId,
        updatedReview.targetType,
      );

      debugPrint('تم تحديث المراجعة بنجاح');
    } catch (e) {
      debugPrint('خطأ في تحديث المراجعة: $e');
      rethrow;
    }
  }

  /// حذف مراجعة
  static Future<void> deleteReview(
    String reviewId,
    String targetId,
    String targetType,
  ) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _firestoreService.deleteDocument('reviews', reviewId);

      // تحديث إحصائيات التقييم
      await _updateTargetRating(targetId, targetType);

      debugPrint('تم حذف المراجعة بنجاح');
    } catch (e) {
      debugPrint('خطأ في حذف المراجعة: $e');
      rethrow;
    }
  }

  /// الحصول على مراجعات عنصر معين
  static Future<List<Review>> getReviews(
    String targetId,
    String targetType,
  ) async {
    try {
      final querySnapshot = await _firestoreService.db
          .collection('reviews')
          .where('targetId', isEqualTo: targetId)
          .where('targetType', isEqualTo: targetType)
          .where('isReported', isEqualTo: false)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Review.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب المراجعات: $e');
      return [];
    }
  }

  /// الحصول على مراجعة المستخدم الحالي
  static Future<Review?> getUserReview(
    String targetId,
    String targetType,
  ) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return null;

      final querySnapshot = await _firestoreService.db
          .collection('reviews')
          .where('targetId', isEqualTo: targetId)
          .where('targetType', isEqualTo: targetType)
          .where('userId', isEqualTo: user.uid)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return Review.fromFirestore(querySnapshot.docs.first);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في جلب مراجعة المستخدم: $e');
      return null;
    }
  }

  /// الحصول على إحصائيات التقييمات
  static Future<Map<String, dynamic>> getReviewStats(
    String targetId,
    String targetType,
  ) async {
    try {
      final reviews = await getReviews(targetId, targetType);

      if (reviews.isEmpty) {
        return {
          'averageRating': 0.0,
          'totalReviews': 0,
          'ratingDistribution': {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
          'verifiedReviews': 0,
        };
      }

      final totalRating = reviews.fold<double>(
        0.0,
        (total, review) => total + review.rating,
      );
      final averageRating = totalRating / reviews.length;

      final distribution = <int, int>{1: 0, 2: 0, 3: 0, 4: 0, 5: 0};
      int verifiedCount = 0;

      for (final review in reviews) {
        final roundedRating = review.rating.round();
        distribution[roundedRating] = (distribution[roundedRating] ?? 0) + 1;

        if (review.isVerified) {
          verifiedCount++;
        }
      }

      return {
        'averageRating': averageRating,
        'totalReviews': reviews.length,
        'ratingDistribution': distribution,
        'verifiedReviews': verifiedCount,
      };
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات التقييمات: $e');
      return {
        'averageRating': 0.0,
        'totalReviews': 0,
        'ratingDistribution': {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
        'verifiedReviews': 0,
      };
    }
  }

  /// تحديث تقييم العنصر المستهدف
  static Future<void> _updateTargetRating(
    String targetId,
    String targetType,
  ) async {
    try {
      final stats = await getReviewStats(targetId, targetType);

      String collection;
      switch (targetType) {
        case 'restaurant':
          collection = AppConstants.restaurantsCollection;
          break;
        case 'product':
          collection = AppConstants.productsCollection;
          break;
        default:
          return;
      }

      await _firestoreService.updateDocument(collection, targetId, {
        'rating': stats['averageRating'],
        'reviewCount': stats['totalReviews'],
      });
    } catch (e) {
      debugPrint('خطأ في تحديث تقييم العنصر: $e');
    }
  }

  /// تمييز مراجعة كمفيدة
  static Future<void> markReviewHelpful(String reviewId) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('يجب تسجيل الدخول أولاً');

      final reviewDoc = await _firestoreService.db
          .collection('reviews')
          .doc(reviewId)
          .get();

      if (!reviewDoc.exists) return;

      final review = Review.fromFirestore(reviewDoc);
      final helpfulUsers = List<String>.from(review.helpfulUsers);

      if (helpfulUsers.contains(user.uid)) {
        // إزالة التصويت
        helpfulUsers.remove(user.uid);
      } else {
        // إضافة التصويت
        helpfulUsers.add(user.uid);
      }

      await _firestoreService.updateDocument('reviews', reviewId, {
        'helpfulUsers': helpfulUsers,
        'helpfulCount': helpfulUsers.length,
      });
    } catch (e) {
      debugPrint('خطأ في تمييز المراجعة كمفيدة: $e');
      rethrow;
    }
  }

  /// الإبلاغ عن مراجعة
  static Future<void> reportReview(String reviewId, String reason) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _firestoreService.updateDocument('reviews', reviewId, {
        'isReported': true,
        'reportReason': reason,
        'reportedBy': user.uid,
        'reportedAt': FieldValue.serverTimestamp(),
      });

      // إضافة سجل في مجموعة التقارير
      await _firestoreService.addDocument('reports', {
        'type': 'review',
        'contentId': reviewId,
        'reporterId': user.uid,
        'reason': reason,
        'status': 'pending',
        'createdAt': FieldValue.serverTimestamp(),
      });

      debugPrint('تم الإبلاغ عن المراجعة بنجاح');
    } catch (e) {
      debugPrint('خطأ في الإبلاغ عن المراجعة: $e');
      rethrow;
    }
  }

  /// الحصول على مراجعات المستخدم
  static Future<List<Review>> getUserReviews() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return [];

      final querySnapshot = await _firestoreService.db
          .collection('reviews')
          .where('userId', isEqualTo: user.uid)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Review.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب مراجعات المستخدم: $e');
      return [];
    }
  }

  /// البحث في المراجعات
  static Future<List<Review>> searchReviews(String query) async {
    try {
      final querySnapshot = await _firestoreService.db
          .collection('reviews')
          .where('isReported', isEqualTo: false)
          .orderBy('createdAt', descending: true)
          .limit(100)
          .get();

      final reviews = querySnapshot.docs
          .map((doc) => Review.fromFirestore(doc))
          .toList();

      // فلترة النتائج محلياً
      return reviews.where((review) {
        final lowerQuery = query.toLowerCase();
        return review.title.toLowerCase().contains(lowerQuery) ||
            review.comment.toLowerCase().contains(lowerQuery) ||
            review.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في المراجعات: $e');
      return [];
    }
  }
}

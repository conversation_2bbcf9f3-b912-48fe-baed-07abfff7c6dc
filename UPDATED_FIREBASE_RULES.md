# قواعد Firebase المحدثة للتطبيق 🔥

## 🎯 **القواعد المحدثة لتتوافق مع AuthProvider**

### **Firestore Rules:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // ==================== Helper Functions ====================
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      // التحقق من role في مستند المستخدم أو custom claims
      return isAuthenticated() && (
        request.auth.token.admin == true || 
        request.auth.token.role == 'admin' ||
        // التحقق من role في مستند المستخدم
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin'
      );
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Check if the user is the owner of the document being updated/deleted
    function isResourceOwner() {
      return isOwner(resource.data.userId);
    }
    
    // Check if the user is creating a document they own
    function isCreatingOwnedResource() {
      return isOwner(request.resource.data.userId);
    }
    
    // Allow safe +1 / -1 adjustments for a single counter field on a parent doc
    function canAdjustCounter(field) {
      return isAuthenticated() &&
             request.resource.data.diff(resource.data).changedKeys().hasOnly([field]) &&
             (
               request.resource.data[field] == ((resource.data[field] == null ? 0 : resource.data[field]) + 1) ||
               request.resource.data[field] == ((resource.data[field] == null ? 0 : resource.data[field]) - 1)
             );
    }
    
    // ==================== User Profile ====================
    match /users/{userId} {
      allow read, update: if isOwner(userId) || isAdmin();
      allow create: if isOwner(userId);
      // منع المستخدمين من تغيير role الخاص بهم
      allow update: if isOwner(userId) && 
                       (!request.resource.data.keys().hasAny(['role']) || 
                        request.resource.data.role == resource.data.role);
      // المشرفون يمكنهم تغيير الأدوار
      allow update: if isAdmin();
    }
    
    // ==================== All Collections Logic ====================
    match /{collection}/{docId} {
      // --- READ ---
      allow read: if
        // Publicly readable collections
        (collection in ['foodItems', 'medications', 'recipes', 'restaurants', 'pharmacies', 'stores', 'articles', 'hospitals', 'doctors', 'emergency_contacts', 'faqs', 'legal', 'sliderItems', 'banners', 'app_settings', 'forum_posts', 'posts']) ||
        // User-generated content (auth required)
        (isAuthenticated() && collection in ['foodSuggestions', 'medicationSuggestions', 'articleContributions', 'doctorContributions', 'user_reports']) ||
        // User's private health data (owner or admin)
        ((isResourceOwner() || isAdmin()) && collection in ['symptom_entries', 'medication_reminders', 'nutrition_entries', 'nutrition_goals']) ||
        // Admin-only collections
        (isAdmin() && collection in ['analytics', 'audit_logs', 'admin']);
      
      // --- CREATE ---
      allow create: if
        // User-generated content (must be owner)
        (isCreatingOwnedResource() && collection in ['foodSuggestions', 'medicationSuggestions', 'articleContributions', 'doctorContributions', 'user_reports', 'forum_posts', 'posts']) ||
        // User's private health data (must be owner)
        (isCreatingOwnedResource() && collection in ['symptom_entries', 'medication_reminders', 'nutrition_entries', 'nutrition_goals']) ||
        // Admin can create anything else
        (isAdmin());
      
      // --- UPDATE ---
      allow update: if
        // User-generated content (owner or admin)
        ((isResourceOwner() || isAdmin()) && collection in ['foodSuggestions', 'medicationSuggestions', 'articleContributions', 'doctorContributions', 'user_reports', 'forum_posts', 'posts']) ||
        // User's private health data (owner only)
        (isResourceOwner() && collection in ['symptom_entries', 'medication_reminders', 'nutrition_entries', 'nutrition_goals']) ||
        // Counter adjustments on medications
        (collection == 'medications' && (canAdjustCounter('likesCount') || canAdjustCounter('commentsCount'))) ||
        // Admin can update anything else
        (isAdmin());
      
      // --- DELETE ---
      allow delete: if
        // User-generated content (owner or admin)
        ((isResourceOwner() || isAdmin()) && collection in ['foodSuggestions', 'medicationSuggestions', 'articleContributions', 'doctorContributions', 'user_reports', 'forum_posts', 'posts']) ||
        // User's private health data (owner only)
        (isResourceOwner() && collection in ['symptom_entries', 'medication_reminders', 'nutrition_entries', 'nutrition_goals']) ||
        // Admin can delete anything else
        (isAdmin());
      
      // ==================== Generic Subcollections ====================
      match /comments/{commentId} {
        allow read: if true;
        allow create: if isCreatingOwnedResource();
        allow update: if isResourceOwner();
        allow delete: if isResourceOwner() || isAdmin();
      }
      
      match /replies/{replyId} {
        allow read: if true;
        allow create: if isCreatingOwnedResource();
        allow update: if isResourceOwner();
        allow delete: if isResourceOwner() || isAdmin();
      }
      
      match /ratings/{userId} {
        allow read: if true;
        allow write: if isOwner(userId);
      }
      
      match /likes/{userId} {
        allow read: if true;
        allow write: if isOwner(userId);
      }
      
      match /favorites/{userId} {
        allow read: if true;
        allow write: if isOwner(userId);
      }
      
      match /readLater/{userId} {
        allow read: if true;
        allow write: if isOwner(userId);
      }
      
      match /reviews/{reviewId} {
        allow read: if true;
        allow create: if isCreatingOwnedResource();
        allow update: if isResourceOwner();
        allow delete: if isResourceOwner() || isAdmin();
      }
    }
    
    // ==================== Doctor Appointments ====================
    match /doctors/{doctorId}/appointments/{appointmentId} {
      allow read: if isResourceOwner() || isOwner(resource.data.doctorId) || isAdmin();
      allow create: if isCreatingOwnedResource();
      allow update: if isResourceOwner() || isOwner(resource.data.doctorId) || isAdmin();
      allow delete: if isResourceOwner() || isAdmin();
    }
    
    // ==================== Messaging / Conversations ====================
    match /conversations/{conversationId} {
      allow create: if isAuthenticated() && request.auth.uid in request.resource.data.participants;
      allow read: if isAuthenticated() && request.auth.uid in resource.data.participants;
      allow update, delete: if isAuthenticated() && request.auth.uid in resource.data.participants;
      
      match /messages/{messageId} {
        allow read: if isAuthenticated() && request.auth.uid in get(/databases/$(database)/documents/conversations/$(conversationId)).data.participants;
        allow create: if isCreatingOwnedResource() && request.auth.uid in get(/databases/$(database)/documents/conversations/$(conversationId)).data.participants;
        allow update, delete: if false;
      }
    }
    
    // ==================== Global Admin Override ====================
    match /{document=**} {
      allow read, write: if isAdmin();
    }
  }
}
```

### **Storage Rules:**
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && (
        request.auth.token.admin == true || 
        request.auth.token.role == 'admin' ||
        get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin'
      );
    }
    
    // صور المحتوى العام
    match /{collection}/{docId}/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // صور المستخدمين
    match /users/{userId}/{allPaths=**} {
      allow read, write: if isAuthenticated() && 
                            (request.auth.uid == userId || isAdmin());
    }
    
    // الملفات المؤقتة
    match /temp/{userId}/{allPaths=**} {
      allow read, write: if isAuthenticated() && request.auth.uid == userId;
    }
  }
}
```

---

## 🔧 **إعداد المشرف الأول**

### **الطريقة 1: يدوياً في Firestore**
1. اذهب إلى **Firestore Database > Data**
2. اذهب إلى مجموعة `users`
3. اختر مستند المستخدم الخاص بك
4. أضف/عدل حقل: `role: "admin"`

### **الطريقة 2: من خلال الكود**
```dart
// في AuthProvider أو أي مكان مناسب
Future<void> makeUserAdmin(String userId) async {
  await FirebaseFirestore.instance
      .collection('users')
      .doc(userId)
      .update({'role': 'admin'});
}
```

---

## 🚀 **التطبيق**

1. **انسخ قواعد Firestore** إلى Firebase Console
2. **انسخ قواعد Storage** إلى Firebase Console
3. **اضغط Publish** لكل منهما
4. **أضف role: "admin"** لمستندك في مجموعة users
5. **أعد تشغيل التطبيق**

---

## ✅ **المميزات**

- ✅ **متوافقة مع AuthProvider الحالي**
- ✅ **أمان عالي** - المستخدمون لا يمكنهم تغيير أدوارهم
- ✅ **مرونة** - تدعم custom claims و role في المستند
- ✅ **شاملة** - تغطي جميع المجموعات والميزات
- ✅ **قابلة للتطوير** - سهلة الإضافة عليها

**جاهزة للاستخدام الفوري! 🎉**
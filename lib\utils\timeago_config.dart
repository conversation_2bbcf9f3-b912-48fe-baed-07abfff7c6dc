import 'package:timeago/timeago.dart' as timeago;

class TimeagoConfig {
  static void initialize() {
    timeago.setLocaleMessages('ar', ArabicMessages());
  }
}

class ArabicMessages implements timeago.LookupMessages {
  @override
  String prefixAgo() => '';
  
  @override
  String prefixFromNow() => '';
  
  @override
  String suffixAgo() => '';
  
  @override
  String suffixFromNow() => 'من الآن';
  
  @override
  String lessThanOneMinute(int seconds) => 'الآن';
  
  @override
  String aboutAMinute(int minutes) => 'منذ دقيقة';
  
  @override
  String minutes(int minutes) => 'منذ $minutes دقائق';
  
  @override
  String aboutAnHour(int minutes) => 'منذ ساعة';
  
  @override
  String hours(int hours) => 'منذ $hours ساعات';
  
  @override
  String aDay(int hours) => 'منذ يوم';
  
  @override
  String days(int days) => 'منذ $days أيام';
  
  @override
  String aboutAMonth(int days) => 'منذ شهر';
  
  @override
  String months(int months) => 'منذ $months أشهر';
  
  @override
  String aboutAYear(int year) => 'منذ سنة';
  
  @override
  String years(int years) => 'منذ $years سنوات';
  
  @override
  String wordSeparator() => ' ';
}

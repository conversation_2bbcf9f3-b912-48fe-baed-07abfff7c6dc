import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:geolocator/geolocator.dart';

import 'package:yassincil/services/location_service.dart';
import 'package:yassincil/models/emergency_contact.dart';

class NearbyHospitalsScreen extends StatefulWidget {
  const NearbyHospitalsScreen({super.key});

  @override
  State<NearbyHospitalsScreen> createState() => _NearbyHospitalsScreenState();
}

class _NearbyHospitalsScreenState extends State<NearbyHospitalsScreen> {
  final LocationService _locationService = LocationService.instance;
  List<Hospital> _hospitals = [];
  bool _isLoading = true;
  String? _errorMessage;
  Position? _currentPosition;

  @override
  void initState() {
    super.initState();
    _initializeLocation();
  }

  Future<void> _initializeLocation() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final hasPermission = await _locationService.initialize();
      if (!hasPermission) {
        if (mounted) {
          final shouldRequest =
              await LocationService.showLocationPermissionDialog(context);
          if (shouldRequest) {
            await _locationService.requestLocationPermission();
          }
        }
      }

      _currentPosition = await _locationService.getCurrentPosition();
      await _loadHospitals();
    } catch (e) {
      setState(() {
        _errorMessage = 'فشل في تحديد الموقع: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadHospitals() async {
    // Sample hospitals data - in a real app, this would come from a database or API
    final sampleHospitals = [
      Hospital(
        id: '1',
        name: 'مستشفى الملك فيصل التخصصي',
        address: 'شارع الملك فيصل، الرياض',
        phone: '+966112889999',
        latitude: 24.7136,
        longitude: 46.6753,
        distance: 0,
        hasEmergency: true,
        hasGastroenterology: true,
        acceptsInsurance: true,
        rating: 4.8,
        services: ['طوارئ 24 ساعة', 'أمراض الجهاز الهضمي', 'عيادة السيلياك'],
        openingHours: '24 ساعة',
        isOpen: true,
      ),
      Hospital(
        id: '2',
        name: 'مستشفى الملك خالد الجامعي',
        address: 'طريق الملك خالد، الرياض',
        phone: '+966114672222',
        latitude: 24.7236,
        longitude: 46.6853,
        distance: 0,
        hasEmergency: true,
        hasGastroenterology: true,
        acceptsInsurance: true,
        rating: 4.6,
        services: ['طوارئ', 'أمراض باطنية', 'تغذية علاجية'],
        openingHours: '24 ساعة',
        isOpen: true,
      ),
      Hospital(
        id: '3',
        name: 'مستشفى دله',
        address: 'حي النخيل، الرياض',
        phone: '+966112000000',
        latitude: 24.7036,
        longitude: 46.6653,
        distance: 0,
        hasEmergency: true,
        hasGastroenterology: false,
        acceptsInsurance: true,
        rating: 4.4,
        services: ['طوارئ', 'عيادات خارجية'],
        openingHours: '24 ساعة',
        isOpen: true,
      ),
    ];

    if (_currentPosition != null) {
      // Calculate distances and sort by proximity
      for (int i = 0; i < sampleHospitals.length; i++) {
        final distance = _locationService.calculateDistance(
          _currentPosition!.latitude,
          _currentPosition!.longitude,
          sampleHospitals[i].latitude,
          sampleHospitals[i].longitude,
        );
        // Create new hospital with updated distance
        sampleHospitals[i] = Hospital(
          id: sampleHospitals[i].id,
          name: sampleHospitals[i].name,
          address: sampleHospitals[i].address,
          phone: sampleHospitals[i].phone,
          latitude: sampleHospitals[i].latitude,
          longitude: sampleHospitals[i].longitude,
          distance: distance,
          hasEmergency: sampleHospitals[i].hasEmergency,
          hasGastroenterology: sampleHospitals[i].hasGastroenterology,
          acceptsInsurance: sampleHospitals[i].acceptsInsurance,
          rating: sampleHospitals[i].rating,
          services: sampleHospitals[i].services,
          openingHours: sampleHospitals[i].openingHours,
          isOpen: sampleHospitals[i].isOpen,
        );
      }

      sampleHospitals.sort((a, b) => a.distance.compareTo(b.distance));
    }

    setState(() {
      _hospitals = sampleHospitals;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'المستشفيات القريبة',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.red.shade600,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _initializeLocation,
            tooltip: 'تحديث الموقع',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحديد الموقع...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: Colors.red.shade400),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: GoogleFonts.cairo(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializeLocation,
              child: Text('إعادة المحاولة', style: GoogleFonts.cairo()),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _initializeLocation,
      child: Column(
        children: [
          if (_currentPosition != null) _buildLocationInfo(),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _hospitals.length,
              itemBuilder: (context, index) {
                return _buildHospitalCard(_hospitals[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        border: Border(bottom: BorderSide(color: Colors.blue.shade200)),
      ),
      child: Row(
        children: [
          Icon(Icons.location_on, color: Colors.blue.shade600),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'تم تحديد موقعك بنجاح',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.blue.shade800,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Text(
            '${_hospitals.length} مستشفى',
            style: GoogleFonts.cairo(fontSize: 12, color: Colors.blue.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildHospitalCard(Hospital hospital) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: hospital.isOpen ? Colors.green : Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    hospital.name,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ),
                if (_currentPosition != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _locationService.formatDistance(hospital.distance),
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    hospital.address,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Text(
                  hospital.openingHours,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
                const Spacer(),
                Row(
                  children: [
                    Icon(Icons.star, size: 16, color: Colors.amber),
                    const SizedBox(width: 4),
                    Text(
                      hospital.rating.toString(),
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            if (hospital.services.isNotEmpty) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 6,
                runSpacing: 6,
                children: hospital.services
                    .map(
                      (service) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color:
                              hospital.hasGastroenterology &&
                                  service.contains('الهضمي')
                              ? Colors.green.shade50
                              : Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color:
                                hospital.hasGastroenterology &&
                                    service.contains('الهضمي')
                                ? Colors.green.shade200
                                : Colors.grey.shade300,
                          ),
                        ),
                        child: Text(
                          service,
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color:
                                hospital.hasGastroenterology &&
                                    service.contains('الهضمي')
                                ? Colors.green.shade700
                                : Colors.grey.shade700,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _makePhoneCall(hospital.phone),
                    icon: const Icon(Icons.phone, size: 18),
                    label: Text(
                      'اتصال',
                      style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade600,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _openDirections(hospital),
                    icon: const Icon(Icons.directions, size: 18),
                    label: Text(
                      'الاتجاهات',
                      style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);

    try {
      if (await canLaunchUrl(launchUri)) {
        await launchUrl(launchUri);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'لا يمكن إجراء المكالمة',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الاتصال: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _openDirections(Hospital hospital) async {
    final Uri launchUri = Uri(
      scheme: 'https',
      host: 'www.google.com',
      path: '/maps/dir/',
      queryParameters: {
        'destination': '${hospital.latitude},${hospital.longitude}',
      },
    );

    try {
      if (await canLaunchUrl(launchUri)) {
        await launchUrl(launchUri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('لا يمكن فتح الخرائط', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح الخرائط: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

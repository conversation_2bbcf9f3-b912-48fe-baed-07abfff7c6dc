import 'package:flutter_test/flutter_test.dart';
import 'package:yassincil/models/doctor.dart';

void main() {
  group('Doctor Model Tests', () {
    test('should create a doctor with required fields', () {
      // Arrange
      final doctor = Doctor(
        name: 'د. أحمد محمد',
        specialty: 'أمراض الجهاز الهضمي',
        clinic: 'مستشفى الملك فيصل',
        address: 'الرياض - حي العليا',
        phoneNumber: '+966112345678',
        createdAt: DateTime.now(),
      );

      // Assert
      expect(doctor.name, 'د. أحمد محمد');
      expect(doctor.specialty, 'أمراض الجهاز الهضمي');
      expect(doctor.clinic, 'مستشفى الملك فيصل');
      expect(doctor.address, 'الرياض - حي العليا');
      expect(doctor.phoneNumber, '+966112345678');
      expect(doctor.createdAt, isA<DateTime>());
    });

    test('should create doctor with default values', () {
      // Arrange
      final doctor = Doctor(
        name: 'د. سارة أحمد',
        specialty: 'طب الأطفال',
        clinic: 'عيادة الأطفال',
        address: 'جدة - حي الزهراء',
        phoneNumber: '+966112345679',
        createdAt: DateTime.now(),
      );

      // Assert
      expect(doctor.workingHours, '');
      expect(doctor.rating, 0.0);
      expect(doctor.imageUrls, isEmpty);
      expect(doctor.languages, isEmpty);
      expect(doctor.services, isEmpty);
      expect(doctor.specializations, isEmpty);
      expect(doctor.isVerified, false);
      expect(doctor.isAvailable, true);
      expect(doctor.isApproved, true);
      expect(doctor.isFeatured, false);
      expect(doctor.likesCount, 0);
      expect(doctor.reviewsCount, 0);
      expect(doctor.appointmentsCount, 0);
    });

    test('should create doctor with optional fields', () {
      // Arrange
      final doctor = Doctor(
        name: 'د. محمد علي',
        specialty: 'جراحة عامة',
        clinic: 'مستشفى الحبيب',
        address: 'الدمام - الكورنيش',
        phoneNumber: '+966112345680',
        createdAt: DateTime.now(),
        bio: 'طبيب متخصص في الجراحة العامة',
        education: 'بكالوريوس طب وجراحة - جامعة الملك سعود',
        experience: '15 سنة خبرة في المجال الطبي',
        languages: ['العربية', 'الإنجليزية'],
        email: '<EMAIL>',
        website: 'https://dr-mohamed.com',
        services: ['استشارات طبية', 'جراحة عامة', 'فحوصات دورية'],
        consultationFee: 200.0,
        isVerified: true,
        isFeatured: true,
        rating: 4.8,
        likesCount: 150,
        reviewsCount: 45,
        appointmentsCount: 300,
      );

      // Assert
      expect(doctor.bio, 'طبيب متخصص في الجراحة العامة');
      expect(doctor.education, 'بكالوريوس طب وجراحة - جامعة الملك سعود');
      expect(doctor.experience, '15 سنة خبرة في المجال الطبي');
      expect(doctor.languages.length, 2);
      expect(doctor.email, '<EMAIL>');
      expect(doctor.website, 'https://dr-mohamed.com');
      expect(doctor.services.length, 3);
      expect(doctor.consultationFee, 200.0);
      expect(doctor.isVerified, true);
      expect(doctor.isFeatured, true);
      expect(doctor.rating, 4.8);
      expect(doctor.likesCount, 150);
      expect(doctor.reviewsCount, 45);
      expect(doctor.appointmentsCount, 300);
    });

    test('should convert to map correctly', () {
      // Arrange
      final doctor = Doctor(
        name: 'د. فاطمة خالد',
        specialty: 'أمراض النساء والولادة',
        clinic: 'مستشفى النساء والولادة',
        address: 'الرياض - حي النرجس',
        phoneNumber: '+966112345681',
        createdAt: DateTime.now(),
        rating: 4.5,
        isVerified: true,
        likesCount: 75,
        reviewsCount: 20,
      );

      // Act
      final map = doctor.toMap();

      // Assert
      expect(map['name'], 'د. فاطمة خالد');
      expect(map['specialty'], 'أمراض النساء والولادة');
      expect(map['clinic'], 'مستشفى النساء والولادة');
      expect(map['address'], 'الرياض - حي النرجس');
      expect(map['phoneNumber'], '+966112345681');
      expect(map['rating'], 4.5);
      expect(map['isVerified'], true);
      expect(map['likesCount'], 75);
      expect(map['reviewsCount'], 20);
      expect(map['createdAt'], isA<dynamic>());
    });

    test('should have valid properties', () {
      // Arrange
      final doctor = Doctor(
        name: 'د. عبدالله سالم',
        specialty: 'طب الأسرة',
        clinic: 'مركز الرعاية الأولية',
        address: 'مكة - العزيزية',
        phoneNumber: '+966112345682',
        createdAt: DateTime.now(),
      );

      // Assert
      expect(doctor.name, isNotEmpty);
      expect(doctor.specialty, isNotEmpty);
      expect(doctor.clinic, isNotEmpty);
      expect(doctor.address, isNotEmpty);
      expect(doctor.phoneNumber, isNotEmpty);
      expect(doctor.createdAt, isA<DateTime>());
      expect(doctor.rating, greaterThanOrEqualTo(0.0));
      expect(doctor.rating, lessThanOrEqualTo(5.0));
    });

    test('should handle imageUrl compatibility', () {
      // Arrange
      final doctorWithImages = Doctor(
        name: 'د. نورا أحمد',
        specialty: 'طب الأطفال',
        clinic: 'عيادة الأطفال المتخصصة',
        address: 'الرياض - حي الملقا',
        phoneNumber: '+966112345683',
        createdAt: DateTime.now(),
        imageUrls: ['https://example.com/doctor1.jpg', 'https://example.com/doctor2.jpg'],
      );

      final doctorWithoutImages = Doctor(
        name: 'د. خالد محمد',
        specialty: 'طب الأسنان',
        clinic: 'عيادة الأسنان',
        address: 'جدة - الروضة',
        phoneNumber: '+966112345684',
        createdAt: DateTime.now(),
      );

      // Assert
      expect(doctorWithImages.imageUrl, 'https://example.com/doctor1.jpg');
      expect(doctorWithoutImages.imageUrl, isNull);
    });
  });
}

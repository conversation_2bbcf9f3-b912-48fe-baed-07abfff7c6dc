import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/data/sample_data.dart';
// import 'package:yassincil/screens/admin/add_edit_store_screen.dart';

class StoresManagementScreen extends StatefulWidget {
  const StoresManagementScreen({super.key});

  @override
  State<StoresManagementScreen> createState() => _StoresManagementScreenState();
}

class _StoresManagementScreenState extends State<StoresManagementScreen> {
  String _searchQuery = '';
  String _selectedCategory = 'الكل';
  final List<String> _categories = [
    'الكل',
    'متاجر صحية',
    'سوبر ماركت',
    'مخابز خاصة',
    'متاجر أونلاين',
  ];

  List<Map<String, dynamic>> get _filteredStores {
    var stores = SampleData.stores;

    if (_searchQuery.isNotEmpty) {
      stores = stores
          .where(
            (store) =>
                store['name'].toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                store['description'].toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ),
          )
          .toList();
    }

    if (_selectedCategory != 'الكل') {
      stores = stores
          .where((store) => store['category'] == _selectedCategory)
          .toList();
    }

    return stores;
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final isAdmin = authProvider.isAdmin;

    if (!isAdmin) {
      return Scaffold(
        appBar: AppBar(title: Text('غير مصرح', style: GoogleFonts.cairo())),
        body: Center(
          child: Text(
            'ليس لديك صلاحية للوصول لهذه الصفحة',
            style: GoogleFonts.cairo(),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'إدارة المتاجر',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.green.shade600,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics, color: Colors.white),
            onPressed: () => _showStatistics(),
            tooltip: 'الإحصائيات',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildStatisticsCard(),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                setState(() {});
              },
              child: _filteredStores.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _filteredStores.length,
                      itemBuilder: (context, index) {
                        return _buildStoreCard(_filteredStores[index]);
                      },
                    ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _addNewStore(),
        backgroundColor: Colors.green.shade600,
        icon: const Icon(Icons.add, color: Colors.white),
        label: Text(
          'إضافة متجر',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade600,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // شريط البحث
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'البحث في المتاجر...',
                hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500),
                prefixIcon: Icon(Icons.search, color: Colors.green.shade600),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 15,
                ),
              ),
              style: GoogleFonts.cairo(),
            ),
          ),
          const SizedBox(height: 16),
          // فلاتر التصنيف
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = _selectedCategory == category;

                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(
                      category,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: isSelected
                            ? Colors.white
                            : Colors.green.shade700,
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = category;
                      });
                    },
                    backgroundColor: Colors.green.shade50,
                    selectedColor: Colors.green.shade700,
                    checkmarkColor: Colors.white,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCard() {
    final totalStores = SampleData.stores.length;
    final categoryCounts = <String, int>{};

    for (final store in SampleData.stores) {
      final category = store['category'];
      categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
    }

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade400, Colors.green.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إجمالي المتاجر',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                Text(
                  '$totalStores',
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'النتائج المفلترة',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                Text(
                  '${_filteredStores.length}',
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.store,
            size: 48,
            color: Colors.white.withValues(alpha: 0.7),
          ),
        ],
      ),
    );
  }

  Widget _buildStoreCard(Map<String, dynamic> store) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.store,
                    color: Colors.green.shade600,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        store['name'],
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        store['category'],
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.green.shade600,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            size: 14,
                            color: Colors.grey.shade500,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              store['address'],
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  icon: Icon(Icons.more_vert, color: Colors.grey.shade600),
                  onSelected: (value) {
                    if (value == 'edit') {
                      _editStore(store);
                    } else if (value == 'delete') {
                      _deleteStore(store);
                    } else if (value == 'view') {
                      _viewStoreDetails(store);
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'view',
                      child: Row(
                        children: [
                          const Icon(Icons.visibility, size: 18),
                          const SizedBox(width: 8),
                          Text('عرض التفاصيل', style: GoogleFonts.cairo()),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          const Icon(Icons.edit, size: 18),
                          const SizedBox(width: 8),
                          Text('تعديل', style: GoogleFonts.cairo()),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          const Icon(Icons.delete, size: 18, color: Colors.red),
                          const SizedBox(width: 8),
                          Text(
                            'حذف',
                            style: GoogleFonts.cairo(color: Colors.red),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              store['description'],
              style: GoogleFonts.cairo(
                fontSize: 13,
                color: Colors.grey.shade700,
                height: 1.4,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: store['hasGlutenFreeSection']
                        ? Colors.green.shade50
                        : Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: store['hasGlutenFreeSection']
                          ? Colors.green.shade200
                          : Colors.orange.shade200,
                    ),
                  ),
                  child: Text(
                    store['hasGlutenFreeSection']
                        ? 'قسم خالي من الجلوتين'
                        : 'منتجات عامة',
                    style: GoogleFonts.cairo(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      color: store['hasGlutenFreeSection']
                          ? Colors.green.shade700
                          : Colors.orange.shade700,
                    ),
                  ),
                ),
                const Spacer(),
                if (store['phone'] != null && store['phone'].isNotEmpty)
                  IconButton(
                    onPressed: () => _callStore(store['phone']),
                    icon: Icon(
                      Icons.phone,
                      color: Colors.green.shade600,
                      size: 20,
                    ),
                    tooltip: 'اتصال',
                  ),
                if (store['website'] != null && store['website'].isNotEmpty)
                  IconButton(
                    onPressed: () => _openWebsite(store['website']),
                    icon: Icon(
                      Icons.language,
                      color: Colors.blue.shade600,
                      size: 20,
                    ),
                    tooltip: 'الموقع الإلكتروني',
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.store_outlined, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'لا توجد متاجر',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على متاجر تطابق البحث',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _addNewStore() {
    _showAddEditStoreDialog();
  }

  void _editStore(Map<String, dynamic> store) {
    _showAddEditStoreDialog(store: store);
  }

  void _showAddEditStoreDialog({Map<String, dynamic>? store}) {
    final nameController = TextEditingController(text: store?['name'] ?? '');
    final categoryController = TextEditingController(
      text: store?['category'] ?? '',
    );
    final addressController = TextEditingController(
      text: store?['address'] ?? '',
    );
    final phoneController = TextEditingController(text: store?['phone'] ?? '');
    final descriptionController = TextEditingController(
      text: store?['description'] ?? '',
    );
    final websiteController = TextEditingController(
      text: store?['website'] ?? '',
    );
    bool hasGlutenFreeSection = store?['hasGlutenFreeSection'] ?? true;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            store == null ? 'إضافة متجر جديد' : 'تعديل المتجر',
            style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: InputDecoration(
                    labelText: 'اسم المتجر',
                    labelStyle: GoogleFonts.cairo(),
                    border: const OutlineInputBorder(),
                  ),
                  style: GoogleFonts.cairo(),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: categoryController,
                  decoration: InputDecoration(
                    labelText: 'التصنيف',
                    labelStyle: GoogleFonts.cairo(),
                    border: const OutlineInputBorder(),
                  ),
                  style: GoogleFonts.cairo(),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: addressController,
                  decoration: InputDecoration(
                    labelText: 'العنوان',
                    labelStyle: GoogleFonts.cairo(),
                    border: const OutlineInputBorder(),
                  ),
                  style: GoogleFonts.cairo(),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: phoneController,
                  decoration: InputDecoration(
                    labelText: 'رقم الهاتف',
                    labelStyle: GoogleFonts.cairo(),
                    border: const OutlineInputBorder(),
                  ),
                  style: GoogleFonts.cairo(),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: websiteController,
                  decoration: InputDecoration(
                    labelText: 'الموقع الإلكتروني',
                    labelStyle: GoogleFonts.cairo(),
                    border: const OutlineInputBorder(),
                  ),
                  style: GoogleFonts.cairo(),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: InputDecoration(
                    labelText: 'الوصف',
                    labelStyle: GoogleFonts.cairo(),
                    border: const OutlineInputBorder(),
                  ),
                  style: GoogleFonts.cairo(),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                CheckboxListTile(
                  title: Text(
                    'يحتوي على قسم خالي من الجلوتين',
                    style: GoogleFonts.cairo(),
                  ),
                  value: hasGlutenFreeSection,
                  onChanged: (value) {
                    setState(() {
                      hasGlutenFreeSection = value ?? true;
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('إلغاء', style: GoogleFonts.cairo()),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'يرجى إدخال اسم المتجر',
                        style: GoogleFonts.cairo(),
                      ),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      store == null ? 'تم إضافة المتجر' : 'تم تحديث المتجر',
                      style: GoogleFonts.cairo(),
                    ),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green.shade600,
                foregroundColor: Colors.white,
              ),
              child: Text(
                store == null ? 'إضافة' : 'تحديث',
                style: GoogleFonts.cairo(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _deleteStore(Map<String, dynamic> store) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف المتجر', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف "${store['name']}"؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // تنفيذ الحذف الفعلي (مؤقتاً: رسالة تأكيد)
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف المتجر', style: GoogleFonts.cairo()),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _viewStoreDetails(Map<String, dynamic> store) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(store['name'], style: GoogleFonts.cairo()),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('التصنيف: ${store['category']}', style: GoogleFonts.cairo()),
              const SizedBox(height: 8),
              Text('العنوان: ${store['address']}', style: GoogleFonts.cairo()),
              const SizedBox(height: 8),
              Text(
                'الوصف:',
                style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
              ),
              Text(store['description'], style: GoogleFonts.cairo()),
              const SizedBox(height: 8),
              Text(
                'المنتجات:',
                style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
              ),
              ...store['products'].map<Widget>(
                (product) =>
                    Text('• $product', style: GoogleFonts.cairo(fontSize: 12)),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _callStore(String phone) {
    // الاتصال بالمتجر (مؤقتاً: رسالة تأكيد)
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('الاتصال بـ $phone', style: GoogleFonts.cairo()),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _openWebsite(String website) {
    // فتح موقع المتجر (مؤقتاً: رسالة تأكيد)
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('فتح الموقع: $website', style: GoogleFonts.cairo()),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _showStatistics() {
    final categoryCounts = <String, int>{};
    for (final store in SampleData.stores) {
      final category = store['category'];
      categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إحصائيات المتاجر', style: GoogleFonts.cairo()),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: categoryCounts.entries
              .map(
                (entry) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(entry.key, style: GoogleFonts.cairo()),
                      Text(
                        '${entry.value}',
                        style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }
}

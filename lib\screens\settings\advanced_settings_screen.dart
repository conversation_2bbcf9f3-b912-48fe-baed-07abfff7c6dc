import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../providers/auth_provider.dart' as app_auth;
import '../../services/local_notification_service.dart';
import '../../services/backup_service.dart';
import '../../services/security_service.dart';
import '../../utils/app_colors.dart';
import '../../utils/error_handler.dart';
import '../../widgets/loading_widget.dart';

class AdvancedSettingsScreen extends StatefulWidget {
  const AdvancedSettingsScreen({super.key});

  @override
  State<AdvancedSettingsScreen> createState() => _AdvancedSettingsScreenState();
}

class _AdvancedSettingsScreenState extends State<AdvancedSettingsScreen> {
  bool _isLoading = false;
  Map<String, bool> _privacySettings = {};
  bool _notificationsEnabled = true;
  String _reminderTime = '20:00';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _privacySettings = await SecurityService.getPrivacySettings();
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(context, e);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'الإعدادات المتقدمة',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const LoadingWidget(message: 'جاري تحميل الإعدادات...')
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildNotificationSettings(),
                  const SizedBox(height: 24),
                  _buildPrivacySettings(),
                  const SizedBox(height: 24),
                  _buildSecuritySettings(),
                  const SizedBox(height: 24),
                  _buildDataManagement(),
                  const SizedBox(height: 24),
                  _buildDangerZone(),
                ],
              ),
            ),
    );
  }

  Widget _buildNotificationSettings() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.notifications, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'الإشعارات',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: Text('تفعيل الإشعارات', style: GoogleFonts.cairo()),
              subtitle: Text(
                'تلقي إشعارات التطبيق',
                style: GoogleFonts.cairo(),
              ),
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
                _updateNotificationSettings();
              },
            ),
            if (_notificationsEnabled) ...[
              const Divider(),
              ListTile(
                title: Text('وقت التذكير اليومي', style: GoogleFonts.cairo()),
                subtitle: Text(_reminderTime, style: GoogleFonts.cairo()),
                trailing: const Icon(Icons.access_time),
                onTap: _selectReminderTime,
              ),
              ListTile(
                title: Text('اختبار الإشعارات', style: GoogleFonts.cairo()),
                subtitle: Text(
                  'إرسال إشعار تجريبي',
                  style: GoogleFonts.cairo(),
                ),
                trailing: const Icon(Icons.send),
                onTap: _testNotification,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacySettings() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.privacy_tip, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'الخصوصية',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildPrivacyOptions(),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildPrivacyOptions() {
    final options = [
      {
        'key': 'shareSymptoms',
        'title': 'مشاركة بيانات الأعراض',
        'subtitle': 'للمساعدة في البحث الطبي',
      },
      {
        'key': 'shareLocation',
        'title': 'مشاركة الموقع',
        'subtitle': 'لاقتراح مطاعم قريبة',
      },
      {
        'key': 'allowAnalytics',
        'title': 'السماح بالتحليلات',
        'subtitle': 'لتحسين التطبيق',
      },
      {
        'key': 'shareProfile',
        'title': 'إظهار الملف الشخصي',
        'subtitle': 'في المنتدى والمراجعات',
      },
    ];

    return options.map((option) {
      final key = option['key'] as String;
      return SwitchListTile(
        title: Text(option['title'] as String, style: GoogleFonts.cairo()),
        subtitle: Text(
          option['subtitle'] as String,
          style: GoogleFonts.cairo(),
        ),
        value: _privacySettings[key] ?? false,
        onChanged: (value) {
          setState(() {
            _privacySettings[key] = value;
          });
          _savePrivacySettings();
        },
      );
    }).toList();
  }

  Widget _buildSecuritySettings() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.security, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'الأمان',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              title: Text('تغيير كلمة المرور', style: GoogleFonts.cairo()),
              subtitle: Text(
                'تحديث كلمة المرور الحالية',
                style: GoogleFonts.cairo(),
              ),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _changePassword,
            ),
            ListTile(
              title: Text('المصادقة الثنائية', style: GoogleFonts.cairo()),
              subtitle: Text('طبقة حماية إضافية', style: GoogleFonts.cairo()),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _manageTwoFactor,
            ),
            ListTile(
              title: Text('سجل تسجيل الدخول', style: GoogleFonts.cairo()),
              subtitle: Text(
                'عرض محاولات تسجيل الدخول',
                style: GoogleFonts.cairo(),
              ),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _viewLoginHistory,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataManagement() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.storage, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'إدارة البيانات',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              title: Text('نسخ احتياطي', style: GoogleFonts.cairo()),
              subtitle: Text(
                'إنشاء نسخة احتياطية من بياناتك',
                style: GoogleFonts.cairo(),
              ),
              trailing: const Icon(Icons.backup),
              onTap: _createBackup,
            ),
            ListTile(
              title: Text('استعادة البيانات', style: GoogleFonts.cairo()),
              subtitle: Text(
                'استعادة من نسخة احتياطية',
                style: GoogleFonts.cairo(),
              ),
              trailing: const Icon(Icons.restore),
              onTap: _restoreBackup,
            ),
            ListTile(
              title: Text('تصدير البيانات', style: GoogleFonts.cairo()),
              subtitle: Text('تحميل جميع بياناتك', style: GoogleFonts.cairo()),
              trailing: const Icon(Icons.download),
              onTap: _exportData,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDangerZone() {
    return Card(
      elevation: 2,
      color: Colors.red.shade50,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning, color: Colors.red.shade600),
                const SizedBox(width: 8),
                Text(
                  'منطقة الخطر',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red.shade600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              title: Text('حذف جميع البيانات', style: GoogleFonts.cairo()),
              subtitle: Text(
                'حذف نهائي لجميع بياناتك',
                style: GoogleFonts.cairo(),
              ),
              trailing: Icon(Icons.delete_forever, color: Colors.red.shade600),
              onTap: _deleteAllData,
            ),
          ],
        ),
      ),
    );
  }

  // الدوال المساعدة
  Future<void> _updateNotificationSettings() async {
    try {
      if (_notificationsEnabled) {
        await LocalNotificationService.requestPermissions();
        await LocalNotificationService.scheduleSymptomReminder(
          time: _reminderTime,
          enabled: true,
        );
      } else {
        await LocalNotificationService.cancelAllNotifications();
      }

      if (mounted) {
        ErrorHandler.showSuccessSnackBar(context, 'تم تحديث إعدادات الإشعارات');
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(context, e);
      }
    }
  }

  Future<void> _selectReminderTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(
        hour: int.parse(_reminderTime.split(':')[0]),
        minute: int.parse(_reminderTime.split(':')[1]),
      ),
    );

    if (time != null) {
      setState(() {
        _reminderTime =
            '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
      });
      _updateNotificationSettings();
    }
  }

  Future<void> _testNotification() async {
    try {
      await LocalNotificationService.showInstantNotification(
        id: 9999,
        title: 'إشعار تجريبي',
        body: 'هذا إشعار تجريبي من تطبيق رفيق السيلياك',
      );

      if (mounted) {
        ErrorHandler.showSuccessSnackBar(context, 'تم إرسال الإشعار التجريبي');
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(context, e);
      }
    }
  }

  Future<void> _savePrivacySettings() async {
    try {
      await SecurityService.savePrivacySettings(_privacySettings);
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(context, e);
      }
    }
  }

  void _changePassword() {
    // تنفيذ تغيير كلمة المرور (مؤقتاً: رسالة تأكيد)
    ErrorHandler.showWarningSnackBar(context, 'هذه الميزة قيد التطوير');
  }

  void _manageTwoFactor() {
    // تنفيذ إدارة المصادقة الثنائية (مؤقتاً: رسالة تأكيد)
    ErrorHandler.showWarningSnackBar(context, 'هذه الميزة قيد التطوير');
  }

  void _viewLoginHistory() {
    // تنفيذ عرض سجل تسجيل الدخول (مؤقتاً: رسالة تأكيد)
    ErrorHandler.showWarningSnackBar(context, 'هذه الميزة قيد التطوير');
  }

  Future<void> _createBackup() async {
    try {
      setState(() {
        _isLoading = true;
      });

      await BackupService.shareBackup();

      if (mounted) {
        ErrorHandler.showSuccessSnackBar(
          context,
          'تم إنشاء النسخة الاحتياطية بنجاح',
        );
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(context, e);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _restoreBackup() {
    // تنفيذ استعادة النسخة الاحتياطية (مؤقتاً: رسالة تأكيد)
    ErrorHandler.showWarningSnackBar(context, 'هذه الميزة قيد التطوير');
  }

  void _exportData() {
    // تنفيذ تصدير البيانات (مؤقتاً: رسالة تأكيد)
    ErrorHandler.showWarningSnackBar(context, 'هذه الميزة قيد التطوير');
  }

  Future<void> _deleteAllData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد الحذف', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف جميع بياناتك؟ هذا الإجراء لا يمكن التراجع عنه.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text('حذف', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        setState(() {
          _isLoading = true;
        });

        await SecurityService.deleteAllUserData();

        if (mounted) {
          Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
        }
      } catch (e) {
        if (mounted) {
          ErrorHandler.showErrorSnackBar(context, e);
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/providers/food_provider.dart';
import 'package:yassincil/models/food_item.dart';
import 'package:yassincil/utils/app_colors.dart';

class FoodStatisticsScreen extends StatelessWidget {
  const FoodStatisticsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final foodProvider = Provider.of<FoodProvider>(context);
    final foods = foodProvider.foodItems;

    // Calculate statistics
    final totalFoods = foods.length;
    final glutenFreeFoods = foods.where((f) => f.isGlutenFree).length;
    final glutenFoods = totalFoods - glutenFreeFoods;
    final foodsPerCategory = _calculateFoodsPerCategory(foods);

    return Scaffold(
      appBar: AppBar(title: const Text('إحصائيات الأطعمة')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatisticCard(
              title: 'إجمالي الأطعمة',
              value: totalFoods.toString(),
              icon: Icons.restaurant_menu,
              color: Colors.blue,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatisticCard(
                    title: 'أطعمة خالية من الجلوتين',
                    value: glutenFreeFoods.toString(),
                    icon: Icons.check_circle,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatisticCard(
                    title: 'أطعمة تحتوي على جلوتين',
                    value: glutenFoods.toString(),
                    icon: Icons.warning,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Text(
              'الأطعمة حسب الفئة',
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...foodsPerCategory.entries.map((entry) {
              return _buildCategoryStatistic(
                category: entry.key,
                count: entry.value,
                total: totalFoods,
              );
            }),
          ],
        ),
      ),
    );
  }

  Map<String, int> _calculateFoodsPerCategory(List<FoodItem> foods) {
    final Map<String, int> categoryCounts = {};
    for (final food in foods) {
      categoryCounts[food.category] = (categoryCounts[food.category] ?? 0) + 1;
    }
    return categoryCounts;
  }

  Widget _buildStatisticCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
              Icon(icon, color: color, size: 28),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryStatistic({
    required String category,
    required int count,
    required int total,
  }) {
    final percentage = total > 0 ? (count / total) * 100 : 0.0;
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                category,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '$count (${percentage.toStringAsFixed(1)}%)',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: total > 0 ? count / total : 0.0,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        ],
      ),
    );
  }
}

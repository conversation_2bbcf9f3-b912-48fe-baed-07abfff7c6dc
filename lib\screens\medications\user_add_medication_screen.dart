import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/widgets/medications_app_bar.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:flutter/services.dart';

import 'package:yassincil/providers/medication_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/models/medication.dart';
import 'package:yassincil/utils/app_colors.dart';

class UserAddMedicationScreen extends StatefulWidget {
  const UserAddMedicationScreen({super.key});

  @override
  State<UserAddMedicationScreen> createState() =>
      _UserAddMedicationScreenState();
}

class _UserAddMedicationScreenState extends State<UserAddMedicationScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _successAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _scaleAnimation;

  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _companyController = TextEditingController();
  final _ingredientsController = TextEditingController();
  final _notesController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _activeIngredientController = TextEditingController();
  final _dosageController = TextEditingController();

  bool _isAllowed = true;
  String _selectedCategory = 'مسكنات الألم';
  final List<File> _selectedImages = [];
  bool _isSubmitting = false;
  bool _showSuccessMessage = false;

  final List<String> _categories = [
    'مسكنات الألم',
    'مضادات الحيوية',
    'أدوية الضغط',
    'أدوية السكري',
    'أدوية الجهاز الهضمي',
    'أدوية الحساسية',
    'فيتامينات ومكملات',
    'أدوية القلب',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _successAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    _slideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _successAnimationController,
        curve: Curves.elasticOut,
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _successAnimationController.dispose();
    _nameController.dispose();
    _companyController.dispose();
    _ingredientsController.dispose();
    _notesController.dispose();
    _barcodeController.dispose();
    _activeIngredientController.dispose();
    _dosageController.dispose();
    super.dispose();
  }

  Future<void> _pickImages() async {
    final ImagePicker picker = ImagePicker();
    try {
      final List<XFile> images = await picker.pickMultiImage();
      if (images.isNotEmpty) {
        setState(() {
          _selectedImages.addAll(
            images
                .take(5 - _selectedImages.length)
                .map((xFile) => File(xFile.path)),
          );
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('حدث خطأ أثناء اختيار الصور')));
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  Future<void> _submitMedication() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (authProvider.currentUser == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('يجب تسجيل الدخول أولاً')));
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      // إنشاء الدواء بحالة "في انتظار المراجعة"
      final medication = Medication(
        name: _nameController.text.trim(),
        company: _companyController.text.trim(),
        ingredients: _ingredientsController.text.trim(),
        isAllowed: _isAllowed,
        notes: _notesController.text.trim(),
        category: _selectedCategory,
        barcode: _barcodeController.text.trim(),
        activeIngredient: _activeIngredientController.text.trim(),
        dosage: _dosageController.text.trim(),
        userId: authProvider.currentUser!.uid,
        username: authProvider.currentUser!.displayName ?? 'مستخدم',
        createdAt: DateTime.now(),
        // حالة الموافقة الجديدة
        approvalStatus:
            MedicationApprovalStatus.pending, // ⏳ في انتظار المراجعة
        isApproved: false, // للتوافق مع الكود القديم
        source: 'مساهمة مجتمعية',
      );

      final medicationProvider = Provider.of<MedicationProvider>(
        context,
        listen: false,
      );
      await medicationProvider.addMedication(medication);

      // عرض رسالة النجاح
      setState(() => _showSuccessMessage = true);
      _successAnimationController.forward();

      // إرسال إشعار للمشرفين (يمكن تطويره لاحقاً)
      _notifyAdmins();

      // انتظار لعرض الرسالة ثم العودة
      await Future.delayed(const Duration(seconds: 3));
      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إضافة الدواء: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  void _notifyAdmins() {
    // TODO: إضافة نظام إشعارات للمشرفين
    // يمكن إرسال إشعار push أو إيميل للمشرفين
    debugPrint('تم إرسال إشعار للمشرفين بوجود دواء جديد للمراجعة');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FFFE),
      appBar: const MedicationsAppBar(title: 'إضافة دواء جديد'),
      body: _showSuccessMessage ? _buildSuccessWidget() : _buildForm(),
    );
  }

  Widget _buildSuccessWidget() {
    return Center(
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          margin: const EdgeInsets.all(32),
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.white.withValues(alpha: 0.95),
                Colors.white.withValues(alpha: 0.85),
              ],
            ),
            borderRadius: BorderRadius.circular(30),
            border: Border.all(
              color: const Color(0xFF00BFA5).withValues(alpha: 0.3),
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF00BFA5).withValues(alpha: 0.2),
                blurRadius: 30,
                offset: const Offset(0, 15),
              ),
            ],
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF00BFA5), Color(0xFF00796B)],
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF00BFA5).withValues(alpha: 0.4),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.check_rounded,
                    color: Colors.white,
                    size: 40,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'تم إرسال الدواء للمراجعة!',
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF00BFA5),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Text(
                  'شكراً لك على مساهمتك في مساعدة مرضى السيلياك\nسيتم مراجعة الدواء من قبل المشرفين ونشره قريباً',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                    height: 1.6,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(0xFF00BFA5).withValues(alpha: 0.1),
                        const Color(0xFF00796B).withValues(alpha: 0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: const Color(0xFF00BFA5).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: const Color(0xFF00BFA5),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        child: Text(
                          'ستصلك رسالة عند الموافقة على الدواء',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: const Color(0xFF00BFA5),
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildForm() {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildInfoCard(),
                    const SizedBox(height: 24),
                    _buildBasicInfoSection(),
                    const SizedBox(height: 24),
                    _buildDetailsSection(),
                    const SizedBox(height: 24),
                    _buildImagesSection(),
                    const SizedBox(height: 32),
                    _buildSubmitButton(),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF00BFA5).withValues(alpha: 0.08),
            const Color(0xFF00796B).withValues(alpha: 0.04),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF00BFA5).withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF00BFA5), Color(0xFF00796B)],
                  ),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: const Icon(
                  Icons.people_outline,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  'مساهمة مجتمعية',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF00BFA5),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'ساعد في بناء قاعدة بيانات أكبر للأدوية الآمنة لمرضى السيلياك. معلوماتك ستساهم في حماية الآلاف من المرضى.',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.grey.shade700,
              height: 1.6,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildInfoPoint('📝', 'ملء المعلومات الأساسية'),
              const SizedBox(width: 16),
              _buildInfoPoint('🔍', 'مراجعة من المشرفين'),
              const SizedBox(width: 16),
              _buildInfoPoint('✅', 'نشر واستفادة الجميع'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoPoint(String icon, String text) {
    return Expanded(
      child: Row(
        children: [
          Text(icon, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.cairo(
                fontSize: 11,
                color: Colors.grey.shade600,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return _buildSection(
      title: 'المعلومات الأساسية',
      icon: Icons.medical_services,
      child: Column(
        children: [
          _buildTextField(
            controller: _nameController,
            label: 'اسم الدواء',
            hint: 'مثال: باراسيتامول',
            validator: (value) =>
                value?.isEmpty == true ? 'اسم الدواء مطلوب' : null,
            icon: Icons.medication,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _companyController,
            label: 'الشركة المصنعة',
            hint: 'مثال: شركة النهدي الطبية',
            validator: (value) =>
                value?.isEmpty == true ? 'اسم الشركة مطلوب' : null,
            icon: Icons.business,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _ingredientsController,
            label: 'المكونات',
            hint: 'اكتب المكونات الفعالة والمساعدة',
            maxLines: 3,
            validator: (value) =>
                value?.isEmpty == true ? 'المكونات مطلوبة' : null,
            icon: Icons.science,
          ),
          const SizedBox(height: 16),
          _buildCategoryDropdown(),
          const SizedBox(height: 16),
          _buildSafetySwitch(),
        ],
      ),
    );
  }

  Widget _buildDetailsSection() {
    return _buildSection(
      title: 'معلومات إضافية',
      icon: Icons.info_outline,
      child: Column(
        children: [
          _buildTextField(
            controller: _activeIngredientController,
            label: 'المادة الفعالة',
            hint: 'مثال: باراسيتامول 500 مج',
            icon: Icons.science_outlined,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _dosageController,
            label: 'الجرعة المعتادة',
            hint: 'مثال: قرص كل 6 ساعات',
            icon: Icons.schedule,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _barcodeController,
            label: 'رقم الباركود (اختياري)',
            hint: 'امسح أو اكتب رقم الباركود',
            icon: Icons.qr_code_scanner,
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _notesController,
            label: 'ملاحظات إضافية (اختياري)',
            hint: 'أي معلومات أخرى مهمة',
            maxLines: 3,
            icon: Icons.note_add,
          ),
        ],
      ),
    );
  }

  Widget _buildImagesSection() {
    return _buildSection(
      title: 'صور الدواء (اختياري)',
      icon: Icons.photo_library,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(15),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.add_photo_alternate,
                  color: Colors.grey.shade500,
                  size: 48,
                ),
                const SizedBox(height: 12),
                Text(
                  'إضافة صور واضحة للدواء تساعد في التعرف عليه',
                  style: GoogleFonts.cairo(
                    color: Colors.grey.shade600,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _selectedImages.length < 5 ? _pickImages : null,
                  icon: const Icon(Icons.add_a_photo),
                  label: Text('اختيار صور (${_selectedImages.length}/5)'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF00BFA5),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (_selectedImages.isNotEmpty) ...[
            const SizedBox(height: 16),
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.only(right: 12),
                    child: Stack(
                      children: [
                        Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            border: Border.all(
                              color: const Color(
                                0xFF00BFA5,
                              ).withValues(alpha: 0.3),
                            ),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(15),
                            child: Image.file(
                              _selectedImages[index],
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        Positioned(
                          top: 8,
                          right: 8,
                          child: GestureDetector(
                            onTap: () => _removeImage(index),
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.red.withValues(alpha: 0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.95),
            Colors.white.withValues(alpha: 0.85),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF00BFA5).withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF00BFA5), Color(0xFF00796B)],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: Colors.white, size: 20),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          child,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? Function(String?)? validator,
    IconData? icon,
    int maxLines = 1,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return TextFormField(
      controller: controller,
      validator: validator,
      maxLines: maxLines,
      keyboardType: keyboardType,
      style: GoogleFonts.cairo(fontSize: 16),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: icon != null
            ? Icon(icon, color: const Color(0xFF00BFA5))
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15),
          borderSide: const BorderSide(color: Color(0xFF00BFA5), width: 2),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        contentPadding: EdgeInsets.symmetric(
          horizontal: 16,
          vertical: maxLines > 1 ? 16 : 12,
        ),
        labelStyle: GoogleFonts.cairo(color: Colors.grey.shade700),
        hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500, fontSize: 14),
      ),
    );
  }

  Widget _buildCategoryDropdown() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonFormField<String>(
        value: _selectedCategory,
        decoration: InputDecoration(
          labelText: 'فئة الدواء',
          prefixIcon: const Icon(Icons.category, color: Color(0xFF00BFA5)),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          labelStyle: GoogleFonts.cairo(color: Colors.grey.shade700),
        ),
        items: _categories.map((category) {
          return DropdownMenuItem(
            value: category,
            child: Text(category, style: GoogleFonts.cairo()),
          );
        }).toList(),
        onChanged: (value) {
          setState(() => _selectedCategory = value!);
        },
        style: GoogleFonts.cairo(fontSize: 16, color: Colors.black87),
        dropdownColor: Colors.white,
        borderRadius: BorderRadius.circular(15),
      ),
    );
  }

  Widget _buildSafetySwitch() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            (_isAllowed ? Colors.green : Colors.red).withValues(alpha: 0.1),
            (_isAllowed ? Colors.green : Colors.red).withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: (_isAllowed ? Colors.green : Colors.red).withValues(
            alpha: 0.3,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            _isAllowed ? Icons.check_circle : Icons.cancel,
            color: _isAllowed ? Colors.green : Colors.red,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _isAllowed
                      ? 'آمن لمرضى السيلياك ✅'
                      : 'غير آمن لمرضى السيلياك ⚠️',
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.bold,
                    color: _isAllowed
                        ? Colors.green.shade700
                        : Colors.red.shade700,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _isAllowed
                      ? 'هذا الدواء آمن ولا يحتوي على الجلوتين'
                      : 'هذا الدواء يحتوي على الجلوتين أو غير آمن',
                  style: GoogleFonts.cairo(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _isAllowed,
            onChanged: (value) {
              setState(() => _isAllowed = value);
              HapticFeedback.lightImpact();
            },
            activeColor: Colors.green,
            inactiveThumbColor: Colors.red,
            inactiveTrackColor: Colors.red.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isSubmitting ? null : _submitMedication,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF00BFA5),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
          elevation: 8,
          shadowColor: const Color(0xFF00BFA5).withValues(alpha: 0.4),
        ),
        child: _isSubmitting
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.5,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Text(
                    'جاري الإرسال...',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.send_rounded, size: 24),
                  const SizedBox(width: 12),
                  Text(
                    'إرسال للمراجعة',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

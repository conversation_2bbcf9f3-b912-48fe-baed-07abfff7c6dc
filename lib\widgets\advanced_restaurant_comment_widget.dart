import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart' as intl;

import 'package:yassincil/models/comment.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/utils/app_colors.dart';
// Note: We will need a CommentProvider later to handle likes on comments.
// For now, we'll leave the like functionality disabled.

class AdvancedRestaurantCommentWidget extends StatelessWidget {
  final Comment comment;
  final String restaurantId;
  final VoidCallback onReply;

  const AdvancedRestaurantCommentWidget({
    super.key,
    required this.comment,
    required this.restaurantId,
    required this.onReply,
  });

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final currentUserId = authProvider.currentUser?.uid;
    final isLiked = false; // مؤقتاً حتى يتم تطبيق نظام الإعجابات للتعليقات

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 12),
          _buildContent(),
          const SizedBox(height: 12),
          _buildActions(context, isLiked, currentUserId),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        CircleAvatar(
          radius: 20,
          backgroundColor: AppColors.primary.withOpacity(0.1),
          backgroundImage: comment.userAvatar != null
              ? CachedNetworkImageProvider(comment.userAvatar!)
              : null,
          child: comment.userAvatar == null
              ? Text(
                  comment.username.isNotEmpty ? comment.username[0] : 'U',
                  style: GoogleFonts.cairo(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                )
              : null,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                comment.username,
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  color: AppColors.textPrimary,
                ),
              ),
              Text(
                _getTimeAgo(comment.createdAt),
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    return Text(
      comment.content,
      style: GoogleFonts.cairo(
        fontSize: 14,
        color: AppColors.textPrimary.withOpacity(0.85),
        height: 1.5,
      ),
    );
  }

  Widget _buildActions(
    BuildContext context,
    bool isLiked,
    String? currentUserId,
  ) {
    return Row(
      children: [
        _buildActionButton(
          context: context,
          icon: isLiked ? Icons.favorite : Icons.favorite_border,
          label: 'إعجاب (${comment.likesCount})',
          color: isLiked ? Colors.red : AppColors.textSecondary,
          onTap: () {
            // TODO: Implement comment liking functionality in a CommentProvider
          },
        ),
        const SizedBox(width: 16),
        _buildActionButton(
          context: context,
          icon: Icons.reply,
          label: 'رد',
          color: AppColors.textSecondary,
          onTap: onReply,
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          children: [
            Icon(icon, size: 18, color: color),
            const SizedBox(width: 6),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getTimeAgo(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 365) {
      return intl.DateFormat.yMMMd('ar').format(date);
    } else if (difference.inDays > 30) {
      return intl.DateFormat.MMMd('ar').format(date);
    } else if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}

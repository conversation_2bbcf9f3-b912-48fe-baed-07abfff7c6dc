import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:yassincil/utils/user_display_utils.dart';

class UserProfile {
  final String uid;
  final String email;
  final String username;
  final String? firstName;
  final String? lastName;
  final String role; // 'user' or 'admin'
  final DateTime createdAt;
  final String? profileImageUrl;

  UserProfile({
    required this.uid,
    required this.email,
    required this.username,
    this.firstName,
    this.lastName,
    this.role = 'user', // Default role is 'user'
    required this.createdAt,
    this.profileImageUrl,
  });

  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'email': email,
      'username': username,
      'firstName': firstName,
      'lastName': lastName,
      'role': role,
      'createdAt': Timestamp.fromDate(createdAt),
      'profileImageUrl': profileImageUrl,
    };
  }

  factory UserProfile.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserProfile(
      uid: doc.id, // UID هو معرف المستند في Firestore
      email: data['email'] ?? '',
      username: data['username'] ?? '',
      firstName: data['firstName'],
      lastName: data['lastName'],
      role: data['role'] ?? 'user',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      profileImageUrl: data['profileImageUrl'],
    );
  }

  /// الحصول على الاسم الكامل للعرض (الاسم الأول + اللقب)
  String get displayName {
    return UserDisplayUtils.getDisplayName(
      firstName: firstName,
      lastName: lastName,
      username: username,
    );
  }

  /// الحصول على الأحرف الأولى للاسم للأفاتار
  String get initials {
    return UserDisplayUtils.getInitials(
      firstName: firstName,
      lastName: lastName,
      username: username,
    );
  }

  /// الحصول على اسم المستخدم للعرض مع @
  String get usernameDisplay {
    return UserDisplayUtils.getUsernameDisplay(username);
  }

  /// التحقق من وجود صورة ملف شخصي صالحة
  bool get hasValidProfileImage {
    return UserDisplayUtils.hasValidProfileImage(profileImageUrl);
  }
}

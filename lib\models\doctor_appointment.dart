import 'package:cloud_firestore/cloud_firestore.dart';

enum AppointmentStatus {
  pending,
  confirmed,
  cancelled,
  completed,
  noShow,
  rescheduled,
}

enum AppointmentType {
  consultation,
  followUp,
  emergency,
  checkup,
  procedure,
}

class DoctorAppointment {
  final String? id;
  final String doctorId;
  final String doctorName;
  final String patientId;
  final String patientName;
  final String? patientPhone;
  final String? patientEmail;
  final DateTime appointmentDate;
  final String timeSlot; // مثل: "10:00 ص - 10:30 ص"
  final AppointmentStatus status;
  final AppointmentType type;
  final String? reason; // سبب الزيارة
  final String? notes; // ملاحظات الطبيب
  final String? patientNotes; // ملاحظات المريض
  final double? fee; // رسوم الاستشارة
  final bool isPaid; // هل تم الدفع
  final String? paymentMethod;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? cancelReason; // سبب الإلغاء
  final DateTime? cancelledAt;
  final String? cancelledBy;
  final Map<String, dynamic>? metadata; // معلومات إضافية

  DoctorAppointment({
    this.id,
    required this.doctorId,
    required this.doctorName,
    required this.patientId,
    required this.patientName,
    this.patientPhone,
    this.patientEmail,
    required this.appointmentDate,
    required this.timeSlot,
    this.status = AppointmentStatus.pending,
    this.type = AppointmentType.consultation,
    this.reason,
    this.notes,
    this.patientNotes,
    this.fee,
    this.isPaid = false,
    this.paymentMethod,
    required this.createdAt,
    this.updatedAt,
    this.cancelReason,
    this.cancelledAt,
    this.cancelledBy,
    this.metadata,
  });

  factory DoctorAppointment.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DoctorAppointment(
      id: doc.id,
      doctorId: data['doctorId'] ?? '',
      doctorName: data['doctorName'] ?? '',
      patientId: data['patientId'] ?? '',
      patientName: data['patientName'] ?? '',
      patientPhone: data['patientPhone'],
      patientEmail: data['patientEmail'],
      appointmentDate: (data['appointmentDate'] as Timestamp).toDate(),
      timeSlot: data['timeSlot'] ?? '',
      status: AppointmentStatus.values.firstWhere(
        (s) => s.name == data['status'],
        orElse: () => AppointmentStatus.pending,
      ),
      type: AppointmentType.values.firstWhere(
        (t) => t.name == data['type'],
        orElse: () => AppointmentType.consultation,
      ),
      reason: data['reason'],
      notes: data['notes'],
      patientNotes: data['patientNotes'],
      fee: data['fee']?.toDouble(),
      isPaid: data['isPaid'] ?? false,
      paymentMethod: data['paymentMethod'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
      cancelReason: data['cancelReason'],
      cancelledAt: data['cancelledAt'] != null
          ? (data['cancelledAt'] as Timestamp).toDate()
          : null,
      cancelledBy: data['cancelledBy'],
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'doctorId': doctorId,
      'doctorName': doctorName,
      'patientId': patientId,
      'patientName': patientName,
      'patientPhone': patientPhone,
      'patientEmail': patientEmail,
      'appointmentDate': Timestamp.fromDate(appointmentDate),
      'timeSlot': timeSlot,
      'status': status.name,
      'type': type.name,
      'reason': reason,
      'notes': notes,
      'patientNotes': patientNotes,
      'fee': fee,
      'isPaid': isPaid,
      'paymentMethod': paymentMethod,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'cancelReason': cancelReason,
      'cancelledAt': cancelledAt != null ? Timestamp.fromDate(cancelledAt!) : null,
      'cancelledBy': cancelledBy,
      'metadata': metadata,
    };
  }

  DoctorAppointment copyWith({
    String? id,
    String? doctorId,
    String? doctorName,
    String? patientId,
    String? patientName,
    String? patientPhone,
    String? patientEmail,
    DateTime? appointmentDate,
    String? timeSlot,
    AppointmentStatus? status,
    AppointmentType? type,
    String? reason,
    String? notes,
    String? patientNotes,
    double? fee,
    bool? isPaid,
    String? paymentMethod,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? cancelReason,
    DateTime? cancelledAt,
    String? cancelledBy,
    Map<String, dynamic>? metadata,
  }) {
    return DoctorAppointment(
      id: id ?? this.id,
      doctorId: doctorId ?? this.doctorId,
      doctorName: doctorName ?? this.doctorName,
      patientId: patientId ?? this.patientId,
      patientName: patientName ?? this.patientName,
      patientPhone: patientPhone ?? this.patientPhone,
      patientEmail: patientEmail ?? this.patientEmail,
      appointmentDate: appointmentDate ?? this.appointmentDate,
      timeSlot: timeSlot ?? this.timeSlot,
      status: status ?? this.status,
      type: type ?? this.type,
      reason: reason ?? this.reason,
      notes: notes ?? this.notes,
      patientNotes: patientNotes ?? this.patientNotes,
      fee: fee ?? this.fee,
      isPaid: isPaid ?? this.isPaid,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      cancelReason: cancelReason ?? this.cancelReason,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      cancelledBy: cancelledBy ?? this.cancelledBy,
      metadata: metadata ?? this.metadata,
    );
  }

  String getStatusDisplayName() {
    switch (status) {
      case AppointmentStatus.pending:
        return 'في الانتظار';
      case AppointmentStatus.confirmed:
        return 'مؤكد';
      case AppointmentStatus.cancelled:
        return 'ملغي';
      case AppointmentStatus.completed:
        return 'مكتمل';
      case AppointmentStatus.noShow:
        return 'لم يحضر';
      case AppointmentStatus.rescheduled:
        return 'تم إعادة الجدولة';
    }
  }

  String getTypeDisplayName() {
    switch (type) {
      case AppointmentType.consultation:
        return 'استشارة';
      case AppointmentType.followUp:
        return 'متابعة';
      case AppointmentType.emergency:
        return 'طارئ';
      case AppointmentType.checkup:
        return 'فحص دوري';
      case AppointmentType.procedure:
        return 'إجراء طبي';
    }
  }

  bool get canBeCancelled {
    return status == AppointmentStatus.pending || 
           status == AppointmentStatus.confirmed;
  }

  bool get canBeRescheduled {
    return status == AppointmentStatus.pending || 
           status == AppointmentStatus.confirmed;
  }

  bool get isUpcoming {
    return appointmentDate.isAfter(DateTime.now()) && 
           (status == AppointmentStatus.pending || 
            status == AppointmentStatus.confirmed);
  }

  bool get isPast {
    return appointmentDate.isBefore(DateTime.now());
  }

  String getFormattedDate() {
    final months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    final weekdays = [
      'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 
      'الجمعة', 'السبت', 'الأحد'
    ];
    
    final weekday = weekdays[appointmentDate.weekday - 1];
    final month = months[appointmentDate.month - 1];
    
    return '$weekday ${appointmentDate.day} $month ${appointmentDate.year}';
  }
}

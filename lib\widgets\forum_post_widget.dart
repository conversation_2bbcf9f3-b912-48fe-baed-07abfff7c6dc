// lib/widgets/forum_post_widget.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:share_plus/share_plus.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../models/forum_post.dart';
import '../providers/forum_provider.dart';
import '../providers/auth_provider.dart' as app_auth;
import '../utils/app_colors.dart';
import '../screens/forum/post_detail_screen.dart';

class ForumPostWidget extends StatefulWidget {
  final ForumPost post;
  final bool showFullContent;
  final VoidCallback? onTap;

  const ForumPostWidget({
    super.key,
    required this.post,
    this.showFullContent = false,
    this.onTap,
  });

  @override
  State<ForumPostWidget> createState() => _ForumPostWidgetState();
}

class _ForumPostWidgetState extends State<ForumPostWidget>
    with SingleTickerProviderStateMixin {
  bool _isLiked = false;
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _checkIfLiked();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _checkIfLiked() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final isLiked = forumProvider.isPostLiked(widget.post.id!);
    if (mounted) {
      setState(() {
        _isLiked = isLiked;
      });
    }
  }

  Future<void> _toggleLike() async {
    try {
      _animationController.forward().then((_) {
        _animationController.reverse();
      });

      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      await forumProvider.togglePostLike(widget.post.id!);

      setState(() {
        _isLiked = !_isLiked;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحديث الإعجاب'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _sharePost() async {
    try {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      await forumProvider.sharePost(widget.post.id!);

      await Share.share(
        'شاهد هذا المنشور في منتدى التطبيق:\n\n${widget.post.content}',
        subject: 'منشور من المنتدى',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء المشاركة'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _openPostDetail() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PostDetailScreen(post: widget.post),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<app_auth.AuthProvider>(context);
    final currentUser = authProvider.currentUser;
    final isOwner = currentUser?.uid == widget.post.userId;

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: widget.onTap ?? _openPostDetail,
                borderRadius: BorderRadius.circular(16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // رأس المنشور
                      _buildPostHeader(isOwner),

                      const SizedBox(height: 12),

                      // محتوى المنشور
                      _buildPostContent(),

                      // الصور والفيديوهات
                      if (widget.post.hasMedia) ...[
                        const SizedBox(height: 12),
                        _buildMediaContent(),
                      ],

                      // الاستطلاع
                      if (widget.post.hasPoll) ...[
                        const SizedBox(height: 12),
                        _buildPollContent(),
                      ],

                      // العلامات
                      if (widget.post.tags.isNotEmpty) ...[
                        const SizedBox(height: 12),
                        _buildTags(),
                      ],

                      const SizedBox(height: 16),

                      // أزرار التفاعل
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPostHeader(bool isOwner) {
    return Row(
      children: [
        // صورة المستخدم
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: AppColors.border, width: 2),
          ),
          child: ClipOval(
            child: widget.post.userAvatar != null
                ? CachedNetworkImage(
                    imageUrl: widget.post.userAvatar!,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      child: Text(
                        _getUserInitials(widget.post.username),
                        style: GoogleFonts.cairo(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      child: Text(
                        _getUserInitials(widget.post.username),
                        style: GoogleFonts.cairo(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  )
                : Container(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    child: Text(
                      _getUserInitials(widget.post.username),
                      style: GoogleFonts.cairo(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
          ),
        ),

        const SizedBox(width: 12),

        // معلومات المستخدم
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    widget.post.username,
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  if (widget.post.isEdited) ...[
                    const SizedBox(width: 4),
                    Text(
                      '(محرر)',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 2),
              Row(
                children: [
                  Text(
                    _formatTime(widget.post.createdAt),
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: _getCategoryColor().withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      widget.post.categoryDisplayName,
                      style: GoogleFonts.cairo(
                        fontSize: 10,
                        color: _getCategoryColor(),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  if (widget.post.isPinned) ...[
                    const SizedBox(width: 4),
                    Icon(Icons.push_pin, size: 14, color: AppColors.warning),
                  ],
                ],
              ),
            ],
          ),
        ),

        // قائمة الخيارات
        PopupMenuButton<String>(
          icon: Icon(Icons.more_vert, color: AppColors.textSecondary, size: 20),
          onSelected: (value) => _handleMenuAction(value, isOwner),
          itemBuilder: (context) => [
            if (isOwner) ...[
              PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 16, color: AppColors.primary),
                    const SizedBox(width: 8),
                    Text('تعديل', style: GoogleFonts.cairo()),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 16, color: AppColors.error),
                    const SizedBox(width: 8),
                    Text('حذف', style: GoogleFonts.cairo()),
                  ],
                ),
              ),
            ],
            PopupMenuItem(
              value: 'report',
              child: Row(
                children: [
                  Icon(Icons.report, size: 16, color: AppColors.warning),
                  const SizedBox(width: 8),
                  Text('إبلاغ', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share, size: 16, color: AppColors.textSecondary),
                  const SizedBox(width: 8),
                  Text('مشاركة', style: GoogleFonts.cairo()),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPostContent() {
    final maxLines = widget.showFullContent ? null : (_isExpanded ? null : 3);
    final shouldShowMore =
        !widget.showFullContent && widget.post.content.length > 150;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.post.content,
          style: GoogleFonts.cairo(
            fontSize: 15,
            color: AppColors.textPrimary,
            height: 1.5,
          ),
          maxLines: maxLines,
          overflow: maxLines != null ? TextOverflow.ellipsis : null,
        ),
        if (shouldShowMore)
          GestureDetector(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            child: Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                _isExpanded ? 'عرض أقل' : 'عرض المزيد',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Color _getCategoryColor() {
    switch (widget.post.category) {
      case PostCategory.questions:
        return AppColors.primary;
      case PostCategory.tips:
        return AppColors.success;
      case PostCategory.experiences:
        return AppColors.warning;
      case PostCategory.discussions:
        return Colors.purple;
      case PostCategory.health:
        return Colors.red;
      case PostCategory.nutrition:
        return Colors.green;
      case PostCategory.recipes:
        return Colors.orange;
      case PostCategory.medications:
        return Colors.blue;
      case PostCategory.emergency:
        return Colors.red.shade700;
      default:
        return AppColors.textSecondary;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  void _handleMenuAction(String action, bool isOwner) {
    switch (action) {
      case 'edit':
        // فتح شاشة التعديل (مؤقتاً: رسالة تأكيد)
        break;
      case 'delete':
        _showDeleteDialog();
        break;
      case 'report':
        _showReportDialog();
        break;
      case 'share':
        _sharePost();
        break;
    }
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف المنشور', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف هذا المنشور؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              final forumProvider = Provider.of<ForumProvider>(
                context,
                listen: false,
              );
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              try {
                await forumProvider.deletePost(widget.post.id!);
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('تم حذف المنشور بنجاح'),
                      backgroundColor: AppColors.success,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ أثناء حذف المنشور'),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              }
            },
            child: Text(
              'حذف',
              style: GoogleFonts.cairo(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaContent() {
    if (widget.post.imageUrls.isNotEmpty) {
      return _buildImageGallery();
    } else if (widget.post.videoUrls.isNotEmpty) {
      return _buildVideoPlayer();
    }
    return const SizedBox.shrink();
  }

  Widget _buildImageGallery() {
    final images = widget.post.imageUrls;

    if (images.length == 1) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: CachedNetworkImage(
          imageUrl: images.first,
          width: double.infinity,
          height: 200,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            height: 200,
            color: AppColors.background,
            child: Center(
              child: CircularProgressIndicator(color: AppColors.primary),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            height: 200,
            color: AppColors.background,
            child: Icon(Icons.broken_image, color: AppColors.textSecondary),
          ),
        ),
      );
    }

    return SizedBox(
      height: 200,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: images.length,
        itemBuilder: (context, index) {
          return Container(
            width: 150,
            margin: EdgeInsets.only(left: index < images.length - 1 ? 8 : 0),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: CachedNetworkImage(
                imageUrl: images[index],
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppColors.background,
                  child: Center(
                    child: CircularProgressIndicator(color: AppColors.primary),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: AppColors.background,
                  child: Icon(
                    Icons.broken_image,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildVideoPlayer() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.play_circle_filled, size: 60, color: AppColors.primary),
            const SizedBox(height: 8),
            Text(
              'فيديو',
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPollContent() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.poll, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'استطلاع رأي',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'استطلاع الرأي متاح قريباً...',
            style: GoogleFonts.cairo(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildTags() {
    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: widget.post.tags.map((tag) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
          ),
          child: Text(
            '#$tag',
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        // زر الإعجاب
        _buildActionButton(
          icon: _isLiked ? Icons.favorite : Icons.favorite_border,
          label: '${widget.post.likesCount}',
          color: _isLiked ? AppColors.error : AppColors.textSecondary,
          onTap: _toggleLike,
        ),

        const SizedBox(width: 24),

        // زر التعليقات
        _buildActionButton(
          icon: Icons.chat_bubble_outline,
          label: '${widget.post.commentsCount}',
          color: AppColors.textSecondary,
          onTap: _openPostDetail,
        ),

        const SizedBox(width: 24),

        // زر المشاركة
        _buildActionButton(
          icon: Icons.share_outlined,
          label: '${widget.post.sharesCount}',
          color: AppColors.textSecondary,
          onTap: _sharePost,
        ),

        const Spacer(),

        // عدد المشاهدات
        Row(
          children: [
            Icon(
              Icons.visibility_outlined,
              size: 16,
              color: AppColors.textSecondary,
            ),
            const SizedBox(width: 4),
            Text(
              '${widget.post.viewsCount}',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          Icon(icon, size: 20, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إبلاغ عن المنشور',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'هل أنت متأكد من أنك تريد الإبلاغ عن هذا المنشور؟',
              style: GoogleFonts.cairo(),
            ),
            const SizedBox(height: 16),
            Text(
              'سيتم مراجعة المنشور من قبل المشرفين.',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _reportPost();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text('إبلاغ', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _reportPost() {
    // تنفيذ الإبلاغ عن المنشور (مؤقتاً: رسالة تأكيد)
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم الإبلاغ عن المنشور بنجاح',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  String _getUserInitials(String username) {
    if (username.isEmpty) return 'م';

    final nameParts = username.split(' ');
    if (nameParts.length > 1) {
      // إذا كان الاسم يحتوي على مسافة، نأخذ الحرف الأول من كل جزء
      return '${nameParts[0][0]}${nameParts[1][0]}';
    } else {
      // إذا كان الاسم مفرداً، نأخذ الحرف الأول فقط
      return username[0];
    }
  }
}

// lib/widgets/advanced_comment_widget.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:cached_network_image/cached_network_image.dart';

import '../models/comment.dart';
import '../providers/food_provider.dart';
import '../providers/auth_provider.dart';
import '../utils/app_colors.dart';

class AdvancedCommentWidget extends StatefulWidget {
  final Comment comment;
  final String foodItemId;
  final VoidCallback? onReply;
  final bool showReplies;

  const AdvancedCommentWidget({
    super.key,
    required this.comment,
    required this.foodItemId,
    this.onReply,
    this.showReplies = true,
  });

  @override
  State<AdvancedCommentWidget> createState() => _AdvancedCommentWidgetState();
}

class _AdvancedCommentWidgetState extends State<AdvancedCommentWidget> {
  bool _isLiked = false;
  bool _showReplies = false;
  bool _isEditing = false;
  final TextEditingController _editController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _checkIfLiked();
    _editController.text = widget.comment.content;
  }

  @override
  void dispose() {
    _editController.dispose();
    super.dispose();
  }

  Future<void> _checkIfLiked() async {
    final foodProvider = Provider.of<FoodProvider>(context, listen: false);
    final liked = await foodProvider.hasLikedComment(
      foodItemId: widget.foodItemId,
      commentId: widget.comment.id!,
    );
    if (mounted) {
      setState(() {
        _isLiked = liked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final foodProvider = Provider.of<FoodProvider>(context);
    final isOwner = authProvider.currentUser?.uid == widget.comment.userId;
    final isAdmin = authProvider.isAdmin;

    return Container(
      margin: EdgeInsets.only(
        bottom: 12,
        right: widget.comment.parentCommentId != null ? 32 : 0,
      ),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس التعليق
              _buildCommentHeader(isOwner, isAdmin),

              const SizedBox(height: 8),

              // محتوى التعليق
              if (_isEditing)
                _buildEditingWidget(foodProvider)
              else
                _buildCommentContent(),

              // صور التعليق
              if (widget.comment.imageUrls.isNotEmpty) _buildCommentImages(),

              const SizedBox(height: 8),

              // أزرار التفاعل
              _buildActionButtons(foodProvider, isOwner, isAdmin),

              // الردود
              if (widget.showReplies && _showReplies)
                _buildRepliesSection(foodProvider),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCommentHeader(bool isOwner, bool isAdmin) {
    return Row(
      children: [
        // صورة المستخدم
        CircleAvatar(
          radius: 20,
          backgroundImage: widget.comment.userAvatar != null
              ? CachedNetworkImageProvider(widget.comment.userAvatar!)
              : null,
          child: widget.comment.userAvatar == null
              ? Text(
                  widget.comment.username.isNotEmpty
                      ? widget.comment.username[0].toUpperCase()
                      : 'م',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )
              : null,
        ),

        const SizedBox(width: 12),

        // معلومات المستخدم
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    widget.comment.username,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  if (isOwner)
                    Container(
                      margin: const EdgeInsets.only(right: 8),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        'أنت',
                        style: TextStyle(
                          fontSize: 10,
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  if (widget.comment.isEdited)
                    const Text(
                      '(معدل)',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                ],
              ),
              Text(
                timeago.format(widget.comment.createdAt, locale: 'ar'),
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),

        // قائمة الخيارات
        if (isOwner || isAdmin)
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(value, isAdmin),
            itemBuilder: (context) => [
              if (isOwner)
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 18),
                      SizedBox(width: 8),
                      Text('تعديل'),
                    ],
                  ),
                ),
              PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 18, color: Colors.red),
                    const SizedBox(width: 8),
                    Text('حذف', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildCommentContent() {
    if (widget.comment.isDeleted) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Row(
          children: [
            Icon(Icons.delete_outline, color: Colors.grey),
            SizedBox(width: 8),
            Text(
              'تم حذف هذا التعليق',
              style: TextStyle(color: Colors.grey, fontStyle: FontStyle.italic),
            ),
          ],
        ),
      );
    }

    return Text(widget.comment.content, style: const TextStyle(fontSize: 14));
  }

  Widget _buildEditingWidget(FoodProvider foodProvider) {
    return Column(
      children: [
        TextField(
          controller: _editController,
          maxLines: null,
          decoration: const InputDecoration(
            hintText: 'اكتب تعليقك...',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () {
                setState(() {
                  _isEditing = false;
                  _editController.text = widget.comment.content;
                });
              },
              child: const Text('إلغاء'),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () => _saveEdit(foodProvider),
              child: const Text('حفظ'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCommentImages() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.comment.imageUrls.length,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.only(left: 8),
            width: 100,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: CachedNetworkImageProvider(
                  widget.comment.imageUrls[index],
                ),
                fit: BoxFit.cover,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildActionButtons(
    FoodProvider foodProvider,
    bool isOwner,
    bool isAdmin,
  ) {
    return Row(
      children: [
        // زر الإعجاب
        InkWell(
          onTap: () => _toggleLike(foodProvider),
          child: Row(
            children: [
              Icon(
                _isLiked ? Icons.favorite : Icons.favorite_border,
                size: 18,
                color: _isLiked ? Colors.red : Colors.grey,
              ),
              const SizedBox(width: 4),
              Text(
                '${widget.comment.likesCount}',
                style: TextStyle(
                  fontSize: 12,
                  color: _isLiked ? Colors.red : Colors.grey,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(width: 16),

        // زر الرد
        if (widget.comment.parentCommentId == null)
          InkWell(
            onTap: widget.onReply,
            child: const Row(
              children: [
                Icon(Icons.reply, size: 18, color: Colors.grey),
                SizedBox(width: 4),
                Text('رد', style: TextStyle(fontSize: 12, color: Colors.grey)),
              ],
            ),
          ),

        const SizedBox(width: 16),

        // عرض الردود
        if (widget.comment.repliesCount > 0 &&
            widget.comment.parentCommentId == null)
          InkWell(
            onTap: () {
              setState(() {
                _showReplies = !_showReplies;
              });
            },
            child: Row(
              children: [
                Icon(
                  _showReplies ? Icons.expand_less : Icons.expand_more,
                  size: 18,
                  color: Colors.grey,
                ),
                const SizedBox(width: 4),
                Text(
                  '${widget.comment.repliesCount} رد',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildRepliesSection(FoodProvider foodProvider) {
    return StreamBuilder<List<Comment>>(
      stream: foodProvider.getRepliesForComment(
        foodItemId: widget.foodItemId,
        parentCommentId: widget.comment.id!,
      ),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        final replies = snapshot.data!;
        if (replies.isEmpty) return const SizedBox.shrink();

        return Container(
          margin: const EdgeInsets.only(top: 12),
          child: Column(
            children: replies
                .map(
                  (reply) => AdvancedCommentWidget(
                    comment: reply,
                    foodItemId: widget.foodItemId,
                    showReplies: false,
                  ),
                )
                .toList(),
          ),
        );
      },
    );
  }

  void _handleMenuAction(String action, bool isAdmin) {
    switch (action) {
      case 'edit':
        setState(() {
          _isEditing = true;
        });
        break;
      case 'delete':
        _showDeleteDialog(isAdmin);
        break;
    }
  }

  void _showDeleteDialog(bool isAdmin) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف التعليق'),
        content: const Text('هل أنت متأكد من حذف هذا التعليق؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteComment(isAdmin);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteComment(bool isAdmin) async {
    try {
      final foodProvider = Provider.of<FoodProvider>(context, listen: false);
      await foodProvider.deleteComment(
        foodItemId: widget.foodItemId,
        commentId: widget.comment.id!,
        isAdmin: isAdmin,
      );

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('تم حذف التعليق بنجاح')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حذف التعليق: $e')));
      }
    }
  }

  Future<void> _saveEdit(FoodProvider foodProvider) async {
    try {
      await foodProvider.updateComment(
        foodItemId: widget.foodItemId,
        commentId: widget.comment.id!,
        newContent: _editController.text,
      );

      setState(() {
        _isEditing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('تم تحديث التعليق بنجاح')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحديث التعليق: $e')));
      }
    }
  }

  Future<void> _toggleLike(FoodProvider foodProvider) async {
    try {
      await foodProvider.toggleCommentLike(
        foodItemId: widget.foodItemId,
        commentId: widget.comment.id!,
      );

      setState(() {
        _isLiked = !_isLiked;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في الإعجاب: $e')));
      }
    }
  }
}

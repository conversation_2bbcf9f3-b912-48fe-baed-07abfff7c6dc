import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/app_colors.dart';
import '../../widgets/modern_widgets.dart';

class HomeCardsManagementScreen extends StatefulWidget {
  const HomeCardsManagementScreen({super.key});

  @override
  State<HomeCardsManagementScreen> createState() =>
      _HomeCardsManagementScreenState();
}

class _HomeCardsManagementScreenState extends State<HomeCardsManagementScreen> {
  final ImagePicker _picker = ImagePicker();
  bool _isLoading = false;

  // قائمة الكروت مع معلوماتها
  final List<Map<String, dynamic>> _homeCards = [
    {
      'id': 'medications',
      'title': 'الأدوية',
      'description': 'إدارة الأدوية والمكملات',
      'defaultIcon': Icons.medical_services_rounded,
      'colors': [Color(0xFF00BFA5), Color(0xFF00796B)],
    },
    {
      'id': 'foods',
      'title': 'الأطعمة',
      'description': 'إدارة الأطعمة الآمنة',
      'defaultIcon': Icons.restaurant_rounded,
      'colors': [Color(0xFFFFA726), Color(0xFFF57C00)],
    },
    {
      'id': 'recipes',
      'title': 'الوصفات',
      'description': 'إدارة الوصفات الصحية',
      'defaultIcon': Icons.menu_book_rounded,
      'colors': [Color(0xFF42A5F5), Color(0xFF1E88E5)],
    },
    {
      'id': 'restaurants',
      'title': 'المطاعم',
      'description': 'إدارة المطاعم الآمنة',
      'defaultIcon': Icons.restaurant_menu_rounded,
      'colors': [Color(0xFFEC407A), Color(0xFFD81B60)],
    },
    {
      'id': 'forum',
      'title': 'المنتدى',
      'description': 'إدارة المنتدى والمناقشات',
      'defaultIcon': Icons.forum_rounded,
      'colors': [Color(0xFF7E57C2), Color(0xFF5E35B1)],
    },
    {
      'id': 'articles',
      'title': 'المقالات',
      'description': 'إدارة المقالات الطبية',
      'defaultIcon': Icons.article_rounded,
      'colors': [Color(0xFF5C6BC0), Color(0xFF3949AB)],
    },
    {
      'id': 'doctors',
      'title': 'الأطباء',
      'description': 'إدارة قائمة الأطباء',
      'defaultIcon': Icons.local_hospital_rounded,
      'colors': [Color(0xFF4CAF50), Color(0xFF388E3C)],
    },
    {
      'id': 'symptoms',
      'title': 'تتبع الأعراض',
      'description': 'إدارة نظام تتبع الأعراض',
      'defaultIcon': Icons.analytics_rounded,
      'colors': [Color(0xFFFF7043), Color(0xFFE64A19)],
    },
    {
      'id': 'pharmacies',
      'title': 'الصيدليات',
      'description': 'إدارة قائمة الصيدليات',
      'defaultIcon': Icons.local_pharmacy_rounded,
      'colors': [Color(0xFF26C6DA), Color(0xFF00ACC1)],
    },
    {
      'id': 'reminders',
      'title': 'تذكير الأدوية',
      'description': 'إدارة نظام التذكير',
      'defaultIcon': Icons.schedule_rounded,
      'colors': [Color(0xFF66BB6A), Color(0xFF4CAF50)],
    },
    {
      'id': 'stores',
      'title': 'المتاجر الآمنة',
      'description': 'إدارة المتاجر الآمنة',
      'defaultIcon': Icons.store_rounded,
      'colors': [Color(0xFFAB47BC), Color(0xFF8E24AA)],
    },
    {
      'id': 'nutrition',
      'title': 'التغذية الشخصية',
      'description': 'إدارة نظام التغذية',
      'defaultIcon': Icons.eco_rounded,
      'colors': [Color(0xFF66BB6A), Color(0xFF4CAF50)],
    },
    {
      'id': 'emergency',
      'title': 'الطوارئ',
      'description': 'إدارة معلومات الطوارئ',
      'defaultIcon': Icons.emergency_rounded,
      'colors': [Color(0xFFEF5350), Color(0xFFE53935)],
    },
  ];

  @override
  void initState() {
    super.initState();
    _loadCardImages();
  }

  Future<void> _loadCardImages() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      for (var card in _homeCards) {
        final imagePath = prefs.getString('card_image_${card['id']}');
        if (imagePath != null) {
          card['customImage'] = imagePath;
        }
      }
    });
  }

  Future<void> _pickImageFromGallery(String cardId) async {
    try {
      setState(() => _isLoading = true);

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        await _saveCardImage(cardId, image.path);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في اختيار الصورة: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _pickImageFromCamera(String cardId) async {
    try {
      setState(() => _isLoading = true);

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        await _saveCardImage(cardId, image.path);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في التقاط الصورة: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _addImageFromUrl(String cardId) async {
    final TextEditingController urlController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إضافة صورة من رابط',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: TextField(
          controller: urlController,
          decoration: InputDecoration(
            hintText: 'أدخل رابط الصورة...',
            hintStyle: GoogleFonts.cairo(),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () async {
              if (urlController.text.isNotEmpty) {
                Navigator.pop(context);
                await _saveCardImageUrl(cardId, urlController.text);
              }
            },
            child: Text('حفظ', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  Future<void> _saveCardImage(String cardId, String imagePath) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('card_image_$cardId', imagePath);

      setState(() {
        final cardIndex = _homeCards.indexWhere((card) => card['id'] == cardId);
        if (cardIndex != -1) {
          _homeCards[cardIndex]['customImage'] = imagePath;
        }
      });

      _showSuccessSnackBar('تم حفظ الصورة بنجاح');
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ الصورة: $e');
    }
  }

  Future<void> _saveCardImageUrl(String cardId, String imageUrl) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('card_image_$cardId', imageUrl);

      setState(() {
        final cardIndex = _homeCards.indexWhere((card) => card['id'] == cardId);
        if (cardIndex != -1) {
          _homeCards[cardIndex]['customImage'] = imageUrl;
        }
      });

      _showSuccessSnackBar('تم حفظ رابط الصورة بنجاح');
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ رابط الصورة: $e');
    }
  }

  Future<void> _removeCardImage(String cardId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('card_image_$cardId');

      setState(() {
        final cardIndex = _homeCards.indexWhere((card) => card['id'] == cardId);
        if (cardIndex != -1) {
          _homeCards[cardIndex].remove('customImage');
        }
      });

      _showSuccessSnackBar('تم حذف الصورة بنجاح');
    } catch (e) {
      _showErrorSnackBar('خطأ في حذف الصورة: $e');
    }
  }

  void _showImageSourceDialog(String cardId) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              child: Text(
                'اختر مصدر الصورة',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ListTile(
              leading: const Icon(
                Icons.photo_library_rounded,
                color: Color(0xFF42A5F5),
              ),
              title: Text('من المعرض', style: GoogleFonts.cairo()),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromGallery(cardId);
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.camera_alt_rounded,
                color: Color(0xFF66BB6A),
              ),
              title: Text('من الكاميرا', style: GoogleFonts.cairo()),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromCamera(cardId);
              },
            ),
            ListTile(
              leading: const Icon(Icons.link_rounded, color: Color(0xFFFF7043)),
              title: Text('من رابط', style: GoogleFonts.cairo()),
              onTap: () {
                Navigator.pop(context);
                _addImageFromUrl(cardId);
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.cairo()),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.cairo()),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);

    if (!authProvider.isAdmin) {
      return Scaffold(
        appBar: AppBar(title: Text('غير مصرح', style: GoogleFonts.cairo())),
        body: const Center(child: Text('ليس لديك صلاحية للوصول لهذه الصفحة')),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: Text(
          'إدارة كروت الشاشة الرئيسية',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF6366F1),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Header Info
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              Icons.dashboard_customize_rounded,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'إدارة كروت الشاشة الرئيسية',
                                  style: GoogleFonts.cairo(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'يمكنك تخصيص صور الكروت من المعرض أو الكاميرا أو الروابط',
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    color: Colors.white.withValues(alpha: 0.9),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Cards List
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _homeCards.length,
                    itemBuilder: (context, index) {
                      final card = _homeCards[index];
                      return _buildCardItem(card);
                    },
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildCardItem(Map<String, dynamic> card) {
    final hasCustomImage = card.containsKey('customImage');

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Card Preview
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  colors: card['colors'],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: card['colors'][0].withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: hasCustomImage
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: _buildCustomImage(card['customImage']),
                    )
                  : Icon(card['defaultIcon'], size: 32, color: Colors.white),
            ),

            const SizedBox(width: 16),

            // Card Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    card['title'],
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    card['description'],
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        hasCustomImage
                            ? Icons.image_rounded
                            : Icons.auto_awesome_rounded,
                        size: 16,
                        color: hasCustomImage ? Colors.green : Colors.orange,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        hasCustomImage ? 'صورة مخصصة' : 'تصميم افتراضي',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: hasCustomImage ? Colors.green : Colors.orange,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Action Buttons
            Column(
              children: [
                IconButton(
                  onPressed: () => _showImageSourceDialog(card['id']),
                  icon: const Icon(Icons.edit_rounded),
                  style: IconButton.styleFrom(
                    backgroundColor: const Color(
                      0xFF42A5F5,
                    ).withValues(alpha: 0.1),
                    foregroundColor: const Color(0xFF42A5F5),
                  ),
                  tooltip: 'تغيير الصورة',
                ),
                if (hasCustomImage)
                  IconButton(
                    onPressed: () => _removeCardImage(card['id']),
                    icon: const Icon(Icons.delete_rounded),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.red.withValues(alpha: 0.1),
                      foregroundColor: Colors.red,
                    ),
                    tooltip: 'حذف الصورة',
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomImage(String imagePath) {
    if (imagePath.startsWith('http')) {
      // Image from URL
      return Image.network(
        imagePath,
        width: 80,
        height: 80,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 80,
            height: 80,
            color: Colors.grey.shade300,
            child: const Icon(Icons.broken_image, color: Colors.grey),
          );
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: 80,
            height: 80,
            color: Colors.grey.shade200,
            child: const Center(
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          );
        },
      );
    } else {
      // Image from device
      return Image.file(
        File(imagePath),
        width: 80,
        height: 80,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 80,
            height: 80,
            color: Colors.grey.shade300,
            child: const Icon(Icons.broken_image, color: Colors.grey),
          );
        },
      );
    }
  }
}

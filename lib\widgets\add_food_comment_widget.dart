import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../models/comment.dart';
import '../providers/food_provider.dart';
import '../providers/auth_provider.dart' as app_auth;
import '../utils/app_colors.dart';

class AddFoodCommentWidget extends StatefulWidget {
  final String foodItemId;
  final String? parentCommentId;
  final String? replyToUsername;
  final VoidCallback? onCommentAdded;

  const AddFoodCommentWidget({
    super.key,
    required this.foodItemId,
    this.parentCommentId,
    this.replyToUsername,
    this.onCommentAdded,
  });

  @override
  State<AddFoodCommentWidget> createState() => _AddFoodCommentWidgetState();
}

class _AddFoodCommentWidgetState extends State<AddFoodCommentWidget> {
  final TextEditingController _commentController = TextEditingController();
  final List<File> _selectedImages = [];
  final List<String> _uploadedImageUrls = [];
  bool _isSubmitting = false;
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  Future<void> _pickImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage();
      if (images.isNotEmpty) {
        setState(() {
          for (final image in images) {
            if (_selectedImages.length < 5) {
              _selectedImages.add(File(image.path));
            }
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء اختيار الصور'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  Future<void> _uploadImages() async {
    _uploadedImageUrls.clear();

    for (int i = 0; i < _selectedImages.length; i++) {
      try {
        // Placeholder for image upload service
        final imageUrl =
            'https://example.com/image_${DateTime.now().millisecondsSinceEpoch}_$i.jpg';
        _uploadedImageUrls.add(imageUrl);
      } catch (e) {
        debugPrint('خطأ في رفع الصورة: $e');
      }
    }
  }

  Future<void> _submitComment() async {
    if (_commentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى كتابة تعليق'),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final authProvider = Provider.of<app_auth.AuthProvider>(
        context,
        listen: false,
      );
      final foodProvider = Provider.of<FoodProvider>(context, listen: false);

      await _uploadImages();

      final comment = Comment(
        postId: widget.foodItemId,
        content: _commentController.text.trim(),
        userId: authProvider.currentUser!.uid,
        username:
            authProvider.userProfile?.displayName ??
            authProvider.userProfile?.username ??
            'مستخدم',
        userAvatar: authProvider.userProfile?.profileImageUrl,
        createdAt: DateTime.now(),
        imageUrls: _uploadedImageUrls,
        parentCommentId: widget.parentCommentId,
      );

      if (widget.parentCommentId != null) {
        await foodProvider.addReplyToComment(
          foodItemId: widget.foodItemId,
          parentCommentId: widget.parentCommentId!,
          reply: comment,
        );
      } else {
        await foodProvider.addCommentToFoodItem(widget.foodItemId, comment);
      }

      _commentController.clear();
      setState(() {
        _selectedImages.clear();
        _uploadedImageUrls.clear();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.parentCommentId != null
                  ? 'تم إضافة الرد بنجاح'
                  : 'تم إضافة التعليق بنجاح',
            ),
            backgroundColor: AppColors.success,
          ),
        );
        widget.onCommentAdded?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إضافة التعليق'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<app_auth.AuthProvider>(context);

    if (authProvider.currentUser == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Text(
            'يجب تسجيل الدخول لإضافة تعليق',
            style: GoogleFonts.cairo(
              color: AppColors.textSecondary,
              fontSize: 16,
            ),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(top: BorderSide(color: AppColors.border)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.replyToUsername != null) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Text(
                'رد على ${widget.replyToUsername}',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 12),
          ],
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Expanded(
                child: TextField(
                  controller: _commentController,
                  maxLines: null,
                  decoration: InputDecoration(
                    hintText: widget.parentCommentId != null
                        ? 'اكتب ردك...'
                        : 'اكتب تعليقك...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: AppColors.background,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  style: GoogleFonts.cairo(),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.textSecondary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  onPressed: _isSubmitting ? null : _pickImages,
                  icon: Icon(Icons.image, color: AppColors.textSecondary),
                  tooltip: 'إضافة صور',
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  onPressed: _isSubmitting ? null : _submitComment,
                  icon: _isSubmitting
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: AppColors.textOnPrimary,
                            strokeWidth: 2,
                          ),
                        )
                      : Icon(Icons.send, color: AppColors.textOnPrimary),
                  tooltip: 'إرسال',
                ),
              ),
            ],
          ),
          if (_selectedImages.isNotEmpty) ...[
            const SizedBox(height: 12),
            SizedBox(
              height: 80,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.only(left: 8),
                    width: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppColors.border),
                    ),
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.file(
                            _selectedImages[index],
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => _removeImage(index),
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                color: AppColors.error,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.close,
                                size: 12,
                                color: AppColors.textOnPrimary,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }
}

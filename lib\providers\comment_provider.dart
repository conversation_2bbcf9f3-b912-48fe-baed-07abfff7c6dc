import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/comment.dart';

class CommentProvider with ChangeNotifier {
  final String collectionPath;
  final String itemId;
  List<Comment> _comments = [];
  bool _isLoading = false;
  String? _errorMessage;

  List<Comment> get comments => _comments;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  CommentProvider({required this.collectionPath, required this.itemId}) {
    fetchComments();
  }

  Future<void> fetchComments() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    try {
      QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(collectionPath)
          .doc(itemId)
          .collection('comments')
          .orderBy('createdAt', descending: false)
          .get();
      _comments = snapshot.docs
          .map((doc) => Comment.fromFirestore(doc))
          .toList();
    } catch (e) {
      _errorMessage = 'حدث خطأ أثناء جلب التعليقات: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addComment(Comment comment) async {
    try {
      await FirebaseFirestore.instance
          .collection(collectionPath)
          .doc(itemId)
          .collection('comments')
          .add(comment.toMap());
      await fetchComments();
    } catch (e) {
      _errorMessage = 'حدث خطأ أثناء إضافة التعليق: $e';
      notifyListeners();
    }
  }
}

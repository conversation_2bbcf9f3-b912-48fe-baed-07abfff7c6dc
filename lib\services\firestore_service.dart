import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

class FirestoreService {
  FirebaseFirestore get db => _db;
  final FirebaseFirestore _db = FirebaseFirestore.instance;

  Future<String> addDocument(
    String collectionPath,
    Map<String, dynamic> data,
  ) async {
    try {
      DocumentReference docRef = await _db.collection(collectionPath).add(data);
      return docRef.id;
    } catch (e) {
      debugPrint("Error adding document to $collectionPath: $e");
      rethrow;
    }
  }

  Future<DocumentSnapshot> getDocument(
    String collectionPath,
    String docId,
  ) async {
    try {
      return await _db.collection(collectionPath).doc(docId).get();
    } catch (e) {
      debugPrint("Error getting document $docId from $collectionPath: $e");
      rethrow;
    }
  }

  Stream<QuerySnapshot> getCollectionStream(
    String collectionPath, {
    String? orderBy,
    bool descending = false,
    String? whereField,
    dynamic isEqualTo,
    int? limit,
  }) {
    Query query = _db.collection(collectionPath);
    if (whereField != null && isEqualTo != null) {
      query = query.where(whereField, isEqualTo: isEqualTo);
    }
    if (orderBy != null) {
      query = query.orderBy(orderBy, descending: descending);
    }
    if (limit != null) {
      query = query.limit(limit);
    }
    return query.snapshots();
  }

  Future<QuerySnapshot> getCollection(
    String collectionPath, {
    String? orderBy,
    bool descending = false,
    String? whereField,
    dynamic isEqualTo,
    int? limit,
  }) async {
    Query query = _db.collection(collectionPath);
    if (whereField != null && isEqualTo != null) {
      query = query.where(whereField, isEqualTo: isEqualTo);
    }
    if (orderBy != null) {
      query = query.orderBy(orderBy, descending: descending);
    }
    if (limit != null) {
      query = query.limit(limit);
    }
    return await query.get();
  }

  Future<void> updateDocument(
    String collectionPath,
    String docId,
    Map<String, dynamic> data,
  ) async {
    try {
      await _db.collection(collectionPath).doc(docId).update(data);
    } catch (e) {
      debugPrint("Error updating document $docId in $collectionPath: $e");
      rethrow;
    }
  }

  Future<void> deleteDocument(String collectionPath, String docId) async {
    try {
      await _db.collection(collectionPath).doc(docId).delete();
    } catch (e) {
      debugPrint("Error deleting document $docId from $collectionPath: $e");
      rethrow;
    }
  }

  Future<void> addDocumentWithId(
    String collectionPath,
    String docId,
    Map<String, dynamic> data,
  ) async {
    try {
      await _db.collection(collectionPath).doc(docId).set(data);
    } catch (e) {
      debugPrint("Error adding document $docId to $collectionPath: $e");
      rethrow;
    }
  }
}

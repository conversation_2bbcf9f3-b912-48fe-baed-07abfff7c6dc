import 'package:flutter/material.dart';
import 'package:yassincil/models/product_model.dart';

class ProductScreen extends StatelessWidget {
  final Product product;

  const ProductScreen({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(product.name)),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Product: ${product.name}',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 10),
            Text(
              'Gluten Free: ${product.isGlutenFree ? 'Yes' : 'No'}',
              style: TextStyle(
                color: product.isGlutenFree ? Colors.green : Colors.red,
                fontSize: 18,
              ),
            ),
            SizedBox(height: 20),
            Text(
              'Ingredients:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Text(product.ingredients),
            SizedBox(height: 20),
            Text('Notes:', style: Theme.of(context).textTheme.titleMedium),
            Text(product.notes),
          ],
        ),
      ),
    );
  }
}

// lib/widgets/add_forum_comment_widget.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../models/comment.dart';
import '../providers/forum_provider.dart';
import '../providers/auth_provider.dart' as app_auth;
import '../utils/app_colors.dart';

class AddForumCommentWidget extends StatefulWidget {
  final String postId;
  final String? parentCommentId;
  final String? replyToUsername;
  final VoidCallback? onCommentAdded;

  const AddForumCommentWidget({
    super.key,
    required this.postId,
    this.parentCommentId,
    this.replyToUsername,
    this.onCommentAdded,
  });

  @override
  State<AddForumCommentWidget> createState() => _AddForumCommentWidgetState();
}

class _AddForumCommentWidgetState extends State<AddForumCommentWidget> {
  final TextEditingController _commentController = TextEditingController();
  final List<File> _selectedImages = [];
  bool _isSubmitting = false;
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  Future<void> _pickImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage();
      if (images.isNotEmpty) {
        setState(() {
          for (final image in images) {
            if (_selectedImages.length < 5) {
              _selectedImages.add(File(image.path));
            }
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء اختيار الصور'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  Future<void> _submitComment() async {
    if (_commentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى كتابة تعليق'),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final authProvider = Provider.of<app_auth.AuthProvider>(
        context,
        listen: false,
      );
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);

      if (authProvider.currentUser == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      // رفع الصور إذا كانت موجودة
      await forumProvider.addComment(
        postId: widget.postId,
        content: _commentController.text.trim(),
        parentCommentId: widget.parentCommentId,
        images: _selectedImages,
      );

      // مسح الحقول
      _commentController.clear();
      setState(() {
        _selectedImages.clear();
      });

      // إشعار بالنجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.parentCommentId != null
                  ? 'تم إضافة الرد بنجاح'
                  : 'تم إضافة التعليق بنجاح',
            ),
            backgroundColor: AppColors.success,
          ),
        );

        // استدعاء callback إذا كان موجود
        widget.onCommentAdded?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إضافة التعليق'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<app_auth.AuthProvider>(context);

    if (authProvider.currentUser == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Text(
            'يجب تسجيل الدخول لإضافة تعليق',
            style: GoogleFonts.cairo(
              color: AppColors.textSecondary,
              fontSize: 16,
            ),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(top: BorderSide(color: AppColors.border)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عرض اسم المستخدم المراد الرد عليه
          if (widget.replyToUsername != null) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Text(
                'رد على ${widget.replyToUsername}',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 12),
          ],

          // حقل النص والأزرار
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // حقل الإدخال
              Expanded(
                child: TextField(
                  controller: _commentController,
                  maxLines: null,
                  decoration: InputDecoration(
                    hintText: widget.parentCommentId != null
                        ? 'اكتب ردك...'
                        : 'اكتب تعليقك...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: AppColors.background,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  style: GoogleFonts.cairo(),
                ),
              ),

              const SizedBox(width: 8),

              // زر اختيار الصور
              Container(
                decoration: BoxDecoration(
                  color: AppColors.textSecondary.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  onPressed: _isSubmitting ? null : _pickImages,
                  icon: Icon(Icons.image, color: AppColors.textSecondary),
                  tooltip: 'إضافة صور',
                ),
              ),

              const SizedBox(width: 8),

              // زر الإرسال
              Container(
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  onPressed: _isSubmitting ? null : _submitComment,
                  icon: _isSubmitting
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: AppColors.textOnPrimary,
                            strokeWidth: 2,
                          ),
                        )
                      : Icon(Icons.send, color: AppColors.textOnPrimary),
                  tooltip: 'إرسال',
                ),
              ),
            ],
          ),

          // عرض الصور المختارة
          if (_selectedImages.isNotEmpty) ...[
            const SizedBox(height: 12),
            SizedBox(
              height: 80,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.only(left: 8),
                    width: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppColors.border),
                    ),
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.file(
                            _selectedImages[index],
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => _removeImage(index),
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                color: AppColors.error,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.close,
                                size: 12,
                                color: AppColors.textOnPrimary,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:yassincil/models/food_item.dart';
import 'package:yassincil/models/medication.dart';
import 'package:yassincil/services/firestore_service.dart';
import 'package:yassincil/utils/app_constants.dart';
import 'package:yassincil/utils/database_helper.dart';

class FavoritesProvider with ChangeNotifier {
  final FirestoreService _firestoreService;
  final DatabaseHelper _dbHelper;

  List<String> _favoriteMedicationIds = [];
  List<Medication> _favoriteMedications = [];
  List<String> _favoriteFoodIds = [];
  List<FoodItem> _favoriteFoods = [];

  bool _isLoading = false;
  String? _errorMessage;

  List<Medication> get favoriteMedications => _favoriteMedications;
  List<FoodItem> get favoriteFoods => _favoriteFoods;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  FavoritesProvider(this._firestoreService, this._dbHelper);

  bool isFavorite(String itemId) {
    return _favoriteMedicationIds.contains(itemId) || _favoriteFoodIds.contains(itemId);
  }

  bool isMedicationFavorite(String medicationId) {
    return _favoriteMedicationIds.contains(medicationId);
  }

  bool isFoodFavorite(String foodId) {
    return _favoriteFoodIds.contains(foodId);
  }

  Future<void> fetchFavorites() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      await _fetchMedicationFavorites(user.uid);
      await _fetchFoodFavorites(user.uid);
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء جلب المفضلة: $e";
      debugPrint("Error fetching favorites: $e");
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _fetchMedicationFavorites(String userId) async {
    final localFavorites = await _dbHelper.getFavorites(userId, type: 'medication');
    _favoriteMedicationIds = localFavorites;
    if (localFavorites.isNotEmpty) {
      await _loadFavoriteMedications();
    }

    final snapshot = await _firestoreService.db
        .collection('users')
        .doc(userId)
        .collection('favorite_medications')
        .get();
    final firebaseFavorites = snapshot.docs.map((doc) => doc.id).toList();

    await _dbHelper.clearFavorites(userId, type: 'medication');
    for (final medicationId in firebaseFavorites) {
      await _dbHelper.addFavorite(userId, medicationId, type: 'medication');
    }

    _favoriteMedicationIds = firebaseFavorites;
    await _loadFavoriteMedications();
  }

  Future<void> _fetchFoodFavorites(String userId) async {
    final localFavorites = await _dbHelper.getFavorites(userId, type: 'food');
    _favoriteFoodIds = localFavorites;
    if (localFavorites.isNotEmpty) {
      await _loadFavoriteFoods();
    }

    final snapshot = await _firestoreService.db
        .collection('users')
        .doc(userId)
        .collection('favorite_foods')
        .get();
    final firebaseFavorites = snapshot.docs.map((doc) => doc.id).toList();

    await _dbHelper.clearFavorites(userId, type: 'food');
    for (final foodId in firebaseFavorites) {
      await _dbHelper.addFavorite(userId, foodId, type: 'food');
    }

    _favoriteFoodIds = firebaseFavorites;
    await _loadFavoriteFoods();
  }

  Future<void> _loadFavoriteMedications() async {
    if (_favoriteMedicationIds.isEmpty) {
      _favoriteMedications = [];
      return;
    }
    // Logic to load medications from local DB or Firestore
  }

  Future<void> _loadFavoriteFoods() async {
    if (_favoriteFoodIds.isEmpty) {
      _favoriteFoods = [];
      return;
    }
    // Logic to load foods from local DB or Firestore
  }

  Future<void> toggleFavorite(dynamic item) async {
    if (item is Medication) {
      await toggleMedicationFavorite(item);
    } else if (item is FoodItem) {
      await toggleFoodFavorite(item);
    }
  }

  Future<void> toggleMedicationFavorite(Medication medication) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;
    final itemId = medication.id!;
    final isFav = isMedicationFavorite(itemId);
    try {
      if (isFav) {
        await _firestoreService.deleteDocument('users/${user.uid}/favorite_medications', itemId);
        await _dbHelper.removeFavorite(user.uid, itemId, type: 'medication');
        _favoriteMedicationIds.remove(itemId);
        _favoriteMedications.removeWhere((med) => med.id == itemId);
      } else {
        await _firestoreService.addDocumentWithId('users/${user.uid}/favorite_medications', itemId, {'addedAt': FieldValue.serverTimestamp()});
        await _dbHelper.addFavorite(user.uid, itemId, type: 'medication');
        _favoriteMedicationIds.add(itemId);
        _favoriteMedications.add(medication);
      }
      notifyListeners();
    } catch (e) {
      // handle error
    }
  }

  Future<void> toggleFoodFavorite(FoodItem food) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;
    final itemId = food.id!;
    final isFav = isFoodFavorite(itemId);
    try {
      if (isFav) {
        await _firestoreService.deleteDocument('users/${user.uid}/favorite_foods', itemId);
        await _dbHelper.removeFavorite(user.uid, itemId, type: 'food');
        _favoriteFoodIds.remove(itemId);
        _favoriteFoods.removeWhere((f) => f.id == itemId);
      } else {
        await _firestoreService.addDocumentWithId('users/${user.uid}/favorite_foods', itemId, {'addedAt': FieldValue.serverTimestamp()});
        await _dbHelper.addFavorite(user.uid, itemId, type: 'food');
        _favoriteFoodIds.add(itemId);
        _favoriteFoods.add(food);
      }
      notifyListeners();
    } catch (e) {
      // handle error
    }
  }

  Map<String, int> getFavoritesStatistics({String itemType = 'medication'}) {
    if (itemType == 'medication') {
      final total = _favoriteMedications.length;
      final safe = _favoriteMedications.where((med) => med.isAllowed).length;
      final unsafe = total - safe;
      return {'total': total, 'safe': safe, 'unsafe': unsafe};
    } else {
      final total = _favoriteFoods.length;
      final glutenFree = _favoriteFoods.where((food) => food.isGlutenFree).length;
      final gluten = total - glutenFree;
      return {'total': total, 'glutenFree': glutenFree, 'gluten': gluten};
    }
  }
}

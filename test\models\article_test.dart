import 'package:flutter_test/flutter_test.dart';
import 'package:yassincil/models/article.dart';

void main() {
  group('Article Model Tests', () {
    test('should create an article with required fields', () {
      // Arrange
      final article = Article(
        title: 'مقال تجريبي',
        content: 'محتوى تجريبي للمقال',
        author: 'كاتب تجريبي',
        category: 'التغذية',
        publishDate: DateTime.now(),
      );

      // Assert
      expect(article.title, 'مقال تجريبي');
      expect(article.content, 'محتوى تجريبي للمقال');
      expect(article.author, 'كاتب تجريبي');
      expect(article.category, 'التغذية');
      expect(article.publishDate, isA<DateTime>());
    });

    test('should create article with default values', () {
      // Arrange
      final article = Article(
        title: 'مقال تجريبي',
        content: 'محتوى تجريبي',
        author: 'كاتب تجريبي',
        category: 'التغذية',
        publishDate: DateTime.now(),
      );

      // Assert
      expect(article.imageUrls, isEmpty);
      expect(article.tags, isEmpty);
      expect(article.sources, isEmpty);
      expect(article.likesCount, 0);
      expect(article.commentsCount, 0);
      expect(article.viewsCount, 0);
      expect(article.sharesCount, 0);
      expect(article.isApproved, true);
      expect(article.isFeatured, false);
      expect(article.isPinned, false);
      expect(article.difficulty, 'متوسط');
      expect(article.readingTime, 5);
    });

    test('should create article with optional fields', () {
      // Arrange
      final article = Article(
        title: 'مقال تجريبي',
        content: 'محتوى تجريبي',
        author: 'كاتب تجريبي',
        category: 'التغذية',
        publishDate: DateTime.now(),
        summary: 'ملخص المقال',
        authorBio: 'نبذة عن الكاتب',
        tags: ['تغذية', 'صحة'],
        sources: ['مصدر 1', 'مصدر 2'],
        imageUrls: ['https://example.com/image.jpg'],
        difficulty: 'سهل',
        readingTime: 10,
        isFeatured: true,
        isPinned: true,
      );

      // Assert
      expect(article.summary, 'ملخص المقال');
      expect(article.authorBio, 'نبذة عن الكاتب');
      expect(article.tags.length, 2);
      expect(article.sources.length, 2);
      expect(article.imageUrls.length, 1);
      expect(article.difficulty, 'سهل');
      expect(article.readingTime, 10);
      expect(article.isFeatured, true);
      expect(article.isPinned, true);
    });

    test('should convert to map correctly', () {
      // Arrange
      final article = Article(
        title: 'مقال تجريبي',
        content: 'محتوى تجريبي',
        author: 'كاتب تجريبي',
        category: 'التغذية',
        publishDate: DateTime.now(),
        tags: ['تغذية', 'صحة'],
        likesCount: 5,
        commentsCount: 3,
      );

      // Act
      final map = article.toMap();

      // Assert
      expect(map['title'], 'مقال تجريبي');
      expect(map['content'], 'محتوى تجريبي');
      expect(map['author'], 'كاتب تجريبي');
      expect(map['category'], 'التغذية');
      expect(map['tags'], ['تغذية', 'صحة']);
      expect(map['likesCount'], 5);
      expect(map['commentsCount'], 3);
      expect(map['isApproved'], true);
      expect(map['isFeatured'], false);
      expect(map['publishDate'], isA<dynamic>());
    });

    test('should have valid properties', () {
      // Arrange
      final article = Article(
        title: 'مقال تجريبي',
        content: 'محتوى تجريبي',
        author: 'كاتب تجريبي',
        category: 'التغذية',
        publishDate: DateTime.now(),
      );

      // Assert
      expect(article.title, isNotEmpty);
      expect(article.content, isNotEmpty);
      expect(article.author, isNotEmpty);
      expect(article.category, isNotEmpty);
      expect(article.publishDate, isA<DateTime>());
    });
  });
}

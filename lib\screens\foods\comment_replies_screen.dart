import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:yassincil/models/comment.dart';
import 'package:yassincil/providers/food_provider.dart';
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/widgets/advanced_food_comment_widget.dart';
import 'package:yassincil/widgets/add_food_comment_widget.dart';

class CommentRepliesScreen extends StatelessWidget {
  final String foodItemId;
  final Comment parentComment;

  const CommentRepliesScreen({
    super.key,
    required this.foodItemId,
    required this.parentComment,
  });

  @override
  Widget build(BuildContext context) {
    final foodProvider = Provider.of<FoodProvider>(context, listen: false);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'الردود على ${parentComment.username}',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: AppColors.textOnPrimary,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
      ),
      body: Column(
        children: [
          Expanded(
            child: CustomScrollView(
              slivers: [
                // Parent Comment
                SliverToBoxAdapter(
                  child: Container(
                    color: Colors.white,
                    padding: const EdgeInsets.all(8.0),
                    margin: const EdgeInsets.only(bottom: 8.0),
                    child: AdvancedFoodCommentWidget(
                      comment: parentComment,
                      foodItemId: foodItemId,
                      showReplies: false, // Don't show replies recursively here
                    ),
                  ),
                ),
                // Replies List
                StreamBuilder<List<Comment>>(
                  stream: foodProvider.getRepliesForComment(
                    foodItemId: foodItemId,
                    parentCommentId: parentComment.id!,
                  ),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const SliverToBoxAdapter(
                        child: Center(child: CircularProgressIndicator()),
                      );
                    }
                    if (snapshot.hasError) {
                      return SliverToBoxAdapter(
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            children: [
                              Icon(
                                Icons.error_outline,
                                size: 48,
                                color: AppColors.error,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'حدث خطأ في تحميل الردود',
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.error,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'يرجى المحاولة مرة أخرى',
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: () {
                                  // إعادة تحميل الصفحة
                                  Navigator.of(context).pushReplacement(
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          CommentRepliesScreen(
                                            foodItemId: foodItemId,
                                            parentComment: parentComment,
                                          ),
                                    ),
                                  );
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.primary,
                                ),
                                child: Text(
                                  'إعادة المحاولة',
                                  style: GoogleFonts.cairo(
                                    color: AppColors.textOnPrimary,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                    final replies = snapshot.data ?? [];
                    if (replies.isEmpty) {
                      return SliverToBoxAdapter(
                        child: Center(
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Text(
                              'لا توجد ردود. كن أول من يرد!',
                              style: GoogleFonts.cairo(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ),
                        ),
                      );
                    }
                    return SliverList(
                      delegate: SliverChildBuilderDelegate((context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(
                            left: 16.0,
                            right: 8.0,
                            bottom: 8.0,
                          ),
                          child: AdvancedFoodCommentWidget(
                            comment: replies[index],
                            foodItemId: foodItemId,
                            showReplies: false,
                          ),
                        );
                      }, childCount: replies.length),
                    );
                  },
                ),
              ],
            ),
          ),
          // Add Reply Widget
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: AppColors.surface,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: AddFoodCommentWidget(
              foodItemId: foodItemId,
              parentCommentId: parentComment.id,
              replyToUsername: parentComment.username,
              onCommentAdded: () {
                // The stream builder will automatically update the list
              },
            ),
          ),
        ],
      ),
    );
  }
}

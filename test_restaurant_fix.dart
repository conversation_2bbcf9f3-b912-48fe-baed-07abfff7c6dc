import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/providers/restaurant_provider.dart';
import 'lib/services/firestore_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // اختبار بسيط لجلب المطاعم
  final firestoreService = FirestoreService();
  final restaurantProvider = RestaurantProvider(firestoreService);

  print("🧪 بدء اختبار جلب المطاعم...");

  try {
    await restaurantProvider.fetchRestaurants();
    print("✅ تم جلب ${restaurantProvider.restaurants.length} مطعم بنجاح");

    for (var restaurant in restaurantProvider.restaurants) {
      print("📍 ${restaurant.name} - ${restaurant.address}");
    }

    if (restaurantProvider.restaurants.isNotEmpty) {
      print("🎉 المشكلة تم حلها! المطاعم تظهر بشكل صحيح");
    } else {
      print("⚠️ لا توجد مطاعم في القائمة");
    }
  } catch (e) {
    print("❌ خطأ في الاختبار: $e");
  }
}

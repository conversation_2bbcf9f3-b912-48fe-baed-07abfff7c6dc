import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/providers/review_provider.dart';
import 'package:yassincil/models/review.dart';

class ReviewsManagementScreen extends StatefulWidget {
  const ReviewsManagementScreen({super.key});

  @override
  State<ReviewsManagementScreen> createState() =>
      _ReviewsManagementScreenState();
}

class _ReviewsManagementScreenState extends State<ReviewsManagementScreen> {
  String _searchQuery = '';
  String _selectedFilter = 'الكل';
  final List<String> _filters = [
    'الكل',
    'مبلغ عنها',
    'تقييم عالي (4-5)',
    'تقييم منخفض (1-2)',
    'موثقة',
    'مع صور',
  ];

  List<Review> _allReviews = [];

  @override
  void initState() {
    super.initState();
    _loadAllReviews();
  }

  Future<void> _loadAllReviews() async {
    // تحميل جميع المراجعات من مجموعات مختلفة (مؤقتاً: بيانات تجريبية)
    // This is a placeholder - in real implementation, you'd fetch from Firestore
    setState(() {
      _allReviews = [];
    });
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final isAdmin = authProvider.isAdmin;

    if (!isAdmin) {
      return Scaffold(
        appBar: AppBar(title: Text('غير مصرح', style: GoogleFonts.cairo())),
        body: Center(
          child: Text(
            'ليس لديك صلاحية للوصول لهذه الصفحة',
            style: GoogleFonts.cairo(),
          ),
        ),
      );
    }

    final filteredReviews = _getFilteredReviews();

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'إدارة التقييمات',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.orange.shade600,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics, color: Colors.white),
            onPressed: () => _showStatistics(),
            tooltip: 'الإحصائيات',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildStatisticsCard(),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                await _loadAllReviews();
              },
              child: filteredReviews.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: filteredReviews.length,
                      itemBuilder: (context, index) {
                        return _buildReviewCard(filteredReviews[index]);
                      },
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.shade600,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // شريط البحث
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'البحث في التقييمات...',
                hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500),
                prefixIcon: Icon(Icons.search, color: Colors.orange.shade600),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 15,
                ),
              ),
              style: GoogleFonts.cairo(),
            ),
          ),
          const SizedBox(height: 16),
          // فلاتر
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _filters.length,
              itemBuilder: (context, index) {
                final filter = _filters[index];
                final isSelected = _selectedFilter == filter;

                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(
                      filter,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: isSelected
                            ? Colors.white
                            : Colors.orange.shade700,
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedFilter = filter;
                      });
                    },
                    backgroundColor: Colors.orange.shade50,
                    selectedColor: Colors.orange.shade700,
                    checkmarkColor: Colors.white,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCard() {
    final totalReviews = _allReviews.length;
    final reportedReviews = _allReviews.where((r) => r.isReported).length;
    final verifiedReviews = _allReviews.where((r) => r.isVerified).length;
    final highRatingReviews = _allReviews.where((r) => r.rating >= 4.0).length;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.orange.shade400, Colors.orange.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إجمالي التقييمات',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                Text(
                  '$totalReviews',
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'مبلغ عنها: $reportedReviews',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إحصائيات إضافية',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                Text(
                  'موثقة: $verifiedReviews',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
                Text(
                  'تقييم عالي: $highRatingReviews',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.rate_review,
            size: 48,
            color: Colors.white.withValues(alpha: 0.7),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewCard(Review review) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.orange.shade100,
                  child: Text(
                    review.userName.isNotEmpty
                        ? review.userName[0].toUpperCase()
                        : 'م',
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      color: Colors.orange.shade700,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            review.userName,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey.shade800,
                            ),
                          ),
                          if (review.isVerified) ...[
                            const SizedBox(width: 4),
                            Icon(
                              Icons.verified,
                              size: 16,
                              color: Colors.green.shade600,
                            ),
                          ],
                          if (review.isReported) ...[
                            const SizedBox(width: 4),
                            Icon(
                              Icons.flag,
                              size: 16,
                              color: Colors.red.shade600,
                            ),
                          ],
                        ],
                      ),
                      Row(
                        children: [
                          _buildStarRating(review.rating),
                          const SizedBox(width: 8),
                          Text(
                            review.timeAgo,
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: Colors.grey.shade500,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        '${review.targetType}: ${review.targetId}',
                        style: GoogleFonts.cairo(
                          fontSize: 11,
                          color: Colors.grey.shade500,
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  icon: Icon(Icons.more_vert, color: Colors.grey.shade600),
                  onSelected: (value) {
                    if (value == 'approve') {
                      _approveReview(review);
                    } else if (value == 'delete') {
                      _deleteReview(review);
                    } else if (value == 'verify') {
                      _verifyReview(review);
                    } else if (value == 'view') {
                      _viewReviewDetails(review);
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'view',
                      child: Row(
                        children: [
                          const Icon(Icons.visibility, size: 18),
                          const SizedBox(width: 8),
                          Text('عرض التفاصيل', style: GoogleFonts.cairo()),
                        ],
                      ),
                    ),
                    if (review.isReported)
                      PopupMenuItem(
                        value: 'approve',
                        child: Row(
                          children: [
                            const Icon(
                              Icons.check,
                              size: 18,
                              color: Colors.green,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'الموافقة',
                              style: GoogleFonts.cairo(color: Colors.green),
                            ),
                          ],
                        ),
                      ),
                    if (!review.isVerified)
                      PopupMenuItem(
                        value: 'verify',
                        child: Row(
                          children: [
                            const Icon(
                              Icons.verified,
                              size: 18,
                              color: Colors.blue,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'توثيق',
                              style: GoogleFonts.cairo(color: Colors.blue),
                            ),
                          ],
                        ),
                      ),
                    PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          const Icon(Icons.delete, size: 18, color: Colors.red),
                          const SizedBox(width: 8),
                          Text(
                            'حذف',
                            style: GoogleFonts.cairo(color: Colors.red),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (review.title.isNotEmpty) ...[
              Text(
                review.title,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade800,
                ),
              ),
              const SizedBox(height: 8),
            ],
            Text(
              review.comment,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade700,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            if (review.hasTags) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 6,
                runSpacing: 6,
                children: review.tags
                    .take(3)
                    .map(
                      (tag) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.orange.shade200),
                        ),
                        child: Text(
                          tag,
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                Text(
                  'مفيد (${review.helpfulCount})',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
                const Spacer(),
                if (review.isReported)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Text(
                      'مبلغ عنه',
                      style: GoogleFonts.cairo(
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                        color: Colors.red.shade700,
                      ),
                    ),
                  ),
                Text(
                  review.ratingText,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: review.rating >= 4.0
                        ? Colors.green.shade600
                        : Colors.orange.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStarRating(double rating) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return Icon(
          index < rating.floor()
              ? Icons.star
              : index < rating
              ? Icons.star_half
              : Icons.star_border,
          color: Colors.orange,
          size: 16,
        );
      }),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.rate_review_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد تقييمات',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على تقييمات تطابق البحث',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  List<Review> _getFilteredReviews() {
    var filtered = _allReviews.where((review) {
      if (_searchQuery.isNotEmpty) {
        return review.title.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            review.comment.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            review.userName.toLowerCase().contains(_searchQuery.toLowerCase());
      }
      return true;
    }).toList();

    switch (_selectedFilter) {
      case 'مبلغ عنها':
        filtered = filtered.where((r) => r.isReported).toList();
        break;
      case 'تقييم عالي (4-5)':
        filtered = filtered.where((r) => r.rating >= 4.0).toList();
        break;
      case 'تقييم منخفض (1-2)':
        filtered = filtered.where((r) => r.rating <= 2.0).toList();
        break;
      case 'موثقة':
        filtered = filtered.where((r) => r.isVerified).toList();
        break;
      case 'مع صور':
        filtered = filtered.where((r) => r.hasImages).toList();
        break;
    }

    return filtered;
  }

  void _approveReview(Review review) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('الموافقة على التقييم', style: GoogleFonts.cairo()),
        content: Text(
          'هل تريد الموافقة على هذا التقييم وإزالة البلاغ؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'تمت الموافقة على التقييم',
                    style: GoogleFonts.cairo(),
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: Text(
              'موافقة',
              style: GoogleFonts.cairo(color: Colors.green),
            ),
          ),
        ],
      ),
    );
  }

  void _deleteReview(Review review) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف التقييم', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف هذا التقييم؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف التقييم', style: GoogleFonts.cairo()),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _verifyReview(Review review) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('توثيق التقييم', style: GoogleFonts.cairo()),
        content: Text('هل تريد توثيق هذا التقييم؟', style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم توثيق التقييم', style: GoogleFonts.cairo()),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: Text('توثيق', style: GoogleFonts.cairo(color: Colors.blue)),
          ),
        ],
      ),
    );
  }

  void _viewReviewDetails(Review review) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل التقييم', style: GoogleFonts.cairo()),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('المستخدم: ${review.userName}', style: GoogleFonts.cairo()),
              const SizedBox(height: 8),
              Text('التقييم: ${review.rating}/5', style: GoogleFonts.cairo()),
              const SizedBox(height: 8),
              Text('العنوان: ${review.title}', style: GoogleFonts.cairo()),
              const SizedBox(height: 8),
              Text(
                'التعليق:',
                style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
              ),
              Text(review.comment, style: GoogleFonts.cairo()),
              const SizedBox(height: 8),
              Text(
                'التاريخ: ${review.createdAt.toString().split(' ')[0]}',
                style: GoogleFonts.cairo(),
              ),
              const SizedBox(height: 8),
              Text(
                'مفيد: ${review.helpfulCount} مرة',
                style: GoogleFonts.cairo(),
              ),
              if (review.isReported) ...[
                const SizedBox(height: 8),
                Text(
                  'سبب البلاغ: ${review.reportReason ?? "غير محدد"}',
                  style: GoogleFonts.cairo(color: Colors.red),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _showStatistics() {
    final totalReviews = _allReviews.length;
    final reportedReviews = _allReviews.where((r) => r.isReported).length;
    final verifiedReviews = _allReviews.where((r) => r.isVerified).length;
    final highRatingReviews = _allReviews.where((r) => r.rating >= 4.0).length;
    final lowRatingReviews = _allReviews.where((r) => r.rating <= 2.0).length;
    final withImages = _allReviews.where((r) => r.hasImages).length;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إحصائيات التقييمات', style: GoogleFonts.cairo()),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatRow('إجمالي التقييمات', '$totalReviews'),
            _buildStatRow('مبلغ عنها', '$reportedReviews'),
            _buildStatRow('موثقة', '$verifiedReviews'),
            _buildStatRow('تقييم عالي (4-5)', '$highRatingReviews'),
            _buildStatRow('تقييم منخفض (1-2)', '$lowRatingReviews'),
            _buildStatRow('مع صور', '$withImages'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: GoogleFonts.cairo()),
          Text(value, style: GoogleFonts.cairo(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:yassincil/models/post.dart';
import 'package:yassincil/models/comment.dart';
import 'package:yassincil/services/firestore_service.dart';
import 'package:yassincil/services/storage_service.dart';
import 'package:yassincil/utils/app_constants.dart';
import 'dart:io';

class PostProvider with ChangeNotifier {
  final FirestoreService _firestoreService;
  final StorageService _storageService;

  List<Post> _posts = [];
  bool _isLoading = false;
  String? _errorMessage;

  FirestoreService get firestoreService => _firestoreService;

  List<Post> get posts => _posts;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  PostProvider(this._firestoreService, this._storageService) {
    fetchPosts();
  }

  Future<void> fetchPosts() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      QuerySnapshot snapshot = await _firestoreService.getCollection(
        AppConstants.postsCollection,
        orderBy: 'createdAt',
        descending: true,
      );
      _posts = snapshot.docs.map((doc) => Post.fromFirestore(doc)).toList();
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء جلب المنشورات: $e";
      debugPrint("Error fetching posts: $e");
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addPost(Post post, {File? imageFile}) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    try {
      String? imageUrl;
      if (imageFile != null) {
        imageUrl = await _storageService.uploadFile(
          imageFile,
          '${AppConstants.postImagesPath}/${DateTime.now().millisecondsSinceEpoch}_${imageFile.path.split('/').last}',
        );
      }
      final postData = post.toMap();
      postData['imageUrl'] = imageUrl;

      await _firestoreService.addDocument(
        AppConstants.postsCollection,
        postData,
      );
      await fetchPosts();
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء إضافة المنشور: $e";
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> updatePost(Post post, {File? imageFile}) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    try {
      if (post.id == null) throw Exception("Post ID is required for update.");

      String? imageUrl = post.imageUrl;
      if (imageFile != null) {
        if (post.imageUrl != null && post.imageUrl!.isNotEmpty) {
          await _storageService.deleteFile(post.imageUrl!);
        }
        imageUrl = await _storageService.uploadFile(
          imageFile,
          '${AppConstants.postImagesPath}/${DateTime.now().millisecondsSinceEpoch}_${imageFile.path.split('/').last}',
        );
      }

      final postData = post.toMap();
      postData['imageUrl'] = imageUrl;

      await _firestoreService.updateDocument(
        AppConstants.postsCollection,
        post.id!,
        postData,
      );
      await fetchPosts();
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء تحديث المنشور: $e";
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> deletePost(String postId) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    try {
      // Fetch post to get image URL for deletion
      final postDoc = await _firestoreService.getDocument(
        AppConstants.postsCollection,
        postId,
      );
      if (postDoc.exists && postDoc.data() != null) {
        final post = Post.fromFirestore(postDoc);
        if (post.imageUrl != null && post.imageUrl!.isNotEmpty) {
          await _storageService.deleteFile(post.imageUrl!);
        }
      }

      // Delete all comments subcollection manually (Firestore doesn't do this automatically)
      final commentsSnapshot = await _firestoreService.getCollection(
        'posts/$postId/${AppConstants.commentsSubcollection}',
      );
      for (var doc in commentsSnapshot.docs) {
        await _firestoreService.deleteDocument(
          'posts/$postId/${AppConstants.commentsSubcollection}',
          doc.id,
        );
      }

      await _firestoreService.deleteDocument(
        AppConstants.postsCollection,
        postId,
      );
      await fetchPosts();
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء حذف المنشور: $e";
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Stream<List<Comment>> getCommentsForPost(String postId) {
    return _firestoreService
        .getCollectionStream(
          '${AppConstants.postsCollection}/$postId/${AppConstants.commentsSubcollection}',
          orderBy: 'createdAt',
        )
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Comment.fromFirestore(doc)).toList(),
        );
  }

  Future<void> addComment(String postId, Comment comment) async {
    try {
      await _firestoreService.addDocument(
        '${AppConstants.postsCollection}/$postId/${AppConstants.commentsSubcollection}',
        comment.toMap(),
      );
      await _firestoreService.updateDocument(
        AppConstants.postsCollection,
        postId,
        {'commentCount': FieldValue.increment(1)},
      );
      // No need to fetchPosts() again as it's a stream listener, but could do for immediate UI update.
    } catch (e) {
      debugPrint("Error adding comment: $e");
      rethrow;
    }
  }

  Future<void> deleteComment(String postId, String commentId) async {
    try {
      await _firestoreService.deleteDocument(
        '${AppConstants.postsCollection}/$postId/${AppConstants.commentsSubcollection}',
        commentId,
      );
      await _firestoreService.updateDocument(
        AppConstants.postsCollection,
        postId,
        {'commentCount': FieldValue.increment(-1)},
      );
    } catch (e) {
      debugPrint("Error deleting comment: $e");
      rethrow;
    }
  }
}

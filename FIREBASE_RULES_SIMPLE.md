# قواعد Firebase المبسطة للتطوير 🔥

## 🚨 **حل سريع لمشكلة جلب الأدوية**

المشكلة: القواعد المتقدمة تتطلب تسجيل دخول لقراءة البيانات، لكن التطبيق يحاول جلب البيانات قبل التحقق من تسجيل الدخول.

### **الحل 1: قواعد مبسطة للتطوير**

انسخ هذه القواعد إلى **Firebase Console > Firestore > Rules**:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // دالة للتحقق من تسجيل الدخول
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // دالة للتحقق من المشرف
    function isAdmin() {
      return isAuthenticated() && 
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // دالة للتحقق من الملكية
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // ==================== قواعد المستخدمين ====================
    match /users/{userId} {
      allow read, write: if isOwner(userId) || isAdmin();
      allow create: if isOwner(userId);
    }
    
    // ==================== قواعد الأدوية ====================
    match /medications/{medicationId} {
      // قراءة مفتوحة للجميع (حتى غير المسجلين)
      allow read: if true;
      // كتابة للمشرفين فقط
      allow write: if isAdmin();
      
      // تعليقات الأدوية
      match /comments/{commentId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() && 
                                 (request.auth.uid == resource.data.userId || isAdmin());
      }
      
      // ردود التعليقات
      match /comments/{commentId}/replies/{replyId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() && 
                                 (request.auth.uid == resource.data.userId || isAdmin());
      }
    }
    
    // ==================== قواعد الأطعمة ====================
    match /foodItems/{foodId} {
      allow read: if true;
      allow write: if isAdmin();
      
      match /comments/{commentId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() && 
                                 (request.auth.uid == resource.data.userId || isAdmin());
      }
    }
    
    // ==================== قواعد المطاعم ====================
    match /restaurants/{restaurantId} {
      allow read: if true;
      allow write: if isAdmin();
      
      match /comments/{commentId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() && 
                                 (request.auth.uid == resource.data.userId || isAdmin());
      }
    }
    
    // ==================== قواعد المقالات ====================
    match /articles/{articleId} {
      allow read: if true;
      allow write: if isAdmin();
      
      match /comments/{commentId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() && 
                                 (request.auth.uid == resource.data.userId || isAdmin());
      }
    }
    
    // ==================== قواعد المفضلة ====================
    match /users/{userId}/favorites/{favoriteId} {
      allow read, write: if isOwner(userId);
    }
    
    // ==================== قواعد عامة للمجموعات الأخرى ====================
    match /recipes/{recipeId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /pharmacies/{pharmacyId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /stores/{storeId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /hospitals/{hospitalId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /doctors/{doctorId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // ==================== قواعد المنتدى ====================
    match /forum_posts/{postId} {
      allow read: if true;
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() && 
                               (request.auth.uid == resource.data.userId || isAdmin());
    }
    
    // ==================== قواعد الإعدادات ====================
    match /app_settings/{settingId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // ==================== قواعد الإحصائيات (للمشرفين فقط) ====================
    match /analytics/{analyticsId} {
      allow read, write: if isAdmin();
    }
    
    // ==================== قواعد التقارير ====================
    match /user_reports/{reportId} {
      allow create: if isAuthenticated();
      allow read, update, delete: if isAdmin();
    }
    
    // ==================== قاعدة عامة للمشرفين ====================
    match /{document=**} {
      allow read, write: if isAdmin();
    }
  }
}
```

### **قواعد Storage المبسطة:**

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // صور المحتوى - قراءة للجميع، كتابة للمشرفين
    match /medications/{allPaths=**} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /foodItems/{allPaths=**} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /restaurants/{allPaths=**} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /articles/{allPaths=**} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // صور المستخدمين
    match /users/{userId}/{allPaths=**} {
      allow read, write: if isAuthenticated() && 
                            (request.auth.uid == userId || isAdmin());
    }
    
    // الملفات المؤقتة
    match /temp/{userId}/{allPaths=**} {
      allow read, write: if isAuthenticated() && request.auth.uid == userId;
    }
  }
}
```

---

## 🔧 **الحل 2: تحديث MedicationProvider**

إذا كنت تريد الاحتفاظ بالقواعد المتقدمة، حدث MedicationProvider:

```dart
Future<void> fetchMedications() async {
  _isLoading = true;
  _errorMessage = null;
  notifyListeners();

  try {
    // التحقق من تسجيل الدخول أولاً
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _errorMessage = "يجب تسجيل الدخول أولاً";
      return;
    }

    // Get data from local database first
    final localMedications = await _dbHelper.getMedications();
    if (localMedications.isNotEmpty) {
      _medications = localMedications;
      _isLoading = false;
      notifyListeners();
    }

    // Get data from Firestore
    QuerySnapshot snapshot = await _firestoreService.getCollection(
      AppConstants.medicationsCollection,
      orderBy: 'name',
    );
    // ... باقي الكود
  } catch (e) {
    _errorMessage = "حدث خطأ أثناء جلب الأدوية: $e";
    debugPrint("Error fetching medications: $e");
  } finally {
    _isLoading = false;
    notifyListeners();
  }
}
```

---

## 🚀 **التوصية**

**استخدم الحل الأول (القواعد المبسطة)** لأنه:
- ✅ يحل المشكلة فوراً
- ✅ يسمح بقراءة البيانات العامة بدون تسجيل دخول
- ✅ يحافظ على الأمان للعمليات الحساسة
- ✅ مناسب للتطوير والاختبار

**طبق القواعد المبسطة الآن وسيعمل التطبيق بشكل مثالي!** 🎉
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import 'package:yassincil/providers/review_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/models/review.dart';
// import 'package:yassincil/screens/reviews/add_review_screen.dart';

class ReviewsScreen extends StatefulWidget {
  final String targetId;
  final String targetType;
  final String targetName;

  const ReviewsScreen({
    super.key,
    required this.targetId,
    required this.targetType,
    required this.targetName,
  });

  @override
  State<ReviewsScreen> createState() => _ReviewsScreenState();
}

class _ReviewsScreenState extends State<ReviewsScreen> {
  String _selectedFilter = 'الكل';
  final List<String> _filters = [
    'الكل',
    '5 نجوم',
    '4 نجوم',
    '3 نجوم',
    '2 نجوم',
    '1 نجمة',
    'مع صور',
    'موثق',
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final reviewProvider = Provider.of<ReviewProvider>(
        context,
        listen: false,
      );
      reviewProvider.fetchReviews(widget.targetId, widget.targetType);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'تقييمات ${widget.targetName}',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.amber.shade600,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.add_comment, color: Colors.white),
            onPressed: () => _showAddReviewScreen(),
            tooltip: 'إضافة تقييم',
          ),
        ],
      ),
      body: Consumer<ReviewProvider>(
        builder: (context, reviewProvider, child) {
          if (reviewProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          final reviews = _getFilteredReviews(reviewProvider.reviews);

          return Column(
            children: [
              if (reviewProvider.currentSummary != null)
                _buildSummarySection(reviewProvider.currentSummary!),
              _buildFilterSection(),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    await reviewProvider.fetchReviews(
                      widget.targetId,
                      widget.targetType,
                    );
                  },
                  child: reviews.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: reviews.length,
                          itemBuilder: (context, index) {
                            return _buildReviewCard(reviews[index]);
                          },
                        ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSummarySection(ReviewSummary summary) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.amber.shade400, Colors.amber.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    summary.averageRating.toStringAsFixed(1),
                    style: GoogleFonts.cairo(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  _buildStarRating(summary.averageRating),
                  const SizedBox(height: 4),
                  Text(
                    summary.totalReviewsText,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    summary.averageRatingText,
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${summary.recommendationPercentage.toStringAsFixed(0)}% يوصون',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                  if (summary.verifiedReviews > 0)
                    Text(
                      '${summary.verifiedReviews} تقييم موثق',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildRatingDistribution(summary),
        ],
      ),
    );
  }

  Widget _buildStarRating(double rating) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return Icon(
          index < rating.floor()
              ? Icons.star
              : index < rating
              ? Icons.star_half
              : Icons.star_border,
          color: Colors.white,
          size: 20,
        );
      }),
    );
  }

  Widget _buildRatingDistribution(ReviewSummary summary) {
    return Column(
      children: [
        for (int i = 5; i >= 1; i--)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              children: [
                Text(
                  '$i',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.star, color: Colors.white, size: 12),
                const SizedBox(width: 8),
                Expanded(
                  child: LinearProgressIndicator(
                    value: summary.getRatingPercentage(i) / 100,
                    backgroundColor: Colors.white.withValues(alpha: 0.3),
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${summary.ratingDistribution[i] ?? 0}',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildFilterSection() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _filters.length,
        itemBuilder: (context, index) {
          final filter = _filters[index];
          final isSelected = _selectedFilter == filter;

          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(
                filter,
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: isSelected ? Colors.white : Colors.amber.shade700,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedFilter = filter;
                });
              },
              backgroundColor: Colors.amber.shade50,
              selectedColor: Colors.amber.shade600,
              checkmarkColor: Colors.white,
            ),
          );
        },
      ),
    );
  }

  Widget _buildReviewCard(Review review) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUserId = authProvider.currentUser?.uid;
    final isUserReview = currentUserId == review.userId;
    final hasMarkedHelpful = review.helpfulUsers.contains(currentUserId);

    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.amber.shade100,
                  child: Text(
                    review.userName.isNotEmpty
                        ? review.userName[0].toUpperCase()
                        : 'م',
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      color: Colors.amber.shade700,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            review.userName,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey.shade800,
                            ),
                          ),
                          if (review.isVerified) ...[
                            const SizedBox(width: 4),
                            Icon(
                              Icons.verified,
                              size: 16,
                              color: Colors.green.shade600,
                            ),
                          ],
                        ],
                      ),
                      Row(
                        children: [
                          _buildStarRating(review.rating),
                          const SizedBox(width: 8),
                          Text(
                            review.timeAgo,
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: Colors.grey.shade500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                if (isUserReview)
                  PopupMenuButton(
                    icon: Icon(Icons.more_vert, color: Colors.grey.shade600),
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            const Icon(Icons.edit, size: 18),
                            const SizedBox(width: 8),
                            Text('تعديل', style: GoogleFonts.cairo()),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            const Icon(
                              Icons.delete,
                              size: 18,
                              color: Colors.red,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'حذف',
                              style: GoogleFonts.cairo(color: Colors.red),
                            ),
                          ],
                        ),
                      ),
                    ],
                    onSelected: (value) {
                      if (value == 'edit') {
                        _editReview(review);
                      } else if (value == 'delete') {
                        _deleteReview(review);
                      }
                    },
                  )
                else
                  IconButton(
                    onPressed: () => _reportReview(review),
                    icon: Icon(
                      Icons.flag,
                      size: 18,
                      color: Colors.grey.shade500,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            if (review.title.isNotEmpty) ...[
              Text(
                review.title,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade800,
                ),
              ),
              const SizedBox(height: 8),
            ],
            Text(
              review.comment,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade700,
              ),
            ),
            if (review.hasTags) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 6,
                runSpacing: 6,
                children: review.tags
                    .map(
                      (tag) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.amber.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.amber.shade200),
                        ),
                        child: Text(
                          tag,
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.amber.shade700,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                TextButton.icon(
                  onPressed: currentUserId != null && !isUserReview
                      ? () => _markAsHelpful(review)
                      : null,
                  icon: Icon(
                    hasMarkedHelpful ? Icons.thumb_up : Icons.thumb_up_outlined,
                    size: 16,
                    color: hasMarkedHelpful
                        ? Colors.amber.shade600
                        : Colors.grey.shade600,
                  ),
                  label: Text(
                    'مفيد (${review.helpfulCount})',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: hasMarkedHelpful
                          ? Colors.amber.shade600
                          : Colors.grey.shade600,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  review.ratingText,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: review.rating >= 4.0
                        ? Colors.green.shade600
                        : Colors.orange.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.rate_review_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد تقييمات بعد',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'كن أول من يقيم ${widget.targetName}',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _showAddReviewScreen(),
              icon: const Icon(Icons.add_comment),
              label: Text('إضافة تقييم', style: GoogleFonts.cairo()),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber.shade600,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Review> _getFilteredReviews(List<Review> reviews) {
    switch (_selectedFilter) {
      case '5 نجوم':
        return reviews.where((r) => r.rating.round() == 5).toList();
      case '4 نجوم':
        return reviews.where((r) => r.rating.round() == 4).toList();
      case '3 نجوم':
        return reviews.where((r) => r.rating.round() == 3).toList();
      case '2 نجوم':
        return reviews.where((r) => r.rating.round() == 2).toList();
      case '1 نجمة':
        return reviews.where((r) => r.rating.round() == 1).toList();
      case 'مع صور':
        return reviews.where((r) => r.hasImages).toList();
      case 'موثق':
        return reviews.where((r) => r.isVerified).toList();
      default:
        return reviews;
    }
  }

  void _showAddReviewScreen() {
    _showAddReviewDialog();
  }

  void _markAsHelpful(Review review) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final reviewProvider = Provider.of<ReviewProvider>(context, listen: false);

    if (authProvider.currentUser != null) {
      reviewProvider.markReviewAsHelpful(
        review.id!,
        authProvider.currentUser!.uid,
      );
    }
  }

  void _editReview(Review review) {
    _showAddReviewDialog(existingReview: review);
  }

  void _showAddReviewDialog({Review? existingReview}) {
    final titleController = TextEditingController(
      text: existingReview?.title ?? '',
    );
    final commentController = TextEditingController(
      text: existingReview?.comment ?? '',
    );
    double rating = existingReview?.rating ?? 5.0;
    List<String> selectedTags = List.from(existingReview?.tags ?? []);

    final availableTags = [
      'خالي من الجلوتين',
      'خدمة ممتازة',
      'أسعار مناسبة',
      'مكان نظيف',
      'طعام لذيذ',
      'موظفون مفيدون',
      'سرعة في الخدمة',
      'مناسب للعائلات',
    ];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            existingReview != null ? 'تعديل التقييم' : 'إضافة تقييم',
            style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'التقييم:',
                  style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),
                Row(
                  children: List.generate(5, (index) {
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          rating = (index + 1).toDouble();
                        });
                      },
                      child: Icon(
                        index < rating ? Icons.star : Icons.star_border,
                        color: Colors.amber,
                        size: 32,
                      ),
                    );
                  }),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: titleController,
                  decoration: InputDecoration(
                    labelText: 'عنوان التقييم',
                    labelStyle: GoogleFonts.cairo(),
                    border: const OutlineInputBorder(),
                  ),
                  style: GoogleFonts.cairo(),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: commentController,
                  decoration: InputDecoration(
                    labelText: 'تعليقك',
                    labelStyle: GoogleFonts.cairo(),
                    border: const OutlineInputBorder(),
                  ),
                  style: GoogleFonts.cairo(),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                Text(
                  'العلامات:',
                  style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: availableTags.map((tag) {
                    final isSelected = selectedTags.contains(tag);
                    return FilterChip(
                      label: Text(tag, style: GoogleFonts.cairo(fontSize: 12)),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            selectedTags.add(tag);
                          } else {
                            selectedTags.remove(tag);
                          }
                        });
                      },
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('إلغاء', style: GoogleFonts.cairo()),
            ),
            ElevatedButton(
              onPressed: () async {
                if (commentController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'يرجى كتابة تعليق',
                        style: GoogleFonts.cairo(),
                      ),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                final authProvider = Provider.of<AuthProvider>(
                  context,
                  listen: false,
                );
                final reviewProvider = Provider.of<ReviewProvider>(
                  context,
                  listen: false,
                );

                if (authProvider.currentUser == null) return;

                final navigator = Navigator.of(context);
                final scaffoldMessenger = ScaffoldMessenger.of(context);

                try {
                  final review = Review(
                    id: existingReview?.id,
                    userId: authProvider.currentUser!.uid,
                    userName: authProvider.currentUser!.displayName ?? 'مستخدم',
                    targetId: widget.targetId,
                    targetType: widget.targetType,
                    rating: rating,
                    title: titleController.text.trim(),
                    comment: commentController.text.trim(),
                    tags: selectedTags,
                    createdAt: existingReview?.createdAt ?? DateTime.now(),
                    updatedAt: DateTime.now(),
                  );

                  if (existingReview != null) {
                    await reviewProvider.updateReview(review);
                  } else {
                    await reviewProvider.addReview(review);
                  }

                  if (mounted) {
                    navigator.pop();
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text(
                          existingReview != null
                              ? 'تم تحديث التقييم'
                              : 'تم إضافة التقييم',
                          style: GoogleFonts.cairo(),
                        ),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text(
                          'فشل في حفظ التقييم: $e',
                          style: GoogleFonts.cairo(),
                        ),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber.shade600,
                foregroundColor: Colors.white,
              ),
              child: Text(
                existingReview != null ? 'تحديث' : 'إضافة',
                style: GoogleFonts.cairo(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _deleteReview(Review review) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف التقييم', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف هذا التقييم؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              final reviewProvider = Provider.of<ReviewProvider>(
                context,
                listen: false,
              );
              reviewProvider.deleteReview(review.id!);
            },
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _reportReview(Review review) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('الإبلاغ عن التقييم', style: GoogleFonts.cairo()),
        content: Text(
          'سيتم الإبلاغ عن هذا التقييم للمراجعة',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              final reviewProvider = Provider.of<ReviewProvider>(
                context,
                listen: false,
              );
              reviewProvider.reportReview(review.id!, 'محتوى غير مناسب');
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'تم الإبلاغ عن التقييم',
                    style: GoogleFonts.cairo(),
                  ),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            child: Text('إبلاغ', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}

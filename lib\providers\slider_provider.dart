import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:yassincil/models/slider_item.dart';
import 'package:yassincil/utils/app_constants.dart';

class SliderProvider extends ChangeNotifier {
  final CollectionReference _sliderCollection = FirebaseFirestore.instance
      .collection(AppConstants.sliderItemsCollection);

  List<SliderItem> _sliderItems = [];
  List<SliderItem> get sliderItems => _sliderItems;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  Future<void> fetchSliderItems() async {
    _isLoading = true;
    _errorMessage = null;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
    try {
      debugPrint('SliderProvider: بدء جلب عناصر السلايدر...');
      final querySnapshot = await _sliderCollection
          .orderBy('order', descending: false) // ترتيب حسب order
          .get();
      debugPrint('SliderProvider: تم جلب ${querySnapshot.docs.length} عنصر');
      _sliderItems = querySnapshot.docs
          .map((doc) => SliderItem.fromFirestore(doc))
          .toList();
      debugPrint('SliderProvider: تم تحويل ${_sliderItems.length} عنصر بنجاح');

      // إذا لم توجد عناصر، أضف عناصر افتراضية
      if (_sliderItems.isEmpty) {
        debugPrint('SliderProvider: لا توجد عناصر، إضافة عناصر افتراضية...');
        await _addDefaultSliderItems();
      } else {
        // تحقق من وجود عناصر بصور قديمة وحذفها
        bool hasOldItems = _sliderItems.any(
          (item) => item.imageUrl.contains('via.placeholder.com'),
        );
        if (hasOldItems) {
          debugPrint(
            'SliderProvider: حذف العناصر القديمة وإضافة عناصر جديدة...',
          );
          await _replaceOldSliderItems();
        }
      }
    } catch (e) {
      debugPrint('SliderProvider: خطأ في جلب عناصر السلايدر: $e');
      _errorMessage = e.toString();
    }
    _isLoading = false;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  // تحديث ترتيب العناصر (تبديل عنصرين)
  Future<void> swapSliderOrder(int oldIndex, int newIndex) async {
    if (oldIndex < 0 ||
        newIndex < 0 ||
        oldIndex >= _sliderItems.length ||
        newIndex >= _sliderItems.length) {
      return;
    }
    final oldItem = _sliderItems[oldIndex];
    final newItem = _sliderItems[newIndex];
    final oldOrder = oldItem.order;
    final newOrder = newItem.order;
    try {
      await _sliderCollection.doc(oldItem.id).update({'order': newOrder});
      await _sliderCollection.doc(newItem.id).update({'order': oldOrder});
      await fetchSliderItems();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  // تحديث ترتيب عنصر واحد (مثلاً عند نقله للأعلى أو الأسفل)
  Future<void> updateSliderOrder(String id, int newOrder) async {
    try {
      await _sliderCollection.doc(id).update({'order': newOrder});
      await fetchSliderItems();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  Future<void> addSliderItem(SliderItem item) async {
    try {
      await _sliderCollection.add(item.toMap());
      await fetchSliderItems();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  Future<void> updateSliderItem(SliderItem item) async {
    if (item.id == null) return;
    try {
      await _sliderCollection.doc(item.id).update(item.toMap());
      await fetchSliderItems();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  Future<void> deleteSliderItem(String id) async {
    try {
      await _sliderCollection.doc(id).delete();
      await fetchSliderItems();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  // تفعيل/تعطيل عنصر سلايدر
  Future<void> setSliderActive(String id, bool isActive) async {
    try {
      await _sliderCollection.doc(id).update({'isActive': isActive});
      await fetchSliderItems();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  // تحديث ترتيب جميع العناصر بعد السحب والإفلات
  Future<void> reorderAll(List<SliderItem> items) async {
    WriteBatch batch = FirebaseFirestore.instance.batch();
    for (int i = 0; i < items.length; i++) {
      final docRef = _sliderCollection.doc(items[i].id);
      batch.update(docRef, {'order': i});
    }
    await batch.commit();
    await fetchSliderItems();
  }

  /// إضافة عناصر افتراضية للسلايدر
  Future<void> _addDefaultSliderItems() async {
    try {
      final defaultItems = [
        SliderItem(
          imageUrl: 'https://picsum.photos/800/400?random=1',
          title: 'مرحباً بك في رفيق السيلياك',
          description: 'تطبيقك المفضل لمعرفة كل ما يخص السيلياك',
          targetScreen: 'home',
          createdAt: Timestamp.now(),
          order: 1,
          isActive: true,
        ),
        SliderItem(
          imageUrl: 'https://picsum.photos/800/400?random=2',
          title: 'اكتشف الأطعمة الآمنة',
          description: 'تصفح قائمة الأطعمة المناسبة لمرضى السيلياك',
          targetScreen: 'foods',
          createdAt: Timestamp.now(),
          order: 2,
          isActive: true,
        ),
        SliderItem(
          imageUrl: 'https://picsum.photos/800/400?random=3',
          title: 'وصفات لذيذة وآمنة',
          description: 'جرب وصفاتنا الخالية من الجلوتين',
          targetScreen: 'recipes',
          createdAt: Timestamp.now(),
          order: 3,
          isActive: true,
        ),
      ];

      for (final item in defaultItems) {
        await _sliderCollection.add(item.toMap());
      }

      debugPrint(
        'SliderProvider: تم إضافة ${defaultItems.length} عنصر افتراضي',
      );

      // إعادة جلب العناصر
      final querySnapshot = await _sliderCollection
          .orderBy('order', descending: false)
          .get();
      _sliderItems = querySnapshot.docs
          .map((doc) => SliderItem.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('SliderProvider: خطأ في إضافة العناصر الافتراضية: $e');
    }
  }

  /// استبدال العناصر القديمة بعناصر جديدة
  Future<void> _replaceOldSliderItems() async {
    try {
      // حذف جميع العناصر الموجودة
      final querySnapshot = await _sliderCollection.get();
      final batch = FirebaseFirestore.instance.batch();
      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }
      await batch.commit();
      debugPrint(
        'SliderProvider: تم حذف ${querySnapshot.docs.length} عنصر قديم',
      );

      // إضافة عناصر جديدة
      await _addDefaultSliderItems();
    } catch (e) {
      debugPrint('SliderProvider: خطأ في استبدال العناصر القديمة: $e');
    }
  }

  /// حذف جميع العناصر وإعادة إنشائها (للاختبار)
  Future<void> resetSliderItems() async {
    try {
      // حذف جميع العناصر الموجودة
      final querySnapshot = await _sliderCollection.get();
      final batch = FirebaseFirestore.instance.batch();
      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }
      await batch.commit();
      debugPrint('SliderProvider: تم حذف جميع العناصر');

      // إضافة عناصر جديدة
      await _addDefaultSliderItems();

      // إعادة جلب العناصر
      await fetchSliderItems();
    } catch (e) {
      debugPrint('SliderProvider: خطأ في إعادة تعيين العناصر: $e');
    }
  }
}

import 'package:flutter/material.dart';

class EmergencyContact {
  final String name;
  final String number;
  final IconData icon;
  final Color color;
  final String? description;
  final bool isAvailable24h;
  final String? website;
  final String? address;

  EmergencyContact({
    required this.name,
    required this.number,
    required this.icon,
    required this.color,
    this.description,
    this.isAvailable24h = true,
    this.website,
    this.address,
  });
}

class Hospital {
  final String id;
  final String name;
  final String address;
  final String phone;
  final double latitude;
  final double longitude;
  final double distance; // in kilometers
  final bool hasEmergency;
  final bool hasGastroenterology;
  final bool acceptsInsurance;
  final double rating;
  final String? website;
  final List<String> services;
  final String openingHours;
  final bool isOpen;

  Hospital({
    required this.id,
    required this.name,
    required this.address,
    required this.phone,
    required this.latitude,
    required this.longitude,
    required this.distance,
    this.hasEmergency = true,
    this.hasGastroenterology = false,
    this.acceptsInsurance = true,
    this.rating = 4.0,
    this.website,
    this.services = const [],
    this.openingHours = '24 ساعة',
    this.isOpen = true,
  });
}

class FirstAidGuide {
  final String id;
  final String title;
  final String description;
  final List<String> symptoms;
  final List<FirstAidStep> steps;
  final String severity; // mild, moderate, severe, critical
  final IconData icon;
  final Color color;
  final bool isSpecificToCeliac;

  FirstAidGuide({
    required this.id,
    required this.title,
    required this.description,
    required this.symptoms,
    required this.steps,
    required this.severity,
    required this.icon,
    required this.color,
    this.isSpecificToCeliac = false,
  });
}

class FirstAidStep {
  final int stepNumber;
  final String title;
  final String description;
  final String? imageUrl;
  final bool isWarning;
  final String? warningText;

  FirstAidStep({
    required this.stepNumber,
    required this.title,
    required this.description,
    this.imageUrl,
    this.isWarning = false,
    this.warningText,
  });
}

class EmergencyProfile {
  final String userId;
  final String fullName;
  final String bloodType;
  final List<String> allergies;
  final List<String> medications;
  final List<String> medicalConditions;
  final String emergencyContactName;
  final String emergencyContactPhone;
  final String doctorName;
  final String doctorPhone;
  final String insuranceInfo;
  final DateTime lastUpdated;
  final bool hasCeliacDisease;
  final String celiacSeverity; // mild, moderate, severe
  final List<String> triggerFoods;

  EmergencyProfile({
    required this.userId,
    required this.fullName,
    required this.bloodType,
    required this.allergies,
    required this.medications,
    required this.medicalConditions,
    required this.emergencyContactName,
    required this.emergencyContactPhone,
    this.doctorName = '',
    this.doctorPhone = '',
    this.insuranceInfo = '',
    required this.lastUpdated,
    this.hasCeliacDisease = true,
    this.celiacSeverity = 'moderate',
    this.triggerFoods = const [],
  });

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'fullName': fullName,
      'bloodType': bloodType,
      'allergies': allergies,
      'medications': medications,
      'medicalConditions': medicalConditions,
      'emergencyContactName': emergencyContactName,
      'emergencyContactPhone': emergencyContactPhone,
      'doctorName': doctorName,
      'doctorPhone': doctorPhone,
      'insuranceInfo': insuranceInfo,
      'lastUpdated': lastUpdated.toIso8601String(),
      'hasCeliacDisease': hasCeliacDisease,
      'celiacSeverity': celiacSeverity,
      'triggerFoods': triggerFoods,
    };
  }

  factory EmergencyProfile.fromMap(Map<String, dynamic> map) {
    return EmergencyProfile(
      userId: map['userId'] ?? '',
      fullName: map['fullName'] ?? '',
      bloodType: map['bloodType'] ?? '',
      allergies: List<String>.from(map['allergies'] ?? []),
      medications: List<String>.from(map['medications'] ?? []),
      medicalConditions: List<String>.from(map['medicalConditions'] ?? []),
      emergencyContactName: map['emergencyContactName'] ?? '',
      emergencyContactPhone: map['emergencyContactPhone'] ?? '',
      doctorName: map['doctorName'] ?? '',
      doctorPhone: map['doctorPhone'] ?? '',
      insuranceInfo: map['insuranceInfo'] ?? '',
      lastUpdated: DateTime.parse(map['lastUpdated'] ?? DateTime.now().toIso8601String()),
      hasCeliacDisease: map['hasCeliacDisease'] ?? true,
      celiacSeverity: map['celiacSeverity'] ?? 'moderate',
      triggerFoods: List<String>.from(map['triggerFoods'] ?? []),
    );
  }
}

class EmergencyAlert {
  final String id;
  final String userId;
  final String type; // gluten_exposure, severe_reaction, general_emergency
  final String description;
  final double? latitude;
  final double? longitude;
  final DateTime timestamp;
  final String status; // active, resolved, cancelled
  final List<String> notifiedContacts;

  EmergencyAlert({
    required this.id,
    required this.userId,
    required this.type,
    required this.description,
    this.latitude,
    this.longitude,
    required this.timestamp,
    this.status = 'active',
    this.notifiedContacts = const [],
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'type': type,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'timestamp': timestamp.toIso8601String(),
      'status': status,
      'notifiedContacts': notifiedContacts,
    };
  }

  factory EmergencyAlert.fromMap(Map<String, dynamic> map) {
    return EmergencyAlert(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      type: map['type'] ?? '',
      description: map['description'] ?? '',
      latitude: map['latitude']?.toDouble(),
      longitude: map['longitude']?.toDouble(),
      timestamp: DateTime.parse(map['timestamp'] ?? DateTime.now().toIso8601String()),
      status: map['status'] ?? 'active',
      notifiedContacts: List<String>.from(map['notifiedContacts'] ?? []),
    );
  }
}

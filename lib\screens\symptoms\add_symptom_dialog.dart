import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/providers/symptoms_provider.dart';
import 'package:yassincil/models/symptom_entry.dart';
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/widgets/bristol_stool_visual_selector.dart';
import 'package:yassincil/widgets/custom_chip_selector.dart';
import 'package:yassincil/widgets/custom_slider.dart';

class AddSymptomDialog extends StatefulWidget {
  final SymptomEntry? existingEntry;

  const AddSymptomDialog({super.key, this.existingEntry});

  @override
  State<AddSymptomDialog> createState() => _AddSymptomDialogState();
}

class _AddSymptomDialogState extends State<AddSymptomDialog> {
  final _formKey = GlobalKey<FormState>();
  final _symptomNameController = TextEditingController();
  final _notesController = TextEditingController();
  final _mealNameController = TextEditingController();
  final _restaurantNameController = TextEditingController();
  final _ingredientsController = TextEditingController();
  final _glutenSourceController = TextEditingController();
  final _glutenNotesController = TextEditingController();

  // Basic Info
  String _symptomName = '';
  int _severity = 3;
  DateTime _selectedDateTime = DateTime.now();
  Duration? _duration;
  List<String> _triggers = [];
  List<String> _medications = [];

  // Celiac Specific
  int? _bristolStoolType;
  int _energyLevel = 3;
  Mood? _mood;
  bool _isGlutenExposure = false;
  GlutenExposureDetails? _glutenExposureDetails;
  bool _isMealRelated = false;
  MealDetails? _mealDetails;

  bool _isLoading = false;

  final List<String> _commonSymptoms = [
    // Digestive
    'ألم في البطن', 'انتفاخ', 'إسهال', 'إمساك', 'غثيان', 'حرقة معدة',
    // Neurological & General
    'صداع', 'ضبابية الدماغ', 'تعب شديد', 'دوار', 'خدر وتنميل',
    // Musculoskeletal & Skin
    'آلام المفاصل', 'آلام العضلات', 'طفح جلدي (DH)', 'تقرحات الفم',
    // Mood
    'قلق', 'اكتئاب'
  ];
  final List<String> _commonTriggers = ['الغلوتين', 'الألبان', 'السكر', 'التوتر', 'قلة النوم'];
  final List<String> _commonMedications = ['مسكن ألم', 'مضاد للحموضة', 'إنزيمات هاضمة'];
  final List<String> _commonGlutenSources = [
    'أدوات مطبخ مشتركة',
    'مطعم غير مخصص',
    'منتج ملوث',
    'تناول طعام منزلي غير آمن',
    'خطأ في قراءة المكونات'
  ];

  @override
  void initState() {
    super.initState();
    if (widget.existingEntry != null) {
      _loadExistingEntry();
    }
  }

  void _loadExistingEntry() {
    final entry = widget.existingEntry!;
    _symptomName = entry.symptomName;
    _symptomNameController.text = entry.symptomName;
    _severity = entry.severity;
    _selectedDateTime = entry.timestamp;
    _duration = entry.duration;
    _notesController.text = entry.notes ?? '';
    _triggers = entry.triggers;
    _medications = entry.medications;
    _bristolStoolType = entry.bristolStoolType;
    _energyLevel = entry.energyLevel ?? 3;
    _mood = entry.mood;
    if (entry.glutenExposure != null) {
      _isGlutenExposure = true;
      _glutenExposureDetails = entry.glutenExposure;
      _glutenSourceController.text = entry.glutenExposure!.suspectedSource ?? '';
      _glutenNotesController.text = entry.glutenExposure!.notes ?? '';
    }
    if (entry.mealDetails != null) {
      _isMealRelated = true;
      _mealDetails = entry.mealDetails;
      _mealNameController.text = entry.mealDetails!.mealName;
      _restaurantNameController.text = entry.mealDetails!.restaurantName ?? '';
      _ingredientsController.text = entry.mealDetails!.ingredients.join(', ');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              const SizedBox(height: 24),
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSectionTitle('العرض الأساسي', Icons.medical_services_outlined),
                      _buildSymptomField(),
                      const SizedBox(height: 16),
                      _buildSeveritySelector(),
                      const SizedBox(height: 16),
                      _buildDateTimeSelector(),
                      const SizedBox(height: 16),
                      _buildDurationSelector(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('مؤشرات السيلياك', Icons.local_florist_outlined),
                      BristolStoolVisualSelector(
                        selectedType: _bristolStoolType,
                        onSelected: (type) {
                          setState(() {
                            _bristolStoolType = type;
                          });
                        },
                      ),
                      const SizedBox(height: 16),
                      _buildEnergySlider(),
                      const SizedBox(height: 16),
                      _buildMoodSelector(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('الأسباب المحتملة', Icons.help_outline),
                      _buildTitledChipSelector('المحفزات', _triggers, _commonTriggers, (selected) => setState(() => _triggers = selected)),
                      const SizedBox(height: 16),
                      _buildGlutenExposureSection(),
                      _buildMealDetailsSection(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('العلاجات المستخدمة', Icons.healing_outlined),
                      _buildTitledChipSelector('الأدوية', _medications, _commonMedications, (selected) => setState(() => _medications = selected)),
                      const SizedBox(height: 24),
                      _buildSectionTitle('ملاحظات إضافية', Icons.notes_outlined),
                      _buildNotesField(),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget.existingEntry != null ? 'تعديل العرض' : 'إضافة عرض جديد',
          style: GoogleFonts.cairo(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        IconButton(onPressed: () => Navigator.pop(context), icon: const Icon(Icons.close)),
      ],
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(children: [
        Icon(icon, color: AppColors.primary, size: 20),
        const SizedBox(width: 8),
        Text(title, style: GoogleFonts.cairo(fontSize: 16, fontWeight: FontWeight.w600)),
      ]),
    );
  }

  Widget _buildSymptomField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _symptomNameController,
          decoration: const InputDecoration(labelText: 'اسم العرض (مثال: انتفاخ)'),
          validator: (value) => (value == null || value.isEmpty) ? 'هذا الحقل مطلوب' : null,
          onSaved: (value) => _symptomName = value!,
        ),
        const SizedBox(height: 8),
        CustomChipSelector(
          options: _commonSymptoms,
          selectedOptions: _symptomName.isEmpty ? [] : [_symptomName],
          onSelected: (selected) {
            setState(() {
              _symptomName = selected.isNotEmpty ? selected.first : '';
              _symptomNameController.text = _symptomName;
            });
          },
          isMultiSelect: false,
        ),
      ],
    );
  }

  Widget _buildSeveritySelector() {
    return CustomSlider(
      label: 'الشدة',
      value: _severity,
      min: 1,
      max: 5,
      divisions: 4,
      onChanged: (value) => setState(() => _severity = value),
      activeColor: _getSeverityColor(_severity),
    );
  }

  Widget _buildDateTimeSelector() {
    return ListTile(
      leading: const Icon(Icons.calendar_today_outlined),
      title: Text('الوقت: ${DateFormat('d MMM y, hh:mm a', 'ar').format(_selectedDateTime)}'),
      onTap: _selectDateTime,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8), side: BorderSide(color: Colors.grey.shade300)),
    );
  }

  Widget _buildDurationSelector() {
    return ListTile(
      leading: const Icon(Icons.timer_outlined),
      title: Text(_duration == null ? 'تحديد المدة (اختياري)' : 'المدة: ${_duration!.inHours} ساعة و ${_duration!.inMinutes.remainder(60)} دقيقة'),
      onTap: _selectDuration,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8), side: BorderSide(color: Colors.grey.shade300)),
    );
  }

  Widget _buildEnergySlider() {
    return CustomSlider(
      label: 'مستوى الطاقة',
      value: _energyLevel,
      min: 1,
      max: 5,
      divisions: 4,
      onChanged: (value) => setState(() => _energyLevel = value),
    );
  }

  Widget _buildMoodSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('الحالة المزاجية (اختياري)'),
        const SizedBox(height: 8),
        CustomChipSelector(
          options: Mood.values.map((m) => m.name).toList(), // Assuming names are descriptive
          selectedOptions: _mood == null ? [] : [_mood!.name],
          onSelected: (selected) {
            setState(() => _mood = selected.isNotEmpty ? Mood.values.byName(selected.first) : null);
          },
          isMultiSelect: false,
        ),
      ],
    );
  }

  Widget _buildTitledChipSelector(String title, List<String> selected, List<String> options, Function(List<String>) onSelected, {bool isMultiSelect = true}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: GoogleFonts.cairo(fontWeight: FontWeight.w600)),
        const SizedBox(height: 8),
        CustomChipSelector(
          options: options,
          selectedOptions: selected,
          onSelected: onSelected,
          isMultiSelect: isMultiSelect,
        ),
      ],
    );
  }

  Widget _buildGlutenExposureSection() {
    return ExpansionTile(
      title: const Text('تسجيل التعرض للجلوتين'),
      initiallyExpanded: _isGlutenExposure,
      onExpansionChanged: (expanded) => setState(() => _isGlutenExposure = expanded),
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              DropdownButtonFormField<GlutenExposureType>(
                value: _glutenExposureDetails?.type,
                hint: const Text('نوع التعرض'),
                items: GlutenExposureType.values.map((type) => DropdownMenuItem(value: type, child: Text(type.name))).toList(),
                onChanged: (value) => setState(() => _glutenExposureDetails = GlutenExposureDetails(type: value!)),
              ),
              const SizedBox(height: 16),
              _buildTitledChipSelector(
                'المصدر المشتبه به',
                _glutenExposureDetails?.suspectedSource != null ? [_glutenExposureDetails!.suspectedSource!] : [],
                _commonGlutenSources,
                (selected) {
                  setState(() {
                    final source = selected.isNotEmpty ? selected.first : null;
                    _glutenExposureDetails = (_glutenExposureDetails ?? GlutenExposureDetails(type: GlutenExposureType.suspected)).copyWith(suspectedSource: source);
                    _glutenSourceController.text = source ?? '';
                  });
                },
                isMultiSelect: false,
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _glutenSourceController,
                decoration: const InputDecoration(labelText: 'أو أدخل مصدرًا آخر'),
                onChanged: (value) {
                  setState(() {
                    _glutenExposureDetails = (_glutenExposureDetails ?? GlutenExposureDetails(type: GlutenExposureType.suspected)).copyWith(suspectedSource: value);
                  });
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _glutenNotesController,
                decoration: const InputDecoration(labelText: 'ملاحظات'),
                onChanged: (value) => _glutenExposureDetails = _glutenExposureDetails?.copyWith(notes: value),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget _buildMealDetailsSection() {
    return ExpansionTile(
      title: const Text('تسجيل تفاصيل الوجبة'),
      initiallyExpanded: _isMealRelated,
      onExpansionChanged: (expanded) => setState(() => _isMealRelated = expanded),
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              TextFormField(
                controller: _mealNameController,
                decoration: const InputDecoration(labelText: 'اسم الوجبة'),
                onChanged: (value) => _mealDetails = (_mealDetails ?? MealDetails(mealName: value, source: MealSource.home)).copyWith(mealName: value),
              ),
              DropdownButtonFormField<MealSource>(
                value: _mealDetails?.source,
                hint: const Text('مصدر الوجبة'),
                items: MealSource.values.map((source) => DropdownMenuItem(value: source, child: Text(source.name))).toList(),
                onChanged: (value) => setState(() => _mealDetails = (_mealDetails ?? MealDetails(mealName: '', source: value!)).copyWith(source: value)),
              ),
              if (_mealDetails?.source == MealSource.restaurant)
                TextFormField(
                  controller: _restaurantNameController,
                  decoration: const InputDecoration(labelText: 'اسم المطعم'),
                  onChanged: (value) => _mealDetails = _mealDetails?.copyWith(restaurantName: value),
                ),
              TextFormField(
                controller: _ingredientsController,
                decoration: const InputDecoration(labelText: 'المكونات'),
                onChanged: (value) => _mealDetails = _mealDetails?.copyWith(ingredients: value.split(',').map((e) => e.trim()).toList()),
              ),
              CheckboxListTile(
                title: const Text('معتمد كخالي من الجلوتين'),
                value: _mealDetails?.isCertifiedGlutenFree ?? false,
                onChanged: (value) => setState(() => _mealDetails = _mealDetails?.copyWith(isCertifiedGlutenFree: value)),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget _buildNotesField() {
    return TextFormField(
      controller: _notesController,
      decoration: const InputDecoration(labelText: 'ملاحظات إضافية', alignLabelWithHint: true),
      maxLines: 3,
    );
  }

  Widget _buildSaveButton() {
    return ElevatedButton(
      onPressed: _isLoading ? null : _saveSymptom,
      style: ElevatedButton.styleFrom(minimumSize: const Size(double.infinity, 50)),
      child: _isLoading ? const CircularProgressIndicator() : const Text('حفظ'),
    );
  }

  Future<void> _selectDateTime() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDateTime,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
    );
    if (date == null) return;

    final time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(_selectedDateTime),
    );
    if (time == null) return;

    setState(() {
      _selectedDateTime = DateTime(date.year, date.month, date.day, time.hour, time.minute);
    });
  }

  Future<void> _selectDuration() async {
    final duration = await showDialog<Duration>(
      context: context,
      builder: (context) => DurationPickerDialog(initialDuration: _duration ?? const Duration(hours: 1)),
    );
    if (duration != null) {
      setState(() => _duration = duration);
    }
  }

  Future<void> _saveSymptom() async {
    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState!.save();

    setState(() => _isLoading = true);

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final symptomsProvider = Provider.of<SymptomsProvider>(context, listen: false);
    final userId = authProvider.currentUser?.uid;

    if (userId == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('يجب تسجيل الدخول لحفظ الأعراض')),
        );
        setState(() => _isLoading = false);
      }
      return;
    }

    final newEntry = SymptomEntry(
      id: widget.existingEntry?.id,
      userId: userId,
      symptomName: _symptomName,
      severity: _severity,
      timestamp: _selectedDateTime,
      notes: _notesController.text.trim(),
      triggers: _triggers,
      duration: _duration,
      medications: _medications,
      bristolStoolType: _bristolStoolType,
      energyLevel: _energyLevel,
      mood: _mood,
      glutenExposure: _isGlutenExposure ? _glutenExposureDetails : null,
      mealDetails: _isMealRelated ? _mealDetails : null,
    );

    try {
      if (widget.existingEntry != null) {
        await symptomsProvider.updateSymptomEntry(newEntry);
      } else {
        await symptomsProvider.addSymptomEntry(newEntry);
      }
      if (mounted) {
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل حفظ العرض: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Color _getSeverityColor(int severity) {
    switch (severity) {
      case 1: return Colors.green;
      case 2: return Colors.lightGreen;
      case 3: return Colors.orange;
      case 4: return Colors.deepOrange;
      case 5: return Colors.red;
      default: return Colors.grey;
    }
  }
}

class DurationPickerDialog extends StatefulWidget {
  final Duration initialDuration;
  const DurationPickerDialog({super.key, required this.initialDuration});

  @override
  State<DurationPickerDialog> createState() => _DurationPickerDialogState();
}

class _DurationPickerDialogState extends State<DurationPickerDialog> {
  late int _hours;
  late int _minutes;

  @override
  void initState() {
    super.initState();
    _hours = widget.initialDuration.inHours;
    _minutes = widget.initialDuration.inMinutes.remainder(60);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تحديد المدة'),
      content: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildTimeSpinner(24, _hours, (val) => setState(() => _hours = val)),
          const Text('ساعة'),
          _buildTimeSpinner(60, _minutes, (val) => setState(() => _minutes = val)),
          const Text('دقيقة'),
        ],
      ),
      actions: [
        TextButton(onPressed: () => Navigator.pop(context), child: const Text('إلغاء')),
        TextButton(
          onPressed: () => Navigator.pop(context, Duration(hours: _hours, minutes: _minutes)),
          child: const Text('تأكيد'),
        ),
      ],
    );
  }

  Widget _buildTimeSpinner(int max, int value, ValueChanged<int> onChanged) {
    return Row(
      children: [
        IconButton(icon: const Icon(Icons.remove), onPressed: () => onChanged(value > 0 ? value - 1 : 0)),
        Text(value.toString(), style: const TextStyle(fontSize: 20)),
        IconButton(icon: const Icon(Icons.add), onPressed: () => onChanged(value < max - 1 ? value + 1 : max - 1)),
      ],
    );
  }
}

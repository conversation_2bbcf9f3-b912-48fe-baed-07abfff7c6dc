import 'package:flutter/material.dart';
import 'package:yassincil/services/notification_service.dart';

class NotificationProvider extends ChangeNotifier {
  bool _notificationsEnabled = true;
  bool _medicationReminders = true;
  bool _newFoodsNotifications = true;
  bool _forumNotifications = true;
  bool _articlesNotifications = true;
  bool _isLoading = false;

  // Getters
  bool get notificationsEnabled => _notificationsEnabled;
  bool get medicationReminders => _medicationReminders;
  bool get newFoodsNotifications => _newFoodsNotifications;
  bool get forumNotifications => _forumNotifications;
  bool get articlesNotifications => _articlesNotifications;
  bool get isLoading => _isLoading;

  /// تهيئة إعدادات الإشعارات
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      _notificationsEnabled = await NotificationService.areNotificationsEnabled();
      _medicationReminders = await NotificationService.areMedicationRemindersEnabled();
      _newFoodsNotifications = await NotificationService.areNewFoodsNotificationsEnabled();
      _forumNotifications = await NotificationService.areForumNotificationsEnabled();
      _articlesNotifications = await NotificationService.areArticlesNotificationsEnabled();
    } catch (e) {
      debugPrint('خطأ في تهيئة إعدادات الإشعارات: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تفعيل/إلغاء تفعيل الإشعارات العامة
  Future<void> setNotificationsEnabled(bool enabled) async {
    _isLoading = true;
    notifyListeners();

    try {
      await NotificationService.setNotificationsEnabled(enabled);
      _notificationsEnabled = enabled;
    } catch (e) {
      debugPrint('خطأ في تحديث إعدادات الإشعارات: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تفعيل/إلغاء تفعيل تذكيرات الأدوية
  Future<void> setMedicationReminders(bool enabled) async {
    try {
      await NotificationService.setMedicationReminders(enabled);
      _medicationReminders = enabled;
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث تذكيرات الأدوية: $e');
    }
  }

  /// تفعيل/إلغاء تفعيل إشعارات الأطعمة الجديدة
  Future<void> setNewFoodsNotifications(bool enabled) async {
    try {
      await NotificationService.setNewFoodsNotifications(enabled);
      _newFoodsNotifications = enabled;
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث إشعارات الأطعمة: $e');
    }
  }

  /// تفعيل/إلغاء تفعيل إشعارات المنتدى
  Future<void> setForumNotifications(bool enabled) async {
    try {
      await NotificationService.setForumNotifications(enabled);
      _forumNotifications = enabled;
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث إشعارات المنتدى: $e');
    }
  }

  /// تفعيل/إلغاء تفعيل إشعارات المقالات
  Future<void> setArticlesNotifications(bool enabled) async {
    try {
      await NotificationService.setArticlesNotifications(enabled);
      _articlesNotifications = enabled;
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث إشعارات المقالات: $e');
    }
  }

  /// حفظ رمز FCM للمستخدم
  Future<void> saveUserToken(String userId) async {
    try {
      await NotificationService.saveUserToken(userId);
    } catch (e) {
      debugPrint('خطأ في حفظ رمز المستخدم: $e');
    }
  }

  /// إزالة رمز FCM عند تسجيل الخروج
  Future<void> removeUserToken(String userId) async {
    try {
      await NotificationService.removeUserToken(userId);
    } catch (e) {
      debugPrint('خطأ في إزالة رمز المستخدم: $e');
    }
  }

  /// إرسال إشعار لمستخدم محدد (للمشرفين فقط)
  Future<void> sendNotificationToUser({
    required String userId,
    required String title,
    required String body,
    Map<String, String>? data,
  }) async {
    try {
      await NotificationService.sendNotificationToUser(
        userId: userId,
        title: title,
        body: body,
        data: data,
      );
    } catch (e) {
      debugPrint('خطأ في إرسال الإشعار للمستخدم: $e');
      rethrow;
    }
  }

  /// إرسال إشعار لجميع المستخدمين (للمشرفين فقط)
  Future<void> sendNotificationToAll({
    required String title,
    required String body,
    Map<String, String>? data,
  }) async {
    try {
      await NotificationService.sendNotificationToAll(
        title: title,
        body: body,
        data: data,
      );
    } catch (e) {
      debugPrint('خطأ في إرسال الإشعار العام: $e');
      rethrow;
    }
  }

  /// جدولة تذكير دواء
  Future<void> scheduleMedicationReminder({
    required String medicationName,
    required DateTime reminderTime,
    String? notes,
  }) async {
    try {
      // يمكن استخدام مكتبة flutter_local_notifications لجدولة التذكيرات المحلية
      debugPrint('جدولة تذكير دواء: $medicationName في $reminderTime');
    } catch (e) {
      debugPrint('خطأ في جدولة تذكير الدواء: $e');
    }
  }

  /// إلغاء تذكير دواء
  Future<void> cancelMedicationReminder(String medicationId) async {
    try {
      debugPrint('إلغاء تذكير دواء: $medicationId');
    } catch (e) {
      debugPrint('خطأ في إلغاء تذكير الدواء: $e');
    }
  }

  /// إرسال إشعار عند إضافة طعام جديد
  Future<void> notifyNewFood(String foodName) async {
    if (!_newFoodsNotifications || !_notificationsEnabled) return;

    try {
      await sendNotificationToAll(
        title: 'طعام جديد متاح!',
        body: 'تم إضافة $foodName إلى قائمة الأطعمة الآمنة',
        data: {'type': 'new_food', 'food_name': foodName},
      );
    } catch (e) {
      debugPrint('خطأ في إرسال إشعار الطعام الجديد: $e');
    }
  }

  /// إرسال إشعار عند إضافة مقال جديد
  Future<void> notifyNewArticle(String articleTitle, String articleId) async {
    if (!_articlesNotifications || !_notificationsEnabled) return;

    try {
      await sendNotificationToAll(
        title: 'مقال جديد!',
        body: articleTitle,
        data: {'type': 'new_article', 'article_id': articleId},
      );
    } catch (e) {
      debugPrint('خطأ في إرسال إشعار المقال الجديد: $e');
    }
  }

  /// إرسال إشعار عند إضافة منشور جديد في المنتدى
  Future<void> notifyNewForumPost(String postTitle, String postId) async {
    if (!_forumNotifications || !_notificationsEnabled) return;

    try {
      await sendNotificationToAll(
        title: 'منشور جديد في المنتدى',
        body: postTitle,
        data: {'type': 'forum_post', 'post_id': postId},
      );
    } catch (e) {
      debugPrint('خطأ في إرسال إشعار المنشور الجديد: $e');
    }
  }
}

import 'package:flutter/material.dart';
import 'package:yassincil/widgets/medications_app_bar.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:yassincil/providers/medication_provider.dart';
import 'package:yassincil/models/medication.dart';
import 'package:yassincil/screens/medications/medication_detail_screen.dart';
import 'package:yassincil/utils/app_colors.dart';

class BarcodeScannerScreen extends StatefulWidget {
  final bool returnBarcodeOnly;

  const BarcodeScannerScreen({super.key, this.returnBarcodeOnly = false});

  @override
  State<BarcodeScannerScreen> createState() => _BarcodeScannerScreenState();
}

class _BarcodeScannerScreenState extends State<BarcodeScannerScreen> {
  bool _isProcessing = false;
  final MobileScannerController _scannerController = MobileScannerController(
    detectionSpeed: DetectionSpeed.normal,
    facing: CameraFacing.back,
  );

  @override
  void dispose() {
    _scannerController.dispose();
    super.dispose();
  }

  Future<void> _onBarcodeDetected(BarcodeCapture capture) async {
    if (_isProcessing) return;

    final String? code = capture.barcodes.first.rawValue;
    if (code == null) {
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    // إذا كان المطلوب إرجاع الباركود فقط
    if (widget.returnBarcodeOnly) {
      Navigator.of(context).pop(code);
      return;
    }

    // Show loading indicator
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'جاري البحث عن الباركود: $code',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.blue,
      ),
    );

    try {
      final medicationProvider = Provider.of<MedicationProvider>(
        context,
        listen: false,
      );
      // This function needs to be created in the provider
      final Medication? medication = await medicationProvider
          .searchMedicationByBarcode(code);

      if (mounted) {
        if (medication != null) {
          // Found medication, navigate to details
          Navigator.of(context).pop(); // Pop the scanner screen
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) =>
                  MedicationDetailScreen(medication: medication),
            ),
          );
        } else {
          // Not found
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'لم يتم العثور على دواء بهذا الباركود',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // Delay to prevent immediate re-scanning
      await Future.delayed(const Duration(seconds: 3));
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MedicationsAppBar(
        title: widget.returnBarcodeOnly
            ? 'مسح الباركود للبحث'
            : 'مسح باركود الدواء',
      ),
      body: Stack(
        children: [
          MobileScanner(
            controller: _scannerController,
            onDetect: _onBarcodeDetected,
          ),
          // Overlay UI
          Center(
            child: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              height: 250,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.red, width: 4),
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          Positioned(
            top: 20,
            left: 0,
            right: 0,
            child: Column(
              children: [
                Text(
                  'وجّه الكاميرا نحو الباركود',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.cairo(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      const Shadow(blurRadius: 8.0, color: Colors.black54),
                    ],
                  ),
                ),
                if (widget.returnBarcodeOnly)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      'سيتم إضافة الباركود إلى شريط البحث',
                      textAlign: TextAlign.center,
                      style: GoogleFonts.cairo(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 14,
                        shadows: [
                          const Shadow(blurRadius: 8.0, color: Colors.black54),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
          if (_isProcessing)
            Container(
              color: Colors.black.withValues(alpha: 0.5),
              child: const Center(child: CircularProgressIndicator()),
            ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:yassincil/services/firestore_service.dart';

/// A generic base provider for handling common Firestore CRUD operations.
/// It manages loading state, error messages, and a list of items of type [T].
abstract class BaseProvider<T> extends ChangeNotifier {
  final FirestoreService firestoreService;
  final String collectionPath;

  List<T> _items = [];
  bool _isLoading = false;
  String? _errorMessage;

  List<T> get items => _items;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  BaseProvider({required this.firestoreService, required this.collectionPath}) {
    fetchItems();
  }

  /// Creates an instance of [T] from a Firestore document.
  T fromFirestore(DocumentSnapshot doc);

  /// Converts an instance of [T] to a map for Firestore.
  Map<String, dynamic> toMap(T item);

  /// Fetches all items from the specified [collectionPath].
  /// Override this method to provide custom sorting or filtering.
  Future<void> fetchItems({String? orderBy, bool descending = false}) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final snapshot = await firestoreService.getCollection(
        collectionPath,
        orderBy: orderBy,
        descending: descending,
      );
      _items = snapshot.docs.map((doc) => fromFirestore(doc)).toList();
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء جلب البيانات: $e";
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// A generic method to wrap common state management for CRUD operations.
  Future<void> performCrudOperation(
    Future<void> Function() operation, {
    String? successMessage,
  }) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      await operation();
      // Refetch items to reflect the change.
      // The specific fetch logic (with orderBy) should be called from the subclass.
    } catch (e) {
      _errorMessage = "حدث خطأ: $e";
      rethrow; // Rethrow to allow the UI to handle it if needed.
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Local-only update for optimistic UI changes
  void updateItemsLocally(List<T> newItems) {
    _items = newItems;
    notifyListeners();
  }
}

import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../models/symptom_entry.dart';
import '../models/product.dart';
import '../services/firestore_service.dart';
import '../utils/app_constants.dart';

class BackupService {
  static final FirestoreService _firestoreService = FirestoreService();

  /// إنشاء نسخة احتياطية شاملة
  static Future<Map<String, dynamic>> createFullBackup() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw Exception('يجب تسجيل الدخول أولاً');

    final backup = <String, dynamic>{
      'version': '1.0',
      'timestamp': DateTime.now().toIso8601String(),
      'userId': user.uid,
      'userEmail': user.email,
      'data': {},
    };

    try {
      // نسخ احتياطي للأعراض
      backup['data']['symptoms'] = await _backupSymptoms(user.uid);

      // نسخ احتياطي للمنتجات المفضلة
      backup['data']['favoriteProducts'] = await _backupFavoriteProducts(
        user.uid,
      );

      // نسخ احتياطي للمطاعم المفضلة
      backup['data']['favoriteRestaurants'] = await _backupFavoriteRestaurants(
        user.uid,
      );

      // نسخ احتياطي لمنشورات المنتدى
      backup['data']['forumPosts'] = await _backupForumPosts(user.uid);

      // نسخ احتياطي للإعدادات
      backup['data']['settings'] = await _backupUserSettings(user.uid);

      return backup;
    } catch (e) {
      debugPrint('خطأ في إنشاء النسخة الاحتياطية: $e');
      rethrow;
    }
  }

  /// نسخ احتياطي للأعراض
  static Future<List<Map<String, dynamic>>> _backupSymptoms(
    String userId,
  ) async {
    try {
      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.symptomsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      debugPrint('خطأ في نسخ الأعراض: $e');
      return [];
    }
  }

  /// نسخ احتياطي للمنتجات المفضلة
  static Future<List<Map<String, dynamic>>> _backupFavoriteProducts(
    String userId,
  ) async {
    try {
      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .collection('favoriteProducts')
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      debugPrint('خطأ في نسخ المنتجات المفضلة: $e');
      return [];
    }
  }

  /// نسخ احتياطي للمطاعم المفضلة
  static Future<List<Map<String, dynamic>>> _backupFavoriteRestaurants(
    String userId,
  ) async {
    try {
      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .collection('favoriteRestaurants')
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      debugPrint('خطأ في نسخ المطاعم المفضلة: $e');
      return [];
    }
  }

  /// نسخ احتياطي لمنشورات المنتدى
  static Future<List<Map<String, dynamic>>> _backupForumPosts(
    String userId,
  ) async {
    try {
      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.forumPostsCollection)
          .where('authorId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      debugPrint('خطأ في نسخ منشورات المنتدى: $e');
      return [];
    }
  }

  /// نسخ احتياطي للإعدادات
  static Future<Map<String, dynamic>> _backupUserSettings(String userId) async {
    try {
      final docSnapshot = await _firestoreService.db
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (docSnapshot.exists) {
        final data = docSnapshot.data() ?? {};
        // إزالة البيانات الحساسة
        data.remove('fcmToken');
        data.remove('lastLoginAt');
        return data;
      }
      return {};
    } catch (e) {
      debugPrint('خطأ في نسخ الإعدادات: $e');
      return {};
    }
  }

  /// حفظ النسخة الاحتياطية كملف
  static Future<File> saveBackupToFile(Map<String, dynamic> backup) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'yassincil_backup_$timestamp.json';
      final file = File('${directory.path}/$fileName');

      final jsonString = const JsonEncoder.withIndent('  ').convert(backup);
      await file.writeAsString(jsonString);

      return file;
    } catch (e) {
      debugPrint('خطأ في حفظ النسخة الاحتياطية: $e');
      rethrow;
    }
  }

  /// مشاركة النسخة الاحتياطية
  static Future<void> shareBackup() async {
    try {
      final backup = await createFullBackup();
      final file = await saveBackupToFile(backup);

      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'نسخة احتياطية من بيانات تطبيق رفيق السيلياك',
        subject: 'نسخة احتياطية - رفيق السيلياك',
      );
    } catch (e) {
      debugPrint('خطأ في مشاركة النسخة الاحتياطية: $e');
      rethrow;
    }
  }

  /// استعادة البيانات من نسخة احتياطية
  static Future<void> restoreFromBackup(Map<String, dynamic> backup) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw Exception('يجب تسجيل الدخول أولاً');

    try {
      final data = backup['data'] as Map<String, dynamic>;

      // استعادة الأعراض
      if (data.containsKey('symptoms')) {
        await _restoreSymptoms(user.uid, data['symptoms']);
      }

      // استعادة المنتجات المفضلة
      if (data.containsKey('favoriteProducts')) {
        await _restoreFavoriteProducts(user.uid, data['favoriteProducts']);
      }

      // استعادة المطاعم المفضلة
      if (data.containsKey('favoriteRestaurants')) {
        await _restoreFavoriteRestaurants(
          user.uid,
          data['favoriteRestaurants'],
        );
      }

      // استعادة الإعدادات
      if (data.containsKey('settings')) {
        await _restoreUserSettings(user.uid, data['settings']);
      }

      debugPrint('تم استعادة البيانات بنجاح');
    } catch (e) {
      debugPrint('خطأ في استعادة البيانات: $e');
      rethrow;
    }
  }

  /// استعادة الأعراض
  static Future<void> _restoreSymptoms(
    String userId,
    List<dynamic> symptoms,
  ) async {
    try {
      final batch = _firestoreService.db.batch();

      for (final symptomData in symptoms) {
        final data = Map<String, dynamic>.from(symptomData);
        data.remove('id'); // إزالة المعرف القديم
        data['userId'] = userId; // التأكد من المعرف الصحيح

        final docRef = _firestoreService.db
            .collection(AppConstants.symptomsCollection)
            .doc();
        batch.set(docRef, data);
      }

      await batch.commit();
      debugPrint('تم استعادة ${symptoms.length} عرض');
    } catch (e) {
      debugPrint('خطأ في استعادة الأعراض: $e');
    }
  }

  /// استعادة المنتجات المفضلة
  static Future<void> _restoreFavoriteProducts(
    String userId,
    List<dynamic> products,
  ) async {
    try {
      final batch = _firestoreService.db.batch();

      for (final productData in products) {
        final data = Map<String, dynamic>.from(productData);
        data.remove('id');

        final docRef = _firestoreService.db
            .collection(AppConstants.usersCollection)
            .doc(userId)
            .collection('favoriteProducts')
            .doc();
        batch.set(docRef, data);
      }

      await batch.commit();
      debugPrint('تم استعادة ${products.length} منتج مفضل');
    } catch (e) {
      debugPrint('خطأ في استعادة المنتجات المفضلة: $e');
    }
  }

  /// استعادة المطاعم المفضلة
  static Future<void> _restoreFavoriteRestaurants(
    String userId,
    List<dynamic> restaurants,
  ) async {
    try {
      final batch = _firestoreService.db.batch();

      for (final restaurantData in restaurants) {
        final data = Map<String, dynamic>.from(restaurantData);
        data.remove('id');

        final docRef = _firestoreService.db
            .collection(AppConstants.usersCollection)
            .doc(userId)
            .collection('favoriteRestaurants')
            .doc();
        batch.set(docRef, data);
      }

      await batch.commit();
      debugPrint('تم استعادة ${restaurants.length} مطعم مفضل');
    } catch (e) {
      debugPrint('خطأ في استعادة المطاعم المفضلة: $e');
    }
  }

  /// استعادة الإعدادات
  static Future<void> _restoreUserSettings(
    String userId,
    Map<String, dynamic> settings,
  ) async {
    try {
      await _firestoreService.db
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update(settings);

      debugPrint('تم استعادة الإعدادات');
    } catch (e) {
      debugPrint('خطأ في استعادة الإعدادات: $e');
    }
  }

  /// جدولة نسخ احتياطي تلقائي
  static Future<void> scheduleAutoBackup() async {
    // يمكن تنفيذ هذا باستخدام WorkManager أو مهام مجدولة
    // حالياً سنحفظ التفضيل فقط
    debugPrint('تم تفعيل النسخ الاحتياطي التلقائي');
  }

  /// إلغاء النسخ الاحتياطي التلقائي
  static Future<void> cancelAutoBackup() async {
    debugPrint('تم إلغاء النسخ الاحتياطي التلقائي');
  }
}

// lib/widgets/add_medication_comment_widget.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../models/comment.dart';
import '../providers/medication_provider.dart';
import '../providers/auth_provider.dart' as app_auth;
import '../utils/app_colors.dart';
import '../services/storage_service.dart';

class AddMedicationCommentWidget extends StatefulWidget {
  final String medicationId;
  final String? parentCommentId;
  final String? replyToUsername;
  final VoidCallback? onCommentAdded;
  final FocusNode? focusNode;

  const AddMedicationCommentWidget({
    super.key,
    required this.medicationId,
    this.parentCommentId,
    this.replyToUsername,
    this.onCommentAdded,
    this.focusNode,
  });

  @override
  State<AddMedicationCommentWidget> createState() =>
      _AddMedicationCommentWidgetState();
}

class _AddMedicationCommentWidgetState
    extends State<AddMedicationCommentWidget> {
  final TextEditingController _commentController = TextEditingController();
  final List<File> _selectedImages = [];
  final List<String> _uploadedImageUrls = [];
  bool _isSubmitting = false;
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  Future<void> _pickImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage();
      if (images.isNotEmpty) {
        setState(() {
          for (final image in images) {
            if (_selectedImages.length < 5) {
              _selectedImages.add(File(image.path));
            }
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء اختيار الصور'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  Future<void> _uploadImages() async {
    _uploadedImageUrls.clear();
    final storageService = StorageService();

    for (int i = 0; i < _selectedImages.length; i++) {
      try {
        // استخدام StorageService لرفع الصور
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final path =
            'medication_comments/${widget.medicationId}/${timestamp}_$i.jpg';
        final imageUrl = await storageService.uploadFile(
          _selectedImages[i],
          path,
        );

        if (imageUrl != null) {
          _uploadedImageUrls.add(imageUrl);
        } else {
          // إظهار رسالة خطأ إذا فشل رفع الصورة
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('فشل في رفع إحدى الصور'),
                backgroundColor: AppColors.warning,
              ),
            );
          }
        }
      } catch (e) {
        debugPrint('خطأ في رفع الصورة: $e');
        // إظهار رسالة خطأ
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ أثناء رفع الصور'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  Future<void> _submitComment() async {
    if (_commentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى كتابة تعليق'),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final authProvider = Provider.of<app_auth.AuthProvider>(
        context,
        listen: false,
      );
      final medicationProvider = Provider.of<MedicationProvider>(
        context,
        listen: false,
      );

      // رفع الصور أولاً
      await _uploadImages();

      final comment = Comment(
        postId: widget.medicationId,
        content: _commentController.text.trim(),
        userId: authProvider.currentUser!.uid,
        username:
            authProvider.userProfile?.displayName ??
            authProvider.userProfile?.username ??
            'مستخدم',
        userAvatar: authProvider.userProfile?.profileImageUrl,
        createdAt: DateTime.now(),
        imageUrls: _uploadedImageUrls,
        parentCommentId: widget.parentCommentId,
      );

      if (widget.parentCommentId != null) {
        // إضافة رد
        await medicationProvider.addReplyToComment(
          medicationId: widget.medicationId,
          parentCommentId: widget.parentCommentId!,
          reply: comment,
        );
      } else {
        // إضافة تعليق جديد
        await medicationProvider.addCommentToMedication(
          widget.medicationId,
          comment,
        );
      }

      // مسح الحقول
      _commentController.clear();
      setState(() {
        _selectedImages.clear();
        _uploadedImageUrls.clear();
      });

      // إشعار بالنجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.parentCommentId != null
                  ? 'تم إضافة الرد بنجاح'
                  : 'تم إضافة التعليق بنجاح',
            ),
            backgroundColor: AppColors.success,
          ),
        );

        // استدعاء callback إذا كان موجود
        widget.onCommentAdded?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إضافة التعليق'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<app_auth.AuthProvider>(context);

    if (authProvider.currentUser == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Text(
            'يجب تسجيل الدخول لإضافة تعليق',
            style: GoogleFonts.cairo(
              color: AppColors.textSecondary,
              fontSize: 16,
            ),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        // تأثير شفاف عصري
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: 0.95),
            Colors.white.withValues(alpha: 0.85),
          ],
        ),
        border: Border(
          top: BorderSide(
            color: const Color(0xFF00BFA5).withValues(alpha: 0.2),
            width: 1.5,
          ),
        ),
        // ظلال عصرية
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF00BFA5).withValues(alpha: 0.08),
            blurRadius: 15,
            offset: const Offset(0, -4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عرض اسم المستخدم المراد الرد عليه - تصميم عصري
          if (widget.replyToUsername != null) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF00BFA5).withValues(alpha: 0.15),
                    const Color(0xFF00796B).withValues(alpha: 0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: const Color(0xFF00BFA5).withValues(alpha: 0.3),
                  width: 1.0,
                ),
                // تأثير Glow خفيف
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF00BFA5).withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.reply_rounded,
                    size: 16,
                    color: const Color(0xFF00BFA5),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'رد على ${widget.replyToUsername}',
                    style: GoogleFonts.cairo(
                      fontSize: 13,
                      color: const Color(0xFF00BFA5),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // حقل النص والأزرار
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // حقل الإدخال العصري
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(25),
                    // ظل خفيف للحقل
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF00BFA5).withValues(alpha: 0.06),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _commentController,
                    focusNode: widget.focusNode, // استخدام نقطة التركيز المخصصة
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _isSubmitting ? null : _submitComment(),
                    decoration: InputDecoration(
                      hintText: widget.parentCommentId != null
                          ? 'اكتب ردك...'
                          : 'اكتب تعليقك...',
                      hintStyle: GoogleFonts.cairo(
                        color: Colors.grey.shade500,
                        fontSize: 14,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: BorderSide(
                          color: const Color(0xFF00BFA5).withValues(alpha: 0.2),
                          width: 1.0,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: BorderSide(
                          color: const Color(0xFF00BFA5).withValues(alpha: 0.2),
                          width: 1.0,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: const BorderSide(
                          color: Color(0xFF00BFA5),
                          width: 2.0,
                        ),
                      ),
                      filled: true,
                      fillColor: Colors.white.withValues(alpha: 0.9),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 14,
                      ),
                    ),
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // زر اختيار الصور العصري
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.grey.shade100, Colors.grey.shade50],
                  ),
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.grey.shade300, width: 1.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.1),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: _isSubmitting ? null : _pickImages,
                  icon: Icon(
                    Icons.image_rounded,
                    color: Colors.grey.shade600,
                    size: 22,
                  ),
                  tooltip: 'إضافة صور',
                ),
              ),

              const SizedBox(width: 12),

              // زر الإرسال العصري
              Container(
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Color(0xFF00BFA5), Color(0xFF00796B)],
                  ),
                  shape: BoxShape.circle,
                  // تأثير Glow للزر
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF00BFA5).withValues(alpha: 0.4),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: Colors.white.withValues(alpha: 0.8),
                      blurRadius: 1,
                      offset: const Offset(0, 1),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: _isSubmitting ? null : _submitComment,
                  icon: _isSubmitting
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2.5,
                          ),
                        )
                      : const Icon(
                          Icons.send_rounded,
                          color: Colors.white,
                          size: 22,
                        ),
                  tooltip: 'إرسال',
                ),
              ),
            ],
          ),

          // عرض الصور المختارة
          if (_selectedImages.isNotEmpty) ...[
            const SizedBox(height: 12),
            SizedBox(
              height: 80,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.only(left: 8),
                    width: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppColors.border),
                    ),
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.file(
                            _selectedImages[index],
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => _removeImage(index),
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                color: AppColors.error,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.close,
                                size: 12,
                                color: AppColors.textOnPrimary,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';

class Review {
  final String? id;
  final String userId;
  final String userName;
  final String userAvatar;
  final String targetId; // ID of restaurant, store, pharmacy, etc.
  final String
  targetType; // 'restaurant', 'store', 'pharmacy', 'medication', etc.
  final double rating; // 1-5 stars
  final String title;
  final String comment;
  final List<String> images; // URLs of uploaded images
  final List<String> tags; // Tags like 'gluten-free', 'good-service', etc.
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isVerified; // Verified purchase/visit
  final int helpfulCount; // Number of users who found this helpful
  final List<String> helpfulUsers; // Users who marked as helpful
  final bool isReported;
  final String? reportReason;

  Review({
    this.id,
    required this.userId,
    required this.userName,
    this.userAvatar = '',
    required this.targetId,
    required this.targetType,
    required this.rating,
    required this.title,
    required this.comment,
    this.images = const [],
    this.tags = const [],
    required this.createdAt,
    required this.updatedAt,
    this.isVerified = false,
    this.helpfulCount = 0,
    this.helpfulUsers = const [],
    this.isReported = false,
    this.reportReason,
  });

  factory Review.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Review(
      id: doc.id,
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userAvatar: data['userAvatar'] ?? '',
      targetId: data['targetId'] ?? '',
      targetType: data['targetType'] ?? '',
      rating: (data['rating'] ?? 0.0).toDouble(),
      title: data['title'] ?? '',
      comment: data['comment'] ?? '',
      images: List<String>.from(data['images'] ?? []),
      tags: List<String>.from(data['tags'] ?? []),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      isVerified: data['isVerified'] ?? false,
      helpfulCount: data['helpfulCount'] ?? 0,
      helpfulUsers: List<String>.from(data['helpfulUsers'] ?? []),
      isReported: data['isReported'] ?? false,
      reportReason: data['reportReason'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userName': userName,
      'userAvatar': userAvatar,
      'targetId': targetId,
      'targetType': targetType,
      'rating': rating,
      'title': title,
      'comment': comment,
      'images': images,
      'tags': tags,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isVerified': isVerified,
      'helpfulCount': helpfulCount,
      'helpfulUsers': helpfulUsers,
      'isReported': isReported,
      'reportReason': reportReason,
    };
  }

  Review copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userAvatar,
    String? targetId,
    String? targetType,
    double? rating,
    String? title,
    String? comment,
    List<String>? images,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVerified,
    int? helpfulCount,
    List<String>? helpfulUsers,
    bool? isReported,
    String? reportReason,
  }) {
    return Review(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userAvatar: userAvatar ?? this.userAvatar,
      targetId: targetId ?? this.targetId,
      targetType: targetType ?? this.targetType,
      rating: rating ?? this.rating,
      title: title ?? this.title,
      comment: comment ?? this.comment,
      images: images ?? this.images,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVerified: isVerified ?? this.isVerified,
      helpfulCount: helpfulCount ?? this.helpfulCount,
      helpfulUsers: helpfulUsers ?? this.helpfulUsers,
      isReported: isReported ?? this.isReported,
      reportReason: reportReason ?? this.reportReason,
    );
  }

  // Helper methods
  String get ratingText {
    if (rating >= 4.5) return 'ممتاز';
    if (rating >= 3.5) return 'جيد جداً';
    if (rating >= 2.5) return 'جيد';
    if (rating >= 1.5) return 'مقبول';
    return 'ضعيف';
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 365) {
      return 'منذ ${(difference.inDays / 365).floor()} سنة';
    } else if (difference.inDays > 30) {
      return 'منذ ${(difference.inDays / 30).floor()} شهر';
    } else if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inDays <= 7;
  }

  bool get isHighRating {
    return rating >= 4.0;
  }

  bool get hasImages {
    return images.isNotEmpty;
  }

  bool get hasTags {
    return tags.isNotEmpty;
  }
}

class ReviewSummary {
  final double averageRating;
  final int totalReviews;
  final Map<int, int> ratingDistribution; // {5: 10, 4: 5, 3: 2, 2: 1, 1: 0}
  final List<String> commonTags;
  final int verifiedReviews;
  final double recommendationPercentage;

  ReviewSummary({
    required this.averageRating,
    required this.totalReviews,
    required this.ratingDistribution,
    required this.commonTags,
    required this.verifiedReviews,
    required this.recommendationPercentage,
  });

  factory ReviewSummary.fromReviews(List<Review> reviews) {
    if (reviews.isEmpty) {
      return ReviewSummary(
        averageRating: 0.0,
        totalReviews: 0,
        ratingDistribution: {5: 0, 4: 0, 3: 0, 2: 0, 1: 0},
        commonTags: [],
        verifiedReviews: 0,
        recommendationPercentage: 0.0,
      );
    }

    // Calculate average rating
    final totalRating = reviews.fold<double>(
      0.0,
      (total, review) => total + review.rating,
    );
    final averageRating = totalRating / reviews.length;

    // Calculate rating distribution
    final Map<int, int> distribution = {5: 0, 4: 0, 3: 0, 2: 0, 1: 0};
    for (final review in reviews) {
      final ratingKey = review.rating.round();
      distribution[ratingKey] = (distribution[ratingKey] ?? 0) + 1;
    }

    // Get common tags
    final Map<String, int> tagCount = {};
    for (final review in reviews) {
      for (final tag in review.tags) {
        tagCount[tag] = (tagCount[tag] ?? 0) + 1;
      }
    }
    final commonTags = tagCount.entries
        .where((entry) => entry.value >= 2) // Tags mentioned at least twice
        .map((entry) => entry.key)
        .take(5)
        .toList();

    // Count verified reviews
    final verifiedCount = reviews.where((review) => review.isVerified).length;

    // Calculate recommendation percentage (4+ stars)
    final recommendedCount = reviews
        .where((review) => review.rating >= 4.0)
        .length;
    final recommendationPercentage = (recommendedCount / reviews.length) * 100;

    return ReviewSummary(
      averageRating: averageRating,
      totalReviews: reviews.length,
      ratingDistribution: distribution,
      commonTags: commonTags,
      verifiedReviews: verifiedCount,
      recommendationPercentage: recommendationPercentage,
    );
  }

  String get averageRatingText {
    if (averageRating >= 4.5) return 'ممتاز';
    if (averageRating >= 3.5) return 'جيد جداً';
    if (averageRating >= 2.5) return 'جيد';
    if (averageRating >= 1.5) return 'مقبول';
    return 'ضعيف';
  }

  String get totalReviewsText {
    if (totalReviews == 0) return 'لا توجد تقييمات';
    if (totalReviews == 1) return 'تقييم واحد';
    if (totalReviews == 2) return 'تقييمان';
    if (totalReviews <= 10) return '$totalReviews تقييمات';
    return '$totalReviews تقييم';
  }

  double getRatingPercentage(int rating) {
    if (totalReviews == 0) return 0.0;
    return ((ratingDistribution[rating] ?? 0) / totalReviews) * 100;
  }
}

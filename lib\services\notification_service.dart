import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:yassincil/models/notification.dart';

class NotificationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseMessaging _firebaseMessaging =
      FirebaseMessaging.instance;

  /// Initializes the notification service and sets up listeners.
  static Future<void> initialize() async {
    try {
      // Request permissions for iOS and web
      await _firebaseMessaging.requestPermission();

      // Get the FCM token
      final fcmToken = await _firebaseMessaging.getToken();
      print('FCM Token: $fcmToken');
      // TODO: Save this token to the user's document in Firestore

      // Set up listeners for incoming messages
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        print('Got a message whilst in the foreground!');
        print('Message data: ${message.data}');
        if (message.notification != null) {
          print(
            'Message also contained a notification: ${message.notification}',
          );
        }
      });

      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        print('A new onMessageOpenedApp event was published!');
        // TODO: Handle navigation to the correct screen based on message data
      });
    } catch (e) {
      print('Failed to initialize notification service: $e');
    }
  }

  /// Saves the user's FCM token to their document in Firestore.
  static Future<void> saveUserToken(String userId) async {
    try {
      final fcmToken = await _firebaseMessaging.getToken();
      if (fcmToken != null) {
        await _firestore.collection('users').doc(userId).update({
          'fcmToken': fcmToken,
        });
      }
    } catch (e) {
      print('Error saving user FCM token: $e');
    }
  }

  /// Removes the user's FCM token from their document in Firestore.
  static Future<void> removeUserToken(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'fcmToken': FieldValue.delete(),
      });
    } catch (e) {
      print('Error removing user FCM token: $e');
    }
  }

  /// Creates a notification in Firestore for the target user.
  static Future<void> createNotification({
    required String userId, // The user who will receive the notification
    required NotificationType type,
    required String targetId,
    required String targetType,
    required String senderId,
    required String senderName,
    String? senderAvatar,
  }) async {
    // A user should not receive a notification for their own action.
    if (userId == senderId) {
      return;
    }

    try {
      final notification = Notification(
        userId: userId,
        type: type,
        targetId: targetId,
        targetType: targetType,
        senderId: senderId,
        senderName: senderName,
        senderAvatar: senderAvatar,
        createdAt: DateTime.now(),
      );

      // We use a subcollection under the user's document for easy fetching.
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('notifications')
          .add(notification.toMap());
    } catch (e) {
      // Log the error but don't throw, as notification is not a critical failure.
      print('Error creating notification: $e');
    }
  }

  /// Fetches a stream of notifications for a specific user.
  static Stream<List<Notification>> getUserNotifications(String userId) {
    return _firestore
        .collection('users')
        .doc(userId)
        .collection('notifications')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => Notification.fromFirestore(doc))
              .toList();
        });
  }

  /// Marks a single notification as read.
  static Future<void> markAsRead(String userId, String notificationId) async {
    try {
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('notifications')
          .doc(notificationId)
          .update({'isRead': true});
    } catch (e) {
      print('Error marking notification as read: $e');
    }
  }

  /// Marks all unread notifications for a user as read.
  static Future<void> markAllAsRead(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('notifications')
          .where('isRead', isEqualTo: false)
          .get();

      if (snapshot.docs.isEmpty) {
        return;
      }

      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.update(doc.reference, {'isRead': true});
      }
      await batch.commit();
    } catch (e) {
      print('Error marking all notifications as read: $e');
    }
  }

  // Methods for managing notification preferences

  static Future<bool> areNotificationsEnabled() async {
    // TODO: Implement logic to get user's notification preference
    return true;
  }

  static Future<bool> areMedicationRemindersEnabled() async {
    // TODO: Implement logic to get user's notification preference
    return true;
  }

  static Future<bool> areNewFoodsNotificationsEnabled() async {
    // TODO: Implement logic to get user's notification preference
    return true;
  }

  static Future<bool> areForumNotificationsEnabled() async {
    // TODO: Implement logic to get user's notification preference
    return true;
  }

  static Future<bool> areArticlesNotificationsEnabled() async {
    // TODO: Implement logic to get user's notification preference
    return true;
  }

  static Future<void> setNotificationsEnabled(bool enabled) async {
    // TODO: Implement logic to set user's notification preference
  }

  static Future<void> setMedicationReminders(bool enabled) async {
    // TODO: Implement logic to set user's notification preference
  }

  static Future<void> setNewFoodsNotifications(bool enabled) async {
    // TODO: Implement logic to set user's notification preference
  }

  static Future<void> setForumNotifications(bool enabled) async {
    // TODO: Implement logic to set user's notification preference
  }

  static Future<void> setArticlesNotifications(bool enabled) async {
    // TODO: Implement logic to set user's notification preference
  }

  /// Sends a notification to a specific user.
  /// TODO: This should be handled by a backend service for security.
  static Future<void> sendNotificationToUser({
    required String userId,
    required String title,
    required String body,
    Map<String, String>? data,
  }) async {
    print('--- SIMULATING NOTIFICATION ---');
    print('To: $userId');
    print('Title: $title');
    print('Body: $body');
    print('Data: $data');
    print('-----------------------------');
    // In a real app, this would trigger a Firebase Cloud Function.
  }

  /// Sends a notification to all users.
  /// TODO: This should be handled by a backend service for security.
  static Future<void> sendNotificationToAll({
    required String title,
    required String body,
    Map<String, String>? data,
  }) async {
    print('--- SIMULATING NOTIFICATION TO ALL ---');
    print('Title: $title');
    print('Body: $body');
    print('Data: $data');
    print('------------------------------------');
    // In a real app, this would trigger a Firebase Cloud Function
    // that sends a message to a topic like 'allUsers'.
  }
}

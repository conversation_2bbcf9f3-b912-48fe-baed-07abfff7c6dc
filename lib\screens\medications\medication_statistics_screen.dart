import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/providers/medication_provider.dart';
import 'package:yassincil/models/medication.dart';
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/widgets/medications_app_bar.dart';

class MedicationStatisticsScreen extends StatelessWidget {
  const MedicationStatisticsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final medicationProvider = Provider.of<MedicationProvider>(context);
    final medications = medicationProvider.medications;

    // Calculate statistics
    final totalMedications = medications.length;
    final allowedMedications = medications.where((m) => m.isAllowed).length;
    final notAllowedMedications = totalMedications - allowedMedications;
    final medicationsPerCategory = _calculateMedicationsPerCategory(
      medications,
    );

    return Scaffold(
      appBar: const MedicationsAppBar(title: 'إحصائيات الأدوية'),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatisticCard(
              title: 'إجمالي الأدوية',
              value: totalMedications.toString(),
              icon: Icons.medical_services,
              color: Colors.blue,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatisticCard(
                    title: 'أدوية آمنة',
                    value: allowedMedications.toString(),
                    icon: Icons.check_circle,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatisticCard(
                    title: 'أدوية غير آمنة',
                    value: notAllowedMedications.toString(),
                    icon: Icons.warning,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Text(
              'الأدوية حسب الفئة',
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...medicationsPerCategory.entries.map((entry) {
              return _buildCategoryStatistic(
                category: entry.key,
                count: entry.value,
                total: totalMedications,
              );
            }),
          ],
        ),
      ),
    );
  }

  Map<String, int> _calculateMedicationsPerCategory(
    List<Medication> medications,
  ) {
    final Map<String, int> categoryCounts = {};
    for (final medication in medications) {
      categoryCounts[medication.category] =
          (categoryCounts[medication.category] ?? 0) + 1;
    }
    return categoryCounts;
  }

  Widget _buildStatisticCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
              Icon(icon, color: color, size: 28),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryStatistic({
    required String category,
    required int count,
    required int total,
  }) {
    final percentage = total > 0 ? (count / total) * 100 : 0.0;
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                category,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '$count (${percentage.toStringAsFixed(1)}%)',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: total > 0 ? count / total : 0.0,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        ],
      ),
    );
  }
}

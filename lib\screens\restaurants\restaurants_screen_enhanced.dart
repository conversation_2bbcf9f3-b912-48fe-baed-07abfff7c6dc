import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:image_picker/image_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';

import '../../providers/restaurant_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/restaurant.dart';
import '../../utils/app_colors.dart';
import '../../utils/error_handler.dart';
import '../../widgets/loading_widget.dart' hide EmptyStateWidget;
import '../../services/storage_service.dart';
import 'restaurant_detail_screen_enhanced.dart';
import 'add_edit_restaurant_screen.dart';
import '../../widgets/advanced_filters_sheet.dart';
import '../../widgets/empty_state_widget.dart';

class RestaurantsScreenEnhanced extends StatefulWidget {
  final bool showBackButton;
  final String? initialSearch;

  const RestaurantsScreenEnhanced({
    super.key,
    this.showBackButton = false,
    this.initialSearch,
  });

  @override
  State<RestaurantsScreenEnhanced> createState() =>
      _RestaurantsScreenEnhancedState();
}

class _RestaurantsScreenEnhancedState extends State<RestaurantsScreenEnhanced>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late TextEditingController _searchController;
  String _searchQuery = '';
  String _selectedCategory = 'الكل';
  bool _showOnlyGlutenFree = false;

  final List<String> _categories = [
    'الكل',
    'مطاعم سريعة',
    'مطاعم عائلية',
    'مقاهي',
    'حلويات',
    'مخابز',
  ];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.initialSearch ?? '');
    _searchQuery = widget.initialSearch ?? '';
    _tabController = TabController(length: _categories.length, vsync: this);
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });

    // التأكد من جلب البيانات عند تهيئة الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _ensureDataLoaded();
    });
  }

  // دالة للتأكد من تحميل البيانات
  Future<void> _ensureDataLoaded() async {
    final restaurantProvider = Provider.of<RestaurantProvider>(
      context,
      listen: false,
    );

    debugPrint("🔍 فحص حالة البيانات:");
    debugPrint(
      "  - عدد المطاعم المحلية: ${restaurantProvider.restaurants.length}",
    );
    debugPrint("  - حالة التحميل: ${restaurantProvider.isLoading}");
    debugPrint("  - رسالة الخطأ: ${restaurantProvider.errorMessage}");

    // إذا كانت القائمة فارغة، أعد جلب البيانات
    if (restaurantProvider.restaurants.isEmpty &&
        !restaurantProvider.isLoading) {
      debugPrint("📥 القائمة فارغة، جاري إعادة جلب البيانات...");

      // اختبار الاتصال أولاً
      final connectionOk = await restaurantProvider.testFirestoreConnection();
      if (!connectionOk) {
        debugPrint("❌ مشكلة في الاتصال مع Firestore");
        return;
      }

      await restaurantProvider.fetchRestaurants();

      // فحص النتيجة بعد الجلب
      debugPrint("📊 نتيجة الجلب:");
      debugPrint(
        "  - عدد المطاعم بعد الجلب: ${restaurantProvider.restaurants.length}",
      );
      debugPrint("  - رسالة الخطأ: ${restaurantProvider.errorMessage}");

      // إذا لم تنجح، شغل الاختبار التشخيصي
      if (restaurantProvider.restaurants.isEmpty) {
        debugPrint("🔬 تشغيل الاختبار التشخيصي...");
        await restaurantProvider.runDiagnosticTest();
      }
    } else {
      debugPrint("✅ البيانات متوفرة محلياً أو جاري التحميل");
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _refresh() async {
    if (!mounted) return;

    try {
      final restaurantProvider = Provider.of<RestaurantProvider>(
        context,
        listen: false,
      );
      await restaurantProvider.fetchRestaurants();
    } catch (e) {
      debugPrint('Error during refresh: $e');
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء تحديث البيانات',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final isAdmin = authProvider.isAdmin;
    final showBackButton = widget.showBackButton;

    final scaffoldWidget = Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: widget.showBackButton
          ? CustomScrollView(
              slivers: [
                _buildCompactSliverAppBar(isAdmin),
                SliverToBoxAdapter(child: _buildModernSearchAndFilters()),
                SliverToBoxAdapter(child: _buildModernCategoryTabs()),
                _buildRestaurantsList(isAdmin),
              ],
            )
          : RefreshIndicator(
              onRefresh: _refresh,
              child: CustomScrollView(
                slivers: [
                  _buildCompactSliverAppBar(isAdmin),
                  SliverToBoxAdapter(child: _buildModernSearchAndFilters()),
                  SliverToBoxAdapter(child: _buildModernCategoryTabs()),
                  _buildRestaurantsList(isAdmin),
                ],
              ),
            ),
      floatingActionButton: _buildModernAddRestaurantFAB(isAdmin),
    );

    if (showBackButton) {
      return PopScope(
        canPop: true,
        onPopInvokedWithResult: (didPop, result) {
          debugPrint("🔙 PopScope: didPop = $didPop");
          if (!didPop) {
            // إذا لم يتم الرجوع تلقائياً، قم بالرجوع يدوياً
            Navigator.of(context).pop();
          }
        },
        child: scaffoldWidget,
      );
    } else {
      return PopScope(canPop: false, child: scaffoldWidget);
    }
  }

  Widget _buildCompactSliverAppBar(bool isAdmin) {
    return SliverAppBar(
      expandedHeight: 160,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      automaticallyImplyLeading: widget.showBackButton,
      leading: widget.showBackButton
          ? Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: IconButton(
                icon: const Icon(
                  Icons.arrow_back_ios_rounded,
                  color: Colors.white,
                  size: 20,
                ),
                onPressed: () {
                  debugPrint("🔙 الضغط على زر الرجوع");
                  if (Navigator.of(context).canPop()) {
                    Navigator.of(context).pop();
                  } else {
                    debugPrint("⚠️ لا يمكن الرجوع، إغلاق الشاشة");
                    Navigator.of(context).pushReplacementNamed('/home');
                  }
                },
                tooltip: 'رجوع',
              ),
            )
          : null,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF00BFA5), Color(0xFF00796B), Color(0xFF004D40)],
            ),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(35),
              bottomRight: Radius.circular(35),
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF00BFA5).withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Stack(
            children: [
              // Background pattern
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(35),
                      bottomRight: Radius.circular(35),
                    ),
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withValues(alpha: 0.15),
                        Colors.transparent,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                ),
              ),
              // Decorative circles
              Positioned(
                top: -50,
                right: -30,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.1),
                  ),
                ),
              ),
              Positioned(
                bottom: -40,
                left: -20,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.08),
                  ),
                ),
              ),
              // Content
              Positioned(
                bottom: 40,
                left: 20,
                right: 20,
                child: Row(
                  children: [
                    // Restaurant icon with glow effect
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.white.withValues(alpha: 0.3),
                            Colors.white.withValues(alpha: 0.1),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.4),
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.white.withValues(alpha: 0.2),
                            blurRadius: 15,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.restaurant_rounded,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 20),
                    // Title and subtitle
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'دليل المطاعم',
                            style: GoogleFonts.cairo(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  blurRadius: 8.0,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'اكتشف أفضل المطاعم الآمنة',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.white.withValues(alpha: 0.9),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        if (isAdmin)
          Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.white.withValues(alpha: 0.3),
                  Colors.white.withValues(alpha: 0.1),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.4),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: IconButton(
              icon: const Icon(
                Icons.admin_panel_settings_rounded,
                color: Colors.white,
                size: 24,
              ),
              onPressed: _showAdminPanel,
              tooltip: 'لوحة الإدارة',
            ),
          ),
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(Icons.filter_alt_rounded, color: Colors.white),
            onPressed: () {
              setState(() {
                _showOnlyGlutenFree = !_showOnlyGlutenFree;
              });
            },
            tooltip: 'فلترة خالي من الجلوتين',
          ),
        ),
      ],
    );
  }

  void _sortRestaurants(String sortBy) {
    if (!mounted || !context.mounted) return;

    setState(() {
      // يمكن إضافة منطق الترتيب هنا لاحقاً
    });

    String message = '';
    switch (sortBy) {
      case 'name':
        message = 'تم ترتيب المطاعم حسب الاسم';
        break;
      case 'rating':
        message = 'تم ترتيب المطاعم حسب التقييم';
        break;
      case 'distance':
        message = 'تم ترتيب المطاعم حسب المسافة';
        break;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.cairo()),
        backgroundColor: const Color(0xFF00BFA5),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Widget _buildModernSearchAndFilters() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 12, 16, 8),
      child: Row(
        children: [
          // Search Bar - يأخذ معظم المساحة
          Expanded(
            flex: 3,
            child: Container(
              height: 48,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: const Color(0xFF00BFA5).withValues(alpha: 0.2),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF00BFA5).withValues(alpha: 0.08),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'ابحث عن مطعم...',
                  hintStyle: GoogleFonts.cairo(
                    color: Colors.grey.shade500,
                    fontSize: 13,
                  ),
                  prefixIcon: Container(
                    margin: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF00BFA5), Color(0xFF00796B)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.search_rounded,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear_rounded, size: 18),
                          onPressed: () {
                            _searchController.clear();
                            setState(() {
                              _searchQuery = '';
                            });
                          },
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 12,
                  ),
                ),
                style: GoogleFonts.cairo(fontSize: 13),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
              ),
            ),
          ),
          const SizedBox(width: 8),

          // أزرار الفلاتر المضغوطة
          _buildCompactFilterButton(
            icon: Icons.category_rounded,
            isActive: _selectedCategory != 'الكل',
            onTap: () => _showCategoryFilter(),
          ),
          const SizedBox(width: 6),
          _buildCompactFilterButton(
            icon: _showOnlyGlutenFree
                ? Icons.check_circle
                : Icons.circle_outlined,
            isActive: _showOnlyGlutenFree,
            onTap: () {
              setState(() {
                _showOnlyGlutenFree = !_showOnlyGlutenFree;
              });
            },
          ),
          const SizedBox(width: 6),
          _buildCompactFilterButton(
            icon: Icons.sort_rounded,
            isActive: false,
            onTap: () => _showSortOptions(),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactFilterButton({
    required IconData icon,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          gradient: isActive
              ? const LinearGradient(
                  colors: [Color(0xFF00BFA5), Color(0xFF00796B)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : LinearGradient(
                  colors: [Colors.white, Colors.grey.shade50],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
          borderRadius: BorderRadius.circular(14),
          border: Border.all(
            color: isActive ? const Color(0xFF00BFA5) : Colors.grey.shade300,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: isActive
                  ? const Color(0xFF00BFA5).withValues(alpha: 0.3)
                  : Colors.black.withValues(alpha: 0.05),
              blurRadius: isActive ? 8 : 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          icon,
          size: 20,
          color: isActive ? Colors.white : const Color(0xFF00BFA5),
        ),
      ),
    );
  }

  void _showSortOptions() {
    final sortOptions = [
      {'title': 'الاسم (أ-ي)', 'icon': Icons.sort_by_alpha, 'value': 'name'},
      {
        'title': 'التقييم (الأعلى أولاً)',
        'icon': Icons.star_rate_rounded,
        'value': 'rating',
      },
      {
        'title': 'المسافة (الأقرب أولاً)',
        'icon': Icons.location_on_rounded,
        'value': 'distance',
      },
      {'title': 'الأحدث أولاً', 'icon': Icons.access_time, 'value': 'date'},
      {
        'title': 'الأكثر شعبية',
        'icon': Icons.trending_up,
        'value': 'popularity',
      },
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(25)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 50,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(3),
              ),
            ),
            // العنوان
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF6366F1), Color(0xFF4F46E5)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(25),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: const Icon(
                      Icons.sort_rounded,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'ترتيب حسب',
                          style: GoogleFonts.cairo(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          'اختر طريقة ترتيب المطاعم',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close_rounded, color: Colors.white),
                  ),
                ],
              ),
            ),
            // القائمة
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: sortOptions.length,
                itemBuilder: (context, index) {
                  final option = sortOptions[index];

                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.white, Colors.grey.shade50],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(
                        color: Colors.grey.shade200,
                        width: 1.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 8,
                      ),
                      title: Text(
                        option['title'] as String,
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey.shade700,
                        ),
                      ),
                      leading: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: const Color(0xFF6366F1).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(
                          option['icon'] as IconData,
                          color: const Color(0xFF6366F1),
                          size: 20,
                        ),
                      ),
                      trailing: const Icon(
                        Icons.arrow_forward_ios_rounded,
                        size: 16,
                        color: Colors.grey,
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        _sortRestaurants(option['value'] as String);
                      },
                    ),
                  );
                },
              ),
            ),
            // مساحة سفلية
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildModernCategoryTabs() {
    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(vertical: 12),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        indicatorSize: TabBarIndicatorSize.label,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        labelStyle: GoogleFonts.cairo(
          fontWeight: FontWeight.bold,
          fontSize: 15,
        ),
        unselectedLabelStyle: GoogleFonts.cairo(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
        labelPadding: const EdgeInsets.symmetric(horizontal: 20),
        onTap: (index) {
          setState(() {
            _selectedCategory = _categories[index];
          });
        },
        tabs: _categories
            .map(
              (category) => Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: Text(category),
              ),
            )
            .toList(),
      ),
    );
  }

  Widget _buildRestaurantsList(bool isAdmin) {
    return Consumer<RestaurantProvider>(
      builder: (context, restaurantProvider, child) {
        final restaurants = restaurantProvider.restaurants;

        if (restaurants.isEmpty) {
          return SliverToBoxAdapter(
            child: SizedBox(
              height: 400,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: const Color(0xFF00BFA5).withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.restaurant_rounded,
                        size: 60,
                        color: const Color(0xFF00BFA5),
                      ),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'لا توجد مطاعم متاحة',
                      style: GoogleFonts.cairo(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'اسحب للأسفل لتحديث القائمة',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey.shade500,
                      ),
                    ),
                    const SizedBox(height: 20),
                    if (isAdmin)
                      ElevatedButton.icon(
                        onPressed: () => _showAddRestaurantDialog(),
                        icon: const Icon(Icons.add_rounded),
                        label: Text(
                          'إضافة مطعم جديد',
                          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF00BFA5),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        }

        final filteredRestaurants = _getFilteredRestaurants(restaurants);

        if (filteredRestaurants.isEmpty && restaurants.isNotEmpty) {
          return SliverToBoxAdapter(
            child: SizedBox(
              height: 300,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.search_off_rounded,
                        size: 50,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد نتائج مطابقة',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'جرب تغيير معايير البحث أو الفلترة',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey.shade500,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextButton.icon(
                      onPressed: () {
                        setState(() {
                          _searchController.clear();
                          _searchQuery = '';
                          _selectedCategory = 'الكل';
                          _showOnlyGlutenFree = false;
                          _tabController.animateTo(0);
                        });
                      },
                      icon: const Icon(Icons.clear_all_rounded),
                      label: Text(
                        'مسح جميع الفلاتر',
                        style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                      ),
                      style: TextButton.styleFrom(
                        foregroundColor: const Color(0xFF00BFA5),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        return SliverList(
          delegate: SliverChildBuilderDelegate((context, index) {
            final restaurant = filteredRestaurants[index];
            return Container(
              margin: const EdgeInsets.only(bottom: 16, left: 16, right: 16),
              child: _buildModernRestaurantCard(restaurant, isAdmin),
            );
          }, childCount: filteredRestaurants.length),
        );
      },
    );
  }

  Widget _buildModernRestaurantCard(Restaurant restaurant, bool isAdmin) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) =>
                RestaurantDetailScreenEnhanced(restaurant: restaurant),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: const Color(0xFF00BFA5).withValues(alpha: 0.1),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF00BFA5).withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
              spreadRadius: 0,
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 15,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF00BFA5).withValues(alpha: 0.15),
                    const Color(0xFF00796B).withValues(alpha: 0.08),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(25),
                ),
              ),
              child: Row(
                children: [
                  // Restaurant Image/Icon
                  Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(18),
                      gradient: LinearGradient(
                        colors: [
                          Colors.white.withValues(alpha: 0.9),
                          Colors.white.withValues(alpha: 0.7),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      border: Border.all(
                        color: const Color(0xFF00BFA5).withValues(alpha: 0.3),
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF00BFA5).withValues(alpha: 0.2),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child:
                          restaurant.imageUrl != null &&
                              restaurant.imageUrl!.isNotEmpty
                          ? CachedNetworkImage(
                              imageUrl: restaurant.imageUrl!,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      const Color(
                                        0xFF00BFA5,
                                      ).withValues(alpha: 0.2),
                                      const Color(
                                        0xFF00796B,
                                      ).withValues(alpha: 0.1),
                                    ],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                ),
                                child: const Center(
                                  child: SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2.5,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Color(0xFF00BFA5),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      const Color(
                                        0xFF00BFA5,
                                      ).withValues(alpha: 0.2),
                                      const Color(
                                        0xFF00796B,
                                      ).withValues(alpha: 0.1),
                                    ],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                ),
                                child: const Icon(
                                  Icons.restaurant_menu_rounded,
                                  color: Color(0xFF00BFA5),
                                  size: 35,
                                ),
                              ),
                            )
                          : Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    const Color(
                                      0xFF00BFA5,
                                    ).withValues(alpha: 0.2),
                                    const Color(
                                      0xFF00796B,
                                    ).withValues(alpha: 0.1),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                              ),
                              child: const Icon(
                                Icons.restaurant_menu_rounded,
                                color: Color(0xFF00BFA5),
                                size: 35,
                              ),
                            ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Restaurant Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          restaurant.name,
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF1F2937),
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          'مطعم', // Placeholder for category
                          style: GoogleFonts.cairo(
                            fontSize: 15,
                            color: const Color(0xFF00BFA5),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: restaurant.hasGlutenFreeOptions
                                  ? [
                                      const Color(
                                        0xFF10B981,
                                      ).withValues(alpha: 0.2),
                                      const Color(
                                        0xFF059669,
                                      ).withValues(alpha: 0.1),
                                    ]
                                  : [
                                      const Color(
                                        0xFFF59E0B,
                                      ).withValues(alpha: 0.2),
                                      const Color(
                                        0xFFD97706,
                                      ).withValues(alpha: 0.1),
                                    ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: restaurant.hasGlutenFreeOptions
                                  ? const Color(
                                      0xFF10B981,
                                    ).withValues(alpha: 0.3)
                                  : const Color(
                                      0xFFF59E0B,
                                    ).withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                restaurant.hasGlutenFreeOptions
                                    ? Icons.check_circle_rounded
                                    : Icons.warning_rounded,
                                size: 16,
                                color: restaurant.hasGlutenFreeOptions
                                    ? const Color(0xFF10B981)
                                    : const Color(0xFFF59E0B),
                              ),
                              const SizedBox(width: 6),
                              Text(
                                restaurant.hasGlutenFreeOptions
                                    ? 'خالي من الجلوتين'
                                    : 'غير مؤكد',
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: restaurant.hasGlutenFreeOptions
                                      ? const Color(0xFF10B981)
                                      : const Color(0xFFF59E0B),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Admin Menu
                  if (isAdmin)
                    Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFF00BFA5).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: const Color(0xFF00BFA5).withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: PopupMenuButton<String>(
                        icon: Icon(
                          Icons.more_vert_rounded,
                          color: const Color(0xFF00BFA5),
                          size: 22,
                        ),
                        onSelected: (value) async {
                          switch (value) {
                            case 'edit':
                              final result = await Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => AddEditRestaurantScreen(
                                    restaurant: restaurant,
                                  ),
                                ),
                              );
                              // إذا تم تحديث المطعم بنجاح، أعد تحميل البيانات
                              if (result == true && mounted) {
                                debugPrint(
                                  'تم تحديث المطعم، جاري تحديث القائمة...',
                                );
                                final restaurantProvider =
                                    Provider.of<RestaurantProvider>(
                                      context,
                                      listen: false,
                                    );
                                await restaurantProvider.fetchRestaurants();
                              }
                              break;
                            case 'delete':
                              _showDeleteConfirmation(restaurant);
                              break;
                            case 'share':
                              _shareRestaurant(restaurant);
                              break;
                          }
                        },
                        itemBuilder: (context) => [
                          PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                const Icon(Icons.edit, size: 18),
                                const SizedBox(width: 8),
                                Text('تعديل', style: GoogleFonts.cairo()),
                              ],
                            ),
                          ),
                          PopupMenuItem(
                            value: 'share',
                            child: Row(
                              children: [
                                const Icon(Icons.share, size: 18),
                                const SizedBox(width: 8),
                                Text('مشاركة', style: GoogleFonts.cairo()),
                              ],
                            ),
                          ),
                          PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.delete,
                                  size: 18,
                                  color: Colors.red,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'حذف',
                                  style: GoogleFonts.cairo(color: Colors.red),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),

            // Content Section
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Description
                  if (restaurant.description.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.grey.shade200,
                          width: 1,
                        ),
                      ),
                      child: Text(
                        restaurant.description,
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                          height: 1.4,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  const SizedBox(height: 12),

                  // Address and Phone
                  Row(
                    children: [
                      if (restaurant.address.isNotEmpty)
                        _buildRestaurantChip(
                          restaurant.address,
                          Icons.location_on_rounded,
                        ),
                      const SizedBox(width: 8),
                      if (restaurant.phone != null &&
                          restaurant.phone!.isNotEmpty)
                        _buildRestaurantChip(
                          restaurant.phone!,
                          Icons.phone_rounded,
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRestaurantChip(String text, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF00BFA5).withValues(alpha: 0.15),
            const Color(0xFF00796B).withValues(alpha: 0.08),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF00BFA5).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: const Color(0xFF00BFA5)),
          const SizedBox(width: 6),
          Text(
            text,
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF00796B),
            ),
          ),
        ],
      ),
    );
  }

  List<Restaurant> _getFilteredRestaurants(List<Restaurant> restaurants) {
    if (_searchQuery.isEmpty &&
        _selectedCategory == 'الكل' &&
        !_showOnlyGlutenFree) {
      return restaurants;
    }

    return restaurants.where((restaurant) {
      // فلترة البحث
      final matchesSearch =
          _searchQuery.isEmpty ||
          restaurant.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          restaurant.description.toLowerCase().contains(
            _searchQuery.toLowerCase(),
          ) ||
          restaurant.address.toLowerCase().contains(_searchQuery.toLowerCase());

      // فلترة الفئة
      final matchesCategory =
          _selectedCategory == 'الكل' ||
          restaurant.description.toLowerCase().contains(
            _selectedCategory.toLowerCase(),
          ) ||
          restaurant.name.toLowerCase().contains(
            _selectedCategory.toLowerCase(),
          );

      // فلترة الجلوتين
      final matchesGluten =
          !_showOnlyGlutenFree || restaurant.hasGlutenFreeOptions;

      return matchesSearch && matchesCategory && matchesGluten;
    }).toList();
  }

  Widget _buildModernAddRestaurantFAB(bool isAdmin) {
    return isAdmin
        ? Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30),
              gradient: const LinearGradient(
                colors: [Color(0xFF00BFA5), Color(0xFF00796B)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF00BFA5).withValues(alpha: 0.4),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: FloatingActionButton.extended(
              onPressed: () => _showAddRestaurantDialog(),
              backgroundColor: Colors.transparent,
              foregroundColor: Colors.white,
              elevation: 0,
              icon: const Icon(Icons.add_rounded, size: 24),
              label: Text(
                'إضافة مطعم',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          )
        : const SizedBox.shrink();
  }

  void _showAddRestaurantDialog() async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AddEditRestaurantScreen()),
    );

    // إذا تم إضافة المطعم بنجاح، أعد تحميل البيانات
    if (result == true && mounted) {
      debugPrint('تم إضافة مطعم جديد، جاري تحديث القائمة...');
      final restaurantProvider = Provider.of<RestaurantProvider>(
        context,
        listen: false,
      );
      await restaurantProvider.fetchRestaurants();
    }
  }

  void _showAdminPanel() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFF2563EB).withValues(alpha: 0.1),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(25),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2563EB).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.admin_panel_settings,
                      color: Color(0xFF2563EB),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'لوحة إدارة المطاعم',
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF2563EB),
                          ),
                        ),
                        Text(
                          'إدارة وتنظيم المطاعم',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close_rounded),
                  ),
                ],
              ),
            ),

            // Admin Options
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    _buildAdminOption(
                      icon: Icons.add_business_rounded,
                      title: 'إضافة مطعم جديد',
                      subtitle: 'إضافة مطعم جديد للقائمة',
                      color: Colors.green,
                      onTap: () {
                        Navigator.of(context).pop();
                        _showAddRestaurantDialog();
                      },
                    ),
                    const SizedBox(height: 16),
                    _buildAdminOption(
                      icon: Icons.analytics_rounded,
                      title: 'إحصائيات المطاعم',
                      subtitle: 'عرض إحصائيات وتقارير المطاعم',
                      color: Colors.blue,
                      onTap: () {
                        Navigator.of(context).pop();
                        _showRestaurantStatistics();
                      },
                    ),
                    const SizedBox(height: 16),
                    _buildAdminOption(
                      icon: Icons.verified_rounded,
                      title: 'التحقق من البيانات',
                      subtitle: 'مراجعة وتحقق من بيانات المطاعم',
                      color: Colors.orange,
                      onTap: () {
                        Navigator.of(context).pop();
                        _showDataVerification();
                      },
                    ),
                    const SizedBox(height: 16),
                    _buildAdminOption(
                      icon: Icons.settings_rounded,
                      title: 'إعدادات المطاعم',
                      subtitle: 'إعدادات عامة لإدارة المطاعم',
                      color: Colors.purple,
                      onTap: () {
                        Navigator.of(context).pop();
                        _showRestaurantSettings();
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdminOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios_rounded,
              color: AppColors.textSecondary,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _showRestaurantStatistics() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم إضافة إحصائيات المطاعم قريباً',
          style: GoogleFonts.cairo(),
        ),
      ),
    );
  }

  void _showDataVerification() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم إضافة نظام التحقق من البيانات قريباً',
          style: GoogleFonts.cairo(),
        ),
      ),
    );
  }

  void _showRestaurantSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم إضافة إعدادات المطاعم قريباً',
          style: GoogleFonts.cairo(),
        ),
      ),
    );
  }

  /// مشاركة معلومات المطعم
  void _shareRestaurant(Restaurant restaurant) {
    final String shareText =
        '''
🍽️ ${restaurant.name}

📍 ${restaurant.address}
${restaurant.phone != null && restaurant.phone!.isNotEmpty ? '📞 ${restaurant.phone}' : ''}

${restaurant.description.isNotEmpty ? '📝 ${restaurant.description}' : ''}

${restaurant.hasGlutenFreeOptions ? '✅ يوفر خيارات خالية من الجلوتين' : ''}
${restaurant.rating != null ? '⭐ التقييم: ${restaurant.rating!.toStringAsFixed(1)}' : ''}

تم المشاركة من تطبيق ياسين سيل - دليل الأطعمة الآمنة
    '''
            .trim();

    Share.share(shareText, subject: 'مطعم ${restaurant.name}');
  }

  /// إظهار تأكيد الحذف
  void _showDeleteConfirmation(Restaurant restaurant) {
    if (!mounted || !context.mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Icon(Icons.warning_rounded, color: Colors.red.shade600, size: 28),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'تأكيد الحذف',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'هل أنت متأكد من حذف مطعم "${restaurant.name}"؟',
              style: GoogleFonts.cairo(fontSize: 16),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200, width: 1),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline_rounded,
                    color: Colors.red.shade600,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'لا يمكن التراجع عن هذا الإجراء',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.red.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteRestaurant(restaurant);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'حذف',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  /// حذف المطعم
  void _deleteRestaurant(Restaurant restaurant) async {
    if (!mounted) return;

    try {
      final restaurantProvider = Provider.of<RestaurantProvider>(
        context,
        listen: false,
      );

      // إظهار مؤشر التحميل
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 16),
              Text('جاري حذف المطعم...', style: GoogleFonts.cairo()),
            ],
          ),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );

      await restaurantProvider.deleteRestaurant(restaurant.id!);

      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف المطعم بنجاح', style: GoogleFonts.cairo()),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error deleting restaurant: $e');
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء حذف المطعم',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _showCategoryFilter() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(25)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 50,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(3),
              ),
            ),
            // العنوان
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF00BFA5), Color(0xFF00796B)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(25),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: const Icon(
                      Icons.category_rounded,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'اختر الفئة',
                          style: GoogleFonts.cairo(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          'فلترة المطاعم حسب الفئة',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close_rounded, color: Colors.white),
                  ),
                ],
              ),
            ),
            // القائمة القابلة للتمرير
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final category = _categories[index];
                  final isSelected = category == _selectedCategory;

                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    decoration: BoxDecoration(
                      gradient: isSelected
                          ? const LinearGradient(
                              colors: [Color(0xFF00BFA5), Color(0xFF00796B)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            )
                          : LinearGradient(
                              colors: [Colors.white, Colors.grey.shade50],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(
                        color: isSelected
                            ? const Color(0xFF00BFA5)
                            : Colors.grey.shade200,
                        width: 1.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: isSelected
                              ? const Color(0xFF00BFA5).withValues(alpha: 0.3)
                              : Colors.black.withValues(alpha: 0.05),
                          blurRadius: isSelected ? 12 : 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 8,
                      ),
                      title: Text(
                        category,
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isSelected
                              ? Colors.white
                              : Colors.grey.shade700,
                        ),
                      ),
                      leading: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Colors.white.withValues(alpha: 0.2)
                              : const Color(0xFF00BFA5).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(
                          isSelected
                              ? Icons.check_circle
                              : Icons.category_outlined,
                          color: isSelected
                              ? Colors.white
                              : const Color(0xFF00BFA5),
                          size: 20,
                        ),
                      ),
                      onTap: () {
                        setState(() {
                          _selectedCategory = category;
                        });
                        Navigator.pop(context);
                      },
                    ),
                  );
                },
              ),
            ),
            // مساحة سفلية
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}

import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:rxdart/rxdart.dart'; // Add rxdart to pubspec.yaml if not already there, for .switchMap
import '../models/user_profile.dart';
import '../utils/app_constants.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  User? getCurrentUser() {
    return _auth.currentUser;
  }

  Stream<User?> get authStateChanges => _auth.authStateChanges();

  Stream<UserProfile?> get currentUserProfile {
    return _auth.authStateChanges().switchMap((user) {
      if (user == null) {
        return Stream.value(null);
      }
      return _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.uid)
          .snapshots()
          .map((snapshot) {
            if (snapshot.exists) {
              return UserProfile.fromFirestore(snapshot);
            }
            return null;
          });
    });
  }

  Future<UserCredential> signIn(String email, String password) async {
    try {
      UserCredential userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return userCredential;
    } on FirebaseAuthException catch (e) {
      throw Exception(e.message ?? 'حدث خطأ أثناء تسجيل الدخول.');
    } catch (e) {
      throw Exception('فشل تسجيل الدخول: $e');
    }
  }

  Future<UserCredential> signUp(
    String email,
    String password,
    String username, {
    String? firstName,
    String? lastName,
  }) async {
    try {
      UserCredential userCredential = await _auth
          .createUserWithEmailAndPassword(email: email, password: password);
      if (userCredential.user != null) {
        final newProfile = UserProfile(
          uid: userCredential.user!.uid,
          email: email,
          username: username,
          firstName: firstName,
          lastName: lastName,
          role: AppConstants.userRole, // Default role
          createdAt: DateTime.now(),
        );
        await _firestore
            .collection(AppConstants.usersCollection)
            .doc(userCredential.user!.uid)
            .set(newProfile.toMap());
      }
      return userCredential;
    } on FirebaseAuthException catch (e) {
      throw Exception(e.message ?? 'حدث خطأ أثناء التسجيل.');
    } catch (e) {
      throw Exception('فشل التسجيل: $e');
    }
  }

  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      throw Exception('فشل تسجيل الخروج: $e');
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      if (e is FirebaseAuthException) {
        switch (e.code) {
          case 'user-not-found':
            throw Exception('لا يوجد حساب مرتبط بهذا البريد الإلكتروني');
          case 'invalid-email':
            throw Exception('البريد الإلكتروني غير صحيح');
          case 'too-many-requests':
            throw Exception(
              'تم إرسال عدد كبير من الطلبات. حاول مرة أخرى لاحقاً',
            );
          default:
            throw Exception('حدث خطأ أثناء إرسال رابط الاستعادة: ${e.message}');
        }
      }
      throw Exception('حدث خطأ غير متوقع: $e');
    }
  }

  Future<void> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // إعادة المصادقة أولاً
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );
      await user.reauthenticateWithCredential(credential);

      // تغيير كلمة المرور
      await user.updatePassword(newPassword);
    } catch (e) {
      if (e is FirebaseAuthException) {
        switch (e.code) {
          case 'wrong-password':
            throw Exception('كلمة المرور الحالية غير صحيحة');
          case 'weak-password':
            throw Exception('كلمة المرور الجديدة ضعيفة جداً');
          case 'requires-recent-login':
            throw Exception('يجب إعادة تسجيل الدخول لتغيير كلمة المرور');
          default:
            throw Exception('حدث خطأ أثناء تغيير كلمة المرور: ${e.message}');
        }
      }
      throw Exception('حدث خطأ غير متوقع: $e');
    }
  }

  Future<void> updateEmail(String newEmail, String password) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // إعادة المصادقة أولاً
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: password,
      );
      await user.reauthenticateWithCredential(credential);

      // تحديث البريد الإلكتروني
      await user.verifyBeforeUpdateEmail(newEmail);

      // تحديث البريد في Firestore أيضاً
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.uid)
          .update({'email': newEmail});
    } catch (e) {
      if (e is FirebaseAuthException) {
        switch (e.code) {
          case 'wrong-password':
            throw Exception('كلمة المرور غير صحيحة');
          case 'email-already-in-use':
            throw Exception('البريد الإلكتروني مستخدم بالفعل');
          case 'invalid-email':
            throw Exception('البريد الإلكتروني غير صحيح');
          case 'requires-recent-login':
            throw Exception('يجب إعادة تسجيل الدخول لتحديث البريد الإلكتروني');
          default:
            throw Exception(
              'حدث خطأ أثناء تحديث البريد الإلكتروني: ${e.message}',
            );
        }
      }
      throw Exception('حدث خطأ غير متوقع: $e');
    }
  }

  // --- Admin functions (only callable by authenticated admins) ---

  Stream<List<UserProfile>> getAllUserProfiles() {
    return _firestore.collection(AppConstants.usersCollection).snapshots().map((
      snapshot,
    ) {
      return snapshot.docs
          .map((doc) => UserProfile.fromFirestore(doc))
          .toList();
    });
  }

  Future<void> updateUserRole(String uid, String newRole) async {
    await _firestore.collection(AppConstants.usersCollection).doc(uid).update({
      'role': newRole,
    });
  }

  Future<void> deleteUserFirestoreProfile(String uid) async {
    await _firestore.collection(AppConstants.usersCollection).doc(uid).delete();
    // Note: To delete from Firebase Auth, an admin SDK from a backend server is typically used
    // or manually from Firebase Console. Directly deleting from Auth from a client app is usually not allowed.
  }
}

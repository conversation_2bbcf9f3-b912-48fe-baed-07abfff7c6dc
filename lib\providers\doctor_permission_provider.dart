import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../models/doctor_permission.dart';
import '../services/firestore_service.dart';
import '../utils/app_constants.dart';

class DoctorPermissionProvider extends ChangeNotifier {
  final FirestoreService _firestoreService;
  
  List<DoctorPermission> _permissions = [];
  DoctorPermission? _currentUserPermission;
  bool _isLoading = false;
  String? _errorMessage;

  DoctorPermissionProvider(this._firestoreService);

  // Getters
  List<DoctorPermission> get permissions => _permissions;
  DoctorPermission? get currentUserPermission => _currentUserPermission;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Check if current user has specific permission
  bool hasPermission(DoctorPermissionType permission) {
    return _currentUserPermission?.hasPermission(permission) ?? false;
  }

  bool hasAnyPermission(List<DoctorPermissionType> permissionList) {
    return _currentUserPermission?.hasAnyPermission(permissionList) ?? false;
  }

  bool hasAllPermissions(List<DoctorPermissionType> permissionList) {
    return _currentUserPermission?.hasAllPermissions(permissionList) ?? false;
  }

  // Load permissions for current user
  Future<void> loadCurrentUserPermissions(String userId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.doctorPermissionsCollection)
          .where('userId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        _currentUserPermission = DoctorPermission.fromFirestore(
          querySnapshot.docs.first,
        );
      } else {
        _currentUserPermission = null;
      }
    } catch (e) {
      _errorMessage = 'خطأ في تحميل الصلاحيات: $e';
      debugPrint('Error loading user permissions: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load all permissions (admin only)
  Future<void> loadAllPermissions() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.doctorPermissionsCollection)
          .orderBy('createdAt', descending: true)
          .get();

      _permissions = querySnapshot.docs
          .map((doc) => DoctorPermission.fromFirestore(doc))
          .toList();
    } catch (e) {
      _errorMessage = 'خطأ في تحميل الصلاحيات: $e';
      debugPrint('Error loading permissions: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Add or update permission
  Future<void> setUserPermissions({
    required String userId,
    required String userEmail,
    required List<DoctorPermissionType> permissions,
    String? notes,
    String? createdBy,
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Check if permission already exists
      final existingQuery = await _firestoreService.db
          .collection(AppConstants.doctorPermissionsCollection)
          .where('userId', isEqualTo: userId)
          .limit(1)
          .get();

      final permission = DoctorPermission(
        userId: userId,
        userEmail: userEmail,
        permissions: permissions,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: createdBy,
        notes: notes,
      );

      if (existingQuery.docs.isNotEmpty) {
        // Update existing permission
        final docId = existingQuery.docs.first.id;
        await _firestoreService.updateDocument(
          AppConstants.doctorPermissionsCollection,
          docId,
          permission.toMap(),
        );
      } else {
        // Create new permission
        await _firestoreService.addDocument(
          AppConstants.doctorPermissionsCollection,
          permission.toMap(),
        );
      }

      await loadAllPermissions();
    } catch (e) {
      _errorMessage = 'خطأ في حفظ الصلاحيات: $e';
      debugPrint('Error setting permissions: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Remove user permissions
  Future<void> removeUserPermissions(String userId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.doctorPermissionsCollection)
          .where('userId', isEqualTo: userId)
          .get();

      for (final doc in querySnapshot.docs) {
        await _firestoreService.deleteDocument(
          AppConstants.doctorPermissionsCollection,
          doc.id,
        );
      }

      await loadAllPermissions();
    } catch (e) {
      _errorMessage = 'خطأ في حذف الصلاحيات: $e';
      debugPrint('Error removing permissions: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Toggle permission active status
  Future<void> togglePermissionStatus(String permissionId) async {
    try {
      final permission = _permissions.firstWhere((p) => p.id == permissionId);
      final updatedPermission = permission.copyWith(
        isActive: !permission.isActive,
        updatedAt: DateTime.now(),
      );

      await _firestoreService.updateDocument(
        AppConstants.doctorPermissionsCollection,
        permissionId,
        updatedPermission.toMap(),
      );

      await loadAllPermissions();
    } catch (e) {
      _errorMessage = 'خطأ في تحديث حالة الصلاحية: $e';
      debugPrint('Error toggling permission status: $e');
    }
  }

  // Get users with specific permission
  List<DoctorPermission> getUsersWithPermission(DoctorPermissionType permission) {
    return _permissions
        .where((p) => p.hasPermission(permission))
        .toList();
  }

  // Get permission statistics
  Map<String, int> getPermissionStatistics() {
    final stats = <String, int>{};
    
    for (final permissionType in DoctorPermissionType.values) {
      final count = _permissions
          .where((p) => p.hasPermission(permissionType))
          .length;
      stats[permissionType.name] = count;
    }
    
    return stats;
  }

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Reset provider
  void reset() {
    _permissions = [];
    _currentUserPermission = null;
    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }
}

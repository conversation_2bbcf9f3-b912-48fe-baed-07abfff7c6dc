import 'package:cloud_firestore/cloud_firestore.dart';

class DoctorReview {
  final String? id;
  final String doctorId;
  final String userId;
  final String userName;
  final String? userAvatar;
  final double rating;
  final String comment;
  final List<String> tags; // مثل: "خدمة ممتازة", "وقت انتظار قصير"
  final bool isVerified; // هل المراجعة محققة
  final bool isApproved; // هل المراجعة معتمدة
  final DateTime createdAt;
  final DateTime? updatedAt;
  final int likesCount;
  final int dislikesCount;
  final List<String> likedBy;
  final List<String> dislikedBy;
  final String? appointmentId; // ربط بموعد محدد
  final Map<String, dynamic>? metadata; // معلومات إضافية

  DoctorReview({
    this.id,
    required this.doctorId,
    required this.userId,
    required this.userName,
    this.userAvatar,
    required this.rating,
    required this.comment,
    this.tags = const [],
    this.isVerified = false,
    this.isApproved = true,
    required this.createdAt,
    this.updatedAt,
    this.likesCount = 0,
    this.dislikesCount = 0,
    this.likedBy = const [],
    this.dislikedBy = const [],
    this.appointmentId,
    this.metadata,
  });

  factory DoctorReview.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DoctorReview(
      id: doc.id,
      doctorId: data['doctorId'] ?? '',
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userAvatar: data['userAvatar'],
      rating: (data['rating'] ?? 0.0).toDouble(),
      comment: data['comment'] ?? '',
      tags: List<String>.from(data['tags'] ?? []),
      isVerified: data['isVerified'] ?? false,
      isApproved: data['isApproved'] ?? true,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
      likesCount: data['likesCount'] ?? 0,
      dislikesCount: data['dislikesCount'] ?? 0,
      likedBy: List<String>.from(data['likedBy'] ?? []),
      dislikedBy: List<String>.from(data['dislikedBy'] ?? []),
      appointmentId: data['appointmentId'],
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'doctorId': doctorId,
      'userId': userId,
      'userName': userName,
      'userAvatar': userAvatar,
      'rating': rating,
      'comment': comment,
      'tags': tags,
      'isVerified': isVerified,
      'isApproved': isApproved,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'likesCount': likesCount,
      'dislikesCount': dislikesCount,
      'likedBy': likedBy,
      'dislikedBy': dislikedBy,
      'appointmentId': appointmentId,
      'metadata': metadata,
    };
  }

  DoctorReview copyWith({
    String? id,
    String? doctorId,
    String? userId,
    String? userName,
    String? userAvatar,
    double? rating,
    String? comment,
    List<String>? tags,
    bool? isVerified,
    bool? isApproved,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? likesCount,
    int? dislikesCount,
    List<String>? likedBy,
    List<String>? dislikedBy,
    String? appointmentId,
    Map<String, dynamic>? metadata,
  }) {
    return DoctorReview(
      id: id ?? this.id,
      doctorId: doctorId ?? this.doctorId,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userAvatar: userAvatar ?? this.userAvatar,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      tags: tags ?? this.tags,
      isVerified: isVerified ?? this.isVerified,
      isApproved: isApproved ?? this.isApproved,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      likesCount: likesCount ?? this.likesCount,
      dislikesCount: dislikesCount ?? this.dislikesCount,
      likedBy: likedBy ?? this.likedBy,
      dislikedBy: dislikedBy ?? this.dislikedBy,
      appointmentId: appointmentId ?? this.appointmentId,
      metadata: metadata ?? this.metadata,
    );
  }

  bool isLikedBy(String userId) {
    return likedBy.contains(userId);
  }

  bool isDislikedBy(String userId) {
    return dislikedBy.contains(userId);
  }

  String getTimeAgo() {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return 'منذ $months ${months == 1 ? 'شهر' : 'أشهر'}';
    } else if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  static List<String> getCommonTags() {
    return [
      'خدمة ممتازة',
      'وقت انتظار قصير',
      'طبيب متفهم',
      'تشخيص دقيق',
      'علاج فعال',
      'موظفين مهذبين',
      'عيادة نظيفة',
      'أسعار مناسبة',
      'سهولة الحجز',
      'متابعة جيدة',
    ];
  }

  double get netRating {
    final totalVotes = likesCount + dislikesCount;
    if (totalVotes == 0) return rating;
    
    final likeRatio = likesCount / totalVotes;
    return rating * (0.7 + 0.3 * likeRatio); // تعديل التقييم بناءً على الإعجابات
  }
}

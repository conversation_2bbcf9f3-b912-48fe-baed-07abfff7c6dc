import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../providers/medication_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/medication.dart';
import '../../utils/app_colors.dart';
import '../../widgets/loading_widget.dart' hide EmptyStateWidget;
import '../../widgets/empty_state_widget.dart';
import '../medications/add_edit_medication_screen_enhanced.dart';
import '../medications/medication_detail_screen.dart';
import 'medication_migration_screen.dart';

class EnhancedMedicationsManagementScreen extends StatefulWidget {
  const EnhancedMedicationsManagementScreen({super.key});

  @override
  State<EnhancedMedicationsManagementScreen> createState() =>
      _EnhancedMedicationsManagementScreenState();
}

class _EnhancedMedicationsManagementScreenState
    extends State<EnhancedMedicationsManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final TextEditingController _searchController = TextEditingController();

  String _selectedFilter = 'الكل';
  String _selectedCategory = 'الكل';
  String _currentSortBy = 'date';
  bool _isLoading = false;
  bool _isGridView = false;

  final List<String> _categoryFilters = [
    'الكل',
    'مسكنات الألم',
    'مضادات الحيوية',
    'أدوية الضغط',
    'أدوية السكري',
    'أدوية الجهاز الهضمي',
    'أدوية الحساسية',
    'فيتامينات ومكملات',
    'أدوية القلب',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadMedications();
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadMedications() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final medicationProvider = Provider.of<MedicationProvider>(
        context,
        listen: false,
      );
      await medicationProvider.fetchMedications();

      if (mounted) {
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Text(
                  'حدث خطأ أثناء تحميل البيانات',
                  style: GoogleFonts.cairo(),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  List<Medication> _getFilteredMedications(List<Medication> medications) {
    String searchQuery = _searchController.text.toLowerCase();

    return medications.where((medication) {
      // فلترة البحث
      final matchesSearch =
          searchQuery.isEmpty ||
          medication.name.toLowerCase().contains(searchQuery) ||
          medication.company.toLowerCase().contains(searchQuery) ||
          medication.ingredients.toLowerCase().contains(searchQuery);

      // فلترة الفئة
      final matchesCategory =
          _selectedCategory == 'الكل' ||
          medication.category == _selectedCategory;

      // فلترة حسب الحالة
      bool matchesFilter = true;
      switch (_selectedFilter) {
        case 'معتمد':
          matchesFilter =
              medication.approvalStatus == MedicationApprovalStatus.approved;
          break;
        case 'معلق':
          matchesFilter =
              medication.approvalStatus == MedicationApprovalStatus.pending;
          break;
        case 'مرفوض':
          matchesFilter =
              medication.approvalStatus == MedicationApprovalStatus.rejected;
          break;
        case 'يحتاج مراجعة':
          matchesFilter =
              medication.approvalStatus ==
              MedicationApprovalStatus.needsRevision;
          break;
        case 'مسموح':
          matchesFilter = medication.isAllowed;
          break;
        case 'ممنوع':
          matchesFilter = !medication.isAllowed;
          break;
        case 'يحتاج وصفة':
          matchesFilter = medication.prescriptionRequired == 'نعم';
          break;
        case 'بدون وصفة':
          matchesFilter = medication.prescriptionRequired == 'لا';
          break;
      }

      return matchesSearch && matchesCategory && matchesFilter;
    }).toList();
  }

  List<Medication> _sortMedications(List<Medication> medications) {
    switch (_currentSortBy) {
      case 'name':
        medications.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'company':
        medications.sort((a, b) => a.company.compareTo(b.company));
        break;
      case 'rating':
        medications.sort((a, b) => b.averageRating.compareTo(a.averageRating));
        break;
      case 'popularity':
        medications.sort((a, b) => b.likesCount.compareTo(a.likesCount));
        break;
      case 'oldest':
        medications.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case 'date':
      default:
        medications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
    }
    return medications;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: CustomScrollView(
          slivers: [
            _buildEnhancedSliverAppBar(),
            SliverToBoxAdapter(
              child: Column(
                children: [
                  _buildAdvancedSearchAndFilters(),
                  _buildCategoryTabs(),
                  _buildStatisticsDashboard(),
                  _buildViewControls(),
                ],
              ),
            ),
            _buildMedicationsContent(),
          ],
        ),
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  Widget _buildEnhancedSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 140,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: AppColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'إدارة الأدوية المتقدمة',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontSize: 18,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primary,
                AppColors.primary.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                right: -50,
                top: -50,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.1),
                  ),
                ),
              ),
              Positioned(
                left: -30,
                bottom: -30,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.05),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.analytics_outlined, color: Colors.white),
          onPressed: _showAdvancedStatistics,
          tooltip: 'إحصائيات متقدمة',
        ),
        IconButton(
          icon: const Icon(Icons.refresh_rounded, color: Colors.white),
          onPressed: _loadMedications,
          tooltip: 'تحديث',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  const Icon(Icons.download_rounded),
                  const SizedBox(width: 8),
                  Text('تصدير البيانات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'import',
              child: Row(
                children: [
                  const Icon(Icons.upload_rounded),
                  const SizedBox(width: 8),
                  Text('استيراد البيانات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'bulk_actions',
              child: Row(
                children: [
                  const Icon(Icons.checklist_rounded),
                  const SizedBox(width: 8),
                  Text('إجراءات مجمعة', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'migration',
              child: Row(
                children: [
                  const Icon(Icons.transform_rounded),
                  const SizedBox(width: 8),
                  Text('ترحيل البيانات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAdvancedSearchAndFilters() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث المتقدم
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              style: GoogleFonts.cairo(fontSize: 16),
              decoration: InputDecoration(
                hintText:
                    'بحث متقدم: اسم الدواء، الشركة، المكونات، الباركود...',
                hintStyle: GoogleFonts.cairo(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                ),
                prefixIcon: Container(
                  padding: const EdgeInsets.all(12),
                  child: Icon(
                    Icons.search_rounded,
                    color: AppColors.primary,
                    size: 24,
                  ),
                ),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_searchController.text.isNotEmpty)
                      IconButton(
                        icon: Icon(
                          Icons.clear_rounded,
                          color: Colors.grey.shade600,
                        ),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {});
                        },
                      ),
                    IconButton(
                      icon: Icon(Icons.tune_rounded, color: AppColors.primary),
                      onPressed: _showAdvancedFilters,
                      tooltip: 'فلاتر متقدمة',
                    ),
                  ],
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
              ),
              onChanged: (value) => setState(() {}),
            ),
          ),

          const SizedBox(height: 16),

          // فلاتر سريعة
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildQuickFilter('الكل', _selectedFilter == 'الكل'),
                _buildQuickFilter('معلق', _selectedFilter == 'معلق'),
                _buildQuickFilter('معتمد', _selectedFilter == 'معتمد'),
                _buildQuickFilter('مرفوض', _selectedFilter == 'مرفوض'),
                _buildQuickFilter('مسموح', _selectedFilter == 'مسموح'),
                _buildQuickFilter('ممنوع', _selectedFilter == 'ممنوع'),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // أدوات الترتيب والعرض
          Row(
            children: [
              Expanded(child: _buildSortDropdown()),
              const SizedBox(width: 12),
              _buildViewToggleButton(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFilter(String label, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedFilter = label;
          });
          HapticFeedback.lightImpact();
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primary : Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isSelected ? AppColors.primary : Colors.grey.shade300,
            ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Text(
            label,
            style: GoogleFonts.cairo(
              color: isSelected ? Colors.white : Colors.grey.shade700,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSortDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _currentSortBy,
          isExpanded: true,
          icon: Icon(Icons.keyboard_arrow_down, color: Colors.grey.shade600),
          style: GoogleFonts.cairo(color: Colors.grey.shade700, fontSize: 14),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _currentSortBy = value;
              });
              HapticFeedback.lightImpact();
            }
          },
          items: [
            DropdownMenuItem(value: 'date', child: Text('الأحدث')),
            DropdownMenuItem(value: 'oldest', child: Text('الأقدم')),
            DropdownMenuItem(value: 'name', child: Text('الاسم')),
            DropdownMenuItem(value: 'company', child: Text('الشركة')),
            DropdownMenuItem(value: 'rating', child: Text('التقييم')),
            DropdownMenuItem(value: 'popularity', child: Text('الشعبية')),
          ],
        ),
      ),
    );
  }

  Widget _buildViewToggleButton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildViewButton(
            Icons.view_list_rounded,
            !_isGridView,
            () => setState(() => _isGridView = false),
          ),
          _buildViewButton(
            Icons.grid_view_rounded,
            _isGridView,
            () => setState(() => _isGridView = true),
          ),
        ],
      ),
    );
  }

  Widget _buildViewButton(IconData icon, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: () {
        onTap();
        HapticFeedback.lightImpact();
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          size: 20,
          color: isSelected ? Colors.white : Colors.grey.shade600,
        ),
      ),
    );
  }

  Widget _buildCategoryTabs() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _categoryFilters.length,
        itemBuilder: (context, index) {
          final category = _categoryFilters[index];
          final isSelected = _selectedCategory == category;

          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedCategory = category;
                });
                HapticFeedback.lightImpact();
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primary : Colors.white,
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: isSelected
                        ? AppColors.primary
                        : Colors.grey.shade300,
                  ),
                ),
                child: Text(
                  category,
                  style: GoogleFonts.cairo(
                    color: isSelected ? Colors.white : Colors.grey.shade700,
                    fontWeight: isSelected
                        ? FontWeight.w600
                        : FontWeight.normal,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatisticsDashboard() {
    return Consumer<MedicationProvider>(
      builder: (context, medicationProvider, child) {
        final medications = medicationProvider.medications;
        final totalMedications = medications.length;
        final approvedMedications = medications
            .where((m) => m.approvalStatus == MedicationApprovalStatus.approved)
            .length;
        final pendingMedications = medications
            .where((m) => m.approvalStatus == MedicationApprovalStatus.pending)
            .length;
        final rejectedMedications = medications
            .where((m) => m.approvalStatus == MedicationApprovalStatus.rejected)
            .length;
        final allowedMedications = medications.where((m) => m.isAllowed).length;
        final averageRating = medications.isNotEmpty
            ? medications.map((m) => m.averageRating).reduce((a, b) => a + b) /
                  medications.length
            : 0.0;

        return Container(
          margin: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'لوحة الإحصائيات',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
              const SizedBox(height: 16),

              // الصف الأول من الإحصائيات
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'الإجمالي',
                      totalMedications.toString(),
                      Icons.medical_services_rounded,
                      AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'معتمد',
                      approvedMedications.toString(),
                      Icons.check_circle_rounded,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'معلق',
                      pendingMedications.toString(),
                      Icons.pending_rounded,
                      Colors.orange,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // الصف الثاني من الإحصائيات
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'مرفوض',
                      rejectedMedications.toString(),
                      Icons.cancel_rounded,
                      Colors.red,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'مسموح',
                      allowedMedications.toString(),
                      Icons.verified_rounded,
                      Colors.teal,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'التقييم',
                      averageRating.toStringAsFixed(1),
                      Icons.star_rounded,
                      Colors.amber,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          Text(
            title,
            style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildViewControls() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'عرض النتائج',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          Row(
            children: [
              IconButton(
                icon: Icon(Icons.select_all_rounded, color: AppColors.primary),
                onPressed: _showBulkActions,
                tooltip: 'تحديد الكل',
              ),
              IconButton(
                icon: Icon(Icons.filter_list_rounded, color: AppColors.primary),
                onPressed: _showAdvancedFilters,
                tooltip: 'فلاتر متقدمة',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMedicationsContent() {
    return Consumer<MedicationProvider>(
      builder: (context, medicationProvider, child) {
        if (_isLoading || medicationProvider.isLoading) {
          return const SliverToBoxAdapter(
            child: Center(
              child: Padding(
                padding: EdgeInsets.all(50),
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        if (medicationProvider.errorMessage != null) {
          return SliverToBoxAdapter(
            child: _buildErrorState(medicationProvider.errorMessage!),
          );
        }

        final filteredMedications = _sortMedications(
          _getFilteredMedications(medicationProvider.medications),
        );

        if (filteredMedications.isEmpty) {
          return SliverToBoxAdapter(child: _buildEmptyState());
        }

        if (_isGridView) {
          return SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverGrid(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.75,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              delegate: SliverChildBuilderDelegate((context, index) {
                final medication = filteredMedications[index];
                return _buildMedicationGridCard(medication);
              }, childCount: filteredMedications.length),
            ),
          );
        } else {
          return SliverList(
            delegate: SliverChildBuilderDelegate((context, index) {
              final medication = filteredMedications[index];
              return Container(
                margin: const EdgeInsets.only(bottom: 16, left: 16, right: 16),
                child: _buildMedicationListCard(medication),
              );
            }, childCount: filteredMedications.length),
          );
        }
      },
    );
  }

  Widget _buildMedicationListCard(Medication medication) {
    return GestureDetector(
      onTap: () => _navigateToDetail(medication),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 20,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // صورة الدواء
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.grey.shade100,
                ),
                child: medication.imageUrls.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: CachedNetworkImage(
                          imageUrl: medication.imageUrls.first,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            color: Colors.grey.shade200,
                            child: const Center(
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: Colors.grey.shade200,
                            child: Icon(
                              Icons.medical_services_rounded,
                              color: Colors.grey.shade400,
                              size: 30,
                            ),
                          ),
                        ),
                      )
                    : Icon(
                        Icons.medical_services_rounded,
                        color: Colors.grey.shade400,
                        size: 30,
                      ),
              ),

              const SizedBox(width: 16),

              // معلومات الدواء
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            medication.name,
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _buildStatusBadge(medication),
                      ],
                    ),

                    const SizedBox(height: 4),

                    Text(
                      medication.company,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    Row(
                      children: [
                        Icon(
                          Icons.category_rounded,
                          size: 16,
                          color: Colors.grey.shade500,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            medication.category,
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: Colors.grey.shade500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    Row(
                      children: [
                        _buildInteractionChip(
                          Icons.favorite_rounded,
                          medication.likesCount.toString(),
                          Colors.red,
                        ),
                        const SizedBox(width: 8),
                        _buildInteractionChip(
                          Icons.comment_rounded,
                          medication.commentsCount.toString(),
                          Colors.blue,
                        ),
                        const SizedBox(width: 8),
                        _buildInteractionChip(
                          Icons.star_rounded,
                          medication.averageRating.toStringAsFixed(1),
                          Colors.amber,
                        ),
                        const Spacer(),
                        PopupMenuButton<String>(
                          onSelected: (value) =>
                              _handleMedicationAction(value, medication),
                          child: Icon(
                            Icons.more_vert_rounded,
                            color: Colors.grey.shade600,
                          ),
                          itemBuilder: (context) => [
                            PopupMenuItem(
                              value: 'view',
                              child: Row(
                                children: [
                                  const Icon(Icons.visibility),
                                  const SizedBox(width: 8),
                                  Text('عرض', style: GoogleFonts.cairo()),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'edit',
                              child: Row(
                                children: [
                                  const Icon(Icons.edit),
                                  const SizedBox(width: 8),
                                  Text('تعديل', style: GoogleFonts.cairo()),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'approve',
                              child: Row(
                                children: [
                                  const Icon(Icons.check, color: Colors.green),
                                  const SizedBox(width: 8),
                                  Text(
                                    'اعتماد',
                                    style: GoogleFonts.cairo(
                                      color: Colors.green,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'reject',
                              child: Row(
                                children: [
                                  const Icon(Icons.close, color: Colors.red),
                                  const SizedBox(width: 8),
                                  Text(
                                    'رفض',
                                    style: GoogleFonts.cairo(color: Colors.red),
                                  ),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  const Icon(Icons.delete, color: Colors.red),
                                  const SizedBox(width: 8),
                                  Text(
                                    'حذف',
                                    style: GoogleFonts.cairo(color: Colors.red),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMedicationGridCard(Medication medication) {
    return GestureDetector(
      onTap: () => _navigateToDetail(medication),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 20,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة الدواء
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(16),
                  ),
                  color: Colors.grey.shade100,
                ),
                child: medication.imageUrls.isNotEmpty
                    ? ClipRRect(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(16),
                        ),
                        child: CachedNetworkImage(
                          imageUrl: medication.imageUrls.first,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            color: Colors.grey.shade200,
                            child: const Center(
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: Colors.grey.shade200,
                            child: Icon(
                              Icons.medical_services_rounded,
                              color: Colors.grey.shade400,
                              size: 40,
                            ),
                          ),
                        ),
                      )
                    : Icon(
                        Icons.medical_services_rounded,
                        color: Colors.grey.shade400,
                        size: 40,
                      ),
              ),
            ),

            // معلومات الدواء
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            medication.name,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _buildStatusBadge(medication, isSmall: true),
                      ],
                    ),

                    const SizedBox(height: 4),

                    Text(
                      medication.company,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const Spacer(),

                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.favorite_rounded,
                              size: 14,
                              color: Colors.red.shade400,
                            ),
                            const SizedBox(width: 2),
                            Text(
                              medication.likesCount.toString(),
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Icon(
                              Icons.star_rounded,
                              size: 14,
                              color: Colors.amber.shade400,
                            ),
                            const SizedBox(width: 2),
                            Text(
                              medication.averageRating.toStringAsFixed(1),
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(Medication medication, {bool isSmall = false}) {
    Color color;
    String text;
    IconData icon;

    switch (medication.approvalStatus) {
      case MedicationApprovalStatus.approved:
        color = medication.isAllowed ? Colors.green : Colors.red;
        text = medication.isAllowed ? 'مسموح' : 'ممنوع';
        icon = medication.isAllowed ? Icons.check_circle : Icons.cancel;
        break;
      case MedicationApprovalStatus.pending:
        color = Colors.orange;
        text = 'معلق';
        icon = Icons.pending;
        break;
      case MedicationApprovalStatus.rejected:
        color = Colors.red;
        text = 'مرفوض';
        icon = Icons.cancel;
        break;
      case MedicationApprovalStatus.needsRevision:
        color = Colors.blue;
        text = 'يحتاج مراجعة';
        icon = Icons.edit;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isSmall ? 6 : 8,
        vertical: isSmall ? 2 : 4,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: isSmall ? 12 : 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: GoogleFonts.cairo(
              fontSize: isSmall ? 10 : 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInteractionChip(IconData icon, String count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            count,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.search_off_rounded,
              size: 60,
              color: Colors.grey.shade400,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'لا توجد أدوية',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على أدوية تطابق معايير البحث والفلترة',
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade500),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: () {
              _searchController.clear();
              setState(() {
                _selectedFilter = 'الكل';
                _selectedCategory = 'الكل';
                _currentSortBy = 'date';
              });
            },
            icon: const Icon(Icons.clear_all_rounded),
            label: Text('مسح جميع الفلاتر', style: GoogleFonts.cairo()),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String errorMessage) {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.error_outline_rounded,
              size: 60,
              color: Colors.red.shade400,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'حدث خطأ',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            errorMessage,
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: _loadMedications,
            icon: const Icon(Icons.refresh_rounded),
            label: Text('إعادة المحاولة', style: GoogleFonts.cairo()),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton(
          heroTag: "add_medication",
          onPressed: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const EnhancedAddEditMedicationScreen(),
              ),
            );
          },
          backgroundColor: AppColors.primary,
          child: const Icon(Icons.add_rounded, color: Colors.white),
        ),
        const SizedBox(height: 16),
        FloatingActionButton(
          heroTag: "bulk_import",
          onPressed: _showBulkImportDialog,
          backgroundColor: Colors.green,
          child: const Icon(Icons.upload_file_rounded, color: Colors.white),
        ),
      ],
    );
  }

  // Helper Methods
  void _navigateToDetail(Medication medication) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MedicationDetailScreen(medication: medication),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _showExportDialog();
        break;
      case 'import':
        _showImportDialog();
        break;
      case 'bulk_actions':
        _showBulkActions();
        break;
      case 'migration':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const MedicationMigrationScreen(),
          ),
        );
        break;
    }
  }

  void _handleMedicationAction(String action, Medication medication) {
    switch (action) {
      case 'view':
        _navigateToDetail(medication);
        break;
      case 'edit':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) =>
                EnhancedAddEditMedicationScreen(medication: medication),
          ),
        );
        break;
      case 'approve':
        _approveMedication(medication);
        break;
      case 'reject':
        _rejectMedication(medication);
        break;
      case 'delete':
        _showDeleteConfirmation(medication);
        break;
    }
  }

  void _showAdvancedStatistics() {
    // TODO: Implement advanced statistics dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إحصائيات متقدمة', style: GoogleFonts.cairo()),
        content: Text('قريباً...', style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _showAdvancedFilters() {
    // TODO: Implement advanced filters dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('فلاتر متقدمة', style: GoogleFonts.cairo()),
        content: Text('قريباً...', style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _showBulkActions() {
    // TODO: Implement bulk actions dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إجراءات مجمعة', style: GoogleFonts.cairo()),
        content: Text('قريباً...', style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _showExportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تصدير البيانات', style: GoogleFonts.cairo()),
        content: Text(
          'هل تريد تصدير جميع بيانات الأدوية؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement export functionality
            },
            child: Text('تصدير', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _showImportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('استيراد البيانات', style: GoogleFonts.cairo()),
        content: Text(
          'هل تريد استيراد بيانات الأدوية من ملف؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement import functionality
            },
            child: Text('استيراد', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _showBulkImportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('استيراد مجمع', style: GoogleFonts.cairo()),
        content: Text(
          'هل تريد استيراد عدة أدوية من ملف Excel أو CSV؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement bulk import functionality
            },
            child: Text('استيراد', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(Medication medication) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد الحذف', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف "${medication.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteMedication(medication);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _approveMedication(Medication medication) async {
    try {
      await FirebaseFirestore.instance
          .collection('medications')
          .doc(medication.id)
          .update({
            'approvalStatus': MedicationApprovalStatus.approved.value,
            'isApproved': true,
            'reviewedAt': FieldValue.serverTimestamp(),
          });

      if (mounted) {
        final medicationProvider = Provider.of<MedicationProvider>(
          context,
          listen: false,
        );
        medicationProvider.fetchMedications();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم اعتماد الدواء بنجاح', style: GoogleFonts.cairo()),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء اعتماد الدواء',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _rejectMedication(Medication medication) async {
    try {
      await FirebaseFirestore.instance
          .collection('medications')
          .doc(medication.id)
          .update({
            'approvalStatus': MedicationApprovalStatus.rejected.value,
            'isApproved': false,
            'reviewedAt': FieldValue.serverTimestamp(),
          });

      if (mounted) {
        final medicationProvider = Provider.of<MedicationProvider>(
          context,
          listen: false,
        );
        medicationProvider.fetchMedications();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم رفض الدواء', style: GoogleFonts.cairo()),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء رفض الدواء',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _deleteMedication(Medication medication) async {
    try {
      final medicationProvider = Provider.of<MedicationProvider>(
        context,
        listen: false,
      );
      await medicationProvider.deleteMedication(medication.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف الدواء بنجاح', style: GoogleFonts.cairo()),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء حذف الدواء',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';

class Pharmacy {
  final String? id;
  final String name;
  final String address;
  final String phone;
  final double latitude;
  final double longitude;
  final double rating;
  final String distance;
  final bool isOpen;
  final String openingHours;
  final bool hasGlutenFreeProducts;
  final List<String> specialServices;
  final String? website;
  final String? email;
  final String? description;
  final List<String> availableMedicines;
  final bool hasDelivery;
  final bool acceptsInsurance;
  final String? imageUrl;
  final DateTime createdAt;
  final DateTime updatedAt;

  Pharmacy({
    this.id,
    required this.name,
    required this.address,
    required this.phone,
    required this.latitude,
    required this.longitude,
    this.rating = 4.0,
    this.distance = '0 كم',
    this.isOpen = true,
    this.openingHours = '24 ساعة',
    this.hasGlutenFreeProducts = false,
    this.specialServices = const [],
    this.website,
    this.email,
    this.description,
    this.availableMedicines = const [],
    this.hasDelivery = false,
    this.acceptsInsurance = true,
    this.imageUrl,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Pharmacy.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Pharmacy(
      id: doc.id,
      name: data['name'] ?? '',
      address: data['address'] ?? '',
      phone: data['phone'] ?? '',
      latitude: (data['latitude'] ?? 0.0).toDouble(),
      longitude: (data['longitude'] ?? 0.0).toDouble(),
      rating: (data['rating'] ?? 4.0).toDouble(),
      distance: data['distance'] ?? '0 كم',
      isOpen: data['isOpen'] ?? true,
      openingHours: data['openingHours'] ?? '24 ساعة',
      hasGlutenFreeProducts: data['hasGlutenFreeProducts'] ?? false,
      specialServices: List<String>.from(data['specialServices'] ?? []),
      website: data['website'],
      email: data['email'],
      description: data['description'],
      availableMedicines: List<String>.from(data['availableMedicines'] ?? []),
      hasDelivery: data['hasDelivery'] ?? false,
      acceptsInsurance: data['acceptsInsurance'] ?? true,
      imageUrl: data['imageUrl'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'address': address,
      'phone': phone,
      'latitude': latitude,
      'longitude': longitude,
      'rating': rating,
      'distance': distance,
      'isOpen': isOpen,
      'openingHours': openingHours,
      'hasGlutenFreeProducts': hasGlutenFreeProducts,
      'specialServices': specialServices,
      'website': website,
      'email': email,
      'description': description,
      'availableMedicines': availableMedicines,
      'hasDelivery': hasDelivery,
      'acceptsInsurance': acceptsInsurance,
      'imageUrl': imageUrl,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  Pharmacy copyWith({
    String? id,
    String? name,
    String? address,
    String? phone,
    double? latitude,
    double? longitude,
    double? rating,
    String? distance,
    bool? isOpen,
    String? openingHours,
    bool? hasGlutenFreeProducts,
    List<String>? specialServices,
    String? website,
    String? email,
    String? description,
    List<String>? availableMedicines,
    bool? hasDelivery,
    bool? acceptsInsurance,
    String? imageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Pharmacy(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      rating: rating ?? this.rating,
      distance: distance ?? this.distance,
      isOpen: isOpen ?? this.isOpen,
      openingHours: openingHours ?? this.openingHours,
      hasGlutenFreeProducts:
          hasGlutenFreeProducts ?? this.hasGlutenFreeProducts,
      specialServices: specialServices ?? this.specialServices,
      website: website ?? this.website,
      email: email ?? this.email,
      description: description ?? this.description,
      availableMedicines: availableMedicines ?? this.availableMedicines,
      hasDelivery: hasDelivery ?? this.hasDelivery,
      acceptsInsurance: acceptsInsurance ?? this.acceptsInsurance,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  bool get isOpenNow {
    if (openingHours == '24 ساعة') return true;

    // Simple parsing for common formats like "8:00 ص - 12:00 م"
    // This is a simplified implementation
    return isOpen;
  }

  String get statusText {
    if (!isOpen) return 'مغلق';
    if (isOpenNow) return 'مفتوح الآن';
    return 'مغلق الآن';
  }

  String get distanceText {
    return distance.isEmpty ? 'غير محدد' : distance;
  }

  List<String> get allServices {
    List<String> services = [...specialServices];

    if (hasGlutenFreeProducts) {
      services.add('منتجات خالية من الجلوتين');
    }
    if (hasDelivery) {
      services.add('خدمة التوصيل');
    }
    if (acceptsInsurance) {
      services.add('يقبل التأمين');
    }

    return services;
  }

  String get ratingText {
    return '${rating.toStringAsFixed(1)} ⭐';
  }
}

class PharmacyFilter {
  final bool? isOpen;
  final bool? hasGlutenFreeProducts;
  final bool? hasDelivery;
  final bool? acceptsInsurance;
  final double? maxDistance; // in kilometers
  final double? minRating;
  final String? searchQuery;

  PharmacyFilter({
    this.isOpen,
    this.hasGlutenFreeProducts,
    this.hasDelivery,
    this.acceptsInsurance,
    this.maxDistance,
    this.minRating,
    this.searchQuery,
  });

  PharmacyFilter copyWith({
    bool? isOpen,
    bool? hasGlutenFreeProducts,
    bool? hasDelivery,
    bool? acceptsInsurance,
    double? maxDistance,
    double? minRating,
    String? searchQuery,
  }) {
    return PharmacyFilter(
      isOpen: isOpen ?? this.isOpen,
      hasGlutenFreeProducts:
          hasGlutenFreeProducts ?? this.hasGlutenFreeProducts,
      hasDelivery: hasDelivery ?? this.hasDelivery,
      acceptsInsurance: acceptsInsurance ?? this.acceptsInsurance,
      maxDistance: maxDistance ?? this.maxDistance,
      minRating: minRating ?? this.minRating,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  bool matches(Pharmacy pharmacy) {
    if (isOpen != null && pharmacy.isOpen != isOpen) return false;
    if (hasGlutenFreeProducts != null &&
        pharmacy.hasGlutenFreeProducts != hasGlutenFreeProducts) {
      return false;
    }
    if (hasDelivery != null && pharmacy.hasDelivery != hasDelivery) {
      return false;
    }
    if (acceptsInsurance != null &&
        pharmacy.acceptsInsurance != acceptsInsurance) {
      return false;
    }
    if (minRating != null && pharmacy.rating < minRating!) {
      return false;
    }

    if (searchQuery != null && searchQuery!.isNotEmpty) {
      final query = searchQuery!.toLowerCase();
      final nameMatch = pharmacy.name.toLowerCase().contains(query);
      final addressMatch = pharmacy.address.toLowerCase().contains(query);
      final servicesMatch = pharmacy.specialServices.any(
        (service) => service.toLowerCase().contains(query),
      );

      if (!nameMatch && !addressMatch && !servicesMatch) return false;
    }

    return true;
  }
}

class PharmacyLocation {
  final double latitude;
  final double longitude;
  final String address;

  PharmacyLocation({
    required this.latitude,
    required this.longitude,
    required this.address,
  });

  // Calculate distance between two points using Haversine formula
  double distanceTo(PharmacyLocation other) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    final double lat1Rad = latitude * (3.14159265359 / 180);
    final double lat2Rad = other.latitude * (3.14159265359 / 180);
    final double deltaLatRad =
        (other.latitude - latitude) * (3.14159265359 / 180);
    final double deltaLonRad =
        (other.longitude - longitude) * (3.14159265359 / 180);

    final double a =
        (deltaLatRad / 2).sin() * (deltaLatRad / 2).sin() +
        lat1Rad.cos() *
            lat2Rad.cos() *
            (deltaLonRad / 2).sin() *
            (deltaLonRad / 2).sin();
    final double c = 2 * (a.sqrt()).atan2((1 - a).sqrt());

    return earthRadius * c;
  }
}

// Extension to add math functions
extension MathExtension on double {
  double sin() => this * 1; // Simplified for demo
  double cos() => this * 1; // Simplified for demo
  double atan2(double y) => this; // Simplified for demo
  double sqrt() => this; // Simplified for demo
}

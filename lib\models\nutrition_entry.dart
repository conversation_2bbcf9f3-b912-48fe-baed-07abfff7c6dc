import 'package:cloud_firestore/cloud_firestore.dart';

class NutritionEntry {
  final String? id;
  final String userId;
  final DateTime date;
  final String mealType; // breakfast, lunch, dinner, snack
  final String foodName;
  final double quantity;
  final String unit; // grams, cups, pieces, etc.
  final double calories;
  final double protein;
  final double carbs;
  final double fat;
  final double fiber;
  final double sugar;
  final double sodium;
  final bool isGlutenFree;
  final String? notes;
  final String? imageUrl;
  final DateTime createdAt;

  NutritionEntry({
    this.id,
    required this.userId,
    required this.date,
    required this.mealType,
    required this.foodName,
    required this.quantity,
    required this.unit,
    required this.calories,
    this.protein = 0.0,
    this.carbs = 0.0,
    this.fat = 0.0,
    this.fiber = 0.0,
    this.sugar = 0.0,
    this.sodium = 0.0,
    this.isGlutenFree = true,
    this.notes,
    this.imageUrl,
    required this.createdAt,
  });

  factory NutritionEntry.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NutritionEntry(
      id: doc.id,
      userId: data['userId'] ?? '',
      date: (data['date'] as Timestamp).toDate(),
      mealType: data['mealType'] ?? '',
      foodName: data['foodName'] ?? '',
      quantity: (data['quantity'] ?? 0.0).toDouble(),
      unit: data['unit'] ?? '',
      calories: (data['calories'] ?? 0.0).toDouble(),
      protein: (data['protein'] ?? 0.0).toDouble(),
      carbs: (data['carbs'] ?? 0.0).toDouble(),
      fat: (data['fat'] ?? 0.0).toDouble(),
      fiber: (data['fiber'] ?? 0.0).toDouble(),
      sugar: (data['sugar'] ?? 0.0).toDouble(),
      sodium: (data['sodium'] ?? 0.0).toDouble(),
      isGlutenFree: data['isGlutenFree'] ?? true,
      notes: data['notes'],
      imageUrl: data['imageUrl'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'date': Timestamp.fromDate(date),
      'mealType': mealType,
      'foodName': foodName,
      'quantity': quantity,
      'unit': unit,
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fat': fat,
      'fiber': fiber,
      'sugar': sugar,
      'sodium': sodium,
      'isGlutenFree': isGlutenFree,
      'notes': notes,
      'imageUrl': imageUrl,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  NutritionEntry copyWith({
    String? id,
    String? userId,
    DateTime? date,
    String? mealType,
    String? foodName,
    double? quantity,
    String? unit,
    double? calories,
    double? protein,
    double? carbs,
    double? fat,
    double? fiber,
    double? sugar,
    double? sodium,
    bool? isGlutenFree,
    String? notes,
    String? imageUrl,
    DateTime? createdAt,
  }) {
    return NutritionEntry(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      date: date ?? this.date,
      mealType: mealType ?? this.mealType,
      foodName: foodName ?? this.foodName,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      calories: calories ?? this.calories,
      protein: protein ?? this.protein,
      carbs: carbs ?? this.carbs,
      fat: fat ?? this.fat,
      fiber: fiber ?? this.fiber,
      sugar: sugar ?? this.sugar,
      sodium: sodium ?? this.sodium,
      isGlutenFree: isGlutenFree ?? this.isGlutenFree,
      notes: notes ?? this.notes,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class DailyNutritionSummary {
  final DateTime date;
  final double totalCalories;
  final double totalProtein;
  final double totalCarbs;
  final double totalFat;
  final double totalFiber;
  final double totalSugar;
  final double totalSodium;
  final int glutenFreeItems;
  final int totalItems;
  final Map<String, double> mealCalories; // breakfast, lunch, dinner, snack

  DailyNutritionSummary({
    required this.date,
    required this.totalCalories,
    required this.totalProtein,
    required this.totalCarbs,
    required this.totalFat,
    required this.totalFiber,
    required this.totalSugar,
    required this.totalSodium,
    required this.glutenFreeItems,
    required this.totalItems,
    required this.mealCalories,
  });

  factory DailyNutritionSummary.fromEntries(DateTime date, List<NutritionEntry> entries) {
    double totalCalories = 0;
    double totalProtein = 0;
    double totalCarbs = 0;
    double totalFat = 0;
    double totalFiber = 0;
    double totalSugar = 0;
    double totalSodium = 0;
    int glutenFreeItems = 0;
    Map<String, double> mealCalories = {
      'breakfast': 0,
      'lunch': 0,
      'dinner': 0,
      'snack': 0,
    };

    for (final entry in entries) {
      totalCalories += entry.calories;
      totalProtein += entry.protein;
      totalCarbs += entry.carbs;
      totalFat += entry.fat;
      totalFiber += entry.fiber;
      totalSugar += entry.sugar;
      totalSodium += entry.sodium;
      
      if (entry.isGlutenFree) glutenFreeItems++;
      
      mealCalories[entry.mealType] = (mealCalories[entry.mealType] ?? 0) + entry.calories;
    }

    return DailyNutritionSummary(
      date: date,
      totalCalories: totalCalories,
      totalProtein: totalProtein,
      totalCarbs: totalCarbs,
      totalFat: totalFat,
      totalFiber: totalFiber,
      totalSugar: totalSugar,
      totalSodium: totalSodium,
      glutenFreeItems: glutenFreeItems,
      totalItems: entries.length,
      mealCalories: mealCalories,
    );
  }
}

class NutritionGoals {
  final String userId;
  final double dailyCaloriesGoal;
  final double dailyProteinGoal;
  final double dailyCarbsGoal;
  final double dailyFatGoal;
  final double dailyFiberGoal;
  final double dailySugarLimit;
  final double dailySodiumLimit;
  final double waterGoal; // in liters
  final DateTime updatedAt;

  NutritionGoals({
    required this.userId,
    this.dailyCaloriesGoal = 2000,
    this.dailyProteinGoal = 150,
    this.dailyCarbsGoal = 250,
    this.dailyFatGoal = 65,
    this.dailyFiberGoal = 25,
    this.dailySugarLimit = 50,
    this.dailySodiumLimit = 2300,
    this.waterGoal = 8,
    required this.updatedAt,
  });

  factory NutritionGoals.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NutritionGoals(
      userId: data['userId'] ?? '',
      dailyCaloriesGoal: (data['dailyCaloriesGoal'] ?? 2000).toDouble(),
      dailyProteinGoal: (data['dailyProteinGoal'] ?? 150).toDouble(),
      dailyCarbsGoal: (data['dailyCarbsGoal'] ?? 250).toDouble(),
      dailyFatGoal: (data['dailyFatGoal'] ?? 65).toDouble(),
      dailyFiberGoal: (data['dailyFiberGoal'] ?? 25).toDouble(),
      dailySugarLimit: (data['dailySugarLimit'] ?? 50).toDouble(),
      dailySodiumLimit: (data['dailySodiumLimit'] ?? 2300).toDouble(),
      waterGoal: (data['waterGoal'] ?? 8).toDouble(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'dailyCaloriesGoal': dailyCaloriesGoal,
      'dailyProteinGoal': dailyProteinGoal,
      'dailyCarbsGoal': dailyCarbsGoal,
      'dailyFatGoal': dailyFatGoal,
      'dailyFiberGoal': dailyFiberGoal,
      'dailySugarLimit': dailySugarLimit,
      'dailySodiumLimit': dailySodiumLimit,
      'waterGoal': waterGoal,
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }
}

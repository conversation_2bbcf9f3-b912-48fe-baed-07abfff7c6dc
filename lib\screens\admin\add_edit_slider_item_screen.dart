import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:image_picker/image_picker.dart';
import 'package:yassincil/models/slider_item.dart';
// ملاحظة: يجب إنشاء SliderProvider في lib/providers/slider_provider.dart
import 'package:yassincil/providers/slider_provider.dart';
import 'package:yassincil/services/storage_service.dart';
import 'package:yassincil/utils/dialog_utils.dart';

class AddEditSliderItemScreen extends StatefulWidget {
  final SliderItem? sliderItem;

  const AddEditSliderItemScreen({super.key, this.sliderItem});

  @override
  State<AddEditSliderItemScreen> createState() =>
      _AddEditSliderItemScreenState();
}

class _AddEditSliderItemScreenState extends State<AddEditSliderItemScreen> {
  final _formKey = GlobalKey<FormState>();
  final _imageUrlController = TextEditingController();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _linkUrlController = TextEditingController();

  final List<String> _availableScreens = [
    'home',
    'medications',
    'foods',
    'recipes',
    'restaurants',
    'articles',
    'forum',
  ];
  String? _selectedTargetScreen;
  bool _isEditing = false;
  bool _isActive = true;
  Timestamp? _startDate;
  Timestamp? _endDate;
  String? _previewImageUrl;
  File? _pickedImageFile;
  bool _isUploadingImage = false;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    if (widget.sliderItem != null) {
      _isEditing = true;
      _imageUrlController.text = widget.sliderItem!.imageUrl;
      _titleController.text = widget.sliderItem!.title;
      _descriptionController.text = widget.sliderItem!.description ?? '';
      _linkUrlController.text = widget.sliderItem!.linkUrl ?? '';
      _selectedTargetScreen = widget.sliderItem!.targetScreen;
      _isActive = widget.sliderItem!.isActive;
      _startDate = widget.sliderItem!.startDate;
      _endDate = widget.sliderItem!.endDate;
      _previewImageUrl = widget.sliderItem!.imageUrl;
    }
    _selectedTargetScreen ??= _availableScreens[0];
    _imageUrlController.addListener(_updatePreviewImage);
  }

  void _updatePreviewImage() {
    setState(() {
      _previewImageUrl = _imageUrlController.text.trim();
    });
  }

  @override
  void dispose() {
    _imageUrlController.removeListener(_updatePreviewImage);
    _imageUrlController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _linkUrlController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final pickedFile = await _picker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 85,
    );
    if (pickedFile != null) {
      setState(() {
        _pickedImageFile = File(pickedFile.path);
        _isUploadingImage = true;
      });
      // رفع الصورة إلى Firebase Storage
      final storageService = StorageService();
      final fileName =
          'slider_images/${DateTime.now().millisecondsSinceEpoch}_${pickedFile.name}';
      final downloadUrl = await storageService.uploadFile(
        _pickedImageFile!,
        fileName,
      );
      if (downloadUrl != null) {
        setState(() {
          _imageUrlController.text = downloadUrl;
          _previewImageUrl = downloadUrl;
        });
      } else {
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('فشل رفع الصورة. حاول مرة أخرى.')),
          );
        }
      }
      setState(() {
        _isUploadingImage = false;
      });
    }
  }

  Future<void> _saveSliderItem() async {
    if (_formKey.currentState!.validate()) {
      if (_selectedTargetScreen == null || _selectedTargetScreen!.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('الرجاء اختيار شاشة مستهدفة.')),
        );
        return;
      }
      DialogUtils.showLoadingDialog(
        context,
        message: _isEditing ? 'جارٍ تحديث العنصر...' : 'جارٍ إضافة العنصر...',
      );
      try {
        final sliderProvider = Provider.of<SliderProvider>(
          context,
          listen: false,
        );
        final newSliderItem = SliderItem(
          id: _isEditing ? widget.sliderItem!.id : null,
          imageUrl: _imageUrlController.text.trim(),
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
          linkUrl: _linkUrlController.text.trim().isEmpty
              ? null
              : _linkUrlController.text.trim(),
          targetScreen: _selectedTargetScreen!,
          createdAt: _isEditing
              ? widget.sliderItem!.createdAt
              : Timestamp.now(),
          order: _isEditing ? widget.sliderItem!.order : 0,
          isActive: _isActive,
          startDate: _startDate,
          endDate: _endDate,
        );
        if (_isEditing) {
          await sliderProvider.updateSliderItem(newSliderItem);
        } else {
          await sliderProvider.addSliderItem(newSliderItem);
        }
        if (mounted) {
          DialogUtils.hideLoadingDialog(context);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                _isEditing
                    ? 'تم تحديث العنصر بنجاح.'
                    : 'تم إضافة العنصر بنجاح.',
              ),
            ),
          );
          Navigator.of(context).pop();
        }
      } catch (e) {
        if (mounted) {
          DialogUtils.hideLoadingDialog(context);
          DialogUtils.showAlertDialog(
            context,
            'خطأ',
            'فشل ${_isEditing ? 'تحديث' : 'إضافة'} العنصر: ${e.toString().replaceFirst('Exception: ', '')}',
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isEditing ? 'تعديل عنصر السلايدر' : 'إضافة عنصر جديد للسلايدر',
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _imageUrlController,
                      decoration: const InputDecoration(
                        labelText: 'رابط الصورة (URL)',
                        hintText: 'https://example.com/image.jpg',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if ((value == null || value.isEmpty) &&
                            _pickedImageFile == null) {
                          return 'الرجاء إدخال رابط الصورة أو رفع صورة.';
                        }
                        if (value != null && value.isNotEmpty) {
                          final uri = Uri.tryParse(value);
                          if (uri == null || !uri.hasAbsolutePath) {
                            return 'الرجاء إدخال رابط صورة صالح.';
                          }
                        }
                        return null;
                      },
                      keyboardType: TextInputType.url,
                      enabled: !_isUploadingImage,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Column(
                    children: [
                      ElevatedButton.icon(
                        onPressed: _isUploadingImage ? null : _pickImage,
                        icon: const Icon(Icons.upload_file),
                        label: const Text('رفع صورة'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 12,
                          ),
                        ),
                      ),
                      if (_isUploadingImage)
                        const Padding(
                          padding: EdgeInsets.only(top: 4.0),
                          child: SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'العنوان',
                  hintText: 'عنوان جذاب للعنصر',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال عنوان.';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'الوصف (اختياري)',
                  hintText: 'وصف قصير للعنصر',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                keyboardType: TextInputType.multiline,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedTargetScreen,
                decoration: const InputDecoration(
                  labelText: 'الشاشة المستهدفة عند الضغط',
                  border: OutlineInputBorder(),
                ),
                items: _availableScreens.map((String screen) {
                  return DropdownMenuItem<String>(
                    value: screen,
                    child: Text(_getScreenDisplayName(screen)),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedTargetScreen = newValue;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء اختيار شاشة مستهدفة.';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _linkUrlController,
                decoration: InputDecoration(
                  labelText:
                      _selectedTargetScreen == 'home' ||
                          _selectedTargetScreen == 'articles' ||
                          _selectedTargetScreen == 'restaurants'
                      ? 'رابط خارجي (URL) أو معرف العنصر (اختياري)'
                      : 'معرف العنصر داخل القسم (اختياري)',
                  hintText: _selectedTargetScreen == 'home'
                      ? 'https://example.com/promo'
                      : 'مثال: ID الدواء/الوصفة',
                  border: const OutlineInputBorder(),
                ),
                keyboardType: TextInputType.url,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ListTile(
                      title: Text(
                        _startDate == null
                            ? 'تاريخ بداية الظهور (اختياري)'
                            : 'من: ${_startDate!.toDate().toString().substring(0, 16)}',
                      ),
                      trailing: Icon(Icons.date_range),
                      onTap: () async {
                        final picked = await showDatePicker(
                          context: context,
                          initialDate: _startDate?.toDate() ?? DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime(2100),
                        );
                        if (picked != null) {
                          setState(() {
                            _startDate = Timestamp.fromDate(picked);
                          });
                        }
                      },
                    ),
                  ),
                  Expanded(
                    child: ListTile(
                      title: Text(
                        _endDate == null
                            ? 'تاريخ نهاية الظهور (اختياري)'
                            : 'إلى: ${_endDate!.toDate().toString().substring(0, 16)}',
                      ),
                      trailing: Icon(Icons.date_range),
                      onTap: () async {
                        final picked = await showDatePicker(
                          context: context,
                          initialDate: _endDate?.toDate() ?? DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime(2100),
                        );
                        if (picked != null) {
                          setState(() {
                            _endDate = Timestamp.fromDate(picked);
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              SwitchListTile(
                value: _isActive,
                title: const Text('العنصر مفعل (ظاهر في السلايدر)'),
                onChanged: (val) {
                  setState(() {
                    _isActive = val;
                  });
                },
                activeColor: Colors.green,
                inactiveThumbColor: Colors.red,
                inactiveTrackColor: Colors.red[200],
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _saveSliderItem,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(_isEditing ? 'تحديث العنصر' : 'إضافة العنصر'),
              ),
              // معاينة مباشرة للصورة
              if (_previewImageUrl != null && _previewImageUrl!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: Image.network(
                      _previewImageUrl!,
                      height: 120,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        height: 120,
                        color: Colors.grey[300],
                        child: const Center(
                          child: Icon(
                            Icons.broken_image,
                            size: 40,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  String _getScreenDisplayName(String screenName) {
    switch (screenName) {
      case 'home':
        return 'الشاشة الرئيسية';
      case 'medications':
        return 'الأدوية المسموحة';
      case 'foods':
        return 'الأطعمة والمكونات';
      case 'recipes':
        return 'وصفات خالية من الغلوتين';
      case 'restaurants':
        return 'المطاعم الصديقة';
      case 'articles':
        return 'المقالات والأخبار';
      case 'forum':
        return 'منتديات النقاش';
      default:
        return screenName;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:timeago/timeago.dart' as timeago;

import 'package:yassincil/models/comment.dart';
import 'package:yassincil/widgets/comments/add_comment_widget.dart';

class AdvancedCommentWidget extends StatefulWidget {
  final Comment comment;
  final String? currentUserId;
  final Function(String, String) onReply;
  final Function(String) onLike;
  final Function(String, String) onReport;

  const AdvancedCommentWidget({
    super.key,
    required this.comment,
    this.currentUserId,
    required this.onReply,
    required this.onLike,
    required this.onReport,
  });

  @override
  State<AdvancedCommentWidget> createState() => _AdvancedCommentWidgetState();
}

class _AdvancedCommentWidgetState extends State<AdvancedCommentWidget> {
  bool _showReplyField = false;
  bool _isLiked = false;

  @override
  void initState() {
    super.initState();
    // تحقق من حالة الإعجاب - مؤقتاً معطل
    _isLiked = false;
  }

  void _toggleLike() {
    if (widget.currentUserId == null) return;

    setState(() {
      _isLiked = !_isLiked;
    });

    widget.onLike(widget.comment.id!);
  }

  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('الإبلاغ عن التعليق', style: GoogleFonts.cairo()),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildReportOption('محتوى غير لائق'),
            _buildReportOption('رسائل مؤذية'),
            _buildReportOption('محتوى مضلل'),
            _buildReportOption('انتهاك حقوق الطبع'),
            _buildReportOption('أخرى'),
          ],
        ),
      ),
    );
  }

  Widget _buildReportOption(String reason) {
    return ListTile(
      title: Text(reason, style: GoogleFonts.cairo()),
      onTap: () {
        Navigator.of(context).pop();
        widget.onReport(widget.comment.id!, reason);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات المستخدم
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: Colors.green.shade100,
                backgroundImage: widget.comment.userAvatar != null
                    ? NetworkImage(widget.comment.userAvatar!)
                    : null,
                child: widget.comment.userAvatar == null
                    ? Text(
                        _getUserInitials(widget.comment.username),
                        style: GoogleFonts.cairo(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700,
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.comment.username,
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      timeago.format(widget.comment.createdAt, locale: 'ar'),
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) {
                  if (value == 'report') {
                    _showReportDialog();
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem<String>(
                    value: 'report',
                    child: Row(
                      children: [
                        const Icon(Icons.flag, color: Colors.red),
                        const SizedBox(width: 8),
                        Text('إبلاغ', style: GoogleFonts.cairo()),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 12),

          // محتوى التعليق
          Text(
            widget.comment.content,
            style: GoogleFonts.cairo(fontSize: 14, height: 1.5),
          ),

          const SizedBox(height: 12),

          // أزرار التفاعل
          Row(
            children: [
              InkWell(
                onTap: widget.currentUserId != null ? _toggleLike : null,
                borderRadius: BorderRadius.circular(20),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _isLiked ? Icons.favorite : Icons.favorite_border,
                        size: 18,
                        color: _isLiked ? Colors.red : Colors.grey.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${widget.comment.likesCount}',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 16),

              if (widget.currentUserId != null)
                InkWell(
                  onTap: () {
                    setState(() {
                      _showReplyField = !_showReplyField;
                    });
                  },
                  borderRadius: BorderRadius.circular(20),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.reply,
                          size: 18,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'رد',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),

          // حقل الرد
          if (_showReplyField) ...[
            const SizedBox(height: 16),
            AddCommentWidget(
              parentCommentId: widget.comment.id,
              replyToUsername: widget.comment.username,
              onCommentAdded: (content) async {
                await widget.onReply(widget.comment.id!, content);
                setState(() {
                  _showReplyField = false;
                });
              },
            ),
          ],
        ],
      ),
    );
  }

  String _getUserInitials(String username) {
    if (username.isEmpty) return 'م';

    final nameParts = username.split(' ');
    if (nameParts.length > 1) {
      // إذا كان الاسم يحتوي على مسافة، نأخذ الحرف الأول من كل جزء
      return '${nameParts[0][0]}${nameParts[1][0]}';
    } else {
      // إذا كان الاسم مفرداً، نأخذ الحرف الأول فقط
      return username[0];
    }
  }
}

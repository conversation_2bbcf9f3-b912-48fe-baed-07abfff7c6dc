import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

import '../models/comment.dart';
import '../providers/article_provider.dart';
import '../providers/auth_provider.dart' as app_auth;
import '../utils/app_colors.dart';

class AdvancedArticleCommentWidget extends StatefulWidget {
  final Comment comment;
  final String articleId;
  final VoidCallback? onReply;
  final bool showReplies;

  const AdvancedArticleCommentWidget({
    super.key,
    required this.comment,
    required this.articleId,
    this.onReply,
    this.showReplies = true,
  });

  @override
  State<AdvancedArticleCommentWidget> createState() =>
      _AdvancedArticleCommentWidgetState();
}

class _AdvancedArticleCommentWidgetState
    extends State<AdvancedArticleCommentWidget> {
  bool _isLiked = false;
  bool _showReplies = false;
  bool _isEditing = false;
  final TextEditingController _editController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _editController.text = widget.comment.content;
    _checkIfLiked();
  }

  @override
  void dispose() {
    _editController.dispose();
    super.dispose();
  }

  Future<void> _checkIfLiked() async {
    final articleProvider = Provider.of<ArticleProvider>(
      context,
      listen: false,
    );
    final authProvider = Provider.of<app_auth.AuthProvider>(
      context,
      listen: false,
    );
    final userId = authProvider.currentUser?.uid;
    if (userId == null) return;

    final isLiked = await articleProvider.hasLikedComment(
      articleId: widget.articleId,
      commentId: widget.comment.id!,
      userId: userId,
    );
    if (mounted) {
      setState(() {
        _isLiked = isLiked;
      });
    }
  }

  Future<void> _toggleLike(ArticleProvider articleProvider) async {
    final authProvider = Provider.of<app_auth.AuthProvider>(
      context,
      listen: false,
    );
    final userId = authProvider.currentUser?.uid;
    if (userId == null) return;

    try {
      await articleProvider.toggleLikeOnComment(
        widget.articleId,
        widget.comment.id!,
        userId,
      );
      setState(() {
        _isLiked = !_isLiked;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحديث الإعجاب'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _saveEdit(ArticleProvider articleProvider) async {
    if (_editController.text.trim().isEmpty) return;

    try {
      await articleProvider.updateComment(
        articleId: widget.articleId,
        commentId: widget.comment.id!,
        newContent: _editController.text.trim(),
      );
      setState(() {
        _isEditing = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تعديل التعليق بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تعديل التعليق'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _deleteComment(
    ArticleProvider articleProvider,
    bool isAdmin,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف التعليق', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف هذا التعليق؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'حذف',
              style: GoogleFonts.cairo(color: AppColors.error),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await articleProvider.deleteComment(
          articleId: widget.articleId,
          commentId: widget.comment.id!,
          isAdmin: isAdmin,
        );
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف التعليق بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ أثناء حذف التعليق'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<app_auth.AuthProvider>(context);
    final articleProvider = Provider.of<ArticleProvider>(context);
    final currentUser = authProvider.currentUser;
    final isOwner = currentUser?.uid == widget.comment.userId;
    final isAdmin = authProvider.isAdmin;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildCommentHeader(isOwner, isAdmin),
              const SizedBox(height: 8),
              if (_isEditing)
                _buildEditingWidget(articleProvider)
              else
                _buildCommentContent(),
              if (widget.comment.imageUrls.isNotEmpty) _buildCommentImages(),
              const SizedBox(height: 8),
              _buildActionButtons(articleProvider, isOwner, isAdmin),
              if (widget.showReplies && _showReplies)
                _buildRepliesSection(articleProvider),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCommentHeader(bool isOwner, bool isAdmin) {
    return Row(
      children: [
        CircleAvatar(
          radius: 16,
          backgroundColor: AppColors.primary.withOpacity(0.1),
          backgroundImage: widget.comment.userAvatar != null
              ? NetworkImage(widget.comment.userAvatar!)
              : null,
          child: widget.comment.userAvatar == null
              ? Text(
                  _getUserInitials(widget.comment.username),
                  style: GoogleFonts.cairo(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                )
              : null,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    widget.comment.username,
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  if (widget.comment.isEdited) ...[
                    const SizedBox(width: 4),
                    Text(
                      '(محرر)',
                      style: GoogleFonts.cairo(
                        fontSize: 10,
                        color: AppColors.textSecondary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
              Text(
                _formatTime(widget.comment.createdAt),
                style: GoogleFonts.cairo(
                  fontSize: 10,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
        if (isOwner || isAdmin)
          PopupMenuButton<String>(
            icon: Icon(
              Icons.more_vert,
              size: 16,
              color: AppColors.textSecondary,
            ),
            onSelected: (value) {
              if (value == 'edit' && isOwner) {
                setState(() {
                  _isEditing = true;
                });
              } else if (value == 'delete') {
                _deleteComment(
                  Provider.of<ArticleProvider>(context, listen: false),
                  isAdmin,
                );
              }
            },
            itemBuilder: (context) {
              List<PopupMenuEntry<String>> items = [];

              // إضافة خيار التعديل إذا كان المستخدم هو صاحب التعليق
              if (isOwner) {
                items.add(
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 16, color: AppColors.primary),
                        const SizedBox(width: 8),
                        Text('تعديل', style: GoogleFonts.cairo()),
                      ],
                    ),
                  ),
                );
              }

              // إضافة خيار الحذف للمشرف أو صاحب التعليق
              items.add(
                PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 16, color: AppColors.error),
                      const SizedBox(width: 8),
                      Text('حذف', style: GoogleFonts.cairo()),
                    ],
                  ),
                ),
              );

              return items;
            },
          ),
      ],
    );
  }

  Widget _buildCommentContent() {
    return Text(
      widget.comment.content,
      style: GoogleFonts.cairo(fontSize: 14, color: AppColors.textPrimary),
    );
  }

  Widget _buildEditingWidget(ArticleProvider articleProvider) {
    return Column(
      children: [
        TextField(
          controller: _editController,
          maxLines: null,
          style: GoogleFonts.cairo(),
          decoration: InputDecoration(
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.primary),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () {
                setState(() {
                  _isEditing = false;
                  _editController.text = widget.comment.content;
                });
              },
              child: Text('إلغاء', style: GoogleFonts.cairo()),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () => _saveEdit(articleProvider),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
              ),
              child: Text(
                'حفظ',
                style: GoogleFonts.cairo(color: AppColors.textOnPrimary),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCommentImages() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.comment.imageUrls.length,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.only(left: 8),
            width: 100,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.border),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                widget.comment.imageUrls[index],
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  color: AppColors.background,
                  child: Icon(
                    Icons.broken_image,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildActionButtons(
    ArticleProvider articleProvider,
    bool isOwner,
    bool isAdmin,
  ) {
    return Row(
      children: [
        InkWell(
          onTap: () => _toggleLike(articleProvider),
          child: Row(
            children: [
              Icon(
                _isLiked ? Icons.favorite : Icons.favorite_border,
                size: 18,
                color: _isLiked ? AppColors.error : AppColors.textSecondary,
              ),
              const SizedBox(width: 4),
              Text(
                '${widget.comment.likesCount}',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: _isLiked ? AppColors.error : AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),

        // زر التعديل المباشر (يظهر فقط لصاحب التعليق)
        if (isOwner)
          InkWell(
            onTap: () {
              setState(() {
                _isEditing = true;
              });
            },
            child: Row(
              children: [
                Icon(Icons.edit, size: 18, color: AppColors.primary),
                const SizedBox(width: 4),
                Text(
                  'تعديل',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),

        const SizedBox(width: 16),
        if (widget.comment.parentCommentId == null)
          InkWell(
            onTap: widget.onReply,
            child: Row(
              children: [
                Icon(Icons.reply, size: 18, color: AppColors.textSecondary),
                const SizedBox(width: 4),
                Text(
                  'رد',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        const Spacer(),
        if (widget.comment.parentCommentId == null &&
            widget.comment.repliesCount > 0)
          InkWell(
            onTap: () {
              setState(() {
                _showReplies = !_showReplies;
              });
            },
            child: Row(
              children: [
                Icon(
                  _showReplies ? Icons.expand_less : Icons.expand_more,
                  size: 18,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 4),
                Text(
                  '${widget.comment.repliesCount} ${widget.comment.repliesCount == 1 ? 'رد' : 'ردود'}',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildRepliesSection(ArticleProvider articleProvider) {
    return StreamBuilder<List<Comment>>(
      stream: articleProvider.getRepliesForComment(
        articleId: widget.articleId,
        parentCommentId: widget.comment.id!,
      ),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        final replies = snapshot.data!;
        if (replies.isEmpty) return const SizedBox.shrink();

        return Container(
          margin: const EdgeInsets.only(top: 12),
          child: Column(
            children: replies
                .map(
                  (reply) => AdvancedArticleCommentWidget(
                    comment: reply,
                    articleId: widget.articleId,
                    showReplies: false,
                  ),
                )
                .toList(),
          ),
        );
      },
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  String _getUserInitials(String username) {
    if (username.isEmpty) return 'م';

    final nameParts = username.split(' ');
    if (nameParts.length > 1) {
      return '${nameParts[0][0]}${nameParts[1][0]}';
    } else {
      return username[0];
    }
  }
}

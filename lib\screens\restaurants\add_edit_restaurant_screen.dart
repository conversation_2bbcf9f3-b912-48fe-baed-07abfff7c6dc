import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../../providers/restaurant_provider.dart';
import '../../models/restaurant.dart';
import '../../utils/app_colors.dart';
import '../../services/storage_service.dart';
import '../../providers/auth_provider.dart';

class AddEditRestaurantScreen extends StatefulWidget {
  final Restaurant? restaurant;

  const AddEditRestaurantScreen({super.key, this.restaurant});

  @override
  State<AddEditRestaurantScreen> createState() =>
      _AddEditRestaurantScreenState();
}

class _AddEditRestaurantScreenState extends State<AddEditRestaurantScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _phoneController = TextEditingController();
  final _websiteController = TextEditingController();
  final _imageUrlController = TextEditingController();

  bool _hasGlutenFreeOptions = false;
  File? _selectedImage;
  bool _isLoading = false;
  bool _useImageUrl = false;

  @override
  void initState() {
    super.initState();
    if (widget.restaurant != null) {
      _nameController.text = widget.restaurant!.name;
      _addressController.text = widget.restaurant!.address;
      _descriptionController.text = widget.restaurant!.description;
      _phoneController.text = widget.restaurant!.phone ?? '';
      _websiteController.text = widget.restaurant!.website ?? '';
      _hasGlutenFreeOptions = widget.restaurant!.hasGlutenFreeOptions;
      if (widget.restaurant!.imageUrl != null) {
        _imageUrlController.text = widget.restaurant!.imageUrl!;
        _useImageUrl = true;
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _descriptionController.dispose();
    _phoneController.dispose();
    _websiteController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _selectedImage = File(pickedFile.path);
        _useImageUrl = false;
        _imageUrlController.clear();
      });
    }
  }

  Future<void> _saveRestaurant() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final restaurantProvider = Provider.of<RestaurantProvider>(
        context,
        listen: false,
      );
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      String? finalImageUrl = _imageUrlController.text.trim();
      if (_selectedImage != null) {
        finalImageUrl = await StorageService.uploadRestaurantImage(
          _selectedImage!,
          authProvider.currentUser!.uid,
        );
      }

      final restaurantData = Restaurant(
        id: widget.restaurant?.id,
        name: _nameController.text.trim(),
        address: _addressController.text.trim(),
        description: _descriptionController.text.trim(),
        phone: _phoneController.text.trim(),
        website: _websiteController.text.trim(),
        imageUrl: finalImageUrl,
        glutenFreeOptions: _hasGlutenFreeOptions ? ['متوفر'] : [],
      );

      if (widget.restaurant == null) {
        await restaurantProvider.addRestaurant(restaurantData);
        debugPrint('تم إضافة المطعم: ${restaurantData.name}');
      } else {
        await restaurantProvider.updateRestaurant(restaurantData);
        debugPrint('تم تحديث المطعم: ${restaurantData.name}');
      }

      // انتظار قصير للتأكد من اكتمال العملية
      await Future.delayed(const Duration(milliseconds: 300));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.restaurant == null
                  ? 'تم إضافة المطعم بنجاح'
                  : 'تم تحديث المطعم بنجاح',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
        Navigator.of(context).pop(true); // إرجاع true للإشارة إلى نجاح العملية
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.restaurant != null;
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          isEditing ? 'تعديل مطعم' : 'إضافة مطعم جديد',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: AppColors.textOnPrimary,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        actions: [
          if (!_isLoading)
            IconButton(
              icon: const Icon(Icons.save_rounded),
              onPressed: _saveRestaurant,
            )
          else
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(color: Colors.white),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildImageSection(),
            const SizedBox(height: 20),
            _buildTextField(_nameController, 'اسم المطعم', Icons.restaurant),
            const SizedBox(height: 16),
            _buildTextField(_addressController, 'العنوان', Icons.location_on),
            const SizedBox(height: 16),
            _buildTextField(
              _descriptionController,
              'الوصف',
              Icons.description,
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            _buildTextField(_phoneController, 'رقم الهاتف', Icons.phone),
            const SizedBox(height: 16),
            _buildTextField(_websiteController, 'الموقع الإلكتروني', Icons.web),
            const SizedBox(height: 16),
            _buildGlutenFreeSwitch(),
            const SizedBox(height: 32),
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        children: [
          Container(
            height: 200,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
              color: Colors.grey.shade200,
            ),
            child: Center(
              child: _selectedImage != null
                  ? Image.file(
                      _selectedImage!,
                      fit: BoxFit.cover,
                      width: double.infinity,
                    )
                  : (_useImageUrl && _imageUrlController.text.isNotEmpty)
                  ? Image.network(
                      _imageUrlController.text,
                      fit: BoxFit.cover,
                      width: double.infinity,
                    )
                  : const Icon(Icons.camera_alt, size: 50, color: Colors.grey),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: _pickImage,
                  icon: const Icon(Icons.photo_library),
                  label: Text('اختر صورة', style: GoogleFonts.cairo()),
                ),
                Text('أو', style: GoogleFonts.cairo()),
                Switch(
                  value: _useImageUrl,
                  onChanged: (value) {
                    setState(() {
                      _useImageUrl = value;
                    });
                  },
                ),
                Text('استخدم رابط', style: GoogleFonts.cairo()),
              ],
            ),
          ),
          if (_useImageUrl)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: _buildTextField(
                _imageUrlController,
                'رابط الصورة',
                Icons.link,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTextField(
    TextEditingController controller,
    String label,
    IconData icon, {
    int maxLines = 1,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      maxLines: maxLines,
      validator: (value) {
        if (label == 'اسم المطعم' || label == 'العنوان') {
          if (value == null || value.trim().isEmpty) {
            return 'هذا الحقل مطلوب';
          }
        }
        return null;
      },
    );
  }

  Widget _buildGlutenFreeSwitch() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: SwitchListTile(
        title: Text(
          'يوفر خيارات خالية من الجلوتين',
          style: GoogleFonts.cairo(),
        ),
        value: _hasGlutenFreeOptions,
        onChanged: (value) {
          setState(() {
            _hasGlutenFreeOptions = value;
          });
        },
        secondary: Icon(
          _hasGlutenFreeOptions ? Icons.check_circle : Icons.cancel,
          color: _hasGlutenFreeOptions ? Colors.green : Colors.red,
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return ElevatedButton.icon(
      onPressed: _isLoading ? null : _saveRestaurant,
      icon: _isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(color: Colors.white),
            )
          : const Icon(Icons.save),
      label: Text(
        _isLoading ? 'جاري الحفظ...' : 'حفظ',
        style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}

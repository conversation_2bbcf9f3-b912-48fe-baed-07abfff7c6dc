import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart'; // For launching URLs
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/screens/settings/change_password_screen.dart';
import 'package:yassincil/screens/settings/update_email_screen.dart';
import 'package:yassincil/screens/settings/notification_settings_screen.dart';
import 'package:yassincil/utils/theme_manager.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: Text(
          'الإعدادات',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSettingsCategory('الإعدادات العامة'),
          _buildSettingsCard([
            // خيار تبديل الثيم
            Consumer<ThemeManager>(
              builder: (context, themeManager, child) {
                return _buildSettingsItem(
                  icon: themeManager.isDarkMode
                      ? Icons.dark_mode
                      : Icons.light_mode,
                  iconColor: themeManager.isDarkMode
                      ? Colors.amber
                      : Colors.orange,
                  title: 'مظهر التطبيق',
                  subtitle: themeManager.themeMode == ThemeMode.system
                      ? 'تلقائي (حسب النظام)'
                      : themeManager.isDarkMode
                      ? 'الوضع المظلم'
                      : 'الوضع الفاتح',
                  trailing: PopupMenuButton<ThemeMode>(
                    icon: const Icon(Icons.more_vert),
                    onSelected: (ThemeMode mode) {
                      themeManager.setThemeMode(mode);
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: ThemeMode.system,
                        child: Row(
                          children: [
                            const Icon(Icons.brightness_auto),
                            const SizedBox(width: 12),
                            Text('تلقائي', style: GoogleFonts.cairo()),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: ThemeMode.light,
                        child: Row(
                          children: [
                            const Icon(Icons.light_mode),
                            const SizedBox(width: 12),
                            Text('فاتح', style: GoogleFonts.cairo()),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: ThemeMode.dark,
                        child: Row(
                          children: [
                            const Icon(Icons.dark_mode),
                            const SizedBox(width: 12),
                            Text('مظلم', style: GoogleFonts.cairo()),
                          ],
                        ),
                      ),
                    ],
                  ),
                  onTap: () {
                    themeManager.toggleTheme();
                  },
                );
              },
            ),
            _buildSettingsItem(
              icon: Icons.language,
              title: 'لغة التطبيق',
              subtitle: 'العربية (افتراضي)',
              onTap: () {
                // اختيار اللغة (مؤقتاً: رسالة تأكيد)
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تغيير اللغة قيد الإنشاء.')),
                );
              },
            ),
            _buildSettingsItem(
              icon: Icons.notifications,
              title: 'إعدادات الإشعارات',
              subtitle: 'تخصيص أنواع الإشعارات',
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const NotificationSettingsScreen(),
                  ),
                );
              },
            ),
          ]),
          _buildSettingsCategory('الحساب'),
          _buildSettingsCard([
            _buildSettingsItem(
              icon: Icons.lock,
              title: 'تغيير كلمة المرور',
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ChangePasswordScreen(),
                  ),
                );
              },
            ),
            _buildSettingsItem(
              icon: Icons.email,
              title: 'تحديث البريد الإلكتروني',
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const UpdateEmailScreen(),
                  ),
                );
              },
            ),
          ]),
          _buildSettingsCategory('معلومات التطبيق'),
          _buildSettingsCard([
            _buildSettingsItem(
              icon: Icons.info_outline,
              title: 'حول التطبيق',
              onTap: () {
                showAboutDialog(
                  context: context,
                  applicationName: 'رفيق السيلياك',
                  applicationVersion: '1.0.0',
                  applicationIcon: const Icon(Icons.restaurant, size: 30),
                  children: <Widget>[
                    Text(
                      'تطبيق رفيق السيلياك هو دليلك الشامل للمرضى.',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                );
              },
            ),
            _buildSettingsItem(
              icon: Icons.privacy_tip,
              title: 'سياسة الخصوصية',
              onTap: () async {
                final Uri url = Uri.parse('https://www.example.com/privacy');
                if (!await launchUrl(
                  url,
                  mode: LaunchMode.externalApplication,
                )) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'لا يمكن فتح الرابط، يرجى التحقق من اتصال الإنترنت.',
                        ),
                      ),
                    );
                  }
                }
              },
            ),
            _buildSettingsItem(
              icon: Icons.description,
              title: 'شروط الاستخدام',
              onTap: () async {
                final Uri url = Uri.parse('https://www.example.com/terms');
                if (!await launchUrl(
                  url,
                  mode: LaunchMode.externalApplication,
                )) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'لا يمكن فتح الرابط، يرجى التحقق من اتصال الإنترنت.',
                        ),
                      ),
                    );
                  }
                }
              },
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildSettingsCategory(String title) {
    return Container(
      margin: const EdgeInsets.only(top: 24, bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: const Color(0xFF374151),
        ),
      ),
    );
  }

  Widget _buildSettingsCard(List<Widget> children) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(children: children),
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    String? subtitle,
    Color? iconColor,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: (iconColor ?? const Color(0xFF6B7280)).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Icon(
          icon,
          color: iconColor ?? const Color(0xFF6B7280),
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: GoogleFonts.cairo(
          fontWeight: FontWeight.w600,
          color: const Color(0xFF1F2937),
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: GoogleFonts.cairo(
                color: const Color(0xFF6B7280),
                fontSize: 12,
              ),
            )
          : null,
      trailing: trailing ?? const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }
}

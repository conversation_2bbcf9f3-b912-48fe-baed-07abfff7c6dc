import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class CustomChipSelector extends StatelessWidget {
  final List<String> options;
  final List<String> selectedOptions;
  final Function(List<String>) onSelected;
  final bool isMultiSelect;

  const CustomChipSelector({
    super.key,
    required this.options,
    required this.selectedOptions,
    required this.onSelected,
    this.isMultiSelect = true,
  });

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 8.0,
      runSpacing: 4.0,
      children: options.map((option) {
        final isSelected = selectedOptions.contains(option);
        return FilterChip(
          label: Text(option, style: GoogleFonts.cairo(color: isSelected ? Colors.white : Colors.black)),
          selected: isSelected,
          onSelected: (selected) {
            if (isMultiSelect) {
              final newSelection = List<String>.from(selectedOptions);
              if (selected) {
                newSelection.add(option);
              } else {
                newSelection.remove(option);
              }
              onSelected(newSelection);
            } else {
              onSelected(selected ? [option] : []);
            }
          },
          selectedColor: Theme.of(context).primaryColor,
          checkmarkColor: Colors.white,
        );
      }).toList(),
    );
  }
}

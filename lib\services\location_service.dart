import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

class LocationService {
  static LocationService? _instance;
  static LocationService get instance => _instance ??= LocationService._();
  LocationService._();

  Position? _currentPosition;
  bool _isLocationEnabled = false;

  Position? get currentPosition => _currentPosition;
  bool get isLocationEnabled => _isLocationEnabled;

  // Check if location services are enabled
  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  // Check location permission status
  Future<LocationPermission> checkLocationPermission() async {
    return await Geolocator.checkPermission();
  }

  // Request location permission
  Future<LocationPermission> requestLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    }
    
    return permission;
  }

  // Initialize location service
  Future<bool> initialize() async {
    try {
      // Check if location services are enabled
      _isLocationEnabled = await isLocationServiceEnabled();
      if (!_isLocationEnabled) {
        debugPrint('Location services are disabled.');
        return false;
      }

      // Check and request permission
      LocationPermission permission = await checkLocationPermission();
      
      if (permission == LocationPermission.denied) {
        permission = await requestLocationPermission();
        if (permission == LocationPermission.denied) {
          debugPrint('Location permissions are denied');
          return false;
        }
      }
      
      if (permission == LocationPermission.deniedForever) {
        debugPrint('Location permissions are permanently denied');
        return false;
      }

      // Get current position
      await getCurrentPosition();
      return true;
    } catch (e) {
      debugPrint('Error initializing location service: $e');
      return false;
    }
  }

  // Get current position
  Future<Position?> getCurrentPosition() async {
    try {
      if (!_isLocationEnabled) {
        await initialize();
      }

      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );
      
      return _currentPosition;
    } catch (e) {
      debugPrint('Error getting current position: $e');
      return null;
    }
  }

  // Calculate distance between two points
  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    ) / 1000; // Convert to kilometers
  }

  // Get distance from current location to a point
  double? getDistanceFromCurrentLocation(double latitude, double longitude) {
    if (_currentPosition == null) return null;
    
    return calculateDistance(
      _currentPosition!.latitude,
      _currentPosition!.longitude,
      latitude,
      longitude,
    );
  }

  // Format distance for display
  String formatDistance(double distanceInKm) {
    if (distanceInKm < 1) {
      return '${(distanceInKm * 1000).round()} م';
    } else {
      return '${distanceInKm.toStringAsFixed(1)} كم';
    }
  }

  // Get location stream for real-time updates
  Stream<Position> getPositionStream() {
    return Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
      ),
    );
  }

  // Open location settings
  Future<void> openLocationSettings() async {
    await Geolocator.openLocationSettings();
  }

  // Open app settings
  Future<void> openAppSettings() async {
    await openAppSettings();
  }

  // Check if location is within radius
  bool isWithinRadius(
    double centerLat,
    double centerLng,
    double targetLat,
    double targetLng,
    double radiusInKm,
  ) {
    final distance = calculateDistance(centerLat, centerLng, targetLat, targetLng);
    return distance <= radiusInKm;
  }

  // Get nearby points from a list
  List<T> getNearbyPoints<T>({
    required List<T> points,
    required double Function(T) getLatitude,
    required double Function(T) getLongitude,
    double radiusInKm = 10.0,
    Position? fromPosition,
  }) {
    final position = fromPosition ?? _currentPosition;
    if (position == null) return points;

    return points.where((point) {
      return isWithinRadius(
        position.latitude,
        position.longitude,
        getLatitude(point),
        getLongitude(point),
        radiusInKm,
      );
    }).toList();
  }

  // Sort points by distance from current location
  List<T> sortByDistance<T>({
    required List<T> points,
    required double Function(T) getLatitude,
    required double Function(T) getLongitude,
    Position? fromPosition,
  }) {
    final position = fromPosition ?? _currentPosition;
    if (position == null) return points;

    final pointsWithDistance = points.map((point) {
      final distance = calculateDistance(
        position.latitude,
        position.longitude,
        getLatitude(point),
        getLongitude(point),
      );
      return {'point': point, 'distance': distance};
    }).toList();

    pointsWithDistance.sort((a, b) => 
      (a['distance'] as double).compareTo(b['distance'] as double));

    return pointsWithDistance.map((item) => item['point'] as T).toList();
  }

  // Get address from coordinates (requires geocoding service)
  Future<String?> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      // This would require a geocoding service like Google Geocoding API
      // For now, return coordinates as string
      return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
    } catch (e) {
      debugPrint('Error getting address: $e');
      return null;
    }
  }

  // Show location permission dialog
  static Future<bool> showLocationPermissionDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إذن الموقع مطلوب'),
        content: const Text(
          'يحتاج التطبيق إلى إذن الموقع لإظهار الصيدليات والمستشفيات القريبة منك.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('السماح'),
          ),
        ],
      ),
    ) ?? false;
  }

  // Show location settings dialog
  static Future<bool> showLocationSettingsDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خدمات الموقع معطلة'),
        content: const Text(
          'يرجى تفعيل خدمات الموقع في إعدادات الجهاز للحصول على أفضل تجربة.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('فتح الإعدادات'),
          ),
        ],
      ),
    ) ?? false;
  }

  // Dispose resources
  void dispose() {
    _currentPosition = null;
    _isLocationEnabled = false;
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/models/comment.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/providers/restaurant_provider.dart';
import 'package:yassincil/utils/app_colors.dart';

class AddRestaurantCommentWidget extends StatefulWidget {
  final String restaurantId;
  final String? parentCommentId;
  final String? replyToUsername;
  final VoidCallback onCommentAdded;

  const AddRestaurantCommentWidget({
    super.key,
    required this.restaurantId,
    this.parentCommentId,
    this.replyToUsername,
    required this.onCommentAdded,
  });

  @override
  State<AddRestaurantCommentWidget> createState() =>
      _AddRestaurantCommentWidgetState();
}

class _AddRestaurantCommentWidgetState
    extends State<AddRestaurantCommentWidget> {
  final _commentController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  Future<void> _submitComment() async {
    if (_commentController.text.trim().isEmpty) {
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.currentUser;

    if (user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'يجب تسجيل الدخول أولاً للتعليق',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final newComment = Comment(
        id: '', // Firestore will generate this
        postId: widget.restaurantId,
        userId: user.uid,
        username: user.displayName ?? 'مستخدم غير معروف',
        userAvatar: user.photoURL,
        content: _commentController.text.trim(),
        createdAt: DateTime.now(),
        parentCommentId: widget.parentCommentId,
      );

      await Provider.of<RestaurantProvider>(
        context,
        listen: false,
      ).addCommentToRestaurant(
        restaurantId: widget.restaurantId,
        comment: newComment,
      );

      _commentController.clear();
      widget.onCommentAdded();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: ${e.toString()}', style: GoogleFonts.cairo()),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.replyToUsername != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Text(
                'الرد على ${widget.replyToUsername}',
                style: GoogleFonts.cairo(
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _commentController,
                  style: GoogleFonts.cairo(fontSize: 14),
                  decoration: InputDecoration(
                    hintText: 'اكتب تعليقك...',
                    hintStyle: GoogleFonts.cairo(
                      color: AppColors.textSecondary,
                    ),
                    filled: true,
                    fillColor: AppColors.background,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  maxLines: 3,
                  minLines: 1,
                ),
              ),
              const SizedBox(width: 12),
              _isLoading
                  ? const CircularProgressIndicator()
                  : IconButton(
                      icon: const Icon(Icons.send_rounded),
                      onPressed: _submitComment,
                      color: AppColors.primary,
                      iconSize: 28,
                      style: IconButton.styleFrom(
                        backgroundColor: AppColors.primary.withOpacity(0.1),
                        padding: const EdgeInsets.all(12),
                      ),
                    ),
            ],
          ),
        ],
      ),
    );
  }
}

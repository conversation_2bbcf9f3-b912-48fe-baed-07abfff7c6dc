# تطبيق ياسين سيل - Yassincil

تطبيق شامل لإدارة مرض السكري والتغذية الصحية باللغة العربية.

## الميزات الجديدة المضافة

### 🍽️ نظام الوصفات المحسن

#### شاشة تفاصيل الوصفة المطورة
- **معرض صور تفاعلي**: عرض متعدد الصور مع إمكانية التكبير والتصغير
- **نظام التبويبات**: تنظيم المحتوى في تبويبات (المكونات، طريقة التحضير، المعلومات الغذائية)
- **معلومات غذائية مفصلة**: عرض السعرات الحرارية، البروتين، الكربوهيدرات، والدهون
- **مؤشرات بصرية**: أيقونات ملونة لكل نوع من المعلومات الغذائية
- **تفاعل اجتماعي**: نظام الإعجاب والتعليقات والمشاركة

#### نظام التعليقات المتقدم
- **إضافة تعليقات**: واجهة سهلة لكتابة التعليقات
- **الرد على التعليقات**: إمكانية الرد على تعليقات المستخدمين الآخرين
- **نظام الإعجاب**: إعجاب بالتعليقات مع عداد
- **الإبلاغ عن المحتوى**: نظام للإبلاغ عن التعليقات غير المناسبة
- **عرض الوقت**: عرض وقت النشر بالعربية (منذ دقيقة، منذ ساعة، إلخ)

#### شاشة إضافة/تعديل الوصفات المحسنة
- **رفع الصور المتعدد**: دعم رفع عدة صور من الكاميرا أو المعرض
- **إضافة الصور بالرابط**: إمكانية إضافة صور عبر الروابط
- **معلومات مفصلة**: حقول لوقت التحضير، وقت الطبخ، عدد الحصص
- **مستوى الصعوبة**: اختيار مستوى صعوبة الوصفة
- **المعلومات الغذائية**: إدخال السعرات الحرارية والعناصر الغذائية
- **خيارات خاصة**: تحديد إذا كانت الوصفة خالية من الجلوتين

#### ميزات المشرف
- **إدارة الوصفات**: تعديل وحذف الوصفات
- **تمييز الوصفات**: وضع علامة "مميزة" على الوصفات المختارة
- **إدارة التعليقات**: مراقبة والتحكم في التعليقات

### 🛠️ التحسينات التقنية

#### تحسينات الأداء
- **تحميل الصور المحسن**: عرض مؤشر التحميل وصور بديلة عند الفشل
- **ذاكرة التخزين المؤقت**: تحسين سرعة تحميل الصور
- **التحميل التدريجي**: تحميل المحتوى حسب الحاجة

#### تحسينات واجهة المستخدم
- **تصميم متجاوب**: واجهة تتكيف مع أحجام الشاشات المختلفة
- **ألوان متسقة**: نظام ألوان موحد عبر التطبيق
- **خطوط عربية**: استخدام خط Cairo للنصوص العربية
- **رسوم متحركة**: انتقالات سلسة بين الشاشات

#### الأمان والخصوصية
- **صلاحيات المستخدمين**: نظام أدوار للمستخدمين والمشرفين
- **حماية البيانات**: تشفير البيانات الحساسة
- **التحقق من الهوية**: نظام مصادقة آمن

### 📱 متطلبات النظام

#### Android
- Android 5.0 (API level 21) أو أحدث
- صلاحية الوصول للكاميرا والمعرض
- اتصال بالإنترنت

#### iOS
- iOS 11.0 أو أحدث
- صلاحية الوصول للكاميرا ومكتبة الصور
- اتصال بالإنترنت

### 🚀 التثبيت والتشغيل

```bash
# استنساخ المشروع
git clone [repository-url]

# الانتقال لمجلد المشروع
cd yassincil

# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق
flutter run
```

### 🧪 الاختبارات

```bash
# تشغيل جميع الاختبارات
flutter test

# تشغيل اختبارات محددة
flutter test test/recipe_detail_test.dart

# توليد mocks للاختبارات
flutter packages pub run build_runner build
```

### 📦 التبعيات الرئيسية

- **flutter**: إطار العمل الأساسي
- **firebase_core**: خدمات Firebase الأساسية
- **cloud_firestore**: قاعدة البيانات السحابية
- **firebase_storage**: تخزين الملفات
- **provider**: إدارة الحالة
- **google_fonts**: الخطوط العربية
- **image_picker**: اختيار الصور
- **timeago**: عرض الأوقات النسبية
- **share_plus**: مشاركة المحتوى

### 🔧 الإعدادات المطلوبة

#### Firebase
1. إنشاء مشروع Firebase جديد
2. تفعيل Firestore Database
3. تفعيل Firebase Storage
4. تفعيل Authentication
5. إضافة ملف `google-services.json` للأندرويد
6. إضافة ملف `GoogleService-Info.plist` لـ iOS

#### الصلاحيات
- **Android**: تم إضافة صلاحيات الكاميرا والتخزين في `AndroidManifest.xml`
- **iOS**: تم إضافة أوصاف الصلاحيات في `Info.plist`

### 📝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push للـ branch
5. إنشاء Pull Request

### 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

### 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء issue في GitHub أو التواصل معنا عبر البريد الإلكتروني.

import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../models/product.dart';
import '../models/restaurant.dart';
import '../models/forum_post.dart';
import '../models/symptom_entry.dart';
import '../services/firestore_service.dart';
import '../utils/app_constants.dart';

enum SearchCategory { all, products, restaurants, forum, symptoms }

class SearchResult {
  final String id;
  final String title;
  final String subtitle;
  final String category;
  final dynamic data;
  final double relevanceScore;

  SearchResult({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.category,
    required this.data,
    this.relevanceScore = 0.0,
  });
}

class SearchService {
  static final FirestoreService _firestoreService = FirestoreService();

  /// البحث الشامل عبر جميع الأقسام
  static Future<List<SearchResult>> searchAll(String query) async {
    if (query.trim().isEmpty) return [];

    final results = <SearchResult>[];

    try {
      // البحث في المنتجات
      final productResults = await searchProducts(query);
      results.addAll(productResults);

      // البحث في المطاعم
      final restaurantResults = await searchRestaurants(query);
      results.addAll(restaurantResults);

      // البحث في المنتدى
      final forumResults = await searchForum(query);
      results.addAll(forumResults);

      // ترتيب النتائج حسب الصلة
      results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));

      return results;
    } catch (e) {
      debugPrint('خطأ في البحث الشامل: $e');
      return [];
    }
  }

  /// البحث في المنتجات
  static Future<List<SearchResult>> searchProducts(String query) async {
    try {
      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.productsCollection)
          .where('isApproved', isEqualTo: true)
          .get();

      final results = <SearchResult>[];
      final lowerQuery = query.toLowerCase();

      for (final doc in querySnapshot.docs) {
        final product = Product.fromFirestore(doc);
        final relevance = _calculateProductRelevance(product, lowerQuery);

        if (relevance > 0) {
          results.add(
            SearchResult(
              id: product.id!,
              title: product.name,
              subtitle: '${product.brand} - ${product.category}',
              category: 'منتجات',
              data: product,
              relevanceScore: relevance,
            ),
          );
        }
      }

      return results;
    } catch (e) {
      debugPrint('خطأ في البحث في المنتجات: $e');
      return [];
    }
  }

  /// البحث في المطاعم
  static Future<List<SearchResult>> searchRestaurants(String query) async {
    try {
      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.restaurantsCollection)
          .where('isApproved', isEqualTo: true)
          .get();

      final results = <SearchResult>[];
      final lowerQuery = query.toLowerCase();

      for (final doc in querySnapshot.docs) {
        final restaurant = Restaurant.fromFirestore(doc);
        final relevance = _calculateRestaurantRelevance(restaurant, lowerQuery);

        if (relevance > 0) {
          results.add(
            SearchResult(
              id: restaurant.id!,
              title: restaurant.name,
              subtitle: '${restaurant.address} - مطعم',
              category: 'مطاعم',
              data: restaurant,
              relevanceScore: relevance,
            ),
          );
        }
      }

      return results;
    } catch (e) {
      debugPrint('خطأ في البحث في المطاعم: $e');
      return [];
    }
  }

  /// البحث في المنتدى
  static Future<List<SearchResult>> searchForum(String query) async {
    try {
      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.forumPostsCollection)
          .where('isDeleted', isEqualTo: false)
          .orderBy('createdAt', descending: true)
          .limit(50)
          .get();

      final results = <SearchResult>[];
      final lowerQuery = query.toLowerCase();

      for (final doc in querySnapshot.docs) {
        final post = ForumPost.fromFirestore(doc);
        final relevance = _calculateForumRelevance(post, lowerQuery);

        if (relevance > 0) {
          results.add(
            SearchResult(
              id: post.id!,
              title: post.content.length > 50
                  ? '${post.content.substring(0, 50)}...'
                  : post.content,
              subtitle:
                  'بواسطة ${post.username} - ${_formatDate(post.createdAt)}',
              category: 'منتدى',
              data: post,
              relevanceScore: relevance,
            ),
          );
        }
      }

      return results;
    } catch (e) {
      debugPrint('خطأ في البحث في المنتدى: $e');
      return [];
    }
  }

  /// البحث في الأعراض (للمستخدم الحالي)
  static Future<List<SearchResult>> searchSymptoms(
    String query,
    String userId,
  ) async {
    try {
      final querySnapshot = await _firestoreService.db
          .collection(AppConstants.symptomsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .limit(100)
          .get();

      final results = <SearchResult>[];
      final lowerQuery = query.toLowerCase();

      for (final doc in querySnapshot.docs) {
        final symptom = SymptomEntry.fromFirestore(doc);
        final relevance = _calculateSymptomRelevance(symptom, lowerQuery);

        if (relevance > 0) {
          results.add(
            SearchResult(
              id: symptom.id!,
              title: symptom.symptomName,
              subtitle:
                  'شدة ${symptom.severity}/5 - ${_formatDate(symptom.timestamp)}',
              category: 'أعراض',
              data: symptom,
              relevanceScore: relevance,
            ),
          );
        }
      }

      return results;
    } catch (e) {
      debugPrint('خطأ في البحث في الأعراض: $e');
      return [];
    }
  }

  /// حساب صلة المنتج بالبحث
  static double _calculateProductRelevance(Product product, String query) {
    double score = 0.0;

    // البحث في الاسم (أعلى أولوية)
    if (product.name.toLowerCase().contains(query)) {
      score += 10.0;
      if (product.name.toLowerCase().startsWith(query)) {
        score += 5.0;
      }
    }

    // البحث في العلامة التجارية
    if (product.brand.toLowerCase().contains(query)) {
      score += 7.0;
    }

    // البحث في الفئة
    if (product.category.toLowerCase().contains(query)) {
      score += 5.0;
    }

    // البحث في الوصف
    if (product.description.toLowerCase().contains(query) == true) {
      score += 3.0;
    }

    // البحث في المكونات
    for (final ingredient in product.ingredients) {
      if (ingredient.toLowerCase().contains(query)) {
        score += 4.0;
        break;
      }
    }

    // البحث في الباركود
    if (product.barcode.contains(query)) {
      score += 8.0;
    }

    return score;
  }

  /// حساب صلة المطعم بالبحث
  static double _calculateRestaurantRelevance(
    Restaurant restaurant,
    String query,
  ) {
    double score = 0.0;

    // البحث في الاسم
    if (restaurant.name.toLowerCase().contains(query)) {
      score += 10.0;
      if (restaurant.name.toLowerCase().startsWith(query)) {
        score += 5.0;
      }
    }

    // البحث في العنوان
    if (restaurant.address.toLowerCase().contains(query)) {
      score += 6.0;
    }

    // البحث في الوصف
    if (restaurant.description.toLowerCase().contains(query)) {
      score += 3.0;
    }

    // البحث في الخيارات الخالية من الجلوتين
    for (final option in restaurant.glutenFreeOptions) {
      if (option.toLowerCase().contains(query)) {
        score += 8.0;
        break;
      }
    }

    return score;
  }

  /// حساب صلة منشور المنتدى بالبحث
  static double _calculateForumRelevance(ForumPost post, String query) {
    double score = 0.0;

    // البحث في المحتوى (كعنوان)
    if (post.content.toLowerCase().contains(query)) {
      score += 10.0;
      if (post.content.toLowerCase().startsWith(query)) {
        score += 5.0;
      }
    }

    // البحث في المحتوى
    if (post.content.toLowerCase().contains(query)) {
      score += 6.0;
    }

    // البحث في الكلمات المفتاحية
    for (final tag in post.tags) {
      if (tag.toLowerCase().contains(query)) {
        score += 8.0;
        break;
      }
    }

    // البحث في اسم الكاتب
    if (post.username.toLowerCase().contains(query)) {
      score += 4.0;
    }

    return score;
  }

  /// حساب صلة العرض بالبحث
  static double _calculateSymptomRelevance(SymptomEntry symptom, String query) {
    double score = 0.0;

    // البحث في اسم العرض
    if (symptom.symptomName.toLowerCase().contains(query)) {
      score += 10.0;
    }

    // البحث في الملاحظات
    if (symptom.notes?.toLowerCase().contains(query) == true) {
      score += 6.0;
    }

    // البحث في المحفزات
    for (final trigger in symptom.triggers) {
      if (trigger.toLowerCase().contains(query)) {
        score += 7.0;
        break;
      }
    }

    // البحث في تفاصيل الوجبة
    if (symptom.mealDetails != null) {
      if (symptom.mealDetails!.mealName.toLowerCase().contains(query)) {
        score += 7.0;
      }
      if (symptom.mealDetails!.restaurantName?.toLowerCase().contains(query) == true) {
        score += 7.0;
      }
      for (final ingredient in symptom.mealDetails!.ingredients) {
        if (ingredient.toLowerCase().contains(query)) {
          score += 5.0;
          break;
        }
      }
    }

    // البحث في تفاصيل التعرض للغلوتين
    if (symptom.glutenExposure != null) {
      if (symptom.glutenExposure!.suspectedSource?.toLowerCase().contains(query) == true) {
        score += 8.0;
      }
      if (symptom.glutenExposure!.notes?.toLowerCase().contains(query) == true) {
        score += 6.0;
      }
    }

    return score;
  }

  /// تنسيق التاريخ
  static String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  /// اقتراحات البحث
  static List<String> getSearchSuggestions(String query) {
    final suggestions = <String>[];

    // اقتراحات شائعة
    final commonSuggestions = [
      'خبز خالي من الجلوتين',
      'مطاعم آمنة',
      'أعراض السيلياك',
      'منتجات معتمدة',
      'وصفات صحية',
      'مكملات غذائية',
      'تشخيص السيلياك',
      'نصائح غذائية',
    ];

    for (final suggestion in commonSuggestions) {
      if (suggestion.toLowerCase().contains(query.toLowerCase())) {
        suggestions.add(suggestion);
      }
    }

    return suggestions.take(5).toList();
  }

  /// حفظ استعلام البحث للإحصائيات
  static Future<void> logSearchQuery(String query, String category) async {
    try {
      await _firestoreService.addDocument('searchLogs', {
        'query': query,
        'category': category,
        'timestamp': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('خطأ في حفظ استعلام البحث: $e');
    }
  }
}
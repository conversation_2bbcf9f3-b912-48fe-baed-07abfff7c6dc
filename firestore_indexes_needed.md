# فهارس Firestore المطلوبة

## مشكلة الردود في شاشة الأدوية

تم إصلاح المشكلة في الكود، لكن قد تحتاج إلى إنشاء فهارس مركبة في Firestore Console.

### الفهارس المطلوبة:

#### 1. فهرس للردود في مجموعة الأدوية:
- **Collection**: `medications/{medicationId}/comments`
- **Fields**: 
  - `parentCommentId` (Ascending)
  - `createdAt` (Ascending)

#### 2. فهرس للردود في مجموعة الأطعمة:
- **Collection**: `food_items/{foodItemId}/comments`
- **Fields**: 
  - `parentCommentId` (Ascending)
  - `createdAt` (Ascending)

### كيفية إنشاء الفهارس:

1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروعك
3. اذهب إلى Firestore Database
4. اضغط على تبويب "Indexes"
5. اضغط على "Create Index"
6. أدخل المعلومات المطلوبة لكل فهرس

### أو استخدم Firebase CLI:

```bash
# إنشاء ملف firestore.indexes.json
{
  "indexes": [
    {
      "collectionGroup": "comments",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "parentCommentId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "createdAt",
          "order": "ASCENDING"
        }
      ]
    }
  ]
}

# ثم تشغيل الأمر:
firebase deploy --only firestore:indexes
```

### ملاحظة:
إذا ظهرت رسالة خطأ في التطبيق تطلب إنشاء فهرس، ستحتوي على رابط مباشر لإنشاء الفهرس المطلوب.
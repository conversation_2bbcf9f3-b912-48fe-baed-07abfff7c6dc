import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/providers/search_provider.dart';
import 'package:yassincil/services/advanced_search_service.dart';
import 'package:yassincil/screens/search/search_results_screen.dart';
import 'package:yassincil/screens/search/search_filters_screen.dart';

class AdvancedSearchScreen extends StatefulWidget {
  const AdvancedSearchScreen({super.key});

  @override
  State<AdvancedSearchScreen> createState() => _AdvancedSearchScreenState();
}

class _AdvancedSearchScreenState extends State<AdvancedSearchScreen> {
  final _searchController = TextEditingController();
  final _focusNode = FocusNode();

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _performSearch() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      Provider.of<SearchProvider>(context, listen: false).searchAll(query);
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => SearchResultsScreen(query: query),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'البحث المتقدم',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.tune),
            tooltip: 'فلاتر البحث',
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SearchFiltersScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: Consumer<SearchProvider>(
        builder: (context, searchProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // شريط البحث الرئيسي
                _buildSearchBar(theme, searchProvider),
                
                const SizedBox(height: 24),
                
                // فئات البحث السريع
                _buildQuickCategories(theme, searchProvider),
                
                const SizedBox(height: 24),
                
                // تاريخ البحث
                if (searchProvider.searchHistory.isNotEmpty) ...[
                  _buildSearchHistory(theme, searchProvider),
                  const SizedBox(height: 24),
                ],
                
                // اقتراحات البحث
                _buildSearchSuggestions(theme),
                
                const SizedBox(height: 24),
                
                // نصائح البحث
                _buildSearchTips(theme),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSearchBar(ThemeData theme, SearchProvider searchProvider) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.deepPurple.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        focusNode: _focusNode,
        decoration: InputDecoration(
          hintText: 'ابحث في الأطعمة، الأدوية، الوصفات، المطاعم...',
          hintStyle: GoogleFonts.cairo(color: Colors.grey[500]),
          prefixIcon: Icon(Icons.search, color: theme.primaryColor),
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_searchController.text.isNotEmpty)
                IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {});
                  },
                ),
              IconButton(
                icon: const Icon(Icons.mic),
                tooltip: 'البحث الصوتي',
                onPressed: () {
                  // يمكن تطوير البحث الصوتي لاحقاً
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('البحث الصوتي قيد التطوير')),
                  );
                },
              ),
            ],
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
        ),
        onSubmitted: (_) => _performSearch(),
        onChanged: (value) {
          setState(() {});
          if (value.length > 2) {
            searchProvider.getSuggestions(value);
          }
        },
      ),
    );
  }

  Widget _buildQuickCategories(ThemeData theme, SearchProvider searchProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'البحث السريع',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: SearchCategory.values.map((category) {
            final isSelected = searchProvider.selectedCategories.contains(category);
            return _buildCategoryChip(
              category,
              isSelected,
              searchProvider,
              theme,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildCategoryChip(
    SearchCategory category,
    bool isSelected,
    SearchProvider searchProvider,
    ThemeData theme,
  ) {
    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            searchProvider.getCategoryIcon(category),
            size: 16,
            color: isSelected ? Colors.white : theme.primaryColor,
          ),
          const SizedBox(width: 6),
          Text(
            searchProvider.getCategoryName(category),
            style: GoogleFonts.cairo(
              color: isSelected ? Colors.white : theme.primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
      selected: isSelected,
      onSelected: (selected) {
        searchProvider.toggleCategory(category);
      },
      selectedColor: theme.primaryColor,
      backgroundColor: Colors.white,
      side: BorderSide(color: theme.primaryColor),
    );
  }

  Widget _buildSearchHistory(ThemeData theme, SearchProvider searchProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'عمليات البحث السابقة',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            TextButton(
              onPressed: () {
                searchProvider.clearSearchHistory();
              },
              child: Text(
                'مسح الكل',
                style: GoogleFonts.cairo(color: theme.primaryColor),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.deepPurple.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: searchProvider.searchHistory.take(5).map((query) {
              return ListTile(
                leading: Icon(Icons.history, color: Colors.grey[600]),
                title: Text(
                  query,
                  style: GoogleFonts.cairo(),
                ),
                trailing: IconButton(
                  icon: Icon(Icons.close, color: Colors.grey[600]),
                  onPressed: () {
                    searchProvider.removeFromSearchHistory(query);
                  },
                ),
                onTap: () {
                  _searchController.text = query;
                  _performSearch();
                },
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildSearchSuggestions(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'اقتراحات البحث',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.deepPurple.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              'خبز خالي من الغلوتين',
              'أدوية آمنة',
              'وصفات حلويات',
              'مطاعم قريبة',
              'مقالات تغذية',
            ].map((suggestion) {
              return ActionChip(
                label: Text(
                  suggestion,
                  style: GoogleFonts.cairo(fontSize: 12),
                ),
                onPressed: () {
                  _searchController.text = suggestion;
                  _performSearch();
                },
                backgroundColor: Colors.grey[100],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildSearchTips(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb_outline, color: Colors.blue[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'نصائح للبحث:',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '• استخدم كلمات مفتاحية واضحة\n'
            '• جرب البحث بأسماء المكونات\n'
            '• استخدم الفلاتر لتضييق النتائج\n'
            '• ابحث بالعلامة التجارية للمنتجات\n'
            '• جرب البحث الصوتي للسهولة',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.blue[700],
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}

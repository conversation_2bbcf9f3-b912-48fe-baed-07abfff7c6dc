import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:yassincil/providers/doctor_provider.dart';
import 'package:yassincil/models/doctor.dart';

class AddEditDoctorScreen extends StatefulWidget {
  final Doctor? doctor;

  const AddEditDoctorScreen({super.key, this.doctor});

  @override
  State<AddEditDoctorScreen> createState() => _AddEditDoctorScreenState();
}

class _AddEditDoctorScreenState extends State<AddEditDoctorScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _specialtyController = TextEditingController();
  final _clinicController = TextEditingController();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();
  final _workingHoursController = TextEditingController();
  final _bioController = TextEditingController();
  final _educationController = TextEditingController();
  final _experienceController = TextEditingController();
  final _emailController = TextEditingController();
  final _websiteController = TextEditingController();
  final _licenseNumberController = TextEditingController();
  final _consultationFeeController = TextEditingController();
  final _imageUrlController = TextEditingController();

  File? _selectedImage;
  bool _isLoading = false;
  bool _useImageUrl = false;
  bool _isVerified = false;
  bool _isAvailable = true;
  bool _isFeatured = false;
  String _selectedSpecialty = 'طب عام';

  // التخصصات الطبية
  final List<String> _specialties = [
    'طب عام',
    'طب الأطفال',
    'طب النساء والتوليد',
    'طب القلب والأوعية الدموية',
    'طب الجهاز الهضمي',
    'طب الأعصاب',
    'طب العظام',
    'طب الجلدية',
    'طب العيون',
    'طب الأنف والأذن والحنجرة',
    'طب الأسنان',
    'طب النفسية',
    'طب الطوارئ',
    'طب التخدير',
    'طب الأشعة',
    'طب المختبرات',
    'طب الأورام',
    'طب الكلى',
    'طب الغدد الصماء',
    'طب الروماتيزم',
    'طب الصدر والجهاز التنفسي',
    'طب المسالك البولية',
    'الجراحة العامة',
    'جراحة القلب',
    'جراحة الأعصاب',
    'جراحة العظام',
    'جراحة التجميل',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.doctor != null) {
      _nameController.text = widget.doctor!.name;
      _specialtyController.text = widget.doctor!.specialty;
      _selectedSpecialty = _specialties.contains(widget.doctor!.specialty)
          ? widget.doctor!.specialty
          : 'أخرى';
      if (_selectedSpecialty == 'أخرى') {
        _specialtyController.text = widget.doctor!.specialty;
      }
      _clinicController.text = widget.doctor!.clinic;
      _addressController.text = widget.doctor!.address;
      _phoneController.text = widget.doctor!.phoneNumber;
      _workingHoursController.text = widget.doctor!.workingHours;
      _bioController.text = widget.doctor!.bio ?? '';
      _educationController.text = widget.doctor!.education ?? '';
      _experienceController.text = widget.doctor!.experience ?? '';
      _emailController.text = widget.doctor!.email ?? '';
      _websiteController.text = widget.doctor!.website ?? '';
      _licenseNumberController.text = widget.doctor!.licenseNumber ?? '';
      _consultationFeeController.text =
          widget.doctor!.consultationFee?.toString() ?? '';
      _isVerified = widget.doctor!.isVerified;
      _isAvailable = widget.doctor!.isAvailable;
      _isFeatured = widget.doctor!.isFeatured;
      if (widget.doctor!.imageUrl != null) {
        _imageUrlController.text = widget.doctor!.imageUrl!;
        _useImageUrl = true;
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _specialtyController.dispose();
    _clinicController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _workingHoursController.dispose();
    _bioController.dispose();
    _educationController.dispose();
    _experienceController.dispose();
    _emailController.dispose();
    _websiteController.dispose();
    _licenseNumberController.dispose();
    _consultationFeeController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 800,
      maxHeight: 600,
      imageQuality: 80,
    );

    if (pickedFile != null) {
      setState(() {
        _selectedImage = File(pickedFile.path);
        _useImageUrl = false;
        _imageUrlController.clear();
      });
    }
  }

  Future<void> _saveDoctor() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final doctorProvider = Provider.of<DoctorProvider>(
        context,
        listen: false,
      );

      // تحديد التخصص النهائي
      final finalSpecialty = _selectedSpecialty == 'أخرى'
          ? _specialtyController.text.trim()
          : _selectedSpecialty;

      // تحديد رابط الصورة النهائي
      String? finalImageUrl;
      if (_useImageUrl && _imageUrlController.text.trim().isNotEmpty) {
        finalImageUrl = _imageUrlController.text.trim();
      }

      // تحويل رسوم الاستشارة
      double? consultationFee;
      if (_consultationFeeController.text.trim().isNotEmpty) {
        consultationFee = double.tryParse(
          _consultationFeeController.text.trim(),
        );
      }

      final doctor = Doctor(
        id: widget.doctor?.id,
        name: _nameController.text.trim(),
        specialty: finalSpecialty,
        clinic: _clinicController.text.trim(),
        address: _addressController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        workingHours: _workingHoursController.text.trim(),
        bio: _bioController.text.trim().isEmpty
            ? null
            : _bioController.text.trim(),
        education: _educationController.text.trim().isEmpty
            ? null
            : _educationController.text.trim(),
        experience: _experienceController.text.trim().isEmpty
            ? null
            : _experienceController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        website: _websiteController.text.trim().isEmpty
            ? null
            : _websiteController.text.trim(),
        licenseNumber: _licenseNumberController.text.trim().isEmpty
            ? null
            : _licenseNumberController.text.trim(),
        consultationFee: consultationFee,
        imageUrls: finalImageUrl != null ? [finalImageUrl] : [],
        rating: widget.doctor?.rating ?? 0.0,
        reviewsCount: widget.doctor?.reviewsCount ?? 0,
        isVerified: _isVerified,
        isAvailable: _isAvailable,
        isFeatured: _isFeatured,
        likesCount: widget.doctor?.likesCount ?? 0,
        appointmentsCount: widget.doctor?.appointmentsCount ?? 0,
        createdAt: widget.doctor?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (widget.doctor == null) {
        await doctorProvider.addDoctor(doctor);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم إضافة الطبيب بنجاح',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        }
      } else {
        await doctorProvider.updateDoctor(doctor);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم تحديث بيانات الطبيب بنجاح',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEditing = widget.doctor != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isEditing ? 'تعديل الطبيب' : 'إضافة طبيب جديد',
        ),
        centerTitle: true,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.save_rounded),
              onPressed: _saveDoctor,
              tooltip: 'حفظ',
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildImageSection(),
            const SizedBox(height: 20),
            _buildBasicInfoSection(),
            const SizedBox(height: 16),
            _buildContactInfoSection(),
            const SizedBox(height: 16),
            _buildProfessionalInfoSection(),
            const SizedBox(height: 16),
            _buildStatusSection(),
            const SizedBox(height: 32),
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    final theme = Theme.of(context);
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        children: [
          Container(
            height: 200,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
              gradient: LinearGradient(
                colors: [theme.primaryColor.withOpacity(0.2), theme.primaryColor.withOpacity(0.05)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Stack(
              children: [
                if (_selectedImage != null)
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                    child: Image.file(
                      _selectedImage!,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  )
                else if (_useImageUrl && _imageUrlController.text.isNotEmpty)
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                    child: Image.network(
                      _imageUrlController.text,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildImagePlaceholder(),
                    ),
                  )
                else if (widget.doctor?.imageUrl != null && !_useImageUrl)
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                    child: Image.network(
                      widget.doctor!.imageUrl!,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildImagePlaceholder(),
                    ),
                  )
                else
                  _buildImagePlaceholder(),

                Positioned(
                  bottom: 16,
                  right: 16,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      FloatingActionButton.small(
                        onPressed: _pickImage,
                        heroTag: "doctor_camera",
                        child: const Icon(Icons.camera_alt),
                      ),
                      const SizedBox(width: 8),
                      FloatingActionButton.small(
                        onPressed: () {
                          setState(() {
                            _useImageUrl = !_useImageUrl;
                            if (_useImageUrl) {
                              _selectedImage = null;
                            } else {
                              _imageUrlController.clear();
                            }
                          });
                        },
                        heroTag: "doctor_link",
                        child: const Icon(Icons.link),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          if (_useImageUrl)
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextFormField(
                controller: _imageUrlController,
                decoration: const InputDecoration(
                  labelText: 'رابط الصورة',
                  prefixIcon: Icon(Icons.link),
                ),
                onChanged: (value) => setState(() {}),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildImagePlaceholder() {
    final theme = Theme.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person,
            size: 60,
            color: theme.primaryColor.withOpacity(0.6),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط لإضافة صورة',
            style: theme.textTheme.titleLarge?.copyWith(color: theme.primaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Theme.of(context).primaryColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  'المعلومات الأساسية',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم الطبيب',
                prefixIcon: Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم الطبيب';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            _buildSpecialtyDropdown(),
            if (_selectedSpecialty == 'أخرى') ...[
              const SizedBox(height: 16),
              TextFormField(
                controller: _specialtyController,
                decoration: const InputDecoration(
                  labelText: 'التخصص (مخصص)',
                  prefixIcon: Icon(Icons.medical_services),
                ),
                validator: (value) {
                  if (_selectedSpecialty == 'أخرى' &&
                      (value == null || value.trim().isEmpty)) {
                    return 'يرجى إدخال التخصص';
                  }
                  return null;
                },
              ),
            ],
            const SizedBox(height: 16),
            TextFormField(
              controller: _clinicController,
              decoration: const InputDecoration(
                labelText: 'اسم العيادة/المستشفى',
                prefixIcon: Icon(Icons.local_hospital),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم العيادة';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpecialtyDropdown() {
    final theme = Theme.of(context);
    return DropdownButtonFormField<String>(
      value: _selectedSpecialty,
      style: theme.textTheme.bodyLarge,
      decoration: InputDecoration(
        labelText: 'التخصص',
        prefixIcon: Icon(
          Icons.medical_services,
          color: theme.primaryColor,
        ),
      ),
      items: _specialties.map((specialty) {
        return DropdownMenuItem(
          value: specialty,
          child: Text(specialty),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedSpecialty = value!;
          if (value != 'أخرى') {
            _specialtyController.clear();
          }
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى اختيار التخصص';
        }
        return null;
      },
    );
  }

  Widget _buildContactInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.contact_phone, color: Theme.of(context).primaryColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  'معلومات الاتصال',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _addressController,
              decoration: const InputDecoration(
                labelText: 'العنوان',
                prefixIcon: Icon(Icons.location_on),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال العنوان';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف',
                prefixIcon: Icon(Icons.phone),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال رقم الهاتف';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني (اختياري)',
                prefixIcon: Icon(Icons.email),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _websiteController,
              keyboardType: TextInputType.url,
              decoration: const InputDecoration(
                labelText: 'الموقع الإلكتروني (اختياري)',
                prefixIcon: Icon(Icons.web),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfessionalInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.work, color: Theme.of(context).primaryColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  'المعلومات المهنية',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _workingHoursController,
              decoration: const InputDecoration(
                labelText: 'ساعات العمل',
                prefixIcon: Icon(Icons.access_time),
                hintText: 'مثال: 9:00 ص - 5:00 م',
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _licenseNumberController,
              decoration: const InputDecoration(
                labelText: 'رقم الترخيص (اختياري)',
                prefixIcon: Icon(Icons.badge),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _consultationFeeController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'رسوم الاستشارة (اختياري)',
                prefixIcon: Icon(Icons.attach_money),
                suffixText: 'ريال',
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _bioController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'نبذة عن الطبيب (اختياري)',
                prefixIcon: Icon(Icons.info),
                alignLabelWithHint: true,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _educationController,
              maxLines: 2,
              decoration: const InputDecoration(
                labelText: 'المؤهلات العلمية (اختياري)',
                prefixIcon: Icon(Icons.school),
                alignLabelWithHint: true,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _experienceController,
              maxLines: 2,
              decoration: const InputDecoration(
                labelText: 'الخبرات (اختياري)',
                prefixIcon: Icon(Icons.work_history),
                alignLabelWithHint: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: Theme.of(context).primaryColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  'حالة الطبيب',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatusSwitch(
              'متاح للمرضى',
              'يظهر الطبيب في قائمة الأطباء المتاحين',
              _isAvailable,
              (value) => setState(() => _isAvailable = value),
              Colors.green,
              Icons.visibility,
            ),
            const SizedBox(height: 12),
            _buildStatusSwitch(
              'طبيب موثق',
              'تم التحقق من بيانات ومؤهلات الطبيب',
              _isVerified,
              (value) => setState(() => _isVerified = value),
              Colors.blue,
              Icons.verified,
            ),
            const SizedBox(height: 12),
            _buildStatusSwitch(
              'طبيب مميز',
              'يظهر الطبيب في قائمة الأطباء المميزين',
              _isFeatured,
              (value) => setState(() => _isFeatured = value),
              Colors.orange,
              Icons.star,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSwitch(
    String title,
    String subtitle,
    bool value,
    Function(bool) onChanged,
    Color color,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: value ? color.withOpacity(0.1) : theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: value ? color.withOpacity(0.3) : theme.dividerColor,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: value
                  ? color.withOpacity(0.2)
                  : Colors.grey.shade200,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: value ? color : Colors.grey.shade600,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleLarge,
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: color,
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    return ElevatedButton.icon(
      onPressed: _isLoading ? null : _saveDoctor,
      icon: _isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
              ),
            )
          : const Icon(Icons.save),
      label: Text(
        _isLoading
            ? 'جاري الحفظ...'
            : (widget.doctor == null ? 'إضافة الطبيب' : 'حفظ التغييرات'),
      ),
    );
  }
}
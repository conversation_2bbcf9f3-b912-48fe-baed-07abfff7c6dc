import 'dart:io';
import 'package:flutter/material.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';

class StorageService {
  final FirebaseStorage _storage = FirebaseStorage.instance;

  Future<String?> uploadFile(File file, String path) async {
    try {
      Reference storageRef = _storage.ref().child(path);
      UploadTask uploadTask = storageRef.putFile(file);
      TaskSnapshot snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      debugPrint("Error uploading file to Firebase Storage at $path: $e");
      return null;
    }
  }

  Future<void> deleteFile(String fileUrl) async {
    try {
      Reference storageRef = _storage.refFromURL(fileUrl);
      await storageRef.delete();
    } catch (e) {
      debugPrint("Error deleting file from Firebase Storage: $e");
    }
  }

  /// رفع صورة من الجهاز مع خيارات متقدمة
  Future<String?> uploadImageFromDevice({
    required String collection,
    String? fileName,
    ImageSource source = ImageSource.gallery,
    int maxWidth = 1024,
    int maxHeight = 1024,
    int imageQuality = 80,
  }) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: maxWidth.toDouble(),
        maxHeight: maxHeight.toDouble(),
        imageQuality: imageQuality,
      );

      if (image == null) return null;

      final file = File(image.path);
      final name = fileName ?? '${DateTime.now().millisecondsSinceEpoch}';
      final path = '$collection/$name.jpg';

      return await uploadFile(file, path);
    } catch (e) {
      debugPrint('خطأ في رفع الصورة من الجهاز: $e');
      return null;
    }
  }

  /// رفع عدة صور من الجهاز
  Future<List<String>> uploadMultipleImagesFromDevice({
    required String collection,
    int maxImages = 5,
    int maxWidth = 1024,
    int maxHeight = 1024,
    int imageQuality = 80,
  }) async {
    try {
      final ImagePicker picker = ImagePicker();
      final List<XFile> images = await picker.pickMultiImage(
        maxWidth: maxWidth.toDouble(),
        maxHeight: maxHeight.toDouble(),
        imageQuality: imageQuality,
      );

      if (images.isEmpty) return [];

      final selectedImages = images.take(maxImages).toList();
      final List<String> uploadedUrls = [];

      for (int i = 0; i < selectedImages.length; i++) {
        final image = selectedImages[i];
        final fileName = '${DateTime.now().millisecondsSinceEpoch}_$i';
        final file = File(image.path);
        final path = '$collection/$fileName.jpg';

        final url = await uploadFile(file, path);
        if (url != null) {
          uploadedUrls.add(url);
        }
      }

      return uploadedUrls;
    } catch (e) {
      debugPrint('خطأ في رفع الصور المتعددة: $e');
      return [];
    }
  }

  /// رفع صورة من الكاميرا
  Future<String?> uploadImageFromCamera({
    required String collection,
    String? fileName,
    int maxWidth = 1024,
    int maxHeight = 1024,
    int imageQuality = 80,
  }) async {
    return await uploadImageFromDevice(
      collection: collection,
      fileName: fileName,
      source: ImageSource.camera,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
      imageQuality: imageQuality,
    );
  }

  /// حفظ صورة من رابط URL
  Future<String?> uploadImageFromUrl({
    required String imageUrl,
    required String collection,
    String? fileName,
  }) async {
    try {
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode != 200) {
        debugPrint('فشل في تحميل الصورة من الرابط: ${response.statusCode}');
        return null;
      }

      final tempDir = await getTemporaryDirectory();
      final tempFileName =
          fileName ?? '${DateTime.now().millisecondsSinceEpoch}';
      final tempFile = File('${tempDir.path}/$tempFileName.jpg');

      await tempFile.writeAsBytes(response.bodyBytes);

      final path = '$collection/$tempFileName.jpg';
      final uploadedUrl = await uploadFile(tempFile, path);

      await tempFile.delete();

      return uploadedUrl;
    } catch (e) {
      debugPrint('خطأ في رفع الصورة من الرابط: $e');
      return null;
    }
  }

  /// عرض خيارات رفع الصورة للمستخدم
  Future<String?> showImageUploadOptions({
    required BuildContext context,
    required String collection,
    String? fileName,
    bool allowUrl = true,
    bool allowCamera = true,
    bool allowGallery = true,
  }) async {
    return await showModalBottomSheet<String>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'اختر طريقة رفع الصورة',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            if (allowGallery)
              ListTile(
                leading: Icon(Icons.photo_library, color: Colors.blue.shade600),
                title: Text('من المعرض', style: GoogleFonts.cairo()),
                onTap: () async {
                  Navigator.pop(context);
                  final url = await uploadImageFromDevice(
                    collection: collection,
                    fileName: fileName,
                    source: ImageSource.gallery,
                  );
                  if (context.mounted) Navigator.pop(context, url);
                },
              ),
            if (allowCamera)
              ListTile(
                leading: Icon(Icons.camera_alt, color: Colors.green.shade600),
                title: Text('من الكاميرا', style: GoogleFonts.cairo()),
                onTap: () async {
                  Navigator.pop(context);
                  final url = await uploadImageFromCamera(
                    collection: collection,
                    fileName: fileName,
                  );
                  if (context.mounted) Navigator.pop(context, url);
                },
              ),
            if (allowUrl)
              ListTile(
                leading: Icon(Icons.link, color: Colors.orange.shade600),
                title: Text('من رابط', style: GoogleFonts.cairo()),
                onTap: () async {
                  Navigator.pop(context);
                  final url = await _showUrlInputDialog(
                    context,
                    collection,
                    fileName,
                  );
                  if (context.mounted) Navigator.pop(context, url);
                },
              ),
            const SizedBox(height: 10),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('إلغاء', style: GoogleFonts.cairo()),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض حوار إدخال الرابط
  Future<String?> _showUrlInputDialog(
    BuildContext context,
    String collection,
    String? fileName,
  ) async {
    final urlController = TextEditingController();

    return await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إدخال رابط الصورة', style: GoogleFonts.cairo()),
        content: TextField(
          controller: urlController,
          decoration: InputDecoration(
            labelText: 'رابط الصورة',
            labelStyle: GoogleFonts.cairo(),
            hintText: 'https://example.com/image.jpg',
            border: const OutlineInputBorder(),
          ),
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () async {
              final url = urlController.text.trim();
              if (url.isEmpty) return;

              Navigator.pop(context);

              // عرض مؤشر التحميل
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) =>
                    const Center(child: CircularProgressIndicator()),
              );

              final uploadedUrl = await uploadImageFromUrl(
                imageUrl: url,
                collection: collection,
                fileName: fileName,
              );

              if (context.mounted) Navigator.pop(context); // إغلاق مؤشر التحميل

              if (uploadedUrl != null) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'تم رفع الصورة بنجاح',
                        style: GoogleFonts.cairo(),
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } else {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'فشل في رفع الصورة',
                        style: GoogleFonts.cairo(),
                      ),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }

              // Return is handled by the dialog
            },
            child: Text('رفع', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  /// رفع صورة مطعم
  static Future<String> uploadRestaurantImage(
    File imageFile,
    String userId,
  ) async {
    final fileName = 'restaurant_${DateTime.now().millisecondsSinceEpoch}.jpg';
    final ref = FirebaseStorage.instance
        .ref()
        .child('restaurants')
        .child(userId)
        .child(fileName);

    final uploadTask = ref.putFile(imageFile);
    final snapshot = await uploadTask;
    return await snapshot.ref.getDownloadURL();
  }
}

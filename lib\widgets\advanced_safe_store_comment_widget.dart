import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../providers/safe_store_provider.dart';
import '../providers/auth_provider.dart';
import '../models/comment.dart';
import '../models/safe_store.dart';
import '../utils/app_colors.dart';
import '../utils/timeago_config.dart';
import '../widgets/report_dialog.dart';

class AdvancedSafeStoreCommentWidget extends StatefulWidget {
  final Comment comment;
  final SafeStore store;
  final VoidCallback? onReply;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const AdvancedSafeStoreCommentWidget({
    super.key,
    required this.comment,
    required this.store,
    this.onReply,
    this.onEdit,
    this.onDelete,
  });

  @override
  State<AdvancedSafeStoreCommentWidget> createState() =>
      _AdvancedSafeStoreCommentWidgetState();
}

class _AdvancedSafeStoreCommentWidgetState
    extends State<AdvancedSafeStoreCommentWidget>
    with TickerProviderStateMixin {
  bool _isLiked = false;
  bool _isExpanded = false;
  late AnimationController _likeAnimationController;
  late AnimationController _expandAnimationController;
  late Animation<double> _likeAnimation;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _likeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _likeAnimation = Tween<double>(begin: 1.0, end: 1.3).animate(
      CurvedAnimation(
        parent: _likeAnimationController,
        curve: Curves.elasticOut,
      ),
    );
    _expandAnimation = CurvedAnimation(
      parent: _expandAnimationController,
      curve: Curves.easeInOut,
    );

    _checkIfLiked();
  }

  @override
  void dispose() {
    _likeAnimationController.dispose();
    _expandAnimationController.dispose();
    super.dispose();
  }

  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  Future<void> _checkIfLiked() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (authProvider.currentUser != null && widget.comment.id != null) {
      // Check if user liked this comment
      // This would require implementing a likes system for comments
      // For now, we'll use a placeholder
      setState(() {
        _isLiked = false;
      });
    }
  }

  Future<void> _toggleLike() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (authProvider.currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يجب تسجيل الدخول أولاً', style: GoogleFonts.cairo()),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLiked = !_isLiked;
    });

    _likeAnimationController.forward().then((_) {
      _likeAnimationController.reverse();
    });

    // Here you would implement the actual like functionality
    // For now, we'll just show a placeholder message
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _expandAnimationController.forward();
    } else {
      _expandAnimationController.reverse();
    }
  }

  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => ReportDialog(
        contentId: widget.comment.id ?? '',
        contentType: 'comment',
      ),
    );
  }

  void _handleReport(String reason) {
    // Handle report
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم الإبلاغ عن التعليق', style: GoogleFonts.cairo()),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showOptionsMenu() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isOwner =
        authProvider.currentUser != null &&
        authProvider.currentUser!.uid == widget.comment.userId;
    final isAdmin = authProvider.isAdmin;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            if (isOwner) ...[
              ListTile(
                leading: const Icon(Icons.edit, color: Colors.blue),
                title: Text('تعديل', style: GoogleFonts.cairo()),
                onTap: () {
                  Navigator.pop(context);
                  if (widget.onEdit != null) widget.onEdit!();
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: Text('حذف', style: GoogleFonts.cairo()),
                onTap: () {
                  Navigator.pop(context);
                  if (widget.onDelete != null) widget.onDelete!();
                },
              ),
            ],
            if (!isOwner) ...[
              ListTile(
                leading: const Icon(Icons.reply, color: Colors.green),
                title: Text('رد', style: GoogleFonts.cairo()),
                onTap: () {
                  Navigator.pop(context);
                  if (widget.onReply != null) widget.onReply!();
                },
              ),
              ListTile(
                leading: const Icon(Icons.report, color: Colors.orange),
                title: Text('إبلاغ', style: GoogleFonts.cairo()),
                onTap: () {
                  Navigator.pop(context);
                  _showReportDialog();
                },
              ),
            ],
            if (isAdmin) ...[
              const Divider(),
              ListTile(
                leading: Icon(
                  widget.comment.isApproved
                      ? Icons.verified
                      : Icons.verified_outlined,
                  color: Colors.green,
                ),
                title: Text(
                  widget.comment.isApproved ? 'إلغاء التحقق' : 'تحقق',
                  style: GoogleFonts.cairo(),
                ),
                onTap: () {
                  Navigator.pop(context);
                  // Handle verification
                },
              ),
            ],
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: widget.comment.isApproved
              ? Colors.green.withValues(alpha: 0.3)
              : AppColors.primary.withValues(alpha: 0.1),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.08),
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  (widget.comment.isApproved ? Colors.green : AppColors.primary)
                      .withValues(alpha: 0.1),
                  (widget.comment.isApproved ? Colors.green : AppColors.primary)
                      .withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                // User Avatar
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withValues(alpha: 0.9),
                        Colors.white.withValues(alpha: 0.7),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    border: Border.all(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      width: 2,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(13),
                    child: widget.comment.userAvatar != null
                        ? Image.network(
                            widget.comment.userAvatar!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Icon(
                                Icons.person,
                                color: AppColors.primary,
                                size: 24,
                              );
                            },
                          )
                        : Icon(
                            Icons.person,
                            color: AppColors.primary,
                            size: 24,
                          ),
                  ),
                ),
                const SizedBox(width: 12),
                // User Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Flexible(
                            child: Text(
                              widget.comment.username,
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: const Color(0xFF1F2937),
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (widget.comment.isApproved) ...[
                            const SizedBox(width: 6),
                            Icon(Icons.verified, color: Colors.green, size: 18),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Text(
                            _formatTimeAgo(widget.comment.createdAt),
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Options Menu
                IconButton(
                  icon: const Icon(Icons.more_vert),
                  onPressed: _showOptionsMenu,
                  color: Colors.grey.shade600,
                ),
              ],
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Comment Text
                AnimatedCrossFade(
                  firstChild: Text(
                    widget.comment.content,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: const Color(0xFF1F2937),
                      height: 1.6,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  secondChild: Text(
                    widget.comment.content,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: const Color(0xFF1F2937),
                      height: 1.6,
                    ),
                  ),
                  crossFadeState: _isExpanded
                      ? CrossFadeState.showSecond
                      : CrossFadeState.showFirst,
                  duration: const Duration(milliseconds: 300),
                ),

                // Expand/Collapse button for long comments
                if (widget.comment.content.length > 150) ...[
                  const SizedBox(height: 8),
                  GestureDetector(
                    onTap: _toggleExpanded,
                    child: Text(
                      _isExpanded ? 'عرض أقل' : 'عرض المزيد',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],

                const SizedBox(height: 16),

                // Actions
                Row(
                  children: [
                    // Like Button
                    AnimatedBuilder(
                      animation: _likeAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _likeAnimation.value,
                          child: GestureDetector(
                            onTap: _toggleLike,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                color: _isLiked
                                    ? Colors.red.withValues(alpha: 0.1)
                                    : Colors.grey.shade100,
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: _isLiked
                                      ? Colors.red.withValues(alpha: 0.3)
                                      : Colors.grey.shade300,
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    _isLiked
                                        ? Icons.favorite
                                        : Icons.favorite_border,
                                    color: _isLiked
                                        ? Colors.red
                                        : Colors.grey.shade600,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    '${widget.comment.likesCount}',
                                    style: GoogleFonts.cairo(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: _isLiked
                                          ? Colors.red
                                          : Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),

                    const SizedBox(width: 12),

                    // Reply Button
                    GestureDetector(
                      onTap: widget.onReply,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.grey.shade300,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.reply,
                              color: Colors.grey.shade600,
                              size: 16,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              'رد',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const Spacer(),

                    // Updated indicator
                    if (widget.comment.updatedAt?.isAfter(
                          widget.comment.createdAt.add(
                            const Duration(minutes: 1),
                          ),
                        ) ==
                        true)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'معدل',
                          style: GoogleFonts.cairo(
                            fontSize: 10,
                            color: Colors.blue,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

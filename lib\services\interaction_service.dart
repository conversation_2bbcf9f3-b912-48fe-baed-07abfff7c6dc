import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:yassincil/models/like.dart';
import 'package:yassincil/models/comment.dart';
import 'package:yassincil/models/forum_post.dart';
import 'package:yassincil/models/notification.dart';
import 'package:yassincil/services/notification_service.dart';

class InteractionService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // ==================== LIKES ====================

  /// إضافة أو إزالة إعجاب
  static Future<bool> toggleLike({
    required String userId,
    required String username,
    String? userAvatar,
    required String targetId,
    required String targetType, // 'post' or 'comment'
    LikeType type = LikeType.like,
  }) async {
    try {
      final likeId = '${userId}_$targetId';
      final likeRef = _firestore.collection('likes').doc(likeId);
      final likeDoc = await likeRef.get();

      if (likeDoc.exists) {
        // إزالة الإعجاب
        await likeRef.delete();
        await _updateLikeCount(targetId, targetType, -1);
        return false; // تم إزالة الإعجاب
      } else {
        // إضافة الإعجاب
        final like = Like(
          id: likeId,
          userId: userId,
          username: username,
          userAvatar: userAvatar,
          targetId: targetId,
          targetType: targetType,
          type: type,
          createdAt: DateTime.now(),
        );

        await likeRef.set(like.toMap());
        await _updateLikeCount(targetId, targetType, 1);

        // Create notification
        final targetDoc = await _firestore.collection(targetType == 'post' ? 'forum_posts' : 'comments').doc(targetId).get();
        if (targetDoc.exists) {
          final targetData = targetDoc.data() as Map<String, dynamic>;
          final targetOwnerId = targetData['userId'];
          await NotificationService.createNotification(
            userId: targetOwnerId,
            type: NotificationType.like,
            targetId: targetId,
            targetType: targetType,
            senderId: userId,
            senderName: username,
            senderAvatar: userAvatar,
          );
        }

        return true; // تم إضافة الإعجاب
      }
    } catch (e) {
      debugPrint('خطأ في toggle like: $e');
      throw Exception('فشل في تحديث الإعجاب');
    }
  }

  /// تحديث عداد الإعجابات
  static Future<void> _updateLikeCount(
    String targetId,
    String targetType,
    int increment,
  ) async {
    try {
      final collection = targetType == 'post' ? 'forum_posts' : 'comments';
      final docRef = _firestore.collection(collection).doc(targetId);

      await docRef.update({'likesCount': FieldValue.increment(increment)});
    } catch (e) {
      debugPrint('خطأ في تحديث عداد الإعجابات: $e');
    }
  }

  /// التحقق من وجود إعجاب
  static Future<bool> isLiked({
    required String userId,
    required String targetId,
  }) async {
    try {
      final likeId = '${userId}_$targetId';
      final likeDoc = await _firestore.collection('likes').doc(likeId).get();
      return likeDoc.exists;
    } catch (e) {
      debugPrint('خطأ في التحقق من الإعجاب: $e');
      return false;
    }
  }

  /// الحصول على إعجابات المنشور/التعليق
  static Stream<List<Like>> getLikes({
    required String targetId,
    required String targetType,
  }) {
    return _firestore
        .collection('likes')
        .where('targetId', isEqualTo: targetId)
        .where('targetType', isEqualTo: targetType)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Like.fromFirestore(doc)).toList(),
        );
  }

  /// الحصول على عدد الإعجابات حسب النوع
  static Future<Map<LikeType, int>> getLikesCountByType({
    required String targetId,
    required String targetType,
  }) async {
    try {
      final snapshot = await _firestore
          .collection('likes')
          .where('targetId', isEqualTo: targetId)
          .where('targetType', isEqualTo: targetType)
          .get();

      final Map<LikeType, int> counts = {};
      for (final type in LikeType.values) {
        counts[type] = 0;
      }

      for (final doc in snapshot.docs) {
        final like = Like.fromFirestore(doc);
        counts[like.type] = (counts[like.type] ?? 0) + 1;
      }

      return counts;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات الإعجابات: $e');
      return {};
    }
  }

  // ==================== COMMENTS ====================

  /// إضافة تعليق
  static Future<String> addComment({
    required String postId,
    required String userId,
    required String username,
    String? userAvatar,
    required String content,
    String? parentCommentId,
    List<String> imageUrls = const [],
  }) async {
    try {
      final comment = Comment(
        postId: postId,
        userId: userId,
        username: username,
        userAvatar: userAvatar,
        content: content,
        parentCommentId: parentCommentId,
        imageUrls: imageUrls,
        createdAt: DateTime.now(),
      );

      final docRef = await _firestore
          .collection('comments')
          .add(comment.toMap());

      // تحديث عداد التعليقات في المنشور
      await _updateCommentCount(postId, 1);

      // إذا كان رد على تعليق، تحديث عداد الردود
      if (parentCommentId != null) {
        await _updateReplyCount(parentCommentId, 1);
      }

      // Create notification for post owner
      final postDoc = await _firestore.collection('forum_posts').doc(postId).get();
      if (postDoc.exists) {
        final postData = postDoc.data() as Map<String, dynamic>;
        final postOwnerId = postData['userId'];
        await NotificationService.createNotification(
          userId: postOwnerId,
          type: parentCommentId == null ? NotificationType.comment : NotificationType.reply,
          targetId: postId,
          targetType: 'post',
          senderId: userId,
          senderName: username,
          senderAvatar: userAvatar,
        );
      }

      // Create notification for parent comment owner
      if (parentCommentId != null) {
        final parentCommentDoc = await _firestore.collection('comments').doc(parentCommentId).get();
        if (parentCommentDoc.exists) {
          final parentCommentData = parentCommentDoc.data() as Map<String, dynamic>;
          final parentCommentOwnerId = parentCommentData['userId'];
          await NotificationService.createNotification(
            userId: parentCommentOwnerId,
            type: NotificationType.reply,
            targetId: postId,
            targetType: 'post',
            senderId: userId,
            senderName: username,
            senderAvatar: userAvatar,
          );
        }
      }

      return docRef.id;
    } catch (e) {
      debugPrint('خطأ في إضافة التعليق: $e');
      throw Exception('فشل في إضافة التعليق');
    }
  }

  /// تحديث تعليق
  static Future<void> updateComment({
    required String commentId,
    required String content,
    List<String>? imageUrls,
  }) async {
    try {
      final updateData = {
        'content': content,
        'isEdited': true,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      };

      if (imageUrls != null) {
        updateData['imageUrls'] = imageUrls;
      }

      await _firestore.collection('comments').doc(commentId).update(updateData);
    } catch (e) {
      debugPrint('خطأ في تحديث التعليق: $e');
      throw Exception('فشل في تحديث التعليق');
    }
  }

  /// حذف تعليق
  static Future<void> deleteComment({
    required String commentId,
    required String postId,
    String? parentCommentId,
  }) async {
    try {
      // حذف التعليق
      await _firestore.collection('comments').doc(commentId).delete();

      // تحديث عداد التعليقات في المنشور
      await _updateCommentCount(postId, -1);

      // إذا كان رد على تعليق، تحديث عداد الردود
      if (parentCommentId != null) {
        await _updateReplyCount(parentCommentId, -1);
      }

      // حذف جميع الردود على هذا التعليق
      final repliesSnapshot = await _firestore
          .collection('comments')
          .where('parentCommentId', isEqualTo: commentId)
          .get();

      final batch = _firestore.batch();
      for (final doc in repliesSnapshot.docs) {
        batch.delete(doc.reference);
      }
      await batch.commit();

      // تحديث عداد التعليقات بعدد الردود المحذوفة
      if (repliesSnapshot.docs.isNotEmpty) {
        await _updateCommentCount(postId, -repliesSnapshot.docs.length);
      }
    } catch (e) {
      debugPrint('خطأ في حذف التعليق: $e');
      throw Exception('فشل في حذف التعليق');
    }
  }

  /// تحديث عداد التعليقات
  static Future<void> _updateCommentCount(String postId, int increment) async {
    try {
      await _firestore.collection('forum_posts').doc(postId).update({
        'commentsCount': FieldValue.increment(increment),
      });
    } catch (e) {
      debugPrint('خطأ في تحديث عداد التعليقات: $e');
    }
  }

  /// تحديث عداد الردود
  static Future<void> _updateReplyCount(String commentId, int increment) async {
    try {
      await _firestore.collection('comments').doc(commentId).update({
        'repliesCount': FieldValue.increment(increment),
      });
    } catch (e) {
      debugPrint('خطأ في تحديث عداد الردود: $e');
    }
  }

  /// الحصول على تعليقات المنشور
  static Stream<List<Comment>> getPostComments({
    required String postId,
    int limit = 50,
  }) {
    return _firestore
        .collection('comments')
        .where('postId', isEqualTo: postId)
        .where('parentCommentId', isNull: true) // التعليقات الرئيسية فقط
        .where('isDeleted', isEqualTo: false)
        .where('isApproved', isEqualTo: true)
        .orderBy('createdAt', descending: false)
        .limit(limit)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Comment.fromFirestore(doc)).toList(),
        );
  }

  /// الحصول على ردود التعليق
  static Stream<List<Comment>> getCommentReplies({
    required String commentId,
    int limit = 20,
  }) {
    return _firestore
        .collection('comments')
        .where('parentCommentId', isEqualTo: commentId)
        .where('isDeleted', isEqualTo: false)
        .where('isApproved', isEqualTo: true)
        .orderBy('createdAt', descending: false)
        .limit(limit)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Comment.fromFirestore(doc)).toList(),
        );
  }

  /// الحصول على تعليق محدد
  static Future<Comment?> getComment(String commentId) async {
    try {
      final doc = await _firestore.collection('comments').doc(commentId).get();
      if (doc.exists) {
        return Comment.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على التعليق: $e');
      return null;
    }
  }

  // ==================== SHARES ====================

  /// تحديث عداد المشاركات
  static Future<void> incrementShareCount(String postId) async {
    try {
      await _firestore.collection('forum_posts').doc(postId).update({
        'sharesCount': FieldValue.increment(1),
      });
    } catch (e) {
      debugPrint('خطأ في تحديث عداد المشاركات: $e');
    }
  }

  /// تحديث عداد المشاهدات
  static Future<void> incrementViewCount(String postId) async {
    try {
      await _firestore.collection('forum_posts').doc(postId).update({
        'viewsCount': FieldValue.increment(1),
      });
    } catch (e) {
      debugPrint('خطأ في تحديث عداد المشاهدات: $e');
    }
  }

  // ==================== REPORTS ====================

  /// إبلاغ عن منشور أو تعليق
  static Future<void> reportContent({
    required String reporterId,
    required String reporterName,
    required String targetId,
    required String targetType, // 'post' or 'comment'
    required String reason,
    String? description,
  }) async {
    try {
      final report = {
        'reporterId': reporterId,
        'reporterName': reporterName,
        'targetId': targetId,
        'targetType': targetType,
        'reason': reason,
        'description': description,
        'createdAt': Timestamp.fromDate(DateTime.now()),
        'status': 'pending', // pending, reviewed, resolved
      };

      await _firestore.collection('reports').add(report);

      // تحديث عداد البلاغات
      final collection = targetType == 'post' ? 'forum_posts' : 'comments';
      await _firestore.collection(collection).doc(targetId).update({
        'reportCount': FieldValue.increment(1),
      });
    } catch (e) {
      debugPrint('خطأ في إرسال البلاغ: $e');
      throw Exception('فشل في إرسال البلاغ');
    }
  }

  // ==================== ADMIN FUNCTIONS ====================

  /// تثبيت/إلغاء تثبيت منشور
  static Future<void> togglePinPost(String postId, bool isPinned) async {
    try {
      await _firestore.collection('forum_posts').doc(postId).update({
        'isPinned': !isPinned,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      debugPrint('خطأ في تثبيت المنشور: $e');
      throw Exception('فشل في تثبيت المنشور');
    }
  }

  /// إخفاء/إظهار منشور
  static Future<void> toggleHidePost(String postId, bool isApproved) async {
    try {
      await _firestore.collection('forum_posts').doc(postId).update({
        'isApproved': !isApproved,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      debugPrint('خطأ في إخفاء المنشور: $e');
      throw Exception('فشل في إخفاء المنشور');
    }
  }

  /// حذف منشور
  static Future<void> deletePost(String postId) async {
    try {
      // حذف جميع التعليقات المرتبطة
      final commentsSnapshot = await _firestore
          .collection('comments')
          .where('postId', isEqualTo: postId)
          .get();

      final batch = _firestore.batch();

      // حذف التعليقات
      for (final doc in commentsSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // حذف الإعجابات
      final likesSnapshot = await _firestore
          .collection('likes')
          .where('targetId', isEqualTo: postId)
          .where('targetType', isEqualTo: 'post')
          .get();

      for (final doc in likesSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // حذف المنشور
      batch.delete(_firestore.collection('forum_posts').doc(postId));

      await batch.commit();
    } catch (e) {
      debugPrint('خطأ في حذف المنشور: $e');
      throw Exception('فشل في حذف المنشور');
    }
  }
}

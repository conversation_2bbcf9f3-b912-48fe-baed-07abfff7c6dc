import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:provider/provider.dart';
import 'package:yassincil/providers/food_provider.dart';

// Removed: import 'dart:io'; // No longer needed for File operations
// Removed: import 'package:image_picker/image_picker.dart'; // No longer needed

import 'package:yassincil/models/food_item.dart';
// Removed: import 'package:yassincil/services/storage_service.dart'; // No longer needed for file upload
// Removed: import 'package:yassincil/utils/app_constants.dart'; // No longer needed for Storage paths

class AddEditFoodItemScreen extends StatefulWidget {
  final FoodItem? foodItem; // Null if adding, not null if editing

  const AddEditFoodItemScreen({super.key, this.foodItem});

  @override
  State<AddEditFoodItemScreen> createState() => _AddEditFoodItemScreenState();
}

class _AddEditFoodItemScreenState extends State<AddEditFoodItemScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _categoryController;
  late TextEditingController _detailsController;
  late TextEditingController _ingredientsController;
  late TextEditingController _warningsController;
  late TextEditingController _ratingController;
  late TextEditingController _caloriesController;
  late TextEditingController _proteinController;
  late TextEditingController _carbohydratesController;
  late TextEditingController _fatController;
  late TextEditingController _glycemicIndexController;
  bool _isGlutenFree = true;
  late TextEditingController _imageUrlTextController;
  bool _available = true;
  DateTime? _updatedAt;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.foodItem?.name ?? '');
    _categoryController = TextEditingController(
      text: widget.foodItem?.category ?? '',
    );
    _detailsController = TextEditingController(
      text: widget.foodItem?.details ?? '',
    );
    _ingredientsController = TextEditingController(
      text: widget.foodItem?.ingredients ?? '',
    );
    _warningsController = TextEditingController(
      text: widget.foodItem?.warnings ?? '',
    );
    _caloriesController = TextEditingController(
      text: widget.foodItem?.calories.toString() ?? '',
    );
    _proteinController = TextEditingController(
      text: widget.foodItem?.protein.toString() ?? '',
    );
    _carbohydratesController = TextEditingController(
      text: widget.foodItem?.carbohydrates.toString() ?? '',
    );
    _fatController = TextEditingController(
      text: widget.foodItem?.fat.toString() ?? '',
    );
    _glycemicIndexController = TextEditingController(
      text: widget.foodItem?.glycemicIndex.toString() ?? '',
    );
    _ratingController = TextEditingController(
      text: widget.foodItem?.rating?.toString() ?? '',
    );
    _isGlutenFree = widget.foodItem?.isGlutenFree ?? true;
    _imageUrlTextController = TextEditingController(
      text: widget.foodItem?.imageUrl ?? '',
    );
    _available = widget.foodItem?.available ?? true;
    _updatedAt = widget.foodItem?.updatedAt;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _categoryController.dispose();
    _detailsController.dispose();
    _ingredientsController.dispose();
    _warningsController.dispose();
    _caloriesController.dispose();
    _proteinController.dispose();
    _carbohydratesController.dispose();
    _fatController.dispose();
    _glycemicIndexController.dispose();
    _ratingController.dispose();
    _imageUrlTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ImageProvider? previewImageProvider;
    if (_imageUrlTextController.text.isNotEmpty) {
      if (Uri.tryParse(_imageUrlTextController.text)?.hasAbsolutePath ??
          false) {
        previewImageProvider = NetworkImage(_imageUrlTextController.text);
      }
    }
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.foodItem == null
              ? 'إضافة عنصر غذائي جديد'
              : 'تعديل عنصر غذائي',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Column(
                  children: [
                    Container(
                      height: 150,
                      width: 150,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.grey[300]!),
                        image: previewImageProvider != null
                            ? DecorationImage(
                                image: previewImageProvider,
                                fit: BoxFit.cover,
                              )
                            : null,
                      ),
                      child: previewImageProvider == null
                          ? Icon(Icons.image, size: 50, color: Colors.grey[400])
                          : null,
                    ),
                    const SizedBox(height: 10),
                  ],
                ),
              ),
              TextFormField(
                controller: _imageUrlTextController,
                decoration: const InputDecoration(
                  labelText: 'رابط الصورة (URL)',
                  hintText: 'https://example.com/image.jpg',
                  prefixIcon: Icon(Icons.link),
                ),
                style: GoogleFonts.cairo(),
                keyboardType: TextInputType.url,
                onChanged: (value) {
                  setState(() {});
                },
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final uri = Uri.tryParse(value);
                    if (uri == null || !uri.hasAbsolutePath) {
                      return 'الرجاء إدخال رابط صورة صالح.';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم العنصر الغذائي',
                  hintText: 'مثال: خبز أرز',
                  prefixIcon: Icon(Icons.fastfood),
                ),
                style: GoogleFonts.cairo(),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال اسم العنصر الغذائي';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 15),
              TextFormField(
                controller: _categoryController,
                decoration: const InputDecoration(
                  labelText: 'الفئة (مثال: حبوب، ألبان، خضروات)',
                  hintText: 'مثال: حبوب',
                  prefixIcon: Icon(Icons.category),
                ),
                style: GoogleFonts.cairo(),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال الفئة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 15),
              TextFormField(
                controller: _ingredientsController,
                decoration: const InputDecoration(
                  labelText: 'المكونات',
                  hintText: 'مثال: دقيق أرز، ماء، ملح',
                  prefixIcon: Icon(Icons.list_alt),
                ),
                style: GoogleFonts.cairo(),
                maxLines: 2,
              ),
              const SizedBox(height: 15),
              TextFormField(
                controller: _warningsController,
                decoration: const InputDecoration(
                  labelText: 'تحذيرات غذائية',
                  hintText: 'مثال: يحتوي على لاكتوز',
                  prefixIcon: Icon(Icons.warning_amber_rounded),
                ),
                style: GoogleFonts.cairo(),
                maxLines: 2,
              ),
              const SizedBox(height: 15),
              TextFormField(
                controller: _ratingController,
                decoration: const InputDecoration(
                  labelText: 'تقييم المستخدمين (من 1 إلى 5)',
                  hintText: 'مثال: 4.5',
                  prefixIcon: Icon(Icons.star),
                ),
                style: GoogleFonts.cairo(),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final rating = double.tryParse(value);
                    if (rating == null || rating < 1 || rating > 5) {
                      return 'أدخل تقييم بين 1 و 5';
                    }
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _caloriesController,
                decoration: const InputDecoration(
                  labelText: 'السعرات الحرارية',
                  hintText: 'مثال: 200',
                  prefixIcon: Icon(Icons.local_fire_department),
                ),
                style: GoogleFonts.cairo(),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
              ),
              TextFormField(
                controller: _proteinController,
                decoration: const InputDecoration(
                  labelText: 'البروتين',
                  hintText: 'مثال: 10',
                  prefixIcon: Icon(Icons.fitness_center),
                ),
                style: GoogleFonts.cairo(),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
              ),
              TextFormField(
                controller: _carbohydratesController,
                decoration: const InputDecoration(
                  labelText: 'الكربوهيدرات',
                  hintText: 'مثال: 30',
                  prefixIcon: Icon(Icons.grain),
                ),
                style: GoogleFonts.cairo(),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
              ),
              TextFormField(
                controller: _fatController,
                decoration: const InputDecoration(
                  labelText: 'الدهون',
                  hintText: 'مثال: 5',
                  prefixIcon: Icon(Icons.opacity),
                ),
                style: GoogleFonts.cairo(),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
              ),
              TextFormField(
                controller: _glycemicIndexController,
                decoration: const InputDecoration(
                  labelText: 'المؤشر الجلايسيمي',
                  hintText: 'مثال: 55',
                  prefixIcon: Icon(Icons.show_chart),
                ),
                style: GoogleFonts.cairo(),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 15),
              SwitchListTile(
                title: Text('خالي من الغلوتين؟', style: GoogleFonts.cairo()),
                value: _isGlutenFree,
                activeColor: Theme.of(context).primaryColor,
                onChanged: (bool value) {
                  setState(() {
                    _isGlutenFree = value;
                  });
                },
              ),
              SwitchListTile(
                title: Text('متوفر في السوق؟', style: GoogleFonts.cairo()),
                value: _available,
                activeColor: Theme.of(context).primaryColor,
                onChanged: (bool value) {
                  setState(() {
                    _available = value;
                  });
                },
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  Text('آخر تحديث:', style: GoogleFonts.cairo()),
                  const SizedBox(width: 8),
                  Text(
                    _updatedAt != null
                        ? _updatedAt!.toString().substring(0, 16)
                        : 'سيتم تعيينه تلقائياً',
                    style: GoogleFonts.cairo(
                      fontSize: 13,
                      color: Colors.grey[700],
                    ),
                  ),
                  const Spacer(),
                  if (widget.foodItem != null)
                    TextButton(
                      onPressed: () async {
                        final picked = await showDatePicker(
                          context: context,
                          initialDate: _updatedAt ?? DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime(2100),
                        );
                        if (picked != null) {
                          setState(() {
                            _updatedAt = picked;
                          });
                        }
                      },
                      child: Text('تعديل', style: GoogleFonts.cairo()),
                    ),
                ],
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      icon: Icon(Icons.save),
                      label: Text(
                        widget.foodItem == null ? 'حفظ' : 'تحديث',
                        style: GoogleFonts.cairo(),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        textStyle: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      onPressed: () async {
                        if (_formKey.currentState?.validate() ?? false) {
                          final food = FoodItem(
                            id: widget.foodItem?.id,
                            name: _nameController.text.trim(),
                            category: _categoryController.text.trim(),
                            isGlutenFree: _isGlutenFree,
                            details: _detailsController.text.trim(),
                            imageUrls:
                                _imageUrlTextController.text.trim().isNotEmpty
                                ? [_imageUrlTextController.text.trim()]
                                : [],
                            ingredients:
                                _ingredientsController.text.trim().isNotEmpty
                                ? _ingredientsController.text.trim()
                                : null,
                            warnings: _warningsController.text.trim().isNotEmpty
                                ? _warningsController.text.trim()
                                : null,
                            averageRating:
                                _ratingController.text.trim().isNotEmpty
                                ? double.tryParse(
                                        _ratingController.text.trim(),
                                      ) ??
                                      0.0
                                : 0.0,
                            available: _available,
                            createdAt:
                                widget.foodItem?.createdAt ?? DateTime.now(),
                            updatedAt: DateTime.now(),
                            likesCount: widget.foodItem?.likesCount ?? 0,
                            commentsCount: widget.foodItem?.commentsCount ?? 0,
                            calories:
                                double.tryParse(
                                  _caloriesController.text.trim(),
                                ) ??
                                0.0,
                            protein:
                                double.tryParse(
                                  _proteinController.text.trim(),
                                ) ??
                                0.0,
                            carbohydrates:
                                double.tryParse(
                                  _carbohydratesController.text.trim(),
                                ) ??
                                0.0,
                            fat:
                                double.tryParse(_fatController.text.trim()) ??
                                0.0,
                            glycemicIndex:
                                int.tryParse(
                                  _glycemicIndexController.text.trim(),
                                ) ??
                                0,
                          );
                          final provider = Provider.of<FoodProvider>(
                            context,
                            listen: false,
                          );
                          try {
                            if (widget.foodItem == null) {
                              await provider.addFoodItem(
                                name: food.name,
                                details: food.details,
                                category: food.category,
                                isGlutenFree: food.isGlutenFree,
                                imageUrl: food.imageUrl,
                              );
                            } else {
                              await provider.updateFoodItem(
                                foodId: widget.foodItem!.id!,
                                name: food.name,
                                details: food.details,
                                category: food.category,
                                isGlutenFree: food.isGlutenFree,
                                imageUrl: food.imageUrl,
                              );
                            }
                            if (mounted && context.mounted) {
                              Navigator.of(context).pop();
                            }
                          } catch (e) {
                            if (mounted && context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'فشل الحفظ: $e',
                                    style: GoogleFonts.cairo(),
                                  ),
                                ),
                              );
                            }
                          }
                        }
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

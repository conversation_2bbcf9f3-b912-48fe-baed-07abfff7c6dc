import 'package:cloud_firestore/cloud_firestore.dart';

class Restaurant {
  final String? id;
  final String name;
  final String address;
  final String? phone;
  final String description;
  final String? imageUrl;
  final List<String> imageUrls;
  final double? rating; // متوسط التقييم
  final int reviewCount; // عدد التقييمات
  final GeoPoint? location; // لإحداثيات الخريطة (اختياري)
  final double? latitude;
  final double? longitude;
  final List<String>
  glutenFreeOptions; // قائمة بالخيارات المتاحة الخالية من الغلوتين
  final List<String> likes;
  final int likesCount;
  final int commentsCount;
  final String? website;
  final List<String>? images;
  final DateTime? createdAt;

  // خاصية محسوبة للتحقق من وجود خيارات خالية من الغلوتين
  bool get hasGlutenFreeOptions => glutenFreeOptions.isNotEmpty;

  Restaurant({
    this.id,
    required this.name,
    required this.address,
    this.phone,
    this.description = '',
    this.imageUrl,
    this.imageUrls = const [],
    this.rating,
    this.reviewCount = 0,
    this.location,
    this.latitude,
    this.longitude,
    this.glutenFreeOptions = const [],
    this.likes = const [],
    this.likesCount = 0,
    this.commentsCount = 0,
    this.website,
    this.images,
    this.createdAt,
  });

  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'name': name.trim(),
      'address': address.trim(),
      'phone': phone?.trim(),
      'description': description.trim(),
      'imageUrl': imageUrl?.trim(),
      'imageUrls': imageUrls,
      'rating': rating ?? 0.0,
      'reviewCount': reviewCount,
      'location': location,
      'latitude': latitude,
      'longitude': longitude,
      'glutenFreeOptions': glutenFreeOptions,
      'likes': likes,
      'likesCount': likesCount,
      'commentsCount': commentsCount,
      'website': website?.trim(),
      'images': images,
      'createdAt': createdAt != null
          ? Timestamp.fromDate(createdAt!)
          : FieldValue.serverTimestamp(),
    };

    // إزالة القيم الفارغة
    map.removeWhere(
      (key, value) => value == null || (value is String && value.isEmpty),
    );

    return map;
  }

  factory Restaurant.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Restaurant(
      id: doc.id,
      name: data['name'] ?? '',
      address: data['address'] ?? '',
      phone: data['phone'] ?? data['phoneNumber'],
      description: data['description'] ?? '',
      imageUrl: data['imageUrl'],
      imageUrls: List<String>.from(data['imageUrls'] ?? data['images'] ?? []),
      rating: (data['rating'] as num?)?.toDouble(),
      reviewCount: data['reviewCount'] ?? 0,
      location: data['location'] as GeoPoint?,
      latitude: (data['latitude'] as num?)?.toDouble(),
      longitude: (data['longitude'] as num?)?.toDouble(),
      glutenFreeOptions: List<String>.from(data['glutenFreeOptions'] ?? []),
      likes: List<String>.from(data['likes'] ?? []),
      likesCount: data['likesCount'] ?? 0,
      commentsCount: data['commentsCount'] ?? 0,
      website: data['website'],
      images: data['images'] != null ? List<String>.from(data['images']) : null,
      createdAt: _parseDateTime(data['createdAt']),
    );
  }

  Restaurant copyWith({
    String? id,
    String? name,
    String? address,
    String? phone,
    String? description,
    String? imageUrl,
    List<String>? imageUrls,
    double? rating,
    int? reviewCount,
    GeoPoint? location,
    double? latitude,
    double? longitude,
    List<String>? glutenFreeOptions,
    List<String>? likes,
    int? likesCount,
    int? commentsCount,
    String? website,
    List<String>? images,
    DateTime? createdAt,
  }) {
    return Restaurant(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      imageUrls: imageUrls ?? this.imageUrls,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      glutenFreeOptions: glutenFreeOptions ?? this.glutenFreeOptions,
      likes: likes ?? this.likes,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      website: website ?? this.website,
      images: images ?? this.images,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // دالة مساعدة لتحويل التاريخ
  static DateTime? _parseDateTime(dynamic dateValue) {
    if (dateValue == null) return null;

    if (dateValue is Timestamp) {
      return dateValue.toDate();
    } else if (dateValue is int) {
      // إذا كان رقم (milliseconds since epoch)
      return DateTime.fromMillisecondsSinceEpoch(dateValue);
    } else if (dateValue is String) {
      // إذا كان نص
      return DateTime.tryParse(dateValue);
    }

    return null;
  }
}

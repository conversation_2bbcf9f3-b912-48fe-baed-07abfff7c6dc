import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:share_plus/share_plus.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:yassincil/providers/food_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/providers/favorites_provider.dart';
import 'package:yassincil/models/food_item.dart';
import 'package:yassincil/screens/foods/food_detail_screen.dart';
import 'package:yassincil/screens/foods/add_edit_food_screen.dart';
import 'package:yassincil/screens/medications/barcode_scanner_screen.dart';
import 'package:yassincil/screens/medications/favorites_screen.dart';
import 'package:yassincil/auth/screens/login_screen.dart';
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/widgets/empty_state_widget.dart';

import 'package:yassincil/screens/foods/food_settings_screen.dart';
import 'package:yassincil/screens/foods/verify_foods_screen.dart';
import 'package:yassincil/screens/foods/user_add_food_screen.dart';
import 'package:yassincil/screens/foods/food_review_screen.dart';
import 'package:yassincil/screens/foods/foods_management_screen.dart';

class EnhancedFoodsScreen extends StatefulWidget {
  final String? initialSearch;
  final bool showBackButton;

  const EnhancedFoodsScreen({
    super.key,
    this.initialSearch,
    this.showBackButton = true,
  });

  @override
  State<EnhancedFoodsScreen> createState() => _EnhancedFoodsScreenState();
}

class _EnhancedFoodsScreenState extends State<EnhancedFoodsScreen>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late AnimationController _animationController;
  late AnimationController _fabController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;

  String _searchQuery = '';
  String _selectedCategory = 'الكل';
  bool _showOnlyGlutenFree = false; // فلتر الأطعمة الخالية من الجلوتين
  String _currentSortBy = 'date';
  bool _isGridView = false;
  final bool _showFilters = false;

  bool _showScrollToTop = false; // إظهار زر العودة للأعلى
  bool _showQuickJump = false; // إظهار الفهرس السريع
  bool _isLoadingMore = false; // تحميل المزيد من البيانات
  int _currentPage = 1; // الصفحة الحالية
  Timer? _debounce;
  List<String> _recentSearches = [];
  ScrollController? _scrollController;
  List<String> _searchSuggestions = [];
  Map<String, int> _categoryUsageCount = {}; // تتبع استخدام الفئات
  String _lastUsedCategory = 'الكل'; // آخر فئة مستخدمة

  bool _isSearching = false;
  final FocusNode _searchFocusNode = FocusNode();

  final List<String> _categories = [
    'الكل',
    'حبوب ومخبوزات',
    'ألبان ومنتجاتها',
    'خضروات',
    'فواكه',
    'لحوم وأسماك',
    'حلويات',
    'مشروبات',
    'توابل وبهارات',
    'زيوت ودهون',
    'مكسرات وبذور',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.initialSearch ?? '');
    _searchQuery = widget.initialSearch ?? '';
    _scrollController = ScrollController();

    // إعداد مراقب التمرير
    _scrollController!.addListener(_onScroll);

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    _fabController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSearchState();
      _loadFoods();
      _animationController.forward();
      _fabController.forward();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController?.dispose();
    _animationController.dispose();
    _fabController.dispose();
    _searchFocusNode.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController!.hasClients) {
      final showScrollToTop = _scrollController!.offset > 200;
      final showQuickJump = _scrollController!.offset > 400;

      if (showScrollToTop != _showScrollToTop ||
          showQuickJump != _showQuickJump) {
        setState(() {
          _showScrollToTop = showScrollToTop;
          _showQuickJump = showQuickJump;
        });
      }

      // التحقق من التحميل اللانهائي
      if (_scrollController!.position.pixels >=
          _scrollController!.position.maxScrollExtent - 200) {
        _loadMoreFoods();
      }
    }
  }

  void _scrollToTop() {
    if (_scrollController!.hasClients) {
      _scrollController!.animateTo(
        0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
      HapticFeedback.lightImpact();
    }
  }

  Future<void> _loadMoreFoods() async {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      // محاكاة تحميل المزيد من البيانات
      await Future.delayed(const Duration(milliseconds: 500));

      // في التطبيق الحقيقي، ستقوم بتحميل الصفحة التالية
      final foodProvider = Provider.of<FoodProvider>(context, listen: false);
      // محاكاة استخدام _currentPage في تحميل البيانات
      print('Loading page: ${_currentPage + 1}');
      await foodProvider.fetchFoodItems();

      if (mounted) {
        setState(() {
          _currentPage++;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  Future<void> _loadFoods() async {
    try {
      final foodProvider = Provider.of<FoodProvider>(context, listen: false);
      await foodProvider.fetchFoodItems();

      if (mounted) {
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('حدث خطأ أثناء تحميل البيانات');
      }
    }
  }

  Future<void> _refreshFoods() async {
    try {
      final foodProvider = Provider.of<FoodProvider>(context, listen: false);

      // إعادة تحميل البيانات من الخادم
      await foodProvider.fetchFoodItems();

      if (mounted) {
        HapticFeedback.mediumImpact();
        _showSuccessSnackBar('تم تحديث البيانات بنجاح');
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('فشل في تحديث البيانات');
      }
    }
  }

  void _onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _searchQuery = query;
        });
        if (query.isNotEmpty && query.length > 2) {
          _addToRecentSearches(query);
        }
        if (query.length > 1) {
          _updateSearchSuggestions(query);
        } else {
          _updateSearchSuggestions('');
        }
        _saveSearchState();
      }
    });
  }

  Future<void> _loadSearchState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (mounted) {
        setState(() {
          _searchQuery =
              ''; // ابدأ دائماً بدون نص بحث لمنع إخفاء القائمة بعد إعادة التشغيل
          _selectedCategory = prefs.getString('last_food_category') ?? 'الكل';
          _currentSortBy = prefs.getString('last_food_sort') ?? 'date';
          _isGridView = prefs.getBool('is_food_grid_view') ?? false;
          _showOnlyGlutenFree = prefs.getBool('show_only_gluten_free') ?? false;
          _recentSearches = prefs.getStringList('recent_food_searches') ?? [];

          // تحميل إحصائيات استخدام الفئات
          final categoryUsageJson =
              prefs.getStringList('food_category_usage') ?? [];
          _categoryUsageCount = {};
          for (final entry in categoryUsageJson) {
            final parts = entry.split(':');
            if (parts.length == 2) {
              _categoryUsageCount[parts[0]] = int.tryParse(parts[1]) ?? 0;
            }
          }
          _lastUsedCategory =
              prefs.getString('last_used_food_category') ?? 'الكل';
        });
        // ابدأ دائماً بحقل بحث فارغ عند فتح الشاشة/إعادة التشغيل
        _searchController.clear();
      }
    } catch (e) {
      // تجاهل الأخطاء في تحميل الحالة
    }
  }

  Future<void> _saveSearchState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_food_search', _searchQuery);
      await prefs.setString('last_food_category', _selectedCategory);
      await prefs.setString('last_food_sort', _currentSortBy);
      await prefs.setBool('is_food_grid_view', _isGridView);
      await prefs.setBool('show_only_gluten_free', _showOnlyGlutenFree);
      await prefs.setStringList('recent_food_searches', _recentSearches);

      // حفظ إحصائيات استخدام الفئات
      final categoryUsageJson = _categoryUsageCount.entries
          .map((e) => '${e.key}:${e.value}')
          .toList();
      await prefs.setStringList('food_category_usage', categoryUsageJson);
      await prefs.setString('last_used_food_category', _lastUsedCategory);
    } catch (e) {
      // تجاهل الأخطاء في حفظ الحالة
    }
  }

  void _updateSearchSuggestions(String query) {
    if (query.isEmpty) {
      setState(() {
        _searchSuggestions.clear();
      });
      return;
    }

    final foodProvider = Provider.of<FoodProvider>(context, listen: false);
    final q = _normalize(query);
    final suggestions = foodProvider.foodItems
        .where((food) {
          final name = _normalize(food.name);
          final details = _normalize(food.details);
          final ingredients = _normalize(food.ingredients ?? '');
          return name.contains(q) ||
              details.contains(q) ||
              ingredients.contains(q);
        })
        .take(5)
        .map((food) => food.name)
        .toSet() // إزالة التكرارات
        .toList();

    setState(() {
      _searchSuggestions = suggestions;
    });
  }

  void _addToRecentSearches(String query) {
    // إزالة البحث إذا كان موجوداً مسبقاً
    _recentSearches.remove(query);
    // إضافة البحث في المقدمة
    _recentSearches.insert(0, query);
    // الاحتفاظ بآخر 10 بحثات فقط
    if (_recentSearches.length > 10) {
      _recentSearches = _recentSearches.take(10).toList();
    }
  }

  void _trackCategoryUsage(String category) {
    _categoryUsageCount[category] = (_categoryUsageCount[category] ?? 0) + 1;
    _lastUsedCategory = category;
    _saveSearchState();
  }

  void _startBarcodeScanner() async {
    try {
      final result = await Navigator.of(context).push<String>(
        MaterialPageRoute(
          builder: (context) =>
              const BarcodeScannerScreen(returnBarcodeOnly: true),
        ),
      );

      if (result != null && result.isNotEmpty) {
        // تحديث حقل البحث بالباركود المسحوب
        _searchController.text = result;
        _onSearchChanged(result);

        // إظهار رسالة نجاح
        _showSuccessSnackBar('تم مسح الباركود بنجاح: $result');

        // إضافة اهتزاز خفيف
        HapticFeedback.mediumImpact();
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء مسح الباركود');
    }
  }

  Widget _buildRecentSearches() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'البحثات الأخيرة',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade700,
                ),
              ),
              TextButton(
                onPressed: _clearRecentSearches,
                child: Text(
                  'مسح الكل',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _recentSearches.map((search) {
              return GestureDetector(
                onTap: () {
                  _searchController.text = search;
                  _onSearchChanged(search);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.grey.shade300, width: 1),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.history,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        search,
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                        ),
                      ),
                      const SizedBox(width: 6),
                      GestureDetector(
                        onTap: () => _removeFromRecentSearches(search),
                        child: Icon(
                          Icons.close,
                          size: 16,
                          color: Colors.grey.shade500,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  void _clearRecentSearches() {
    setState(() {
      _recentSearches.clear();
    });
    _saveSearchState();
  }

  void _removeFromRecentSearches(String search) {
    setState(() {
      _recentSearches.remove(search);
    });
    _saveSearchState();
  }

  List<FoodItem> _getFilteredAndSortedFoods(List<FoodItem> foods) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isAdmin = authProvider.isAdmin;

    final q = _normalize(_searchQuery);
    List<FoodItem> filteredFoods = foods.where((food) {
      // فلترة البحث (مع تطبيع العربية)
      final matchesSearch =
          q.isEmpty ||
          _normalize(food.name).contains(q) ||
          _normalize(food.details).contains(q) ||
          _normalize(food.category).contains(q) ||
          _normalize(food.ingredients ?? '').contains(q);

      // فلترة الفئة
      final matchesCategory =
          _selectedCategory == 'الكل' || food.category == _selectedCategory;

      // فلتر الأطعمة الخالية من الجلوتين
      final matchesGlutenFreeOption = food.isGlutenFree;

      // فلترة حالة الموافقة
      final matchesApproval = isAdmin || food.isApproved;

      return matchesSearch &&
          matchesCategory &&
          matchesGlutenFreeOption &&
          matchesApproval;
    }).toList();

    // الترتيب
    switch (_currentSortBy) {
      case 'name':
        filteredFoods.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'category':
        filteredFoods.sort((a, b) => a.category.compareTo(b.category));
        break;
      case 'popularity':
        filteredFoods.sort((a, b) => b.likesCount.compareTo(a.likesCount));
        break;
      case 'rating':
        filteredFoods.sort(
          (a, b) => b.averageRating.compareTo(a.averageRating),
        );
        break;
      case 'date':
      default:
        filteredFoods.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
    }

    return filteredFoods;
  }

  // Helper Methods
  String _normalize(String input) {
    if (input.isEmpty) return '';
    var s = input.toLowerCase();
    // Remove diacritics
    s = s.replaceAll(
      RegExp('[\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06ED]'),
      '',
    );
    // Remove tatweel
    s = s.replaceAll('\u0640', '');
    // Normalize Alef variants
    s = s.replaceAll(RegExp('[إأآٱ]'), 'ا');
    // Normalize hamza carriers and ya/aleph maqsura and ta marbuta
    s = s
        .replaceAll('ؤ', 'و')
        .replaceAll('ئ', 'ي')
        .replaceAll('ى', 'ي')
        .replaceAll('ة', 'ه');
    // Normalize Arabic-Indic digits to Latin
    const arabicNums = [
      '\u0660',
      '\u0661',
      '\u0662',
      '\u0663',
      '\u0664',
      '\u0665',
      '\u0666',
      '\u0667',
      '\u0668',
      '\u0669',
    ];
    for (var i = 0; i < 10; i++) {
      s = s.replaceAll(arabicNums[i], '$i');
    }
    // Collapse spaces
    s = s.replaceAll(RegExp('\n+'), ' ');
    s = s.replaceAll(RegExp(r'\s+'), ' ').trim();
    return s;
  }

  void _navigateToDetail(FoodItem food) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => FoodDetailScreen(foodItem: food)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (_isSearching) {
          setState(() {
            _isSearching = false;
          });
          _searchFocusNode.unfocus();
          return false; // لا تغادر الشاشة، فقط أغلق البحث
        }
        return true;
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF8FAFC),
        floatingActionButton: _buildFloatingActionButtons(),
        body: Stack(
          children: [
            // المحتوى الأساسي
            FadeTransition(
              opacity: _fadeAnimation,
              child: RefreshIndicator(
                onRefresh: _refreshFoods,
                color: AppColors.primary,
                backgroundColor: Colors.white,
                child: Scrollbar(
                  thumbVisibility: false,
                  thickness: 4.0,
                  radius: const Radius.circular(8.0),
                  child: CustomScrollView(
                    controller: _scrollController,
                    key: const PageStorageKey<String>('foods_scroll'),
                    physics: const BouncingScrollPhysics(
                      parent: AlwaysScrollableScrollPhysics(),
                    ),
                    slivers: [
                      _buildEnhancedSliverAppBar(),
                      // اقتراحات البحث أسفل الشريط مباشرة أو "البحثات الأخيرة" إذا لا يوجد نص
                      if (_isSearching &&
                          _searchQuery.isEmpty &&
                          _recentSearches.isNotEmpty)
                        SliverToBoxAdapter(
                          child: Container(
                            margin: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                            child: _buildRecentSearches(),
                          ),
                        ),
                      if (_isSearching &&
                          _searchQuery.isNotEmpty &&
                          _searchSuggestions.isNotEmpty)
                        SliverToBoxAdapter(
                          child: Container(
                            margin: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: _searchSuggestions.length,
                              itemBuilder: (context, index) {
                                final suggestion = _searchSuggestions[index];
                                return ListTile(
                                  leading: const Icon(
                                    Icons.search_rounded,
                                    color: Colors.black54,
                                    size: 20,
                                  ),
                                  title: Text(
                                    suggestion,
                                    style: GoogleFonts.cairo(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  onTap: () {
                                    _searchController.text = suggestion;
                                    _onSearchChanged(suggestion);
                                    // إبقاء شريط البحث مفتوحاً مثل فيسبوك
                                    _searchFocusNode.unfocus();
                                  },
                                );
                              },
                              separatorBuilder: (_, __) =>
                                  const Divider(height: 1),
                            ),
                          ),
                        ),
                      if (_isSearching &&
                          _searchQuery.isNotEmpty &&
                          _searchSuggestions.isEmpty)
                        SliverToBoxAdapter(
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                            child: Container(
                              padding: const EdgeInsets.all(14),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.search_off_rounded,
                                    color: Colors.black38,
                                  ),
                                  const SizedBox(width: 10),
                                  Expanded(
                                    child: Text(
                                      'لا توجد اقتراحات مطابقة. جرّب كلمات أخرى.',
                                      style: GoogleFonts.cairo(
                                        fontSize: 14,
                                        color: Colors.black54,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      // إخفاء الفلاتر أثناء البحث مثل نمط فيسبوك
                      if (!_isSearching)
                        SliverToBoxAdapter(child: _buildQuickCategoryFilters()),
                      if (!_isSearching)
                        SliverToBoxAdapter(
                          child: _buildSortAndDisplayControls(),
                        ),
                      // الفلاتر المتقدمة (إخفاؤها أثناء البحث)
                      if (_showFilters && !_isSearching)
                        SliverToBoxAdapter(child: _buildAdvancedFilters()),
                      _buildFoodsContent(),

                      // مؤشر التحميل اللانهائي
                      if (_isLoadingMore)
                        const SliverToBoxAdapter(
                          child: Padding(
                            padding: EdgeInsets.all(16.0),
                            child: Center(child: CircularProgressIndicator()),
                          ),
                        ),

                      // مساحة إضافية في النهاية
                      const SliverPadding(padding: EdgeInsets.only(bottom: 80)),
                    ],
                  ),
                ),
              ),
            ),
            // الفهرس السريع
            if (_showQuickJump) _buildQuickJumpIndex(),
          ],
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message, style: GoogleFonts.cairo())),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message, style: GoogleFonts.cairo())),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Widget _buildEnhancedSliverAppBar() {
    final theme = Theme.of(context);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isAdmin = authProvider.isAdmin;

    return SliverAppBar(
      expandedHeight: _isSearching ? 190 : 140, // زيادة الارتفاع عند ظهور البحث
      pinned: true,
      floating: false,
      snap: false,
      elevation: 0,
      backgroundColor: Colors.transparent,
      automaticallyImplyLeading: false,
      stretch: true,
      stretchTriggerOffset: 80.0,
      onStretchTrigger: () async {
        HapticFeedback.lightImpact();
        await _refreshFoods();
      },
      leading: widget.showBackButton
          ? Builder(
              builder: (BuildContext context) {
                return Container(
                  margin: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withOpacity(0.35),
                        Colors.white.withOpacity(0.15),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(18),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.5),
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.white.withOpacity(0.1),
                        blurRadius: 15,
                        offset: const Offset(0, 8),
                      ),
                      BoxShadow(
                        color: Colors.black.withOpacity(0.15),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: const Icon(
                      Icons.arrow_back_ios,
                      color: Colors.white,
                      size: 22,
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                    tooltip: 'رجوع',
                  ),
                );
              },
            )
          : null,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.primaryColor,
                theme.colorScheme.secondary,
                theme.primaryColor.withOpacity(0.8),
              ],
            ),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(35),
              bottomRight: Radius.circular(35),
            ),
            boxShadow: [
              BoxShadow(
                color: theme.primaryColor.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Stack(
            children: [
              // Background pattern
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(35),
                      bottomRight: Radius.circular(35),
                    ),
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withOpacity(0.15),
                        Colors.transparent,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                ),
              ),
              // Decorative circles
              Positioned(
                top: -30,
                right: -20,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.08),
                  ),
                ),
              ),
              Positioned(
                bottom: -25,
                left: -15,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.06),
                  ),
                ),
              ),
              // Content Layout
              Positioned(
                bottom: _isSearching ? 80 : 25,
                left: 20,
                right: 20,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Food Icon
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.white.withOpacity(0.25),
                            Colors.white.withOpacity(0.1),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(18),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.white.withOpacity(0.2),
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                          ),
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 15,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Icon(
                        isAdmin
                            ? Icons.admin_panel_settings_rounded
                            : Icons.restaurant_menu_rounded,
                        size: 24,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                    const SizedBox(width: 15),
                    // Text Layout
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Title - يتغير حسب الصلاحية
                          Text(
                            isAdmin ? 'إدارة الأطعمة' : 'الأطعمة الآمنة',
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withOpacity(0.3),
                                  blurRadius: 8.0,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 4),
                          // Subtitle - يتغير حسب الصلاحية
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 10,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Colors.white.withOpacity(0.15),
                                  Colors.white.withOpacity(0.05),
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.2),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              isAdmin
                                  ? 'إدارة وتحرير قاعدة بيانات الأطعمة'
                                  : 'اكتشف الأطعمة الآمنة لمرضى السيلياك',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: Colors.white.withOpacity(0.95),
                                fontWeight: FontWeight.w500,
                                letterSpacing: 0.3,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Decorative element
                    Container(
                      width: 35,
                      height: 35,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        isAdmin
                            ? Icons.settings_rounded
                            : Icons.health_and_safety_rounded,
                        size: 18,
                        color: Colors.white.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),

              // شريط البحث المدمج (يظهر عند الضغط على الأيقونة)
              if (_isSearching)
                Positioned(
                  bottom: 20,
                  left: 16,
                  right: 16,
                  child: AnimatedOpacity(
                    duration: const Duration(milliseconds: 200),
                    opacity: _isSearching ? 1 : 0,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.black.withOpacity(0.05),
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.12),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.search_rounded,
                            color: AppColors.primary,
                            size: 22,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Directionality(
                              textDirection: TextDirection.rtl,
                              child: TextField(
                                focusNode: _searchFocusNode,
                                controller: _searchController,
                                onChanged: _onSearchChanged,
                                textAlign: TextAlign.start,
                                style: GoogleFonts.cairo(
                                  color: Colors.black87,
                                  fontSize: 14,
                                ),
                                cursorColor: AppColors.primary,
                                textInputAction: TextInputAction.search,
                                decoration: InputDecoration(
                                  hintText: 'ابحث باسم الطعام أو المكونات...',
                                  hintStyle: GoogleFonts.cairo(
                                    color: Colors.black54,
                                    fontSize: 13,
                                  ),
                                  border: InputBorder.none,
                                ),
                                onSubmitted: (_) => _searchFocusNode.unfocus(),
                              ),
                            ),
                          ),
                          if (_searchQuery.isNotEmpty)
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  _searchController.clear();
                                  _searchQuery = '';
                                });
                              },
                              child: Icon(
                                Icons.close_rounded,
                                color: Colors.black54,
                                size: 20,
                              ),
                            ),
                          const SizedBox(width: 8),
                          IconButton(
                            icon: Icon(
                              Icons.qr_code_scanner_rounded,
                              color: AppColors.primary,
                              size: 22,
                            ),
                            onPressed: _startBarcodeScanner,
                            tooltip: 'مسح باركود',
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
      actions: _buildAppBarActions(isAdmin),
    );
  }

  List<Widget> _buildAppBarActions(bool isAdmin) {
    List<Widget> actions = [];

    // أيقونة البحث - متاحة للجميع (تبديل البحث المدمج)
    actions.add(
      Container(
        margin: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.white.withOpacity(0.35),
              Colors.white.withOpacity(0.15),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: Colors.white.withOpacity(0.5), width: 1.5),
          boxShadow: [
            BoxShadow(
              color: Colors.white.withOpacity(0.1),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: IconButton(
          icon: Icon(
            _isSearching ? Icons.close_rounded : Icons.search_rounded,
            color: Colors.white,
            size: 22,
          ),
          onPressed: () {
            setState(() {
              _isSearching = !_isSearching;
            });
            if (_isSearching) {
              // فتح البحث المدمج والتركيز على الحقل مع إفراغ الحقل السابق
              setState(() {
                _searchController.clear();
                _searchQuery = '';
              });
              _searchFocusNode.requestFocus();
              HapticFeedback.lightImpact();
            } else {
              // إغلاق البحث المدمج وإفراغ الحقل لضمان ظهور القائمة كاملة
              setState(() {
                _searchController.clear();
                _searchQuery = '';
              });
              _searchFocusNode.unfocus();
              HapticFeedback.selectionClick();
            }
          },
          tooltip: 'البحث',
        ),
      ),
    );

    if (isAdmin) {
      // أيقونة إدارة الأطعمة - للمدير فقط
      actions.add(
        Container(
          margin: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.white.withOpacity(0.35),
                Colors.white.withOpacity(0.15),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(18),
            border: Border.all(
              color: Colors.white.withOpacity(0.5),
              width: 1.5,
            ),
          ),
          child: IconButton(
            icon: const Icon(
              Icons.settings_rounded,
              color: Colors.white,
              size: 22,
            ),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const FoodsManagementScreen(),
                ),
              );
            },
            tooltip: 'لوحة الإدارة',
          ),
        ),
      );

      // أيقونة المفضلة - متاحة أيضاً للمشرفين
      actions.add(
        Container(
          margin: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.white.withOpacity(0.35),
                Colors.white.withOpacity(0.15),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(18),
            border: Border.all(
              color: Colors.white.withOpacity(0.5),
              width: 1.5,
            ),
          ),
          child: IconButton(
            icon: const Icon(
              Icons.favorite_rounded,
              color: Colors.white,
              size: 22,
            ),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const FavoritesScreen(),
                ),
              );
            },
            tooltip: 'المفضلة',
          ),
        ),
      );
    } else {
      // أيقونة المفضلة - للمستخدمين العاديين
      actions.add(
        Container(
          margin: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.white.withOpacity(0.35),
                Colors.white.withOpacity(0.15),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(18),
            border: Border.all(
              color: Colors.white.withOpacity(0.5),
              width: 1.5,
            ),
          ),
          child: IconButton(
            icon: const Icon(
              Icons.favorite_rounded,
              color: Colors.white,
              size: 22,
            ),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const FavoritesScreen(),
                ),
              );
            },
            tooltip: 'المفضلة',
          ),
        ),
      );
    }

    return actions;
  }

  Widget _buildQuickCategoryFilters() {
    return Container(
      margin: const EdgeInsets.only(top: 16, bottom: 8),
      height: 50,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          return _buildModernQuickFilter(
            category,
            _selectedCategory == category,
            () {
              setState(() {
                _selectedCategory = category;
              });
              _trackCategoryUsage(category);
            },
          );
        },
      ),
    );
  }

  Widget _buildModernQuickFilter(
    String label,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(left: 12),
      child: GestureDetector(
        onTap: () {
          onTap();
          HapticFeedback.lightImpact();
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          decoration: BoxDecoration(
            gradient: isSelected
                ? LinearGradient(
                    colors: [
                      AppColors.primary,
                      AppColors.primary.withOpacity(0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : null,
            color: isSelected ? null : Colors.white,
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: isSelected ? Colors.transparent : Colors.grey.shade300,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: isSelected
                    ? AppColors.primary.withOpacity(0.3)
                    : Colors.black.withOpacity(0.05),
                blurRadius: isSelected ? 12 : 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Center(
            child: Text(
              label,
              style: GoogleFonts.cairo(
                color: isSelected ? Colors.white : Colors.grey.shade700,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSortAndDisplayControls() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Sorting Dropdown
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _currentSortBy,
                icon: const Icon(Icons.sort_rounded, size: 20),
                items: const [
                  DropdownMenuItem(value: 'date', child: Text('الأحدث')),
                  DropdownMenuItem(
                    value: 'popularity',
                    child: Text('الأكثر شعبية'),
                  ),
                  DropdownMenuItem(
                    value: 'rating',
                    child: Text('الأعلى تقييماً'),
                  ),
                  DropdownMenuItem(value: 'name', child: Text('الاسم')),
                  DropdownMenuItem(value: 'category', child: Text('الفئة')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _currentSortBy = value;
                    });
                    _saveSearchState();
                    HapticFeedback.lightImpact();
                  }
                },
                style: GoogleFonts.cairo(
                  color: Colors.grey.shade700,
                  fontSize: 14,
                ),
              ),
            ),
          ),

          // Display Mode Toggle
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Row(
              children: [
                _buildViewButton(Icons.view_list_rounded, !_isGridView, () {
                  setState(() => _isGridView = false);
                  _saveSearchState();
                }),
                _buildViewButton(Icons.grid_view_rounded, _isGridView, () {
                  setState(() => _isGridView = true);
                  _saveSearchState();
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildViewButton(IconData icon, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: () {
        onTap();
        HapticFeedback.lightImpact();
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          icon,
          size: 24,
          color: isSelected ? Colors.white : Colors.grey.shade600,
        ),
      ),
    );
  }

  Widget _buildAdvancedFilters() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(Icons.tune_rounded, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Text(
                    'فلاتر متقدمة',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ],
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedCategory = 'الكل';
                  });
                  _saveSearchState();
                },
                child: Text('إعادة تعيين', style: GoogleFonts.cairo()),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // فلتر الفئة
          Text(
            'الفئة',
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(12),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _selectedCategory,
                isExpanded: true,
                style: GoogleFonts.cairo(color: Colors.grey.shade800),
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value!;
                  });
                  _trackCategoryUsage(value!);
                },
                items: _categories.map((category) {
                  return DropdownMenuItem<String>(
                    value: category,
                    child: Text(category),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFoodsContent() {
    return Consumer<FoodProvider>(
      builder: (context, foodProvider, child) {
        if (foodProvider.isLoading) {
          return SliverToBoxAdapter(
            child: SizedBox(
              height: 300,
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('جاري تحميل الأطعمة...'),
                  ],
                ),
              ),
            ),
          );
        }

        if (foodProvider.errorMessage != null) {
          return SliverToBoxAdapter(
            child: _buildErrorState(foodProvider.errorMessage!),
          );
        }

        final filteredFoods = _getFilteredAndSortedFoods(
          foodProvider.foodItems,
        );

        if (filteredFoods.isEmpty) {
          return SliverToBoxAdapter(child: _buildEmptyState());
        }

        if (_isGridView) {
          return SliverPadding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            sliver: SliverGrid(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.75,
                crossAxisSpacing: 20,
                mainAxisSpacing: 24,
              ),
              delegate: SliverChildBuilderDelegate((context, index) {
                final food = filteredFoods[index];
                return SlideTransition(
                  position: _slideAnimation,
                  child: ScaleTransition(
                    scale: _scaleAnimation,
                    child: _buildFoodGridCard(food),
                  ),
                );
              }, childCount: filteredFoods.length),
            ),
          );
        } else {
          return SliverPadding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate((context, index) {
                final food = filteredFoods[index];
                return Container(
                  margin: const EdgeInsets.only(bottom: 20),
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: ScaleTransition(
                      scale: _scaleAnimation,
                      child: _buildFoodListCard(food),
                    ),
                  ),
                );
              }, childCount: filteredFoods.length),
            ),
          );
        }
      },
    );
  }

  Widget _buildFoodListCard(FoodItem food) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToDetail(food),
          borderRadius: BorderRadius.circular(16),
          splashColor: AppColors.primary.withOpacity(0.1),
          highlightColor: AppColors.primary.withOpacity(0.05),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // صورة الطعام
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: Colors.grey.shade100,
                    ),
                    child: food.imageUrls.isNotEmpty
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: CachedNetworkImage(
                              imageUrl: food.imageUrls.first,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: Colors.grey.shade200,
                                child: const Center(
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: Colors.grey.shade200,
                                child: Icon(
                                  Icons.restaurant_rounded,
                                  color: Colors.grey.shade400,
                                  size: 30,
                                ),
                              ),
                            ),
                          )
                        : Icon(
                            Icons.restaurant_rounded,
                            color: Colors.grey.shade400,
                            size: 30,
                          ),
                  ),

                  const SizedBox(width: 16),

                  // معلومات الطعام
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                food.name,
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.grey.shade800,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 8),
                            _buildGlutenFreeBadge(),
                          ],
                        ),

                        const SizedBox(height: 4),

                        Text(
                          food.category,
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: 8),

                        Row(
                          children: [
                            Icon(
                              Icons.category_rounded,
                              size: 16,
                              color: Colors.grey.shade500,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                food.details,
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: Colors.grey.shade500,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 8),

                        Row(
                          children: [
                            _buildInteractionChip(
                              Icons.favorite_rounded,
                              food.likesCount.toString(),
                              Colors.red,
                            ),
                            const SizedBox(width: 8),
                            _buildInteractionChip(
                              Icons.comment_rounded,
                              food.commentsCount.toString(),
                              Colors.blue,
                            ),
                            const SizedBox(width: 8),
                            _buildInteractionChip(
                              Icons.star_rounded,
                              food.averageRating.toStringAsFixed(1),
                              Colors.amber,
                            ),
                            const Spacer(),
                            Consumer<FavoritesProvider>(
                              builder: (context, favProvider, child) {
                                final isFavorite = favProvider.isFoodFavorite(
                                  food.id!,
                                );
                                return IconButton(
                                  icon: Icon(
                                    isFavorite
                                        ? Icons.favorite_rounded
                                        : Icons.favorite_border_rounded,
                                    color: isFavorite
                                        ? Colors.red
                                        : Colors.grey.shade400,
                                    size: 20,
                                  ),
                                  onPressed: () async {
                                    try {
                                      await favProvider.toggleFoodFavorite(
                                        food,
                                      );
                                      HapticFeedback.lightImpact();
                                    } catch (e) {
                                      if (mounted) {
                                        ScaffoldMessenger.of(
                                          context,
                                        ).showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              'فشل في تحديث المفضلة',
                                              style: GoogleFonts.cairo(),
                                            ),
                                            backgroundColor: Colors.red,
                                          ),
                                        );
                                      }
                                    }
                                  },
                                  tooltip: isFavorite
                                      ? 'إزالة من المفضلة'
                                      : 'إضافة للمفضلة',
                                );
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGlutenFreeBadge({bool isSmall = false}) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isSmall ? 6 : 8,
        vertical: isSmall ? 2 : 4,
      ),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.check_circle,
            size: isSmall ? 12 : 14,
            color: Colors.green,
          ),
          const SizedBox(width: 4),
          Text(
            'خالي من الجلوتين',
            style: GoogleFonts.cairo(
              fontSize: isSmall ? 10 : 12,
              fontWeight: FontWeight.w600,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInteractionChip(IconData icon, String count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            count,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.search_off_rounded,
              size: 60,
              color: Colors.grey.shade400,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'لا توجد أطعمة',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على أطعمة تطابق معايير البحث',
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade500),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: () {
              _searchController.clear();
              setState(() {
                _searchQuery = '';
                _selectedCategory = 'الكل';
                _showOnlyGlutenFree = false;
              });
            },
            icon: const Icon(Icons.clear_all_rounded),
            label: Text('مسح جميع الفلاتر', style: GoogleFonts.cairo()),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String errorMessage) {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.error_outline_rounded,
              size: 60,
              color: Colors.red.shade400,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'حدث خطأ',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            errorMessage,
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: _loadFoods,
            icon: const Icon(Icons.refresh_rounded),
            label: Text('إعادة المحاولة', style: GoogleFonts.cairo()),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFoodGridCard(FoodItem food) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToDetail(food),
          borderRadius: BorderRadius.circular(16),
          splashColor: AppColors.primary.withOpacity(0.1),
          highlightColor: AppColors.primary.withOpacity(0.05),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // صورة الطعام
                Expanded(
                  flex: 3,
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(16),
                      ),
                      color: Colors.grey.shade100,
                    ),
                    child: Stack(
                      children: [
                        food.imageUrls.isNotEmpty
                            ? ClipRRect(
                                borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(16),
                                ),
                                child: CachedNetworkImage(
                                  imageUrl: food.imageUrls.first,
                                  width: double.infinity,
                                  height: double.infinity,
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) => Container(
                                    color: Colors.grey.shade200,
                                    child: const Center(
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                      ),
                                    ),
                                  ),
                                  errorWidget: (context, url, error) =>
                                      Container(
                                        color: Colors.grey.shade200,
                                        child: Icon(
                                          Icons.restaurant_rounded,
                                          color: Colors.grey.shade400,
                                          size: 40,
                                        ),
                                      ),
                                ),
                              )
                            : Icon(
                                Icons.restaurant_rounded,
                                color: Colors.grey.shade400,
                                size: 40,
                              ),

                        // Gluten-Free Badge
                        Positioned(
                          top: 8,
                          right: 8,
                          child: _buildGlutenFreeBadge(isSmall: true),
                        ),
                        // أيقونة المفضلة
                        Positioned(
                          top: 8,
                          left: 8,
                          child: Consumer<FavoritesProvider>(
                            builder: (context, favProvider, child) {
                              final isFavorite = favProvider.isFoodFavorite(
                                food.id!,
                              );
                              return GestureDetector(
                                onTap: () async {
                                  try {
                                    await favProvider.toggleFoodFavorite(food);
                                    HapticFeedback.lightImpact();
                                  } catch (e) {
                                    if (mounted) {
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        SnackBar(
                                          content: Text(
                                            'فشل في تحديث المفضلة',
                                            style: GoogleFonts.cairo(),
                                          ),
                                          backgroundColor: Colors.red,
                                        ),
                                      );
                                    }
                                  }
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.9),
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Icon(
                                    isFavorite
                                        ? Icons.favorite_rounded
                                        : Icons.favorite_border_rounded,
                                    color: isFavorite
                                        ? Colors.red
                                        : Colors.grey.shade600,
                                    size: 16,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // معلومات الطعام
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          food.name,
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade800,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: 4),

                        Text(
                          food.category,
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const Spacer(),

                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.favorite_rounded,
                                  size: 14,
                                  color: Colors.red.shade400,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  food.likesCount.toString(),
                                  style: GoogleFonts.cairo(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                Icon(
                                  Icons.star_rounded,
                                  size: 14,
                                  color: Colors.amber.shade400,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  food.averageRating.toStringAsFixed(1),
                                  style: GoogleFonts.cairo(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isAdmin = authProvider.isAdmin;

    return ScaleTransition(
      scale: _fabController,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // زر العودة للأعلى
          if (_showScrollToTop)
            Container(
              margin: const EdgeInsets.only(bottom: 16),
              child: FloatingActionButton.small(
                heroTag: "scroll_to_top",
                onPressed: _scrollToTop,
                backgroundColor: Colors.white,
                foregroundColor: AppColors.primary,
                elevation: 4,
                child: const Icon(Icons.keyboard_arrow_up_rounded, size: 28),
              ),
            ),
          // زر إضافة طعام - للمشرفين فقط
          if (isAdmin)
            FloatingActionButton(
              heroTag: "add_food",
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const AddEditFoodScreen(),
                  ),
                );
              },
              backgroundColor: AppColors.primary,
              child: const Icon(Icons.add_rounded, color: Colors.white),
            ),
          if (isAdmin) const SizedBox(height: 16),
          // زر طلب إضافة طعام - للمستخدمين العاديين فقط
          if (!isAdmin)
            FloatingActionButton(
              heroTag: "user_add",
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const UserAddFoodScreen(),
                  ),
                );
              },
              backgroundColor: AppColors.primary,
              child: const Icon(Icons.add_rounded, color: Colors.white),
            ),
        ],
      ),
    );
  }

  Widget _buildQuickJumpIndex() {
    return Positioned(
      right: 16,
      top: MediaQuery.of(context).size.height * 0.3,
      child: AnimatedOpacity(
        opacity: _showQuickJump ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 300),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: _categories.take(8).map((category) {
              final isSelected = _selectedCategory == category;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedCategory = category;
                  });
                  _trackCategoryUsage(category);
                  HapticFeedback.lightImpact();
                  _scrollToTop();
                },
                child: Container(
                  width: 32,
                  height: 32,
                  margin: const EdgeInsets.symmetric(vertical: 2),
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.primary : Colors.transparent,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: Text(
                      category.substring(0, 1),
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: isSelected ? Colors.white : AppColors.primary,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/providers/medication_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/models/medication.dart';
import 'package:yassincil/screens/medications/medication_detail_screen.dart';
import 'package:yassincil/widgets/medications_app_bar.dart';
import 'package:yassincil/utils/app_colors.dart';

class VerifyMedicationsScreen extends StatefulWidget {
  const VerifyMedicationsScreen({super.key});

  @override
  State<VerifyMedicationsScreen> createState() =>
      _VerifyMedicationsScreenState();
}

class _VerifyMedicationsScreenState extends State<VerifyMedicationsScreen> {
  @override
  Widget build(BuildContext context) {
    final isAdmin = Provider.of<AuthProvider>(context).isAdmin;
    if (!isAdmin) {
      return Scaffold(
        appBar: const MedicationsAppBar(title: 'التحقق من الأدوية'),
        body: Center(
          child: Text(
            'ليست لديك صلاحية للوصول إلى هذه الصفحة',
            style: GoogleFonts.cairo(fontSize: 16),
          ),
        ),
      );
    }

    final medicationProvider = Provider.of<MedicationProvider>(context);
    final unapprovedMedications = medicationProvider.medications
        .where((m) => !m.isApproved)
        .toList();

    return Scaffold(
      appBar: const MedicationsAppBar(title: 'التحقق من الأدوية'),
      body: unapprovedMedications.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    size: 80,
                    color: Colors.green,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد أدوية تحتاج إلى تحقق حالياً',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(8.0),
              itemCount: unapprovedMedications.length,
              itemBuilder: (context, index) {
                final medication = unapprovedMedications[index];
                return Card(
                  margin: const EdgeInsets.symmetric(
                    vertical: 8.0,
                    horizontal: 4.0,
                  ),
                  elevation: 3,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.all(12.0),
                    leading: CircleAvatar(
                      radius: 30,
                      backgroundImage: medication.imageUrl != null
                          ? NetworkImage(medication.imageUrl!)
                          : null,
                      child: medication.imageUrl == null
                          ? const Icon(Icons.medical_services, size: 30)
                          : null,
                    ),
                    title: Text(
                      medication.name,
                      style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Text(
                      medication.company,
                      style: GoogleFonts.cairo(),
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => MedicationDetailScreen(
                            medication: medication,
                            isForVerification: true,
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
    );
  }
}

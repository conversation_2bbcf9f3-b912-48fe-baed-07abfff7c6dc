// قواعد Firestore المحدثة للسماح بالقراءة والكتابة أثناء التطوير
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // السماح للمستخدمين المسجلين بالقراءة والكتابة
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
    
    // قواعد خاصة للمفضلة - كل مستخدم يصل لمفضلاته فقط
    match /users/{userId}/favorites/{document=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد الأدوية - قراءة للجميع، كتابة للمشرفين فقط
    match /medications/{document=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null; // مؤقتاً للتطوير
    }
    
    // قواعد الأطعمة
    match /foods/{document=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null; // مؤقتاً للتطوير
    }
    
    // قواعد المطاعم
    match /restaurants/{document=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null; // مؤقتاً للتطوير
    }
    
    // قواعد المقالات
    match /articles/{document=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null; // مؤقتاً للتطوير
    }
  }
}
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';

import 'package:yassincil/providers/nutrition_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/models/nutrition_entry.dart';
import 'package:yassincil/utils/app_colors.dart';
import 'package:yassincil/widgets/loading_widget.dart';

class NutritionTrackingScreen extends StatefulWidget {
  const NutritionTrackingScreen({super.key});

  @override
  State<NutritionTrackingScreen> createState() =>
      _NutritionTrackingScreenState();
}

class _NutritionTrackingScreenState extends State<NutritionTrackingScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late DateTime _selectedDate;

  @override
  void initState() {
    super.initState();
    _selectedDate = DateTime.now();
    _tabController = TabController(length: 4, vsync: this);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final nutritionProvider = Provider.of<NutritionProvider>(
        context,
        listen: false,
      );

      if (authProvider.currentUser != null) {
        nutritionProvider.fetchNutritionEntries(authProvider.currentUser!.uid);
        nutritionProvider.fetchNutritionGoals(authProvider.currentUser!.uid);
        nutritionProvider.setSelectedDate(_selectedDate);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'التغذية الشخصية',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics, color: Colors.white),
            onPressed: () => _tabController.animateTo(3),
            tooltip: 'التقارير والإحصائيات',
          ),
          IconButton(
            icon: const Icon(Icons.track_changes, color: Colors.white),
            onPressed: () => _tabController.animateTo(2),
            tooltip: 'الأهداف الغذائية',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onSelected: (value) => _handleMenuAction(value),
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    const Icon(Icons.settings, size: 20),
                    const SizedBox(width: 8),
                    Text('الإعدادات', style: GoogleFonts.cairo()),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    const Icon(Icons.download, size: 20),
                    const SizedBox(width: 8),
                    Text('تصدير البيانات', style: GoogleFonts.cairo()),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'help',
                child: Row(
                  children: [
                    const Icon(Icons.help, size: 20),
                    const SizedBox(width: 8),
                    Text('المساعدة', style: GoogleFonts.cairo()),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Consumer2<NutritionProvider, AuthProvider>(
        builder: (context, nutritionProvider, authProvider, child) {
          if (authProvider.currentUser == null) {
            return _buildLoginPrompt();
          }

          if (nutritionProvider.isLoading) {
            return const LoadingWidget();
          }

          return Column(
            children: [
              _buildDateSelector(nutritionProvider),
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOverviewTab(nutritionProvider),
                    _buildMealsTab(nutritionProvider),
                    _buildGoalsTab(nutritionProvider),
                    _buildReportsTab(nutritionProvider),
                  ],
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (authProvider.currentUser == null) {
            return const SizedBox.shrink();
          }

          return FloatingActionButton.extended(
            onPressed: () => _showAddMealOptions(),
            backgroundColor: AppColors.primary,
            icon: const Icon(Icons.add, color: Colors.white),
            label: Text(
              'إضافة',
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDailySummaryCard(NutritionProvider nutritionProvider) {
    final summary = nutritionProvider.dailySummary;
    final goals = nutritionProvider.nutritionGoals;

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [Colors.green.shade400, Colors.green.shade600],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص اليوم',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'السعرات',
                    '${summary.totalCalories.toInt()}',
                    goals?.dailyCaloriesGoal.toInt().toString() ?? '2000',
                    'كالوري',
                    Icons.local_fire_department,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'البروتين',
                    '${summary.totalProtein.toInt()}',
                    goals?.dailyProteinGoal.toInt().toString() ?? '150',
                    'جم',
                    Icons.fitness_center,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'الكربوهيدرات',
                    '${summary.totalCarbs.toInt()}',
                    goals?.dailyCarbsGoal.toInt().toString() ?? '250',
                    'جم',
                    Icons.grain,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'الدهون',
                    '${summary.totalFat.toInt()}',
                    goals?.dailyFatGoal.toInt().toString() ?? '65',
                    'جم',
                    Icons.opacity,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    String label,
    String value,
    String goal,
    String unit,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 24),
        const SizedBox(height: 8),
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: Colors.white.withValues(alpha: 0.9),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '$value/$goal',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          unit,
          style: GoogleFonts.cairo(
            fontSize: 10,
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildGoalsProgressCard(NutritionProvider nutritionProvider) {
    final progress = nutritionProvider.getGoalsProgress();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تقدم الأهداف',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
            const SizedBox(height: 16),
            ...progress.entries.map(
              (entry) => _buildProgressBar(entry.key, entry.value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressBar(String label, double percentage) {
    final arabicLabels = {
      'calories': 'السعرات الحرارية',
      'protein': 'البروتين',
      'carbs': 'الكربوهيدرات',
      'fat': 'الدهون',
      'fiber': 'الألياف',
    };

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                arabicLabels[label] ?? label,
                style: GoogleFonts.cairo(fontSize: 14),
              ),
              Text(
                '${percentage.toInt()}%',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.green.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.green.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildMealsSection(NutritionProvider nutritionProvider) {
    final mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];
    final mealLabels = {
      'breakfast': 'الإفطار',
      'lunch': 'الغداء',
      'dinner': 'العشاء',
      'snack': 'وجبة خفيفة',
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الوجبات',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade800,
          ),
        ),
        const SizedBox(height: 12),
        ...mealTypes.map((mealType) {
          final entries = nutritionProvider.getEntriesByMealType(mealType);
          return _buildMealCard(mealLabels[mealType]!, mealType, entries);
        }),
      ],
    );
  }

  Widget _buildMealCard(String mealLabel, String mealType, List entries) {
    final totalCalories = entries.fold(
      0.0,
      (sum, entry) => sum + entry.calories,
    );

    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  mealLabel,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade800,
                  ),
                ),
                Row(
                  children: [
                    Text(
                      '${totalCalories.toInt()} كالوري',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.green.shade600,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: () => _addMealEntry(),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.green.shade600,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: const Icon(
                          Icons.add,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            if (entries.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                '${entries.length} عنصر مضاف',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ] else ...[
              const SizedBox(height: 8),
              Text(
                'لم يتم إضافة أطعمة بعد',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _addMealEntry([String? mealType]) {
    Navigator.pop(context);

    String mealName = 'وجبة';
    switch (mealType) {
      case 'breakfast':
        mealName = 'إفطار';
        break;
      case 'lunch':
        mealName = 'غداء';
        break;
      case 'dinner':
        mealName = 'عشاء';
        break;
      case 'snack':
        mealName = 'وجبة خفيفة';
        break;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم إضافة شاشة إدخال $mealName قريباً',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.green.shade600,
      ),
    );
  }

  Widget _buildLoginPrompt() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.restaurant_menu, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'يرجى تسجيل الدخول',
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لتتبع تغذيتك الشخصية وتحقيق أهدافك الصحية',
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => Navigator.of(context).pushNamed('/login'),
            icon: const Icon(Icons.login),
            label: Text('تسجيل الدخول', style: GoogleFonts.cairo()),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelector(NutritionProvider nutritionProvider) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          IconButton(
            onPressed: () => _changeDate(-1, nutritionProvider),
            icon: const Icon(Icons.chevron_left),
          ),
          Expanded(
            child: Center(
              child: InkWell(
                onTap: () => _selectDate(nutritionProvider),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: AppColors.primary),
                  ),
                  child: Text(
                    DateFormat('EEEE، d MMMM yyyy', 'ar').format(_selectedDate),
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ),
            ),
          ),
          IconButton(
            onPressed: () => _changeDate(1, nutritionProvider),
            icon: const Icon(Icons.chevron_right),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: Colors.grey.shade600,
        labelStyle: GoogleFonts.cairo(
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
        unselectedLabelStyle: GoogleFonts.cairo(
          fontWeight: FontWeight.normal,
          fontSize: 12,
        ),
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        isScrollable: false,
        tabs: const [
          Tab(text: 'نظرة عامة'),
          Tab(text: 'الوجبات'),
          Tab(text: 'الأهداف'),
          Tab(text: 'التقارير'),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(NutritionProvider nutritionProvider) {
    return RefreshIndicator(
      onRefresh: () => _refreshData(nutritionProvider),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDailySummaryCard(nutritionProvider),
            const SizedBox(height: 16),
            _buildGoalsProgressCard(nutritionProvider),
            const SizedBox(height: 16),
            _buildQuickActionsCard(),
            const SizedBox(height: 16),
            _buildRecentMealsCard(nutritionProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildMealsTab(NutritionProvider nutritionProvider) {
    return RefreshIndicator(
      onRefresh: () => _refreshData(nutritionProvider),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(children: [_buildMealsSection(nutritionProvider)]),
      ),
    );
  }

  Widget _buildGoalsTab(NutritionProvider nutritionProvider) {
    return RefreshIndicator(
      onRefresh: () => _refreshData(nutritionProvider),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(children: [_buildGoalsManagement(nutritionProvider)]),
      ),
    );
  }

  Widget _buildReportsTab(NutritionProvider nutritionProvider) {
    return RefreshIndicator(
      onRefresh: () => _refreshData(nutritionProvider),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(children: [_buildNutritionReports(nutritionProvider)]),
      ),
    );
  }

  // Helper methods
  void _changeDate(int days, NutritionProvider nutritionProvider) {
    setState(() {
      _selectedDate = _selectedDate.add(Duration(days: days));
    });
    nutritionProvider.setSelectedDate(_selectedDate);
  }

  Future<void> _selectDate(NutritionProvider nutritionProvider) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      nutritionProvider.setSelectedDate(_selectedDate);
    }
  }

  Future<void> _refreshData(NutritionProvider nutritionProvider) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (authProvider.currentUser != null) {
      await nutritionProvider.fetchNutritionEntries(
        authProvider.currentUser!.uid,
      );
      await nutritionProvider.fetchNutritionGoals(
        authProvider.currentUser!.uid,
      );
    }
  }

  // Placeholder methods for missing widgets
  Widget _buildQuickActionsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات سريعة',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionButton(
                    'إضافة وجبة',
                    Icons.restaurant,
                    Colors.green,
                    () => _addMealEntry(),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildQuickActionButton(
                    'شرب ماء',
                    Icons.local_drink,
                    Colors.blue,
                    () => _addWaterEntry(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentMealsCard(NutritionProvider nutritionProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الوجبات الأخيرة',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () => _tabController.animateTo(1),
                  child: Text('عرض الكل', style: GoogleFonts.cairo()),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // TODO: Add recent meals list
            Center(
              child: Text(
                'لا توجد وجبات مسجلة اليوم',
                style: GoogleFonts.cairo(color: Colors.grey.shade600),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalsManagement(NutritionProvider nutritionProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إدارة الأهداف الغذائية',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            // TODO: Add goals management interface
            Center(
              child: Text(
                'قيد التطوير...',
                style: GoogleFonts.cairo(color: Colors.grey.shade600),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNutritionReports(NutritionProvider nutritionProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'التقارير والإحصائيات',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            // TODO: Add nutrition reports and charts
            Center(
              child: Text(
                'قيد التطوير...',
                style: GoogleFonts.cairo(color: Colors.grey.shade600),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addWaterEntry() {
    // TODO: Implement water entry
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة كوب ماء', style: GoogleFonts.cairo()),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _showAddMealOptions() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'إضافة إلى يومك',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              childAspectRatio: 1.2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: [
                _buildAddOptionCard(
                  'إفطار',
                  Icons.free_breakfast,
                  Colors.orange,
                  () => _addMealEntry('breakfast'),
                ),
                _buildAddOptionCard(
                  'غداء',
                  Icons.lunch_dining,
                  Colors.green,
                  () => _addMealEntry('lunch'),
                ),
                _buildAddOptionCard(
                  'عشاء',
                  Icons.dinner_dining,
                  Colors.purple,
                  () => _addMealEntry('dinner'),
                ),
                _buildAddOptionCard(
                  'وجبة خفيفة',
                  Icons.cookie,
                  Colors.brown,
                  () => _addMealEntry('snack'),
                ),
                _buildAddOptionCard('ماء', Icons.local_drink, Colors.blue, () {
                  Navigator.pop(context);
                  _addWaterEntry();
                }),
                _buildAddOptionCard(
                  'مكمل غذائي',
                  Icons.medication,
                  Colors.red,
                  () => _addSupplementEntry(),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildAddOptionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _addSupplementEntry() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'إضافة المكملات الغذائية قيد التطوير',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'settings':
        _showNutritionSettings();
        break;
      case 'export':
        _exportNutritionData();
        break;
      case 'help':
        _showHelp();
        break;
    }
  }

  void _showNutritionSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إعدادات التغذية',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.notifications),
              title: Text('تذكيرات الوجبات', style: GoogleFonts.cairo()),
              trailing: Switch(
                value: true,
                onChanged: (value) {
                  // TODO: Implement notifications toggle
                },
              ),
            ),
            ListTile(
              leading: const Icon(Icons.water_drop),
              title: Text('تذكيرات الماء', style: GoogleFonts.cairo()),
              trailing: Switch(
                value: true,
                onChanged: (value) {
                  // TODO: Implement water reminders toggle
                },
              ),
            ),
            ListTile(
              leading: const Icon(Icons.language),
              title: Text('وحدة القياس', style: GoogleFonts.cairo()),
              subtitle: Text('جرام/كيلوجرام', style: GoogleFonts.cairo()),
              onTap: () {
                // TODO: Show unit selection
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _exportNutritionData() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تصدير البيانات قيد التطوير', style: GoogleFonts.cairo()),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'المساعدة',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'كيفية استخدام تطبيق التغذية:',
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              Text(
                '• اضغط على "إضافة وجبة" لتسجيل وجباتك اليومية\n'
                '• تابع تقدمك نحو أهدافك الغذائية في تبويب "الأهداف"\n'
                '• راجع تقاريرك الأسبوعية والشهرية في تبويب "التقارير"\n'
                '• استخدم الإجراءات السريعة لإضافة الماء والوجبات بسرعة',
                style: GoogleFonts.cairo(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('فهمت', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:yassincil/models/comment.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/providers/pharmacy_provider.dart';
import 'package:yassincil/utils/app_colors.dart';

class AdvancedPharmacyCommentWidget extends StatelessWidget {
  final Comment comment;
  final String pharmacyId;
  final VoidCallback onReply;

  const AdvancedPharmacyCommentWidget({
    super.key,
    required this.comment,
    required this.pharmacyId,
    required this.onReply,
  });

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final currentUserId = authProvider.currentUser?.uid;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  child: Text(comment.username.isNotEmpty ? comment.username[0] : '?'),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        comment.username,
                        style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        '${comment.createdAt.toLocal()}'.split(' ')[0],
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                if (authProvider.isAdmin || currentUserId == comment.userId)
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: () => _deleteComment(context),
                  ),
              ],
            ),
            const SizedBox(height: 10),
            Text(comment.content, style: GoogleFonts.cairo()),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  icon: const Icon(Icons.reply, size: 16),
                  label: Text('رد', style: GoogleFonts.cairo()),
                  onPressed: onReply,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _deleteComment(BuildContext context) {
    final pharmacyProvider =
        Provider.of<PharmacyProvider>(context, listen: false);
    pharmacyProvider.deleteCommentFromPharmacy(pharmacyId, comment.id!);
  }
}

import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:file_picker/file_picker.dart';

import 'package:yassincil/providers/medication_provider.dart';
import 'package:yassincil/providers/auth_provider.dart';
import 'package:yassincil/providers/favorites_provider.dart';
import 'package:yassincil/models/medication.dart';
import 'package:yassincil/screens/medications/add_edit_medication_screen_enhanced.dart';
import 'package:yassincil/screens/medications/medication_detail_screen.dart';
import 'package:yassincil/screens/medications/verify_medications_screen.dart';
import 'package:yassincil/screens/medications/medication_review_screen.dart';
import 'package:yassincil/screens/medications/medication_settings_screen.dart';
import 'package:yassincil/screens/medications/medication_statistics_screen.dart';
import 'package:yassincil/widgets/medications_app_bar.dart';
import 'package:yassincil/utils/app_colors.dart';

class MedicationsManagementScreen extends StatefulWidget {
  const MedicationsManagementScreen({super.key});

  @override
  State<MedicationsManagementScreen> createState() =>
      _MedicationsManagementScreenState();
}

class _MedicationsManagementScreenState
    extends State<MedicationsManagementScreen>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  String _searchQuery = '';
  String _selectedCategory = 'الكل';
  String _currentSortBy = 'date';
  bool _showOnlyPending = false;
  bool _isGridView = false;

  final List<String> _categories = [
    'الكل',
    'مسكنات الألم',
    'مضادات الحيوية',
    'أدوية الضغط',
    'أدوية السكري',
    'أدوية الجهاز الهضمي',
    'أدوية الحساسية',
    'فيتامينات ومكملات',
    'أدوية القلب',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _tabController = TabController(length: _categories.length, vsync: this);
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // تهيئة البيانات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final medicationProvider = Provider.of<MedicationProvider>(
          context,
          listen: false,
        );
        medicationProvider.fetchMedications();
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _refresh() async {
    if (!mounted) return;

    try {
      final medicationProvider = Provider.of<MedicationProvider>(
        context,
        listen: false,
      );
      await medicationProvider.fetchMedications();

      if (mounted) {
        HapticFeedback.lightImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text('تم تحديث البيانات بنجاح', style: GoogleFonts.cairo()),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error during refresh: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Text(
                  'حدث خطأ أثناء تحديث البيانات',
                  style: GoogleFonts.cairo(),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }

  List<Medication> _getFilteredAndSortedMedications(
    List<Medication> medications,
  ) {
    List<Medication> filteredMedications = medications.where((medication) {
      // فلترة البحث
      final matchesSearch =
          _searchQuery.isEmpty ||
          medication.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          medication.company.toLowerCase().contains(
            _searchQuery.toLowerCase(),
          ) ||
          medication.ingredients.toLowerCase().contains(
            _searchQuery.toLowerCase(),
          );

      // فلترة الفئة
      final matchesCategory =
          _selectedCategory == 'الكل' ||
          medication.category == _selectedCategory;

      // فلترة الحالة المعلقة
      final matchesPending =
          !_showOnlyPending ||
          medication.approvalStatus == MedicationApprovalStatus.pending;

      return matchesSearch && matchesCategory && matchesPending;
    }).toList();

    // الترتيب
    switch (_currentSortBy) {
      case 'name':
        filteredMedications.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'company':
        filteredMedications.sort((a, b) => a.company.compareTo(b.company));
        break;
      case 'status':
        filteredMedications.sort(
          (a, b) => b.isAllowed.toString().compareTo(a.isAllowed.toString()),
        );
        break;
      case 'popularity':
        filteredMedications.sort(
          (a, b) => b.likesCount.compareTo(a.likesCount),
        );
        break;
      case 'date':
      default:
        filteredMedications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
    }

    return filteredMedications;
  }

  @override
  Widget build(BuildContext context) {
    final medicationProvider = Provider.of<MedicationProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final isAdmin = authProvider.isAdmin;

    if (!isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: Text('إدارة الأدوية', style: GoogleFonts.cairo()),
        ),
        body: Center(
          child: Text(
            'ليست لديك صلاحية للوصول إلى هذه الصفحة',
            style: GoogleFonts.cairo(fontSize: 16),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: CustomScrollView(
          slivers: [
            _buildModernSliverAppBar(isAdmin),
            SliverToBoxAdapter(
              child: Column(
                children: [
                  _buildModernSearchAndFilters(),
                  _buildModernCategoryTabs(),
                  _buildStatisticsCards(medicationProvider.medications),
                  _buildViewToggle(),
                ],
              ),
            ),
            _buildMedicationsList(medicationProvider, isAdmin),
          ],
        ),
      ),
      floatingActionButton: isAdmin ? _buildFloatingActionButtons() : null,
    );
  }

  Widget _buildModernSliverAppBar(bool isAdmin) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: AppColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'إدارة الأدوية',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontSize: 18,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primary,
                AppColors.primary.withValues(alpha: 0.85),
              ],
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                right: -50,
                top: -50,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.1),
                  ),
                ),
              ),
              Positioned(
                left: -30,
                bottom: -30,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.05),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh_rounded, color: Colors.white),
          onPressed: _refresh,
          tooltip: 'تحديث',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  const Icon(Icons.download),
                  const SizedBox(width: 8),
                  Text('تصدير البيانات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'import',
              child: Row(
                children: [
                  const Icon(Icons.upload),
                  const SizedBox(width: 8),
                  Text('استيراد البيانات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'verify',
              child: Row(
                children: [
                  const Icon(Icons.verified_rounded),
                  const SizedBox(width: 8),
                  Text('التحقق من الأدوية', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'review',
              child: Row(
                children: [
                  const Icon(Icons.rate_review_rounded),
                  const SizedBox(width: 8),
                  Text('مراجعة الأدوية', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'statistics',
              child: Row(
                children: [
                  const Icon(Icons.bar_chart_rounded),
                  const SizedBox(width: 8),
                  Text('إحصائيات الأدوية', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  const Icon(Icons.settings),
                  const SizedBox(width: 8),
                  Text('الإعدادات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildModernSearchAndFilters() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              style: GoogleFonts.cairo(fontSize: 16),
              decoration: InputDecoration(
                hintText: 'ابحث عن دواء أو شركة أو مكون...',
                hintStyle: GoogleFonts.cairo(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                ),
                prefixIcon: Container(
                  padding: const EdgeInsets.all(12),
                  child: Icon(
                    Icons.search_rounded,
                    color: const Color(0xFF2563EB),
                    size: 24,
                  ),
                ),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: Icon(
                          Icons.clear_rounded,
                          color: Colors.grey.shade600,
                        ),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          const SizedBox(height: 16),

          // فلاتر سريعة
          Row(
            children: [
              Expanded(
                child: _buildFilterChip(
                  'المعلقة فقط',
                  _showOnlyPending,
                  Icons.pending_actions,
                  () => setState(() => _showOnlyPending = !_showOnlyPending),
                ),
              ),
              const SizedBox(width: 12),
              _buildSortButton(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    String label,
    bool isSelected,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF2563EB) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? const Color(0xFF2563EB) : Colors.grey.shade300,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: const Color(0xFF2563EB).withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 18,
              color: isSelected ? Colors.white : Colors.grey.shade600,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: GoogleFonts.cairo(
                color: isSelected ? Colors.white : Colors.grey.shade700,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSortButton() {
    return PopupMenuButton<String>(
      onSelected: (value) {
        setState(() {
          _currentSortBy = value;
        });
        HapticFeedback.lightImpact();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.sort_rounded, size: 18, color: Colors.grey.shade600),
            const SizedBox(width: 8),
            Text(
              'ترتيب',
              style: GoogleFonts.cairo(
                color: Colors.grey.shade700,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'date',
          child: Row(
            children: [
              const Icon(Icons.access_time),
              const SizedBox(width: 8),
              Text('الأحدث', style: GoogleFonts.cairo()),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'name',
          child: Row(
            children: [
              const Icon(Icons.sort_by_alpha),
              const SizedBox(width: 8),
              Text('الاسم', style: GoogleFonts.cairo()),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'company',
          child: Row(
            children: [
              const Icon(Icons.business),
              const SizedBox(width: 8),
              Text('الشركة', style: GoogleFonts.cairo()),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'popularity',
          child: Row(
            children: [
              const Icon(Icons.favorite),
              const SizedBox(width: 8),
              Text('الشعبية', style: GoogleFonts.cairo()),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'status',
          child: Row(
            children: [
              const Icon(Icons.check_circle),
              const SizedBox(width: 8),
              Text('الحالة', style: GoogleFonts.cairo()),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildModernCategoryTabs() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: const Color(0xFF2563EB),
        indicatorWeight: 3,
        indicatorSize: TabBarIndicatorSize.label,
        labelColor: const Color(0xFF2563EB),
        unselectedLabelColor: Colors.grey.shade600,
        labelStyle: GoogleFonts.cairo(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
        unselectedLabelStyle: GoogleFonts.cairo(
          fontWeight: FontWeight.normal,
          fontSize: 14,
        ),
        onTap: (index) {
          setState(() {
            _selectedCategory = _categories[index];
          });
          HapticFeedback.lightImpact();
        },
        tabs: _categories.map((category) => Tab(text: category)).toList(),
      ),
    );
  }

  Widget _buildStatisticsCards(List<Medication> allMedications) {
    final totalMedications = allMedications.length;
    final allowedMedications = allMedications.where((m) => m.isAllowed).length;
    final pendingMedications = allMedications
        .where((m) => m.approvalStatus == MedicationApprovalStatus.pending)
        .length;
    final rejectedMedications = allMedications
        .where((m) => m.approvalStatus == MedicationApprovalStatus.rejected)
        .length;

    return Container(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'الإجمالي',
              totalMedications.toString(),
              Icons.medical_services_rounded,
              const Color(0xFF2563EB),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'مسموح',
              allowedMedications.toString(),
              Icons.check_circle_rounded,
              Colors.green,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'معلق',
              pendingMedications.toString(),
              Icons.pending_rounded,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'مرفوض',
              rejectedMedications.toString(),
              Icons.cancel_rounded,
              Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          Text(
            title,
            style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildViewToggle() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildViewButton(
                  Icons.view_list_rounded,
                  !_isGridView,
                  () => setState(() => _isGridView = false),
                ),
                _buildViewButton(
                  Icons.grid_view_rounded,
                  _isGridView,
                  () => setState(() => _isGridView = true),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildViewButton(IconData icon, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: () {
        onTap();
        HapticFeedback.lightImpact();
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF2563EB) : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          size: 20,
          color: isSelected ? Colors.white : Colors.grey.shade600,
        ),
      ),
    );
  }

  Widget _buildMedicationsList(
    MedicationProvider medicationProvider,
    bool isAdmin,
  ) {
    if (medicationProvider.isLoading) {
      return const SliverToBoxAdapter(
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(50),
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    if (medicationProvider.errorMessage != null) {
      return SliverToBoxAdapter(child: _buildErrorState(medicationProvider));
    }

    final filteredMedications = _getFilteredAndSortedMedications(
      medicationProvider.medications,
    );

    if (filteredMedications.isEmpty) {
      return SliverToBoxAdapter(child: _buildEmptyState());
    }

    if (_isGridView) {
      return SliverPadding(
        padding: const EdgeInsets.all(16),
        sliver: SliverGrid(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.75,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
          ),
          delegate: SliverChildBuilderDelegate((context, index) {
            final medication = filteredMedications[index];
            return _buildMedicationGridCard(medication, isAdmin);
          }, childCount: filteredMedications.length),
        ),
      );
    } else {
      return SliverList(
        delegate: SliverChildBuilderDelegate((context, index) {
          final medication = filteredMedications[index];
          return Container(
            margin: const EdgeInsets.only(bottom: 16, left: 16, right: 16),
            child: _buildMedicationListCard(medication, isAdmin),
          );
        }, childCount: filteredMedications.length),
      );
    }
  }

  Widget _buildMedicationListCard(Medication medication, bool isAdmin) {
    return GestureDetector(
      onTap: () => _navigateToDetail(medication),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 20,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // صورة الدواء
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.grey.shade100,
                ),
                child: medication.imageUrls.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: CachedNetworkImage(
                          imageUrl: medication.imageUrls.first,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            color: Colors.grey.shade200,
                            child: const Center(
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: Colors.grey.shade200,
                            child: Icon(
                              Icons.medical_services_rounded,
                              color: Colors.grey.shade400,
                              size: 30,
                            ),
                          ),
                        ),
                      )
                    : Icon(
                        Icons.medical_services_rounded,
                        color: Colors.grey.shade400,
                        size: 30,
                      ),
              ),

              const SizedBox(width: 16),

              // معلومات الدواء
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            medication.name,
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _buildStatusBadge(medication),
                      ],
                    ),

                    const SizedBox(height: 4),

                    Text(
                      medication.company,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    Row(
                      children: [
                        Icon(
                          Icons.category_rounded,
                          size: 16,
                          color: Colors.grey.shade500,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            medication.category,
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: Colors.grey.shade500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    Row(
                      children: [
                        _buildInteractionChip(
                          Icons.favorite_rounded,
                          medication.likesCount.toString(),
                          Colors.red,
                        ),
                        const SizedBox(width: 8),
                        _buildInteractionChip(
                          Icons.comment_rounded,
                          medication.commentsCount.toString(),
                          Colors.blue,
                        ),
                        const Spacer(),
                        if (isAdmin)
                          PopupMenuButton<String>(
                            onSelected: (value) =>
                                _handleMedicationAction(value, medication),
                            child: Icon(
                              Icons.more_vert_rounded,
                              color: Colors.grey.shade600,
                            ),
                            itemBuilder: (context) => [
                              PopupMenuItem(
                                value: 'edit',
                                child: Row(
                                  children: [
                                    const Icon(Icons.edit),
                                    const SizedBox(width: 8),
                                    Text('تعديل', style: GoogleFonts.cairo()),
                                  ],
                                ),
                              ),
                              PopupMenuItem(
                                value: 'delete',
                                child: Row(
                                  children: [
                                    const Icon(Icons.delete, color: Colors.red),
                                    const SizedBox(width: 8),
                                    Text(
                                      'حذف',
                                      style: GoogleFonts.cairo(
                                        color: Colors.red,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              PopupMenuItem(
                                value: 'approve',
                                child: Row(
                                  children: [
                                    const Icon(
                                      Icons.check,
                                      color: Colors.green,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'موافقة',
                                      style: GoogleFonts.cairo(
                                        color: Colors.green,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMedicationGridCard(Medication medication, bool isAdmin) {
    return GestureDetector(
      onTap: () => _navigateToDetail(medication),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 20,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة الدواء
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(16),
                  ),
                  color: Colors.grey.shade100,
                ),
                child: medication.imageUrls.isNotEmpty
                    ? ClipRRect(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(16),
                        ),
                        child: CachedNetworkImage(
                          imageUrl: medication.imageUrls.first,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            color: Colors.grey.shade200,
                            child: const Center(
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: Colors.grey.shade200,
                            child: Icon(
                              Icons.medical_services_rounded,
                              color: Colors.grey.shade400,
                              size: 40,
                            ),
                          ),
                        ),
                      )
                    : Icon(
                        Icons.medical_services_rounded,
                        color: Colors.grey.shade400,
                        size: 40,
                      ),
              ),
            ),

            // معلومات الدواء
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            medication.name,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _buildStatusBadge(medication, isSmall: true),
                      ],
                    ),

                    const SizedBox(height: 4),

                    Text(
                      medication.company,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const Spacer(),

                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.favorite_rounded,
                              size: 14,
                              color: Colors.red.shade400,
                            ),
                            const SizedBox(width: 2),
                            Text(
                              medication.likesCount.toString(),
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                        if (isAdmin)
                          GestureDetector(
                            onTap: () => _showGridCardMenu(context, medication),
                            child: Icon(
                              Icons.more_vert_rounded,
                              size: 16,
                              color: Colors.grey.shade600,
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(Medication medication, {bool isSmall = false}) {
    Color color;
    String text;
    IconData icon;

    if (medication.approvalStatus == MedicationApprovalStatus.approved) {
      color = medication.isAllowed ? Colors.green : Colors.red;
      text = medication.isAllowed ? 'مسموح' : 'ممنوع';
      icon = medication.isAllowed ? Icons.check_circle : Icons.cancel;
    } else if (medication.approvalStatus == MedicationApprovalStatus.pending) {
      color = Colors.orange;
      text = 'معلق';
      icon = Icons.pending;
    } else {
      color = Colors.red;
      text = 'مرفوض';
      icon = Icons.cancel;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isSmall ? 6 : 8,
        vertical: isSmall ? 2 : 4,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: isSmall ? 12 : 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: GoogleFonts.cairo(
              fontSize: isSmall ? 10 : 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInteractionChip(IconData icon, String count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            count,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.search_off_rounded,
              size: 60,
              color: Colors.grey.shade400,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'لا توجد أدوية',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على أدوية تطابق معايير البحث',
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade500),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: () {
              _searchController.clear();
              setState(() {
                _searchQuery = '';
                _selectedCategory = 'الكل';
                _showOnlyPending = false;
              });
            },
            icon: const Icon(Icons.clear_all_rounded),
            label: Text('مسح الفلاتر', style: GoogleFonts.cairo()),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2563EB),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(MedicationProvider medicationProvider) {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.error_outline_rounded,
              size: 60,
              color: Colors.red.shade400,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'حدث خطأ',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            medicationProvider.errorMessage!,
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: () => medicationProvider.fetchMedications(),
            icon: const Icon(Icons.refresh_rounded),
            label: Text('إعادة المحاولة', style: GoogleFonts.cairo()),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2563EB),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton(
          heroTag: "add_medication",
          onPressed: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const EnhancedAddEditMedicationScreen(),
              ),
            );
          },
          backgroundColor: const Color(0xFF2563EB),
          child: const Icon(Icons.add_rounded, color: Colors.white),
        ),
        const SizedBox(height: 16),
        FloatingActionButton(
          heroTag: "scan_barcode",
          onPressed: () {
            // TODO: Implement barcode scanning
          },
          backgroundColor: Colors.green,
          child: const Icon(Icons.qr_code_scanner_rounded, color: Colors.white),
        ),
      ],
    );
  }

  void _navigateToDetail(Medication medication) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MedicationDetailScreen(medication: medication),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _showExportDialog();
        break;
      case 'import':
        _showImportDialog();
        break;
      case 'verify':
        Navigator.of(context).push(
          MaterialPageRoute(builder: (_) => const VerifyMedicationsScreen()),
        );
        break;
      case 'review':
        Navigator.of(context).push(
          MaterialPageRoute(builder: (_) => const MedicationReviewScreen()),
        );
        break;
      case 'statistics':
        Navigator.of(context).push(
          MaterialPageRoute(builder: (_) => const MedicationStatisticsScreen()),
        );
        break;
      case 'settings':
        _showSettingsDialog();
        break;
    }
  }

  void _handleMedicationAction(String action, Medication medication) {
    switch (action) {
      case 'edit':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) =>
                EnhancedAddEditMedicationScreen(medication: medication),
          ),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(medication);
        break;
      case 'approve':
        _approveMedication(medication);
        break;
    }
  }

  void _showGridCardMenu(BuildContext context, Medication medication) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 50,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(3),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Text(
                    medication.name,
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  ListTile(
                    leading: const Icon(Icons.edit, color: Colors.blue),
                    title: Text('تعديل', style: GoogleFonts.cairo()),
                    onTap: () {
                      Navigator.pop(context);
                      _handleMedicationAction('edit', medication);
                    },
                  ),
                  ListTile(
                    leading: const Icon(Icons.delete, color: Colors.red),
                    title: Text(
                      'حذف',
                      style: GoogleFonts.cairo(color: Colors.red),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      _handleMedicationAction('delete', medication);
                    },
                  ),
                  ListTile(
                    leading: const Icon(Icons.check, color: Colors.green),
                    title: Text(
                      'موافقة',
                      style: GoogleFonts.cairo(color: Colors.green),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      _handleMedicationAction('approve', medication);
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showExportDialog() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تصدير البيانات', style: GoogleFonts.cairo()),
        content: Text(
          'هل تريد تصدير جميع بيانات الأدوية؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _exportMedications();
            },
            child: Text('تصدير', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  Future<void> _showImportDialog() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('استيراد البيانات', style: GoogleFonts.cairo()),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختر كيفية التعامل مع العناصر المكررة:',
              style: GoogleFonts.cairo(),
            ),
            const SizedBox(height: 12),
            Text('• تخطي المكررات: لن يتم تعديل العناصر الموجودة.'),
            const SizedBox(height: 6),
            Text(
              '• الاستبدال: سيتم استبدال العناصر المطابقة (حسب الباركود أو الاسم+الشركة).',
            ),
            const SizedBox(height: 6),
            Text(
              '• الاستبدال مع دمج الصور: يتم الاستبدال ودمج الصور بدون تكرار.',
            ),
            const SizedBox(height: 8),
            Text(
              'ملاحظة: دمج الصور قد يزيد من حجم التخزين المستخدم عند وجود صور كثيرة.',
              style: GoogleFonts.cairo(fontSize: 12, color: Colors.black54),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _importMedications(replaceDuplicates: false);
            },
            child: Text('تخطي المكررات', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _importMedications(replaceDuplicates: true);
            },
            child: Text('الاستبدال', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _importMedications(
                replaceDuplicates: true,
                mergeImages: true,
              );
            },
            child: Text('الاستبدال (دمج الصور)', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إعدادات الإدارة', style: GoogleFonts.cairo()),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.notifications),
              title: Text(
                'تنبيهات الأدوية الجديدة',
                style: GoogleFonts.cairo(),
              ),
              trailing: Switch(value: true, onChanged: (value) {}),
            ),
            ListTile(
              leading: const Icon(Icons.auto_delete),
              title: Text(
                'حذف الأدوية المرفوضة تلقائياً',
                style: GoogleFonts.cairo(),
              ),
              trailing: Switch(value: false, onChanged: (value) {}),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(Medication medication) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد الحذف', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من حذف "${medication.name}"؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteMedication(medication);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _deleteMedication(Medication medication) async {
    try {
      final medicationProvider = Provider.of<MedicationProvider>(
        context,
        listen: false,
      );
      await medicationProvider.deleteMedication(medication.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف الدواء بنجاح', style: GoogleFonts.cairo()),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء حذف الدواء',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // ===== Export/Import (JSON) =====
  Future<void> _exportMedications() async {
    try {
      final medicationProvider = Provider.of<MedicationProvider>(
        context,
        listen: false,
      );
      final meds = medicationProvider.medications;

      // حوّل لكل دواء Map مناسب للملف (forDb: true لتبسيط الحقول)
      final List<Map<String, dynamic>> data = meds
          .map((m) => m.toMap(forDb: true))
          .toList();

      final jsonString = const JsonEncoder.withIndent('  ').convert(data);

      // احفظ الملف في Documents
      final dir = await getApplicationDocumentsDirectory();
      final file = File(
        '${dir.path}/medications_export_${DateTime.now().millisecondsSinceEpoch}.json',
      );
      await file.writeAsString(jsonString, encoding: utf8);

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم حفظ الملف: ${file.path}',
            style: GoogleFonts.cairo(),
          ),
        ),
      );

      // مشاركة اختيارية
      await Share.shareXFiles([XFile(file.path)], text: 'تصدير أدوية');
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل تصدير الأدوية: $e', style: GoogleFonts.cairo()),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _importMedications({
    bool replaceDuplicates = false,
    bool mergeImages = false,
  }) async {
    try {
      // اختيار ملف JSON
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );
      if (result == null || result.files.single.path == null) return;

      final path = result.files.single.path!;
      final file = File(path);
      final content = await file.readAsString(encoding: utf8);
      final List<dynamic> decoded = jsonDecode(content);

      // تحويل كل عنصر إلى Medication عبر fromMap
      final List<Medication> meds = decoded
          .whereType<Map<String, dynamic>>()
          .map((map) => Medication.fromMap(map))
          .toList();

      if (meds.isEmpty) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'الملف لا يحتوي أدوية صالحة',
              style: GoogleFonts.cairo(),
            ),
          ),
        );
        return;
      }

      final medicationProvider = Provider.of<MedicationProvider>(
        context,
        listen: false,
      );

      // أسماء الشركات والاسم قد تأتي بتباين مسافات/حروف، نقارن بعد تنسيق بسيط
      String norm(String? s) => (s ?? '').trim().toLowerCase();
      // دمج الصور بدون تكرار مع الحفاظ على الترتيب
      List<String> mergeUniqueImages(List<String> a, List<String> b) {
        final seen = <String>{};
        final out = <String>[];
        for (final url in [...a, ...b]) {
          final v = url.trim();
          if (v.isEmpty) continue;
          if (seen.add(v)) out.add(v);
        }
        return out;
      }

      int added = 0, skipped = 0, updated = 0, failed = 0;

      // اجلب قائمة حالية لتجنب الاستعلام لكل عنصر
      final existing = List<Medication>.from(medicationProvider.medications);

      for (final m in meds) {
        try {
          // ابحث عن مطابق (باركود أو اسم+شركة)
          final hasBarcode =
              (m.barcode != null && m.barcode!.trim().isNotEmpty);
          final matchIndex = existing.indexWhere(
            (e) =>
                (hasBarcode && norm(e.barcode) == norm(m.barcode)) ||
                (norm(e.name) == norm(m.name) &&
                    norm(e.company) == norm(m.company)),
          );

          final exists = matchIndex != -1;

          if (exists && !replaceDuplicates) {
            skipped++;
            continue;
          }

          if (exists && replaceDuplicates) {
            final existingMed = existing[matchIndex];
            final nextImages = mergeImages
                ? mergeUniqueImages(existingMed.imageUrls, m.imageUrls)
                : (m.imageUrls.isNotEmpty
                      ? m.imageUrls
                      : existingMed.imageUrls);
            // استبدال مع الحفاظ على createdAt والتفاعلات
            final updatedMed = m.copyWith(
              id: existingMed.id,
              createdAt: existingMed.createdAt,
              imageUrls: nextImages,
              likes: existingMed.likes,
              likesCount: existingMed.likesCount,
              commentsCount: existingMed.commentsCount,
              ratingsCount: existingMed.ratingsCount,
              averageRating: existingMed.averageRating,
            );
            await medicationProvider.updateMedication(updatedMed);
            existing[matchIndex] = updatedMed;
            updated++;
          } else {
            // إضافة جديدة مع ضبط createdAt للوقت الحالي
            final newMed = m.copyWith(
              createdAt: DateTime.now(),
              updatedAt: null,
            );
            await medicationProvider.addMedication(newMed);
            existing.add(newMed);
            added++;
          }
        } catch (_) {
          failed++;
          // نتجاوز الأخطاء الفردية ونكمل
        }
      }

      if (!mounted) return;
      final msg = replaceDuplicates
          ? 'المضافة $added | المحدّثة $updated | المتجاوزة $skipped | فشلت $failed'
          : 'المضافة $added | المتجاوزة $skipped | فشلت $failed';
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم الاستيراد: $msg', style: GoogleFonts.cairo()),
          backgroundColor: (added + updated) > 0 && failed == 0
              ? Colors.green
              : null,
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل استيراد الأدوية: $e', style: GoogleFonts.cairo()),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _approveMedication(Medication medication) async {
    try {
      final medicationProvider = Provider.of<MedicationProvider>(
        context,
        listen: false,
      );
      await medicationProvider.approveMedication(medication.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم اعتماد الدواء بنجاح', style: GoogleFonts.cairo()),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء اعتماد الدواء',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
